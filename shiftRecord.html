<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        a {
            color: #099DFC;
            font-weight: bold;
        }

        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 3px;
            font-weight: bold;
        }

        table td {
            font-size: 15px !important;
            height: 40px !important;
        }
    </style>
</head>

<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box"
             style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <input id="shiftSeatContentId" type="hidden" />
            <div class="group-box-title">交接班记录<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;">
                    <span>&nbsp;调度台</span>
                    <input id="seatId" name="dept" style="width: 200px;margin-top: 10px;" value="">
                    交接时间： <input id="startTime" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endTime" class="easyui-datetimebox" style="width:200px;" />
                    <a class="common-button" id="selectShiftContentListDatasBtn"
                       style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;"
                       href="javascript:selectShiftSeatContentListDatas();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                    <a class="common-button" id="executeShiftBtn"
                       style="width: 100px; height: 30px; line-height: 30px;font-size: 14px;margin-left: 10px;"
                       href="javascript:executeShiftSeat();">
                        <i class="fa fa-exchange"></i>&nbsp;执行交接班
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-calls-pages" style="height: Calc(100% - 100px)" pagination="true"
                       singleSelect="true" rownumbers="true" fit="true" toolbar="#tbCallList">
                    <thead>
                        <tr>
                            <th field="seatCode" width="100" align="center">调度台</th>
                            <th field="offGoingTime" width="140" align="center">交班时间</th>
                            <th field="offGoingScheduleName" width="120" align="center">交班班次</th>
                            <th field="offGoingName" width="100" align="center">当班人员</th>
                            <th field="callIn" width="90" align="center">呼入(次)</th>
                            <th field="accept" width="90" align="center">受理(次)</th>
                            <th field="dispatch" width="90" align="center">派车(辆)</th>
                            <th field="vaildDispatch" width="100" align="center">有效出车(辆)</th>
                            <th field="emptyDispatch" width="90" align="center">空车(辆)</th>
                            <th field="cancelDispatch" width="100" align="center">取消派车(辆)</th>
                            <th field="patientNum" width="100" align="center">患者人数(人)</th>
                            <th field="pick" width="90" align="center">摘机率(%)</th>
                            <th field="publicEventNum" width="100" align="center">重大事件(次)</th>
                            <th field="woundedNum" width="100" align="center">重大事件(伤)</th>
                            <th field="deadNum" width="100" align="center">重大事件(亡)</th>
                            <th field="successorTime" width="140" align="center">接班时间</th>
                            <th field="summaryStatusDesc" width="80" align="center">汇总状态</th>
                            <th field="id" width="100" fixed="right" align="center" formatter="operation">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <!--修改弹窗-->
        <div id="change-shifts-win" class="easyui-window" title="修改交接班内容" style="width:800px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:100%;font-size:18px;color: #F59A23;font-weight:bolder;">
                    交班人：
                    <span id="shift-offGoingUser"></span>
                    ->
                    <span id="shift-successorUser"></span>
                </span>
            </p>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:100%;font-size:18px;color: #F59A23;font-weight:bolder;">
                    交班班次：
                    <span id="shift-offGoingScheduleName"></span>
                    ->
                    <span id="shift-successorScheduleName"></span>
                </span>
            </p>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:350px;font-size:16px;color: #F59A23;">
                    交班时间：
                    <span id="shift-offGoing-time"></span>
                </span>
                <!-- <span style="display:inline-block;width:350px;font-size:16px;color: #F59A23;">
                    夏令时：
                    <span id="shift-daylight-saving-time"></span>
                </span> -->
            </p>
            <div style="text-align:center;margin: 20px;font-size:16px;">
                <table>
                    <tr>
                        <th colspan="8">交班内容</th>
                    </tr>
                    <tr>
                        <td><span style="color:red;">*</span>呼入(次)</td>
                        <td><span style="color:red;">*</span>受理(次)</td>
                        <td><span style="color:red;">*</span>派车(辆)</td>
                        <td><span style="color:red;">*</span>有效出车(辆)</td>
                        <td><span style="color:red;">*</span>空车(辆)</td>
                        <td><span style="color:red;">*</span>取消派车(辆)</td>
                        <td><span style="color:red;">*</span>患者人数(人)</td>
                        <td><span style="color:red;">*</span>摘机率(%)</td>
                    </tr>
                    <tr>
                        <td><input id="callIn" type="text" value="" oninput="inputCount(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="accept" type="text" value="" oninput="inputCount(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="dispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="vaildDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="emptyDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="cancelDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="patientNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="pick" type="text" value="" style="height:100%;width: 100%;text-align:center" maxlength="10" disabled readonly /></td>
                    </tr>
                </table>
                <table style="margin-top: 15px;font-size:16px;">
                    <tr>
                        <th colspan="3">突发公共事件情况</th>
                    </tr>
                    <tr>
                        <td><span style="color:red;">*</span>次数</td>
                        <td><span style="color:red;">*</span>伤(人)</td>
                        <td><span style="color:red;">*</span>亡(人)</td>
                    </tr>
                    <tr>
                        <td><input id="publicEventNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 245px;text-align:center" maxlength="10" /></td>
                        <td><input id="woundedNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 245px;text-align:center" maxlength="10" /></td>
                        <td><input id="deadNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 245px;text-align:center" maxlength="10" /></td>
                    </tr>
                </table>
            </div>
            <p style="text-align:center;margin: 5px 0;">
                <a class="easyui-linkbutton" id="update-btn" href="javascript:updateShiftSeatContent();"
                   style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px; display: none">修改</a>
                <a class="easyui-linkbutton" id="cancel-btn" href="javascript:closeUpdateShiftSeatWin();"
                   style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">关闭</a>
            </p>
        </div>
        
        <!--执行交接班弹窗-->
        <div id="execute-shifts-win" class="easyui-window" title="正在执行交接班" style="width:920px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <div style="padding: 12px;">
                <!-- 卡片1：座席和班次信息 -->
                <div style="background: linear-gradient(135deg, #099dfc 0%, #099dfc 100%); border-radius: 6px; padding: 12px; margin-bottom: 8px; color: white;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; align-items: center;">
                        <div style="font-size: 16px; font-weight: 600;">
                            <div style="margin-bottom: 4px;">
                                <span style="opacity: 0.9;">座席：</span><span id="execute-shift-seat" style="font-weight: bold; margin-left: 8px;"></span>
                            </div>
                            <div>
                                <span style="opacity: 0.9;">当前班次：</span><span id="execute-shift-curScheduleName" style="font-weight: bold; margin-left: 8px;"></span>
                            </div>
                        </div>
                        <div style="font-size: 16px; font-weight: 600;">
                            <div style="margin-bottom: 4px;">
                                <span style="opacity: 0.9;">交班人：</span><span id="execute-shift-offGoingUser" style="font-weight: bold; margin-left: 8px;"></span> [<span id="execute-shift-offGoingUserName"></span>]
                            </div>
                            <div>
                                <span style="opacity: 0.9;">交班班次：</span><span id="execute-shift-offGoingScheduleName" style="font-weight: bold; margin-left: 8px;"></span> → <span id="execute-shift-successorScheduleName" style="font-weight: bold;"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 卡片2：交班时间设置 -->
                <div style="background: #f8f9fa; border: 1px solid #e1e8ed; border-radius: 6px; padding: 12px; margin-bottom: 8px;">
                    <div style="text-align: center;">
                        <span style="font-size: 14px; color: #495057; margin-right: 15px;">
                            <span id="execute-shift-offGoingScheduleName-time" style="font-weight: 600; color: #2c3e50;"></span><span style="font-weight: 600; color: #2c3e50;">班次时间：</span>
                            <input id="execute-change-start-times" data-options="required:true,showSeconds:false" class="easyui-datetimebox" style="width:140px; margin: 0 4px; font-size: 14px;" />
                            <span style="color: #7f8c8d; margin: 0 4px;">至</span>
                            <input id="execute-change-end-times" data-options="required:true,showSeconds:false" class="easyui-datetimebox" style="width:140px; margin: 0 4px; font-size: 14px;" />
                        </span>
                        <span style="font-size: 14px; color: #495057; margin-left: 15px;">
                            <span style="font-weight: 600; color: #2c3e50;">交班时间：</span>
                            <input id="execute-shift-handover-times" data-options="required:true,showSeconds:false" class="easyui-datetimebox" style="width:140px; margin-left: 4px; font-size: 14px;" />
                        </span>
                    </div>
                </div>

                <!-- 卡片3：交班内容 -->
                <div style="background: #f8f9fa; border: 1px solid #e1e8ed; border-radius: 6px; padding: 12px; margin-bottom: 8px;">
                    <div style="text-align: center;">
                        <div style="font-weight: 600; margin-bottom: 8px; font-size: 15px; color: #2c3e50;">📊 交班内容</div>
                                                 <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                             <tr style="background: #6c757d; color: white;">
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>呼入</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>受理</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>派车</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>有效出车</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>空车</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>取消派车</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>患者人数</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>摘机率</th>
                             </tr>
                            <tr style="background: white;">
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeCallIn" type="text" value="" oninput="executeInputCount(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeAccept" type="text" value="" oninput="executeInputCount(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeDispatch" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeVaildDispatch" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeEmptyDispatch" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeCancelDispatch" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executePatientNum" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executePick" value="" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 4px; background: #e8f4fd; color: #666;" maxlength="10" disabled readonly /></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 卡片4：突发公共事件 -->
                <div style="background: #f8f9fa; border: 1px solid #e1e8ed; border-radius: 6px; padding: 12px; margin-bottom: 8px;">
                    <div style="text-align: center;">
                        <div style="font-weight: 600; margin-bottom: 8px; font-size: 15px; color: #2c3e50;">⚠️ 突发公共事件情况</div>
                                                 <table style="width: 50%; margin: 0 auto; border-collapse: collapse; font-size: 14px;">
                             <tr style="background: #6c757d; color: white;">
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>次数</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>伤(人)</th>
                                 <th style="border: 1px solid #ddd; padding: 6px; font-size: 14px; font-weight: 600;"><span style="color: red;">*</span>亡(人)</th>
                             </tr>
                            <tr style="background: white;">
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executePublicEventNum" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 6px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeWoundedNum" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 6px; background: transparent;" maxlength="10" /></td>
                                <td style="border: 1px solid #ddd; padding: 3px;"><input id="executeDeadNum" type="text" value="" oninput="executeValidateInputPositiveOrZero(this)" style="width: 100%; text-align:center; border: none; font-size: 14px; padding: 6px; background: transparent;" maxlength="10" /></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 卡片5：接班人和密码 -->
                <div style="background: #f8f9fa; border: 1px solid #e1e8ed; border-radius: 6px; padding: 12px; margin-bottom: 12px;">
                    <div style="display: flex; justify-content: center; align-items: center; gap: 25px;">
                        <div style="font-size: 14px; color: #495057; display: flex; align-items: center;">
                            <span style="color: #e74c3c; font-weight: bold;">*</span>
                            <span style="font-weight: 600; color: #2c3e50; margin: 0 8px;">接班人：</span>
                            <input id="executeSuccessor" name="dept" style="width: 260px; height: 30px; font-size: 14px; border: 1px solid #ddd; border-radius: 4px; padding: 0 8px;" value="">
                        </div>
                        <div style="font-size: 14px; color: #495057; display: flex; align-items: center;">
                            <span style="color: #e74c3c; font-weight: bold;">*</span>
                            <span style="font-weight: 600; color: #2c3e50; margin: 0 8px;">密码：</span>
                            <input id="executePassword" type="password" value="" style="width: 260px; height: 30px; font-size: 14px; border: 1px solid #ddd; border-radius: 4px; padding: 0 8px;" maxlength="20" />
                        </div>
                    </div>
                </div>

                                 <!-- 操作按钮 -->
                 <div style="text-align: center; padding-top: 8px;">
                     <a class="common-button" id="execute-shift-btn" href="javascript:doExecuteSuccessor();" style="width: 100px; height: 30px; line-height: 30px; font-size: 14px; margin: 0 8px;">
                         <i class="fa fa-exchange"></i>&nbsp;交班
                     </a>
                     <a class="common-button" id="execute-cancel-btn" href="javascript:cancleExecuteSuccessor();" style="width: 100px; height: 30px; line-height: 30px; font-size: 14px; margin: 0 8px;">
                         <i class="fa fa-times"></i>&nbsp;取消
                     </a>
                 </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
        var _user = null;
        try {
            _user = evalJson(window.parent.gProxy.getUserInfo());
        } catch (e) {
            _user = evalJson(window.parent.bsProxy.getUserInfo());
        }
        _seatsList = []//所有座席
        var _successorList = [] //接班人字典列表（执行交接班用）
        var _executeSuccessorData = null //执行交接班人数据
        
        //获得座席交接班记录内容分页
        $(document).ready(function () {
            //初始化座席和用户信息
            try {
                _token = window.parent._token;
            } catch (e) {
                console.log('获取主窗口信息失败:', e);
            }
            //获得开始时间
            var curDate = new Date();
            var fromTime = new Date(curDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            $("#startTime").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd'));

            $('#dg-calls-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });
            //分页控件
            var pg = $("#dg-calls-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: 10,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        selectShiftSeatContentListDatas(pageNumber, pageSize);
                    }
                });
            }

        });
        //数据列表
        function selectShiftSeatContentListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 10;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 10;
            }
            var startTimeVal = $('#startTime').datetimebox('getValue');
            var startTime = startTimeVal == undefined || startTimeVal == "" ? null : new Date(startTimeVal).formatString("yyyy-MM-dd");
            var endTimeVal = $('#endTime').datetimebox('getValue');
            var endTime = endTimeVal == undefined || endTimeVal == "" ? null : new Date(endTimeVal).formatString("yyyy-MM-dd");
            var seatId = $('#seatId').val() == "全部" || $('#seatId').val() == "" ? "" : $('#seatId').combobox('getValue');
            //打开进度条：
            $('#dg-calls-pages').datagrid('loading');//打开等待div

            $('selectShiftContentListDatasBtn').attr('disabled', 'disabled');//按钮变成不能使用
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startTime": startTime,
                "endTime": endTime,
                "seatId": seatId
            };
            selectShiftSeatList(params, function (data) {
                $('#dg-calls-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    //交班时间格式转换
                    if (data.content[i].offGoingTime) {
                        let timeArr = data.content[i].offGoingTime.split('')
                        let y = timeArr.slice(0, 4).join('')
                        let m = timeArr.slice(4, 6).join('')
                        let d = timeArr.slice(6, 8).join('')
                        let hh = timeArr.slice(8, 10).join('')
                        let mm = timeArr.slice(10, 12).join('')
                        let ss = timeArr.slice(12, 14).join('')
                        data.content[i].offGoingTimeFormat = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss;
                    }
                    //接班时间格式转换
                    if (data.content[i].successorTime) {
                        let timeArr = data.content[i].successorTime.split('')
                        let y = timeArr.slice(0, 4).join('')
                        let m = timeArr.slice(4, 6).join('')
                        let d = timeArr.slice(6, 8).join('')
                        let hh = timeArr.slice(8, 10).join('')
                        let mm = timeArr.slice(10, 12).join('')
                        let ss = timeArr.slice(12, 14).join('')
                        data.content[i].successorTimeFormat = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss;
                    }
                    //汇总状态
                    if (data.content[i].summaryStatus) {
                        let summaryStatusDesc = '';
                        if (data.content[i].summaryStatus == '0') {
                            summaryStatusDesc = '待汇总'
                        } else if (data.content[i].summaryStatus == '1') {
                            summaryStatusDesc = '汇总中'
                        } else if (data.content[i].summaryStatus == '2') {
                            summaryStatusDesc = '已汇总'
                        } else {
                            summaryStatusDesc = '';
                        }
                        data.content[i].summaryStatusDesc = summaryStatusDesc
                    }
                    $('#dg-calls-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            seatCode: data.content[i].seatCode,
                            offGoingTime: data.content[i].offGoingTimeFormat,
                            offGoingScheduleName: data.content[i].offGoingScheduleName,
                            offGoingName: data.content[i].offGoingName,
                            callIn: data.content[i].callIn,
                            accept: data.content[i].accept,
                            dispatch: data.content[i].dispatch,
                            vaildDispatch: data.content[i].vaildDispatch,
                            emptyDispatch: data.content[i].emptyDispatch,
                            cancelDispatch: data.content[i].cancelDispatch,
                            patientNum: data.content[i].patientNum,
                            pick: data.content[i].pick,
                            publicEventNum: data.content[i].publicEventNum,
                            woundedNum: data.content[i].woundedNum,
                            deadNum: data.content[i].deadNum,
                            successorTime: data.content[i].successorTimeFormat,
                            id: data.content[i].id,
                            shiftSeatContentId: data.content[i].shiftSeatContentId,
                            summaryStatus: data.content[i].summaryStatus,
                            summaryStatusDesc: data.content[i].summaryStatusDesc
                        }
                    });
                }
                $("#dg-calls-pages").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                $('#dg-calls-pages').datagrid('loaded');//关闭loding进度条；
                $('#selectShiftContentListDatasBtn').removeAttr('disabled');//按钮变成可用
            }, function (e, url, errMsg) {
                $('#dg-calls-pages').datagrid('loaded');//关闭loding进度条；
                $('#selectShiftContentListDatasBtn').removeAttr('disabled');//按钮变成可用
                //失败才做处理
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        //操作
        function operation(value, row, index) {
            let updateBtn = '<a href="#" style="color: #099DFC;" onclick="updateShiftSeatContentWin(\'' + row.id + '\', \'edit\')">修改</a>';
            let viewBtn = '<a href="#" style="color: #099DFC;" onclick="updateShiftSeatContentWin(\'' + row.id + '\', \'view\')">详情</a>';
            if (row.summaryStatus == '0' || row.summaryStatus == '1') {
                return updateBtn + '&nbsp;' + viewBtn;
            } else if (row.summaryStatus == '2') {
                return viewBtn;
            } else {
                return viewBtn;
            }
        }
        //打开弹窗
        function updateShiftSeatContentWin(id, type) {
            if (type === 'edit') {
                $('#change-shifts-win').attr('title', '修改交接班内容');
            } else if (type === 'view') {
                $('#change-shifts-win').attr('title', '查看交接班内容');
            }

            getShiftSeatDetailById({ 'id': id },
                function (res) {
                    $('#change-shifts-win').window('open'); //打开交接班界面

                    $('#shift-offGoingUser').text(res.offGoingName);
                    $('#shift-successorUser').text(res.successorName ? res.successorName : '');

                    $('#shift-offGoingScheduleName').text(res.offGoingScheduleName)
                    $('#shift-offGoingScheduleName-time').text(res.offGoingScheduleName)
                    $('#shift-successorScheduleName').text(res.successorScheduleName ? res.successorScheduleName : '')

                    if (res.offGoingTime) {
                        let timeArr = res.offGoingTime.split('')
                        let y = timeArr.slice(0, 4).join('')
                        let m = timeArr.slice(4, 6).join('')
                        let d = timeArr.slice(6, 8).join('')
                        let hh = timeArr.slice(8, 10).join('')
                        let mm = timeArr.slice(10, 12).join('')
                        let ss = timeArr.slice(12, 14).join('')
                        $('#shift-offGoing-time').text(y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss)
                    }

                    $('#shift-daylight-saving-time').text(res.offGoingScheduleStartTime + ' - ' + res.offGoingScheduleEndTime)
                    if (type === 'edit') {
                        $('#shiftSeatContentId').val(res.shiftSeatContentId).prop('readonly', false);
                        $('#callIn').val(res.callIn).prop('readonly', false);
                        $('#accept').val(res.accept).prop('readonly', false);
                        $('#dispatch').val(res.dispatch).prop('readonly', false);
                        $('#vaildDispatch').val(res.vaildDispatch).prop('readonly', false);
                        $('#emptyDispatch').val(res.emptyDispatch).prop('readonly', false);
                        $('#cancelDispatch').val(res.cancelDispatch).prop('readonly', false);
                        $('#patientNum').val(res.patientNum).prop('readonly', false);
                        $('#pick').val(res.pick).prop('readonly', false);

                        $('#publicEventNum').val(res.publicEventNum).prop('readonly', false);
                        $('#woundedNum').val(res.woundedNum).prop('readonly', false);
                        $('#deadNum').val(res.deadNum).prop('readonly', false);

                        $('#update-btn').show();
                    } else if (type === 'view') {

                        $('#shiftSeatContentId').val(res.shiftSeatContentId).prop('readonly', true);
                        $('#callIn').val(res.callIn).prop('readonly', true);
                        $('#accept').val(res.accept).prop('readonly', true);
                        $('#dispatch').val(res.dispatch).prop('readonly', true);
                        $('#vaildDispatch').val(res.vaildDispatch).prop('readonly', true);
                        $('#emptyDispatch').val(res.emptyDispatch).prop('readonly', true);
                        $('#cancelDispatch').val(res.cancelDispatch).prop('readonly', true);
                        $('#patientNum').val(res.patientNum).prop('readonly', true);
                        $('#pick').val(res.pick).prop('readonly', true);

                        $('#publicEventNum').val(res.publicEventNum).prop('readonly', true);
                        $('#woundedNum').val(res.woundedNum).prop('readonly', true);
                        $('#deadNum').val(res.deadNum).prop('readonly', true);

                        $('#update-btn').hide();
                    }
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                }
            );
            // $('#change-shifts-win').window('close', true);
            // if (row.id) {
            // $('#change-shifts-win').window('open'); //打开交接班界面
            // shiftContentUpdate({ data }, function (res) {
            //     $.messager.alert('成功', res.msg);
            // },
            //     function (e, url, errMsg) {
            //         $.messager.alert('异常', '异常信息：' + errMsg);
            //     }
            // )
            // }
        }
        //交接班内容更新
        function updateShiftSeatContent() {
            if ($('#callIn').val() == null || $('#callIn').val() === '') {
                $.messager.alert('温馨提示', '请输入呼入次数', 'info');
                return;
            }
            if ($('#accept').val() == null || $('#accept').val() === '') {
                $.messager.alert('温馨提示', '请输入受理次数', 'info');
                return;
            }
            if ($('#dispatch').val() == null || $('#dispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入派车辆数', 'info');
                return;
            }
            if ($('#vaildDispatch').val() == null || $('#vaildDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入有效出车辆数', 'info');
                return;
            }
            if ($('#emptyDispatch').val() == null || $('#emptyDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入空车辆数', 'info');
                return;
            }
            if ($('#cancelDispatch').val() == null || $('#cancelDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入取消派车辆数', 'info');
                return;
            }
            if ($('#patientNum').val() == null || $('#patientNum').val() === '') {
                $.messager.alert('温馨提示', '请输入患者人数', 'info');
                return;
            }
            if ($('#pick').val() == null || $('#pick').val() === '') {
                $.messager.alert('温馨提示', '请输入摘机率', 'info');
                return;
            }
            if ($('#publicEventNum').val() == null || $('#publicEventNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件次数', 'info');
                return;
            }
            if ($('#woundedNum').val() == null || $('#woundedNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件伤员人数', 'info');
                return;
            }
            if ($('#deadNum').val() == null || $('#deadNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件死亡人数', 'info');
                return;
            }
            
            var params = {
                "id": $('#shiftSeatContentId').val(),
                "callIn": $('#callIn').val(),
                "accept": $('#accept').val(),
                "dispatch": $('#dispatch').val(),
                "vaildDispatch": $('#vaildDispatch').val(),
                "emptyDispatch": $('#emptyDispatch').val(),
                "cancelDispatch": $('#cancelDispatch').val(),
                "patientNum": $('#patientNum').val(),
                "pick": $('#pick').val(),
                "publicEventNum": $('#publicEventNum').val(),
                "woundedNum": $('#woundedNum').val(),
                "deadNum": $('#deadNum').val()
            }
            shiftContentUpdate(params,
                function (res) {
                    //交接班更新弹窗关闭
                    $('#change-shifts-win').window('close', true);
                    cleanChangeShiftsWin();
                    selectShiftSeatContentListDatas();
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '更新交接班记录失败，异常信息：' + errMsg);
                    //$('#change-shifts-win').window('close', true);
                    selectShiftSeatContentListDatas();
                }
            )

        }
        //取消修改
        function closeUpdateShiftSeatWin() {
            //交接班更新弹窗关闭
            $('#change-shifts-win').window('close');
            cleanChangeShiftsWin();
        }

        //清理数据
        function cleanChangeShiftsWin() {
            $('#shift-offGoingUser').text("");
            $('#shift-successorUser').text("");

            $('#shift-offGoingScheduleName').text('');
            $('#shift-offGoingScheduleName-time').text('');
            $('#shift-successorScheduleName').text('');

            $('#shift-offGoing-time').text('');
            $('#shift-daylight-saving-time').text('');

            $('#shiftSeatContentId').val('');
            $('#callIn').val('');
            $('#accept').val('');
            $('#dispatch').val('');
            $('#vaildDispatch').val('');
            $('#emptyDispatch').val('');
            $('#cancelDispatch').val('');
            $('#patientNum').val('');
            $('#pick').val('');

            $('#publicEventNum').val('');
            $('#woundedNum').val('');
            $('#deadNum').val('');
        }

        //输入只能是0或正整数
        function validateInputPositiveOrZero(input) {
            const value = input.value;
            const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
            if (!regex.test(value)) {
                input.value = ""; // 清空输入框
            }
        }

        //计算摘机率
        function inputCount(input) {
            validateInputPositiveOrZero(input)
            if ($('#callIn').val() == '0' || $('#callIn').val() == '' || $('#accept').val() == '0' || $('#accept').val() == '') {
                $('#pick').val('0.00')
            }
            if ($('#callIn').val() > 0 && $('#accept').val() > 0) {
                var pick = $('#accept').val() / $('#callIn').val() * 100;
                $('#pick').val(pick.toFixed(2));
            }
        }

        //获取所有座席
        getSeatsList({},
            function (res) {
                _seatsList = res
                _seatsList.unshift({
                    id: '',
                    seatId: '全部座席'
                })
                $('#seatId').combobox({
                    prompt: '输入座席号',
                    editable: true,
                    hasDownArrow: true,
                    valueField: 'id',
                    textField: 'seatId',
                    data: _seatsList,
                    filter: function (q, row) {
                        var opts = $(this).combobox('options');
                        return row[opts.textField].indexOf(q) > -1;
                    },
                    onSelect: function (params) {
                    }
                });
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('提示', '获取所有座席：' + errMsg);
            });

        //获取座席的所有用户（执行交接班用）
        try {
            getAllSeatUser({},
                function (res) {
                    _successorList = res
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获取用户失败：' + errMsg);
                });
        } catch (e) {
            console.log('获取接班人列表失败:', e);
        }

        /** 执行交接班 */
        function executeShiftSeat() {
            //检查是否有权限执行交接班
            if (!_pSeat || !_user) {
                $.messager.alert('提示', '座席信息获取失败，无法执行交接班', 'error');
                return;
            }

            //接警分机开启免打扰（只有在主屏的座席才需要这样操作）
            try {
                var ele = window.parent.$('#callInStatus').children(".move");
                var noDisturb = "on";
                var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
                window.parent.extAssign(params,
                    function (res) {
                        window.parent.showProcess(false);
                        //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                        if (ele.attr("data-state") == "on") {
                            //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                            ele.animate({ left: "0" }, 300, function () {
                                ele.attr("data-state", "off");
                            });
                            window.parent.$('#callInStatus').removeClass("on").addClass("off");
                        } else if (ele.attr("data-state") == "off") {
                            ele.animate({ left: '25px' }, 300, function () {
                                window.parent.$('#callInStatus').attr("data-state", "on");
                            });
                            window.parent.$('#callInStatus').removeClass("off").addClass("on");
                        }
                    },
                );
            } catch (e) {
                console.log('设置免打扰失败:', e);
            }

            $('#execute-shifts-win').window('open'); //打开交接班界面
            _executeSuccessorData = null;
            //接班人下拉自动搜索
            $('#executeSuccessor').combobox({
                prompt: '输入接班人工号和姓名',
                editable: true,
                hasDownArrow: true,
                valueField: 'userId',
                textField: 'displayName',
                data: _successorList,
                filter: function (q, row) {
                    var opts = $(this).combobox('options');
                    return row[opts.textField].indexOf(q) > -1;
                },
                onSelect: function (params) {
                    _executeSuccessorData = {};
                    _executeSuccessorData.successorId = params.userId;
                    _executeSuccessorData.successorUserName = params.username;
                    _executeSuccessorData.successorName = params.displayName;
                }
            });
            _executeSuccessorData = {};
            _executeSuccessorData.successorUserName = _user.Username

            //交班基础信息初始化
            $('#execute-shift-seat').text(_pSeat.seatId);
            $('#execute-shift-offGoingUser').text(_pSeat.user);
            $('#execute-shift-offGoingUserName').text(_pSeat.userName)
            $('#execute-shift-curScheduleName').text(_user.ScheduleTimeConfig.ScheduleName);

            $('#execute-shift-offGoingScheduleName').text(_user.ScheduleTimeConfig.ScheduleName)
            $('#execute-shift-offGoingScheduleName-time').text(_user.ScheduleTimeConfig.ScheduleName)
            $('#execute-shift-successorScheduleName').text(_user.ScheduleTimeConfig.NextScheduleName)

            //设置默认交班时间为当前时间
            var now = new Date();
            var nowStr = now.getFullYear() + '-' + 
                        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(now.getDate()).padStart(2, '0') + ' ' + 
                        String(now.getHours()).padStart(2, '0') + ':' + 
                        String(now.getMinutes()).padStart(2, '0');
            $('#execute-shift-handover-times').datetimebox('setValue', nowStr);
            
            //设置班次开始结束时间（从用户班次配置中获取）
            var scheduleStartTime = '';
            var scheduleEndTime = '';
            
            if (_user.ScheduleTimeConfig && _user.ScheduleTimeConfig.offGoingScheduleStartTime && _user.ScheduleTimeConfig.offGoingScheduleEndTime) {
                // 如果有具体的班次开始结束时间配置，使用配置的时间
                var startTimeStr = _user.ScheduleTimeConfig.offGoingScheduleStartTime;
                var endTimeStr = _user.ScheduleTimeConfig.offGoingScheduleEndTime;
                
                // 格式化时间字符串 (假设格式为 yyyyMMddHHmmss)
                if (startTimeStr.length >= 14) {
                    var startYear = startTimeStr.substr(0, 4);
                    var startMonth = startTimeStr.substr(4, 2);
                    var startDay = startTimeStr.substr(6, 2);
                    var startHour = startTimeStr.substr(8, 2);
                    var startMin = startTimeStr.substr(10, 2);
                    scheduleStartTime = startYear + '-' + startMonth + '-' + startDay + ' ' + startHour + ':' + startMin;
                }
                
                if (endTimeStr.length >= 14) {
                    var endYear = endTimeStr.substr(0, 4);
                    var endMonth = endTimeStr.substr(4, 2);
                    var endDay = endTimeStr.substr(6, 2);
                    var endHour = endTimeStr.substr(8, 2);
                    var endMin = endTimeStr.substr(10, 2);
                    scheduleEndTime = endYear + '-' + endMonth + '-' + endDay + ' ' + endHour + ':' + endMin;
                }
            } else {
                // 如果没有具体配置，使用当天的时间范围作为默认值
                var startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0);
                var endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59);
                scheduleStartTime = startOfDay.getFullYear() + '-' + 
                              String(startOfDay.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(startOfDay.getDate()).padStart(2, '0') + ' ' + 
                              String(startOfDay.getHours()).padStart(2, '0') + ':' + 
                              String(startOfDay.getMinutes()).padStart(2, '0');
                scheduleEndTime = endOfDay.getFullYear() + '-' + 
                            String(endOfDay.getMonth() + 1).padStart(2, '0') + '-' + 
                            String(endOfDay.getDate()).padStart(2, '0') + ' ' + 
                            String(endOfDay.getHours()).padStart(2, '0') + ':' + 
                            String(endOfDay.getMinutes()).padStart(2, '0');
            }
            
            $('#execute-change-start-times').datetimebox('setValue', scheduleStartTime);
            $('#execute-change-end-times').datetimebox('setValue', scheduleEndTime);

            var params = { "offGoingId": _user.Id, "scheduleTimeConfigId": _user.ScheduleTimeConfig.Id, "seatId": _pSeat.id }
            $('#execute-change-start-times').datetimebox({
                onChange: function (newValue, oldValue) {
                    // 在这里可以添加你的逻辑代码
                    if ($('#execute-change-start-times').val() && $('#execute-change-end-times').val()) {
                        let startTime = $('#execute-change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间

                        let endTime = $('#execute-change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间

                        params.startTime = startTime
                        params.endTime = endTime

                        //交接班记录内容初始化
                        //获取交接时统计数据

                        getShiftSeatStatisticalData4Turnover(params,
                            function (res) {
                                $('#executeCallIn').val(res.callIn);
                                $('#executeAccept').val(res.accept);
                                $('#executeDispatch').val(res.dispatch);
                                $('#executeVaildDispatch').val(res.vaildDispatch);
                                $('#executeEmptyDispatch').val(res.emptyDispatch);
                                $('#executeCancelDispatch').val(res.cancelDispatch);
                                $('#executePatientNum').val(res.patientNum);
                                $('#executePick').val(res.pick);

                                $('#executePublicEventNum').val(res.publicEventNum);
                                $('#executeWoundedNum').val(res.woundedNum);
                                $('#executeDeadNum').val(res.deadNum);
                                
                            },
                            function (e, url, errMsg) {
                                $.messager.alert('提示', '获取交接班数据异常：' + errMsg);
                            }
                        );
                    }

                }
            });

            $('#execute-change-end-times').datetimebox({
                onChange: function (newValue, oldValue) {
                    // 在这里可以添加你的逻辑代码
                    if ($('#execute-change-start-times').val() && $('#execute-change-end-times').val()) {
                        let startTime = $('#execute-change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间
                        let endTime = $('#execute-change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间

                        params.startTime = startTime
                        params.endTime = endTime

                        //交接班记录内容初始化
                        //获取交接时统计数据

                        getShiftSeatStatisticalData4Turnover(params,
                            function (res) {
                                $('#executeCallIn').val(res.callIn);
                                $('#executeAccept').val(res.accept);
                                $('#executeDispatch').val(res.dispatch);
                                $('#executeVaildDispatch').val(res.vaildDispatch);
                                $('#executeEmptyDispatch').val(res.emptyDispatch);
                                $('#executeCancelDispatch').val(res.cancelDispatch);
                                $('#executePatientNum').val(res.patientNum);
                                $('#executePick').val(res.pick);

                                $('#executePublicEventNum').val(res.publicEventNum);
                                $('#executeWoundedNum').val(res.woundedNum);
                                $('#executeDeadNum').val(res.deadNum);
                            },
                            function (e, url, errMsg) {
                                $.messager.alert('提示', '获取交接班数据异常：' + errMsg);
                            }
                        );
                    }

                }
            });

            $('#executePassword').val('');
        }

        /** 执行交接班更新 */
        function doExecuteSuccessor() {
            if (_executeSuccessorData == null || _executeSuccessorData.successorId == null || _executeSuccessorData.successorId == "") {
                $.messager.alert('温馨提示', '请选择接班人员', 'info');
                return null;
            }
            /**交接班记录 */
            _executeSuccessorData.seatId = _pSeat.id
            _executeSuccessorData.offGoingId = _user.Id
            /**接班人信息 */
            _executeSuccessorData.userId = _executeSuccessorData.successorId
            _executeSuccessorData.username = _executeSuccessorData.successorUserName
            _executeSuccessorData.displayName = _executeSuccessorData.successorName
            _executeSuccessorData.password = $('#executePassword').val()
            if ($('#execute-change-start-times').val() == null || $('#execute-change-start-times').val() === '') {
                $.messager.alert('温馨提示', '请输入交班开始时间', 'info');
                return;
            }
            if ($('#execute-change-end-times').val() == null || $('#execute-change-end-times').val() === '') {
                $.messager.alert('温馨提示', '请输入交班结束时间', 'info');
                return;
            }
            if (_executeSuccessorData.password == null || _executeSuccessorData.password == "") {
                $.messager.alert('温馨提示', '请输入密码', 'info');
                return null;
            }
            if ($('#executeCallIn').val() == null || $('#executeCallIn').val() === '') {
                $.messager.alert('温馨提示', '请输入呼入次数', 'info');
                return;
            }
            if ($('#executeAccept').val() == null || $('#executeAccept').val() === '') {
                $.messager.alert('温馨提示', '请输入受理次数', 'info');
                return;
            }
            if ($('#executeDispatch').val() == null || $('#executeDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入派车辆数', 'info');
                return;
            }
            if ($('#executeVaildDispatch').val() == null || $('#executeVaildDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入有效出车辆数', 'info');
                return;
            }
            if ($('#executeEmptyDispatch').val() == null || $('#executeEmptyDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入空车辆数', 'info');
                return;
            }
            if ($('#executeCancelDispatch').val() == null || $('#executeCancelDispatch').val() === '') {
                $.messager.alert('温馨提示', '请输入取消派车辆数', 'info');
                return;
            }
            if ($('#executePatientNum').val() == null || $('#executePatientNum').val() === '') {
                $.messager.alert('温馨提示', '请输入患者人数', 'info');
                return;
            }
            if ($('#executePick').val() == null || $('#executePick').val() === '') {
                $.messager.alert('温馨提示', '请输入摘机率', 'info');
                return;
            }
            if ($('#executePublicEventNum').val() == null || $('#executePublicEventNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件次数', 'info');
                return;
            }
            if ($('#executeWoundedNum').val() == null || $('#executeWoundedNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件伤员人数', 'info');
                return;
            }
            if ($('#executeDeadNum').val() == null || $('#executeDeadNum').val() === '') {
                $.messager.alert('温馨提示', '请输入突发公共事件死亡人数', 'info');
                return;
            }

            /**交接班记录内容 */
            _executeSuccessorData.callIn = $('#executeCallIn').val()//呼入
            _executeSuccessorData.accept = $('#executeAccept').val()//受理（次）
            _executeSuccessorData.dispatch = $('#executeDispatch').val()//派车（辆）
            _executeSuccessorData.vaildDispatch = $('#executeVaildDispatch').val()//有效出车（辆）
            _executeSuccessorData.emptyDispatch = $('#executeEmptyDispatch').val()//空车（辆）
            _executeSuccessorData.cancelDispatch = $('#executeCancelDispatch').val()//取消派车（辆）
            _executeSuccessorData.patientNum = $('#executePatientNum').val()//患者人数（人）
            _executeSuccessorData.pick = $('#executePick').val()//摘机率（%）
            _executeSuccessorData.publicEventNum = $('#executePublicEventNum').val()//突发公共事件次数
            _executeSuccessorData.woundedNum = $('#executeWoundedNum').val()//突发公共事件伤（人）
            _executeSuccessorData.deadNum = $('#executeDeadNum').val()//突发公共事件次数亡（人）

            _executeSuccessorData.offGoingScheduleStartTime = $('#execute-change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间
            _executeSuccessorData.offGoingScheduleEndTime = $('#execute-change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间
            _executeSuccessorData.offGoingTime = $('#execute-shift-handover-times').val().replace(/[\s:-]/g, "") + '00'  //交班时间

            try {
                window.parent.showProcess(true, '温馨提示', '正在进行交接班，请稍后...');
            } catch (e) {
                console.log('无法调用主窗口进度条', e);
            }
            
            updateShiftSeatTurnover(_executeSuccessorData,
                function (res) {
                    $('#execute-shifts-win').window('close', true);
                    try {
                        window.parent.showProcess(false);
                    } catch (e) {
                        console.log('无法调用主窗口进度条', e);
                    }
                    //座席下线
                    try {
                        downSeat(_pSeat.id,
                            function (res) {
                                try {
                                    window.parent.showProcess(false);
                                    window.parent.$('#login-un').val('');
                                    window.parent.$('#login-pwd').val('');
                                    window.parent.$('#loginlogin-schedule-time-config-id').val('');
                                    window.parent.showLogin();//showLogin有调用 checkActiveShiftSeat()
                                    try {
                                        window.parent.gProxy.hangUpSecondScreen();
                                    } catch (e) {
                                        window.parent.bsProxy.hangUpSecondScreen();
                                    }
                                    window.parent.closeInterval(); //清理所有的定时器
                                } catch (e) {
                                    console.log('无法调用主窗口方法', e);
                                    $.messager.alert('提示', '交接班成功，请手动刷新主页面重新登录');
                                }
                            },
                            function (e, url, errMsg) {
                                try {
                                    window.parent.showProcess(false);
                                    $.messager.alert('提示', '座席下线失败，异常信息：' + errMsg);
                                    window.parent.showProcess(false);
                                    window.parent.$('#login-un').val('');
                                    window.parent.$('#login-pwd').val('');
                                    window.parent.$('#loginlogin-schedule-time-config-id').val('');
                                    window.parent._token = null;
                                    window.parent.showLogin();//showLogin有调用 checkActiveShiftSeat()
                                    try {
                                        window.parent.gProxy.hangUpSecondScreen();
                                    } catch (e) {
                                        window.parent.bsProxy.hangUpSecondScreen();
                                    }
                                    window.parent.closeInterval(); //清理所有的定时器
                                } catch (e) {
                                    console.log('无法调用主窗口方法', e);
                                    $.messager.alert('提示', '交接班成功，请手动刷新主页面重新登录');
                                }
                            });
                    } catch (e) {
                        console.log('座席下线失败', e);
                        $.messager.alert('提示', '交接班成功，请手动刷新主页面重新登录');
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '交接班失败，异常信息：' + errMsg);
                    try {
                        window.parent.showProcess(false);
                    } catch (e) {
                        console.log('无法调用主窗口进度条', e);
                    }
                }
            )
            
            //接警分机上线
            try {
                var ele = window.parent.$('#callInStatus').children(".move");
                var noDisturb = "off";
                var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
                window.parent.extAssign(params,
                    function (res) {
                        //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                        if (ele.attr("data-state") == "on") {
                            //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                            ele.animate({ left: "0" }, 300, function () {
                                ele.attr("data-state", "off");
                            });
                            window.parent.$('#callInStatus').removeClass("on").addClass("off");
                        } else if (ele.attr("data-state") == "off") {
                            ele.animate({ left: '25px' }, 300, function () {
                                window.parent.$('#callInStatus').attr("data-state", "on");
                            });
                            window.parent.$('#callInStatus').removeClass("off").addClass("on");
                        }
                    },
                );
            } catch (e) {
                console.log('设置免打扰失败:', e);
            }
        }
        
        /** 取消执行交接班 */
        function cancleExecuteSuccessor() {
            //交接班更新弹窗关闭
            $('#execute-shifts-win').window('close');
            //接警分机上线
            try {
                var ele = window.parent.$('#callInStatus').children(".move");
                var noDisturb = "off";
                var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
                window.parent.extAssign(params,
                    function (res) {
                        //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                        if (ele.attr("data-state") == "on") {
                            //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                            ele.animate({ left: "0" }, 300, function () {
                                ele.attr("data-state", "off");
                            });
                            window.parent.$('#callInStatus').removeClass("on").addClass("off");
                        } else if (ele.attr("data-state") == "off") {
                            ele.animate({ left: '25px' }, 300, function () {
                                window.parent.$('#callInStatus').attr("data-state", "on");
                            });
                            window.parent.$('#callInStatus').removeClass("off").addClass("on");
                        }
                    },
                );
            } catch (e) {
                console.log('设置免打扰失败:', e);
            }
        }

        //执行交接班弹窗右上角关闭事件
        $('#execute-shifts-win').window({
            onBeforeClose: function () {
                // 这个回调会在窗口关闭前触发，可以在此处阻止窗口关闭
                //接警分机上线
                try {
                    var ele = window.parent.$('#callInStatus').children(".move");
                    var noDisturb = "off";
                    var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
                    window.parent.extAssign(params,
                        function (res) {
                            //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                            if (ele.attr("data-state") == "on") {
                                //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                                ele.animate({ left: "0" }, 300, function () {
                                    ele.attr("data-state", "off");
                                });
                                window.parent.$('#callInStatus').removeClass("on").addClass("off");
                            } else if (ele.attr("data-state") == "off") {
                                ele.animate({ left: '25px' }, 300, function () {
                                    window.parent.$('#callInStatus').attr("data-state", "on");
                                });
                                window.parent.$('#callInStatus').removeClass("off").addClass("on");
                            }
                        },
                    );
                } catch (e) {
                    console.log('设置免打扰失败:', e);
                }
                return true; // 返回 true 表示允许关闭，返回 false 则阻止关闭
            },
        });

        //执行交接班输入只能是0或正整数
        function executeValidateInputPositiveOrZero(input) {
            const value = input.value;
            const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
            if (!regex.test(value)) {
                input.value = ""; // 清空输入框
            }
        }

        //执行交接班计算摘机率
        function executeInputCount(input) {
            executeValidateInputPositiveOrZero(input)
            if ($('#executeCallIn').val() == '0' || $('#executeCallIn').val() == '' || $('#executeAccept').val() == '0' || $('#executeAccept').val() == '') {
                $('#executePick').val('0.00')
            }
            if ($('#executeCallIn').val() > 0 && $('#executeAccept').val() > 0) {
                var pick = $('#executeAccept').val() / $('#executeCallIn').val() * 100;
                $('#executePick').val(pick.toFixed(2));
            }
        }


    </script>
</body>

</html>