<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8"/>
    <title>编辑通知单</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css"/>
    <link rel="stylesheet" type="text/css" href="style/common.css"/>
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/ueditor.all.js"></script>
    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/lang/zh-cn/zh-cn.js"></script>
    <style>
        #mainPanel_McWinformMVC {
            height: calc(100% - 1.5em - 10px);
        }
        .notice-form {
            padding: 20px;
            max-width: 1200px;
        }
        .form-item {
            margin-bottom: 20px;
            display: flex;
        }
        .form-label {
            width: 120px;
            text-align: right;
            padding-right: 10px;
            line-height: 32px;
            font-weight: bold;
        }
        .form-content {
            flex: 1;
        }
        .required {
            color: red;
        }
        .toolbar {
            text-align: center;
            padding: 30px 0 20px 0;
        }
        .textbox-text {
            font-size: 17px !important;
        }

        /* 自定义 combobox 样式，保持白色背景 */
        .custom-combobox .textbox-text {
            background-color: #fff !important;
            color: #333 !important;
        }

        .custom-combobox .textbox-text[disabled] {
            background-color: #fff !important;
            color: #333 !important;
            opacity: 1 !important;
            cursor: pointer !important;
        }

        /* 确保下拉箭头仍然可见 */
        .custom-combobox .textbox-addon {
            background-color: #fff !important;
        }

        /* 下拉面板控制按钮样式 */
        .select-controls {
            padding: 5px;
            border-bottom: 1px solid #eee;
            background: #f8f8f8;
            text-align: center;
        }

        .select-controls a {
            margin: 0 10px;
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
        }

        .select-controls a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
    <div class="notice-form">
        <div class="form-item">
            <div class="form-label"><span class="required">*</span>通知标题：</div>
            <div class="form-content">
                <input id="noticeTitle" class="easyui-textbox" style="width: 100%; height: 35px;"
                       data-options="prompt:'请输入通知标题'">
            </div>
        </div>
        <div class="form-item">
            <div class="form-label">发送座席：</div>
            <div class="form-content">
                <select id="sendSeats" class="easyui-combobox"
                        data-options="prompt:'请选择要发送的座席'"                style="width: 100%; height: 32px;"></select>
            </div>
        </div>
        <div class="form-item">
            <div class="form-label">发送车辆：</div>
            <div class="form-content">
                <select id="sendMobiles" class="easyui-combobox"
                        data-options="multiple:true, prompt:'请选择要发送的车辆', editable:false"                style="width: 100%; height: 32px;"></select>
            </div>
        </div>
        <div class="form-item">
            <div class="form-label">发送分站：</div>
            <div class="form-content">
                <select id="sendStations" class="easyui-combobox"
                        data-options="multiple:true, prompt:'请选择要发送的分站', editable:false"                style="width: 100%; height: 32px;"></select>
            </div>
        </div>
        <div class="form-item">
            <div class="form-label"><span class="required">*</span>通知内容：</div>
            <div class="form-content">
                <script id="editor" type="text/plain" style="width:100%;height:400px;"></script>
            </div>
        </div>
        <div class="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'"
               onclick="saveNotice()" style="width: 100px; height: 35px; margin-right: 20px;">保存</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'"
               onclick="cancel()" style="width: 100px; height: 35px;">取消</a>
        </div>
    </div>
</div>

<script type="text/javascript" src="script/md5-js.js"></script>
<script type="text/javascript" src="script/crypto-js.min.js"></script>
<script type="text/javascript" src="script/linq.min.js"></script>
<script type="text/javascript" src="script/edcui.js"></script>
<script type="text/javascript" src="script/ajax.js"></script>
<script type="text/javascript" src="script/checkbox.js"></script>
<script type="text/javascript" src="script/audio.js"></script>
<script type="text/javascript">
    // 实例化编辑器 - 添加配置禁用上传功能
    var ue = UE.getEditor('editor', {
        // 禁用所有上传相关功能
        serverUrl: "",
        imageActionName: "",
        scrawlActionName: "",
        videoActionName: "",
        fileActionName: "",
        catcherActionName: "",
        snapscreenActionName: "",
        imageManagerActionName: "",
        fileManagerActionName: "",
        // 禁用自动保存
        enableAutoSave: false,
        saveInterval: 0,
        // 其他配置
        elementPathEnabled: false,
        wordCount: false
    });
    // 添加错误处理，避免控制台报错
    ue.ready(function() {
        console.log('UEditor 编辑器初始化完成，已禁用所有上传功能');
    });
    // 捕获可能的服务器请求错误
    ue.addListener('error', function(type, message) {
        if (message && message.indexOf('controller.jsp') > -1) {
            console.log('已忽略上传功能相关错误:', message);
            return false; // 阻止错误显示
        }
    });
    try {
        _baseUrl = window.parent.gProxy.getBaseUrl();
    } catch (e) {
        _baseUrl = window.parent.bsProxy.getBaseUrl();
    }
    let id = null;
    let seatMap = new Map();
    let stationMap = new Map();
    let mobileMap = new Map();

    $(document).ready(function () {
        id = window.location.search.replace("?id=", "");
        // 初始化下拉框数据
        initSelectData();
        if (id) {
            // 编辑模式
            getNotice();
        } else {
            // 添加模式
            $('#noticeTitle').textbox();
        }
    });

    // 初始化下拉框数据
    function initSelectData() {
        let seats = [];
        let stations = [];
        let mobiles = [];
        
        selectSeatAndStation({}, function (data) {
            data.seatList.forEach(e => {
                seats.push({value: e.id, text: e.seatId});
                seatMap.set(e.id, e.seatId);
            });
            $('#sendSeats').combobox({
                data: seats,
                valueField: 'value',
                textField: 'text',
                multiple: true,
                editable: false,
                prompt: '请选择要发送的座席',
                panelMaxHeight: 300,
                onShowPanel: function() {
                    // 面板显示时添加操作按钮
                    addSelectControlButtons('sendSeats');
                }
            }).next('.combo').addClass('custom-combobox');
            data.stationList.forEach(e => {
                stations.push({value: e.stationId, text: e.stationName});
                stationMap.set(e.stationId, e.stationName);
            });
            $('#sendStations').combobox({
                data: stations,
                valueField: 'value',
                textField: 'text',
                multiple: true,
                editable: false,
                prompt: '请选择要发送的分站',
                panelMaxHeight: 300,
                onShowPanel: function() {
                    // 面板显示时添加操作按钮
                    addSelectControlButtons('sendStations');
                }
            }).next('.combo').addClass('custom-combobox');
        }, function (e, url, errMsg) {
            $.messager.alert('异常', '异常信息：' + errMsg);
        });

        listMobile({}, function (data) {
            data.forEach(e => {
                mobiles.push({value: e.id, text: e.carName});
                mobileMap.set(e.id, e.carName);
            });
            $('#sendMobiles').combobox({
                data: mobiles,
                valueField: 'value',
                textField: 'text',
                multiple: true,
                editable: false,
                prompt: '请选择要发送的车辆',
                panelMaxHeight: 300,
                onShowPanel: function() {
                    // 面板显示时添加操作按钮
                    addSelectControlButtons('sendMobiles');
                }
            }).next('.combo').addClass('custom-combobox');
        }, function (e, url, errMsg) {
            $.messager.alert('异常', '异常信息：' + errMsg);
        });
    }

    // 添加全选/全不选控制按钮
    function addSelectControlButtons(comboId) {
        var panel = $('#' + comboId).combo('panel');
        var combo = $('#' + comboId);

        // 检查是否已经添加过按钮
        if (panel.find('.select-controls').length > 0) {
            return;
        }

        // 创建控制按钮
        var controls = $('<div class="select-controls" style="padding: 5px; border-bottom: 1px solid #eee; background: #f8f8f8;">' +
            '<a href="javascript:void(0)" class="select-all" style="margin-right: 10px; color: #1890ff; text-decoration: none;">全选</a>' +
            '<a href="javascript:void(0)" class="select-none" style="color: #1890ff; text-decoration: none;">全不选</a>' +
            '</div>');

        // 绑定全选事件
        controls.find('.select-all').click(function() {
            var data = combo.combobox('getData');
            var values = [];
            for (var i = 0; i < data.length; i++) {
                values.push(data[i].value);
            }
            combo.combobox('setValues', values);
            panel.combo('hidePanel');
        });

        // 绑定全不选事件
        controls.find('.select-none').click(function() {
            combo.combobox('clear');
            panel.combo('hidePanel');
        });

        // 将按钮添加到面板顶部
        panel.prepend(controls);
    }

    // 在页面样式中添加相关样式

    // 设置编辑器内容
    function setEditorContent(content) {
        $('#noticeContent').html(content);
    }

    // 获取通知单详情
    function getNotice() {
        var params = {
            id: id
        };
        getNoticeById(params, function (data) {
            $('#noticeTitle').textbox('setValue', data.noticeTitle);
            ue.ready(function() {
                ue.setContent(data.noticeContent);
            });
            if(data.recSeat){
                let recSeat = JSON.parse(data.recSeat);
                if(recSeat){
                    $('#sendSeats').combobox('setValues', recSeat.map(e => e.id));
                }
            }
            if(data.recMobile){
                let recMobile = JSON.parse(data.recMobile);
                if(recMobile){
                    $('#sendMobiles').combobox('setValues', recMobile.map(e => e.id));
                }
            }
            if(data.recStation){
                let recStation = JSON.parse(data.recStation);
                if(recStation){
                    $('#sendStations').combobox('setValues', recStation.map(e => e.id));
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '异常信息：' + errMsg);
        });
    }

    // 保存通知单
    function saveNotice() {
        let editor = UE.getEditor('editor');
        let title = $('#noticeTitle').textbox('getValue');
        let content = editor.getContent();
        let sendSeats = $('#sendSeats').combobox('getValues');
        let sendMobiles = $('#sendMobiles').combobox('getValues');
        let sendStations = $('#sendStations').combobox('getValues');

        // 验证必填项
        if (!title) {
            $.messager.alert('提示', '请输入通知标题！', 'warning');
            return;
        }

        if (!content || content.trim() === '' || content.trim() === '<br>') {
            $.messager.alert('提示', '请输入通知内容！', 'warning');
            return;
        }
        let recSeat = [];
        sendSeats.forEach(e => {
            recSeat.push({id: e, name: seatMap.get(e)});
        });
        let recStation = [];
        sendStations.forEach(e => {
            recStation.push({id: e, name: stationMap.get(e)});
        });
        let recMobile = [];
        sendMobiles.forEach(e => {
            recMobile.push({id: e, name: mobileMap.get(e)});
        });
        // 构造保存参数
        var params = {
            noticeTitle: title,
            noticeContent: content,
            recSeat: recSeat,
            recMobile: recMobile,
            recStation: recStation
        };

        // 如果是编辑模式，添加ID
        if (id) {
            params.id = id;
            updateNotice(params, function(data) {
                $.messager.alert('提示', '保存成功！', 'info');
                // 延迟关闭，给用户时间看到提示
                setTimeout(function() {
                    refreshNoticeList();
                    if (window.parent) {
                        window.parent.$('#main-tabs').tabs('close', '编辑通知单');
                    }
                }, 1000);
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '保存失败：' + errMsg, 'error');
            });
        }else{
            addNotice(params, function(data) {
                $.messager.alert('提示', '保存成功！', 'info');
                // 延迟关闭，给用户时间看到提示
                setTimeout(function() {
                    refreshNoticeList();
                    if (window.parent) {
                        window.parent.$('#main-tabs').tabs('close', '添加通知单');
                    }
                }, 1000);
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '保存失败：' + errMsg, 'error');
            });
        }
    }
    
    function refreshNoticeList(){
        let noticeListTab = window.parent.$('#main-tabs').tabs('getTab', '通知单列表');
        if (noticeListTab) {
            let iframe = noticeListTab.find('iframe')[0];
            if (iframe && iframe.contentWindow) {
                // 调用列表页面的刷新方法
                iframe.contentWindow.getNoticeList();
            }
        }
    }

    // 取消操作
    function cancel() {
        $.messager.confirm('确认', '确定要取消编辑吗？未保存的数据将会丢失', function (r) {
            if (r) {
                // 关闭当前标签页
                if (window.parent && window.parent.$) {
                    var title = id ? '编辑通知单' : '添加通知单';
                    window.parent.$('#main-tabs').tabs('close', title);
                }
            }
        });
    }
</script>
</body>
</html>