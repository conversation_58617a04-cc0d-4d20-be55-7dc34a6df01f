<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>事件详情</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />

    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <style>
        .tabs-header {
            background-color: #f5f5f5;
            padding: 8px 8px 0 8px;
            border-bottom: 1px solid #ddd;
        }
        .tabs li a.tabs-inner {
            padding: 8px 15px;
            border-radius: 4px 4px 0 0;
            font-size: 14px;
        }
        .tabs li.tabs-selected a.tabs-inner {
            background: #fff;
            border-bottom: 1px solid #fff;
            color: #333;
            font-weight: bold;
        }
        .tabs-panels {
            padding: 0;
            border: none;
        }
        .tabs-panels .panel {
            overflow: hidden;
        }
        .panel-body {
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body class="easyui-layout">
    <div data-options="region:'center'" style="padding:5px;">
        <div id="eventTabs" class="easyui-tabs" data-options="fit:true,border:false">
            <!-- Tabs will be dynamically added here -->
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript" src="script/jQuery.print.js"></script>
    <script type="text/javascript" src="script/symptomData.js"></script>

    <script type="text/javascript">
        var _baseUrl = "";

        // 获取URL参数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return null;
        }

        //事件标签页
        function createEventTab(eventId, title, index) {
            $('#eventTabs').tabs('add', {
                title: title,
                content: '<iframe id="eventFrame_' + index + '" src="eventInfo.html?eventId=' + eventId + '&value=secondScr" frameborder="0" style="width:100%;height:100%;"></iframe>',
                closable: false
            });
        }

        $(document).ready(function() {
            try {
                _baseUrl = window.parent.parent.gProxy.getBaseUrl();
            } catch (e) {
                _baseUrl = window.parent.parent.bsProxy.getBaseUrl();
            }

        
            // 获取传入的事件ID
            var eventId = getQueryVariable("eventId");
            if (!eventId) {
                $.messager.alert('错误', '未找到事件ID', 'error');
                return;
            }

            //通过事件ID查询受理集合，并创建标签页
            selectEventAcceptanceList({ id: eventId }, function(data) {
                data.forEach((item, index) => {
                    var title = "";
                    if (item.serialNo == 0) {
                        title = "首次事件受理";
                    } else {
                        title = "第" + (item.serialNo + 1) + "次事件受理";
                    }
                    createEventTab(item.id, title, item.serialNo);
                });
            });
        });
    </script>
</body>
</html> 