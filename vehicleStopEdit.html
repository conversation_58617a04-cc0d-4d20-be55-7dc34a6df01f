<!--
/* *****************************************************************
* 120EDC急救指挥中心报停车辆编辑页面
* 本页面用于新增和编辑车辆报停记录
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 系统自动生成
* Created: 2024/12/19
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心报停车辆编辑</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        body {
            overflow-y: hidden;
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        /* 所有input元素去除outline */
        input {
            outline: none !important;
        }
        
        .textbox-text {
            outline: none !important;
        }
        
        .combo-text {
            outline: none !important;
        }
        
        .numberbox-text {
            outline: none !important;
        }
        
        .datebox-text {
            outline: none !important;
        }
        
        /* 与createEvents.html保持一致的按钮样式 */
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        
        /* 参考createEvents.html的group-box样式 */
        .group-box {
            min-width: 100px;
            min-height: 100px;
            border: solid #E2E7EA 1px;
            border-radius: 5px;
            margin: 3px 5px 5px 5px;
            padding: 0 16px;
        }
        
        .group-box-title {
            width: calc(100% + 16px);
            height: 30px;
            line-height: 30px;
            background: url(./style/img/grouptitle.png) repeat-x;
            color: #333;
            font-weight: bold;
            font-size: 14px;
            padding-left: 10px;
            margin: 0 -16px 10px -16px;
        }
        
        /* 表单行样式 - 参考createEvents.html */
        .form-line {
            display: flex;
            width: 100%;
            height: 40px;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .form-item {
            width: 50%;
            height: 40px;
            display: flex;
            align-items: center;
            padding-right: 20px;
        }
        
        .form-label {
            width: 160px;
            font-size: 14px;
            text-align: right;
            margin-right: 10px;
            color: #333;
        }
        
        .form-control {
            width: 180px;
        }
        
        .form-control-wide {
            width: calc(100% - 130px);
        }
        
        /* 状态样式 */
        .status-active { color: #ff6b35; font-weight: bold; }
        .status-recovered { color: #28a745; font-weight: bold; }
        .status-timeout { color: #dc3545; font-weight: bold; }
        
        /* 车辆状态样式 */
        .vehicle-status-online { color: #28a745; font-weight: bold; }
        .vehicle-status-offline { color: #dc3545; font-weight: bold; }
        .vehicle-status-busy { color: #ffc107; font-weight: bold; }
        .vehicle-status-stop { color: #6c757d; font-weight: bold; }
        
        /* DataGrid样式优化 */
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            height: 28px;
        }
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 0px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover,
        .datagrid-row-selected {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        .datagrid-wrap .datagrid-body tr:hover a,
        .datagrid-row-selected a {
            color: #ffffff !important;
        }
        
        /* 搜索区域样式 */
        .search-area {
            /* padding: 10px; */
            background: #f8f9fa;
            /* border-radius: 5px; */
            /* margin-bottom: 5px; */
            padding-top: 5px;
        }
        
        .search-line {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .search-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        
        .search-label {
            font-size: 14px;
            margin-right: 8px;
            min-width: 60px;
        }
        
        /* 底部按钮区域 */
        .bottom-buttons {
            text-align: center;
            padding: 10px;
            border-top: 1px solid #E2E7EA;
            background: #f8f9fa;
        }
        
        /* 已选择车辆信息样式 */
        .selected-vehicle-info {
            background: #e8f4f8;
            border: 1px solid #17a2b8;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
            margin-top: 20px;
            min-height: 160px;  /* 增加高度为原来的两倍 */
        }
        
        .selected-vehicle-title {
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 16px;
            font-size: 16px;
        }
        
        .vehicle-info-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            padding: 10px 0;
        }
        
        .vehicle-info-item {
            font-size: 16px;
            line-height: 1.8;
        }
        
        .vehicle-info-label {
            color: #666;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .vehicle-info-value {
            font-weight: bold;
            color: #333;
            line-height: 2;
            font-size: 16px;
        }
        
        /* 修复下拉框背景色问题 */
        .combo-text {
            background-color: #fff !important;
        }
        
        .textbox-text {
            background-color: #fff !important;
        }
        
        .numberbox .textbox-text {
            background-color: #fff !important;
        }
        
        .datebox .textbox-text {
            background-color: #fff !important;
        }
        
        /* 确保所有输入框背景为白色 */
        input[type="text"] {
            background-color: #fff !important;
        }
        
        select {
            background-color: #fff !important;
        }
        
        /* 强制移除combobox disabled状态 */
        .combobox-item-disabled {
            color: #000 !important;
            cursor: pointer !important;
            pointer-events: auto !important;
            background-color: #fff !important;
        }
        
        .combobox-item-disabled:hover {
            background-color: #eaf2ff !important;
        }
    </style>
</head>
<body>
    <div style="width:100%; height: auto; min-height: 100%; padding: 5px;">
                 <!-- 报停信息表单 -->
        <div class="group-box" style="height: auto; margin-bottom: 5px;">
            <div class="group-box-title">
                报停信息 <span style="color: #ff6b35; font-weight: bold;">[报停中]</span>
            </div>
            <form id="stopRecordForm">
                <input type="hidden" id="form_id" name="id" />
                
                <!-- 第一行：报停原因、预计报停时长 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">报停原因：</label>
                        <select id="form_stopReasonCode" name="stopReasonCode" class="easyui-combobox form-control" required="true"
                                data-options="
                                    valueField:'id',
                                    textField:'reasonName',
                                    editable:false,
                                    disabled:false">
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">预计报停时长(小时)：</label>
                        <input id="form_expectedDuration" name="expectedDuration" class="easyui-numberbox form-control" 
                               data-options="precision:1,min:0.5,max:168" required="true">
                    </div>
                </div>
                
                <!-- 第二行：预警阈值、提醒间隔 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">预警阈值(小时)：</label>
                        <input id="form_warningThreshold" name="warningThreshold" class="easyui-numberbox form-control" 
                               data-options="precision:1,min:0.5,max:168">
                    </div>
                    <div class="form-item">
                        <label class="form-label">提醒间隔(分钟)：</label>
                        <input id="form_reminderInterval" name="reminderInterval" class="easyui-numberbox form-control" 
                               data-options="min:5,max:1440">
                    </div>
                </div>
                
                <!-- 第三行：报停开始时间、预计结束时间 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">报停开始时间：</label>
                        <input id="form_startTime" name="startTime" class="easyui-datetimebox form-control" required="true">
                    </div>
                    <div class="form-item">
                        <label class="form-label">预计结束时间：</label>
                        <input id="form_expectedEndTime" name="expectedEndTime" class="easyui-datetimebox form-control">
                    </div>
                </div>
                
                <!-- 注释掉第四行：实际结束时间、录音文件 
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">实际结束时间：</label>
                        <div style="display: flex; align-items: center;">
                            <input id="form_actualEndTime" name="actualEndTime" class="easyui-datetimebox form-control">
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="form-label">录音文件：</label>
                        <div style="display: flex; align-items: center;">
                            <span id="audioFileText" style="color: #666;">无</span>
                            <a id="playAudioBtn" class="easyui-linkbutton" href="javascript:playAudioFile();" 
                               style="height: 28px; margin-left: 5px; display: none;" data-options="iconCls:'icon-sound'">播放</a>
                        </div>
                    </div>
                </div>
                -->
                
                <!-- 第五行：备注 -->
                <div class="form-line">
                    <div class="form-item" style="width: 100%;">
                        <label class="form-label">备注：</label>
                        <input id="form_remarks" name="remarks" type="text" style="height:28px;padding: 0 10px;border: 1px solid #ddd;border-radius: 3px;width: 588px;" placeholder="请输入备注信息...">
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 车辆选择区域 -->
        <div class="group-box" style="height: auto; margin-bottom: 5px;">
            <div class="group-box-title">车辆选择</div>
            
            <!-- 已选择车辆信息 -->
            <div id="selectedVehicleInfo" class="selected-vehicle-info">
                <div class="selected-vehicle-title" style="display: flex; justify-content: space-between; align-items: center;">
                    <span>已选择车辆信息</span>
                    <a href="javascript:reselectVehicle();" style="color: #1890ff; text-decoration: none; font-size: 13px;">重新选择车辆</a>
                </div>
                <div id="selectedVehicleDetail" class="vehicle-info-grid">
                    <!-- 车辆信息将在这里动态显示 -->
                </div>
            </div>
            
                        <!-- 车辆选择区域 -->
            <div id="vehicleSelectArea">
                <!-- 搜索条件 -->
                <div class="search-area">
                    <div class="search-line">
                        <div class="search-item">
                            <label class="search-label">区域：</label>
                            <select id="search_regionId" class="easyui-combobox" style="width:120px;" data-options="editable:false">
                                <option value="">全部区域</option>
                                <option value="1">江阳区</option>
                                <option value="2">龙马潭区</option>
                                <option value="3">纳溪区</option>
                                <option value="4">泸县</option>
                                <option value="5">合江县</option>
                                <option value="6">叙永县</option>
                                <option value="7">古蔺县</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <label class="search-label">分站：</label>
                            <select id="search_stationId" class="easyui-combobox" style="width:150px;" data-options="editable:false">
                                <option value="">全部分站</option>
                                <option value="1">泸州市人民医院</option>
                                <option value="2">江阳区急救站</option>
                                <option value="3">龙马潭区急救站</option>
                                <option value="4">纳溪区急救站</option>
                                <option value="5">合江县急救站</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <label class="search-label">车牌号：</label>
                            <input id="search_vehicleNo" class="easyui-textbox" style="width:120px;" data-options="prompt:'输入车牌号'">
                        </div>
     
                        <div class="search-item">
                            <a class="easyui-linkbutton" href="javascript:searchVehicles();" 
                               style="width: 70px; height: 28px;" data-options="iconCls:'icon-search'">搜索</a>
                            <a class="easyui-linkbutton" href="javascript:clearSearch();" 
                               style="width: 70px; height: 28px; margin-left: 10px;" data-options="iconCls:'icon-reload'">重置</a>
                        </div>
                    </div>
                </div>
                
                <!-- 车辆列表 -->
                <div style="height: 200px;">
                    <table class="easyui-datagrid" id="dg-vehicle-list" style="height: 100%;"
                           data-options="pagination:true,singleSelect:true,rownumbers:true,fit:true,fitColumns:false,pageSize:15">
                        <thead>
                            <tr>
                                <th data-options="field:'vehicleNo',width:120,align:'center'">车牌号</th>
                                <th data-options="field:'vehicleType',width:100,align:'center'">车辆类型</th>
                                <th data-options="field:'stationName',width:250,align:'center'">所属分站</th>
                                <th data-options="field:'contact',width:180,align:'center'">联系电话</th>
                                <th data-options="field:'statusName',width:120,align:'center'">当前状态</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="bottom-buttons">
            <a class="easyui-linkbutton" href="javascript:saveStopRecord();" 
               style="width:100px;height:32px;line-height: 32px; font-size: 14px; margin: 0 10px;" 
               data-options="iconCls:'icon-save'">保存报停</a>
            <!-- <a class="easyui-linkbutton" href="javascript:closeWindow();" 
               style="width:80px;height:32px;line-height: 32px; font-size: 14px; margin: 0 10px;" 
               data-options="iconCls:'icon-cancel'">取消</a> -->
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            try{
                _baseUrl = window.parent.bsProxy.getBaseUrl();
            } catch (e) {
                _baseUrl = window.parent.parent.bsProxy.getBaseUrl();
            }
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            try{
                _pSeat = evalJson(window.parent.bsProxy.getSeatInfo())
            } catch (e) {
                _pSeat = evalJson(window.parent.parent.bsProxy.getSeatInfo());
            };
        }
        
        // 当前选中的车辆
        var selectedVehicle = null;
        // 编辑模式下的记录ID
        var editingRecordId = null;
        // 分站区域列表
        var _stationRegionList = [];
        // 分站列表
        var _substationList = [];
        // 车辆类型字典
        var _carTypeDict = {};
        // 初始化完成标记
        var _initFlags = {
            carTypeDict: false,
            regionStation: false
        };
        
        /**
         * 检查初始化是否完成
         */
        function checkInitComplete() {
            if (_initFlags.carTypeDict && _initFlags.regionStation) {
                $('#dg-vehicle-list').datagrid('load');
            }
        }
        
        $(document).ready(function () {
            // 检查URL参数，判断是否是编辑模式
            var recordId = getUrlParam('id');
            var isEditMode = !!recordId;
            
            // 初始化页面
            initializePage();
            
            // 延迟加载报停原因列表
            setTimeout(function() {
                selectMobileStopReasonList(
                    function (data) {
                        $('#form_stopReasonCode').combobox({
                            data: data,
                            valueField: 'id',
                            textField: 'reasonName',
                            editable: false,
                            disabled: false,
                            onLoadSuccess: function(data) {
                                $(this).combobox('enable');
                                $('.combobox-item-disabled').removeClass('combobox-item-disabled');
                                
                                // 如果是编辑模式，加载报停记录
                                if (isEditMode) {
                                    loadStopRecord(recordId);
                                } else {
                                    // 新增模式，设置当前时间
                                    var currentTime = new Date().formatString('yyyy-MM-dd hh:mm:ss');
                                    $('#form_startTime').datetimebox('setValue', currentTime);
                                }
                            },
                            onSelect: function(record) {
                                onStopReasonChange(record);
                            }
                        });
                    },
                    function (e, url, errMsg) {
                        console.error('加载报停原因失败:', errMsg);
                        $.messager.alert('提示', '加载报停原因列表失败：' + errMsg);
                    }
                );
            }, 500);
            
            // 查询车辆类型字典
            queryAllDic(["car_type"],
                function (data) {
                    if (data && Array.isArray(data)) {
                        _carTypeDict = {};
                        data.forEach(function(item) {
                            _carTypeDict[item.codeName] = item.codeVale;
                        });
                        _initFlags.carTypeDict = true;
                        checkInitComplete();
                    } else {
                        console.error('车辆类型字典数据格式不正确');
                    }
                }
            );
            
            loadRegionAndStationData();
            
            // 初始化时间控件
            $('#form_startTime').datetimebox({
                onChange: function(newValue, oldValue) {
                    if (newValue && newValue !== oldValue) {
                        setTimeout(function() {
                            calculateExpectedEndTime();
                        }, 100);
                    }
                }
            });
            
            // 预计报停时长控件初始化
            $('#form_expectedDuration').numberbox({
                onChange: function(newValue, oldValue) {
                    if (newValue && newValue !== oldValue) {
                        setTimeout(function() {
                            calculateExpectedEndTime();
                        }, 100);
                    }
                }
            });
        });
        
        /**
         * 初始化页面
         */
        function initializePage() {
            // 初始化车辆列表
            $('#dg-vehicle-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
                nowrap: true,
                rownumbers: true,
                singleSelect: true,
                fit: true,
                fitColumns: false,
                pagination: true,
                pageSize: 10,
                pageList: [10, 20, 30, 50],
                onClickRow: function (rowIndex, rowData) {
                    selectVehicle(rowData, false);
                },
                onDblClickRow: function(rowIndex, rowData) {
                    selectVehicle(rowData, false);
                },
                onBeforeLoad: function(param) {
                    param.pageNum = param.page;
                    param.pageSize = param.rows;
                    return true;
                },
                loader: function(param, success, error) {
                    // 获取搜索条件
                    var searchRegion = $('#search_regionId').combobox('getValue');
                    var searchStation = $('#search_stationId').combobox('getValue');
                    var searchVehicleNo = $('#search_vehicleNo').textbox('getValue');
                    
                    // 构建查询参数,只查询待命状态的车辆
                    var queryParams = {
                        pageNum: param.pageNum || 1,
                        pageSize: param.pageSize || 10,
                        plateNum: searchVehicleNo || '',
                        stationName: searchStation ? $('#search_stationId').combobox('getText') : '',
                        stationRegionId: searchRegion || '',
                        statusList: ["0"] // 只查询站内待命车辆
                    };
                    
                    // 调用接口获取数据
                    getMobilesStatusListWithDistance(queryParams,
                        function(result) {
                            if (Array.isArray(result)) {
                                success({
                                    total: result.length,
                                    rows: result.map(function(item) {
                                        var carTypeName = _carTypeDict[item.carType] || item.carType || '救护车';
                                        
                                        return {
                                            id: item.id,
                                            vehicleNo: item.plateNum,
                                            vehicleType: item.carType + ' - ' + carTypeName,
                                            stationName: item.stationName || '',
                                            contact: item.contact || '',
                                            statusName: item.statusStr || item.mobileStatusStr || '',
                                            originalData: item
                                        };
                                    })
                                });
                            } else {
                                console.error('接口返回数据格式不正确');
                                success({
                                    total: 0,
                                    rows: []
                                });
                            }
                        },
                        function(e, url, errMsg) {
                            console.error('获取车辆数据失败:', errMsg);
                            error();
                            $.messager.alert('提示', '获取车辆数据失败：' + errMsg, 'warning');
                        }
                    );
                }
            });
            
            // 初始化完成后立即加载第一页数据
            $('#dg-vehicle-list').datagrid('load');
        }
        
        /**
         * 报停原因变化事件
         */
        function onStopReasonChange(record) {
            if (record) {
                setTimeout(function() {
                    try {
                        if (record.defaultDuration) {
                            $('#form_expectedDuration').numberbox('setValue', record.defaultDuration);
                        }
                        if (record.defaultWarningThreshold) {
                            $('#form_warningThreshold').numberbox('setValue', record.defaultWarningThreshold);
                        }
                        if (record.defaultReminderInterval) {
                            $('#form_reminderInterval').numberbox('setValue', record.defaultReminderInterval);
                        }
                        
                        var startTime = $('#form_startTime').datetimebox('getValue');
                        if (startTime) {
                            calculateExpectedEndTime();
                        }
                    } catch (e) {
                        console.error('设置默认值时出现错误:', e);
                        if (record.defaultDuration) {
                            $('#form_expectedDuration').val(record.defaultDuration);
                        }
                        if (record.defaultWarningThreshold) {
                            $('#form_warningThreshold').val(record.defaultWarningThreshold);
                        }
                        if (record.defaultReminderInterval) {
                            $('#form_reminderInterval').val(record.defaultReminderInterval);
                        }
                        
                        setTimeout(function() {
                            calculateExpectedEndTime();
                        }, 200);
                    }
                }, 100);
            }
        }
        
        /**
         * 计算预计结束时间
         */
        function calculateExpectedEndTime() {
            try {
                var startTime = $('#form_startTime').datetimebox('getValue');
                var duration = $('#form_expectedDuration').numberbox('getValue');
                
                if (startTime && duration && duration > 0) {
                    var start = new Date(startTime);
                    if (!isNaN(start.getTime())) {
                        var end = new Date(start.getTime() + duration * 60 * 60 * 1000);
                        var endTimeStr = end.formatString('yyyy-MM-dd hh:mm:ss');
                        $('#form_expectedEndTime').datetimebox('setValue', endTimeStr);
                    }
                }
            } catch (e) {
                console.error('计算预计结束时间时出现错误:', e);
                setTimeout(function() {
                    try {
                        calculateExpectedEndTime();
                    } catch (retryError) {
                        console.error('重试计算预计结束时间失败:', retryError);
                    }
                }, 500);
            }
        }
        
        /**
         * 加载区域和分站数据
         */
        function loadRegionAndStationData() {
            var loadCount = 0;
            var totalLoad = 2;
            
            function checkLoadComplete() {
                loadCount++;
                if (loadCount >= totalLoad) {
                    _initFlags.regionStation = true;
                    checkInitComplete();
                }
            }
            
            // 加载分站区域列表
            getAllStationRegionList(
                function (data) {
                    _stationRegionList = data || [];
                    initializeRegionCombobox();
                    checkLoadComplete();
                },
                function (e, url, errMsg) {
                    console.error('加载分站区域列表失败:', errMsg);
                    $.messager.alert('提示', '加载分站区域列表失败：' + errMsg);
                    checkLoadComplete();
                }
            );
            
            // 加载分站列表
            getAllStationList(
                function (data) {
                    _substationList = data || [];
                    initializeStationCombobox();
                    checkLoadComplete();
                },
                function (e, url, errMsg) {
                    console.error('加载分站列表失败:', errMsg);
                    $.messager.alert('提示', '加载分站列表失败：' + errMsg);
                    checkLoadComplete();
                }
            );
        }
        
        /**
         * 初始化区域下拉框
         */
        function initializeRegionCombobox() {
            var regionData = [{ id: '', name: '全部区域' }];
            for (var i = 0; i < _stationRegionList.length; i++) {
                regionData.push({
                    id: _stationRegionList[i].id,
                    name: _stationRegionList[i].regionName
                });
            }
            
            $('#search_regionId').combobox({
                data: regionData,
                valueField: 'id',
                textField: 'name',
                editable: false,
                onSelect: function(record) {
                    // 区域选择变化时，只重新加载分站下拉框
                    filterStationsByRegion(record.id);
                }
            });
        }
        
        /**
         * 初始化分站下拉框
         */
        function initializeStationCombobox() {
            var stationData = [{ id: '', name: '全部分站' }];
            for (var i = 0; i < _substationList.length; i++) {
                stationData.push({
                    id: _substationList[i].id,
                    name: _substationList[i].stationName
                });
            }
            
            $('#search_stationId').combobox({
                data: stationData,
                valueField: 'id',
                textField: 'name',
                editable: false
            });
        }
        
        /**
         * 根据区域过滤分站
         */
        function filterStationsByRegion(regionId) {
            var filteredStations = [{ id: '', name: '全部分站' }];
            
            if (regionId) {
                // 过滤属于该区域的分站
                for (var i = 0; i < _substationList.length; i++) {
                    if (_substationList[i].stationRegionId == regionId) {
                        filteredStations.push({
                            id: _substationList[i].id,
                            name: _substationList[i].stationName
                        });
                    }
                }
            } else {
                // 显示全部分站
                for (var i = 0; i < _substationList.length; i++) {
                    filteredStations.push({
                        id: _substationList[i].id,
                        name: _substationList[i].stationName
                    });
                }
            }
            
            // 更新分站下拉框数据
            $('#search_stationId').combobox('loadData', filteredStations);
            $('#search_stationId').combobox('clear');
        }
        
        /**
         * 选择车辆
         */
        function selectVehicle(vehicle, isEditMode) {
            // 只在非编辑模式下检查车辆状态
            if (!isEditMode && vehicle.originalData && vehicle.originalData.status !== "0") {
                var statusName = vehicle.statusName;
                $.messager.alert('提示', '该车辆当前状态为"' + statusName + '"，只有站内待命的车辆才能报停', 'warning');
                return;
            }
            
            selectedVehicle = vehicle;
            
            try {
                if (isEditMode) {
                    // 编辑模式下调用getVehicleById获取最新车辆信息
                    getVehicleById(vehicle.id, 
                        function(vehicleData) {
                            // 更新selectedVehicle对象
                            selectedVehicle = {
                                ...selectedVehicle,
                                contact: vehicleData.contact || '',
                                carType: vehicleData.carType || '',
                                stationId: vehicleData.stationId || ''
                            };
                            
                            // 如果有分站ID，调用getStationByIdV2获取分站详情
                            if (vehicleData.stationId) {
                                getStationByIdV2({ id: vehicleData.stationId },
                                    function(stationData) {
                                        // 显示选中车辆信息
                                        var vehicleInfoHtml = 
                                            '<div class="vehicle-info-item">' +
                                                '<div class="vehicle-info-label">车牌号</div>' +
                                                '<div class="vehicle-info-value">' + (vehicle.vehicleNo || '无') + '</div>' +
                                            '</div>' +
                                            '<div class="vehicle-info-item">' +
                                                '<div class="vehicle-info-label">车辆类型</div>' +
                                                '<div class="vehicle-info-value">' + (_carTypeDict[vehicleData.carType] || vehicleData.carType || '救护车') + '</div>' +
                                            '</div>' +
                                            '<div class="vehicle-info-item">' +
                                                '<div class="vehicle-info-label">所属分站</div>' +
                                                '<div class="vehicle-info-value">' + (stationData.stationName || '未知') + '</div>' +
                                            '</div>' +
                                            '<div class="vehicle-info-item">' +
                                                '<div class="vehicle-info-label">联系电话</div>' +
                                                '<div class="vehicle-info-value">' + (vehicleData.contact || '未知') + '</div>' +
                                            '</div>';
                                            
                                        $('#selectedVehicleDetail').html(vehicleInfoHtml);
                                        $('#selectedVehicleInfo').show();
                                    },
                                    function(e, url, errMsg) {
                                        console.error('获取分站详细信息失败:', errMsg);
                                        // 获取分站信息失败时显示原始分站名称
                                        showVehicleInfoWithOriginalStation(vehicle, vehicleData);
                                    }
                                );
                            } else {
                                showVehicleInfoWithOriginalStation(vehicle, vehicleData);
                            }
                        },
                        function(e, url, errMsg) {
                            console.error('获取车辆详细信息失败:', errMsg);
                            // 获取失败时仍然显示基本信息
                            showBasicVehicleInfo(vehicle);
                        }
                    );
                } else {
                    showBasicVehicleInfo(vehicle);
                }
                
                // 隐藏车辆选择区域
                $('#vehicleSelectArea').hide();
                
                if (!isEditMode) {
                    $.messager.alert('提示', '已选择车辆：' + (vehicle.vehicleNo || vehicle.id), 'info');
                }
            } catch (e) {
                console.error('显示选中车辆信息时出错:', e);
                $.messager.alert('错误', '显示车辆信息时出现错误：' + e.message, 'error');
            }
        }
        
        /**
         * 显示带有原始分站名称的车辆信息
         */
        function showVehicleInfoWithOriginalStation(vehicle, vehicleData) {
            var vehicleInfoHtml = 
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">车牌号</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.vehicleNo || '无') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">车辆类型</div>' +
                    '<div class="vehicle-info-value">' + (_carTypeDict[vehicleData.carType] || vehicleData.carType || '救护车') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">所属分站</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.stationName || '未知') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">联系电话</div>' +
                    '<div class="vehicle-info-value">' + (vehicleData.contact || '未知') + '</div>' +
                '</div>';
                
            $('#selectedVehicleDetail').html(vehicleInfoHtml);
            $('#selectedVehicleInfo').show();
        }
        
        /**
         * 显示基本车辆信息
         */
        function showBasicVehicleInfo(vehicle) {
            var vehicleInfoHtml = 
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">车牌号</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.vehicleNo || '无') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">车辆类型</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.vehicleType || '救护车') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">所属分站</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.stationName || '未知') + '</div>' +
                '</div>' +
                '<div class="vehicle-info-item">' +
                    '<div class="vehicle-info-label">联系电话</div>' +
                    '<div class="vehicle-info-value">' + (vehicle.contact || '未知') + '</div>' +
                '</div>';
                
            $('#selectedVehicleDetail').html(vehicleInfoHtml);
            $('#selectedVehicleInfo').show();
        }
        
        /**
         * 重新选择车辆
         */
        function reselectVehicle() {
            // 隐藏已选择车辆信息
            $('#selectedVehicleInfo').hide();
            // 显示车辆选择区域
            $('#vehicleSelectArea').show();
            // 清空当前选择
            selectedVehicle = null;
        }
        
        /**
         * 搜索车辆
         */
        function searchVehicles() {
            // 获取搜索条件
            var searchRegion = $('#search_regionId').combobox('getValue');
            var searchStation = $('#search_stationId').combobox('getValue');
            var searchVehicleNo = $('#search_vehicleNo').textbox('getValue');
            
            // 构建查询参数,只查询待命状态的车辆
            var queryParams = {
                pageNum: 1,
                pageSize: 10,
                plateNum: searchVehicleNo || '',
                stationName: searchStation ? $('#search_stationId').combobox('getText') : '',
                stationRegionId: searchRegion || '',
                statusList: ["0"] // 只查询站内待命车辆
            };
            
            // 调用接口获取数据
            getMobilesStatusListWithDistance(queryParams,
                function(result) {
                    if (Array.isArray(result)) {
                        $('#dg-vehicle-list').datagrid('load', {
                            total: result.length,
                            rows: result.map(function(item) {
                                var carTypeName = _carTypeDict[item.carType] || item.carType || '救护车';
                                
                                return {
                                    id: item.id,
                                    vehicleNo: item.plateNum,
                                    vehicleType: item.carType + ' - ' + carTypeName,
                                    stationName: item.stationName || '',
                                    contact: item.contact || '',
                                    statusName: item.statusStr || item.mobileStatusStr || '',
                                    originalData: item
                                };
                            })
                        });
                    } else {
                        console.error('接口返回数据格式不正确');
                        $('#dg-vehicle-list').datagrid('load', {
                            total: 0,
                            rows: []
                        });
                    }
                },
                function(e, url, errMsg) {
                    console.error('获取车辆数据失败:', errMsg);
                    $.messager.alert('提示', '获取车辆数据失败：' + errMsg, 'warning');
                }
            );
        }
        
        /**
         * 清空搜索条件
         */
        function clearSearch() {
            $('#search_regionId').combobox('clear');
            $('#search_stationId').combobox('clear');
            $('#search_vehicleNo').textbox('clear');
            // 重新加载分站下拉框为全部数据
            filterStationsByRegion('');
            // 重新加载数据
            $('#dg-vehicle-list').datagrid('load');
        }
        
        /**
         * 播放录音文件
         */
        /*
        function playAudioFile() {
            var audioFileId = $('#audioFileText').data('audioFileId');
            if (!audioFileId) {
                $.messager.alert('提示', '没有录音文件', 'warning');
                return;
            }
            $.messager.alert('播放录音', '正在播放录音文件: ' + audioFileId, 'info');
        }
        
        // 设置录音文件显示
        function setAudioFile(audioFileId) {
            if (audioFileId) {
                $('#audioFileText').text('播放').css('color', '#1890ff').css('cursor', 'pointer')
                    .data('audioFileId', audioFileId)
                    .off('click').on('click', playAudioFile);
            } else {
                $('#audioFileText').text('无').css('color', '#666').css('cursor', 'default')
                    .data('audioFileId', null)
                    .off('click');
            }
        }
        */
        
        /**
         * 加载报停记录（编辑模式）
         */
        function loadStopRecord(recordId) {
            // 编辑模式下直接隐藏车辆选择区域
            $('#vehicleSelectArea').hide();
            
            // 调用接口获取报停记录详情
            getVehicleStopRecordById({ id: recordId },
                function(record) {
                    // 填充表单
                    $('#form_id').val(record.id);
                    $('#form_expectedDuration').numberbox('setValue', record.expectedDuration);
                    $('#form_warningThreshold').numberbox('setValue', record.warningThreshold);
                    $('#form_reminderInterval').numberbox('setValue', record.reminderInterval);
                    $('#form_startTime').datetimebox('setValue', record.startTime);
                    $('#form_expectedEndTime').datetimebox('setValue', record.expectedEndTime);
                    $('#form_remarks').val(record.remarks || '');
                    
                    // 设置报停原因（确保combobox已加载完数据）
                    var setStopReason = function() {
                        if ($('#form_stopReasonCode').combobox('getData').length > 0) {
                            $('#form_stopReasonCode').combobox('setValue', record.stopReasonId);
                        } else {
                            setTimeout(setStopReason, 100);
                        }
                    };
                    setStopReason();
                    
                    // 模拟选中车辆
                    var vehicleData = {
                        id: record.vehicleId,
                        vehicleNo: record.vehicleNo,
                        vehicleType: record.stopReasonCode + ' - ' + record.stopReason,
                        stationName: record.stationName || '',
                        contact: record.contact || '',
                        status: record.status,
                        originalData: record
                    };
                    selectVehicle(vehicleData, true);  // 传入true表示编辑模式
                },
                function(e, url, errMsg) {
                    console.error('获取报停记录失败:', errMsg);
                    $.messager.alert('错误', '获取报停记录失败：' + errMsg, 'error');
                }
            );
        }
        
        /**
         * 保存报停记录
         */
        function saveStopRecord() {
            // 验证表单
            var isValid = $('#stopRecordForm').form('validate');
            if (!isValid) {
                $.messager.alert('提示', '请填写完整的报停信息', 'warning');
                return;
            }
            
            // 验证是否选择了车辆
            if (!selectedVehicle) {
                $.messager.alert('提示', '请选择要报停的车辆', 'warning');
                return;
            }
            
            // 显示进度提示
            showProcess(true, '温馨提示', '正在保存报停记录，请稍后...');
            
            // 获取表单数据
            var stopReasonId = $('#form_stopReasonCode').combobox('getValue');
            var expectedDuration = $('#form_expectedDuration').numberbox('getValue');
            var warningThreshold = $('#form_warningThreshold').numberbox('getValue');
            var reminderInterval = $('#form_reminderInterval').numberbox('getValue');
            var startTime = $('#form_startTime').datetimebox('getValue');
            var expectedEndTime = $('#form_expectedEndTime').datetimebox('getValue');
            var remarks = $('#form_remarks').val();
            var recordId = $('#form_id').val();
            
            if (recordId) {
                // 编辑模式 - 调用更新接口
                var updateData = {
                    id: recordId,
                    expectedDuration: parseFloat(expectedDuration) || 0,
                    remarks: remarks || '',
                    reminderInterval: parseInt(reminderInterval) || 0,
                    startTime: startTime || '',
                    expectedEndTime: expectedEndTime || '',
                    stopReasonId: stopReasonId || '',
                    vehicleId: selectedVehicle.id || '',
                    vehicleNo: selectedVehicle.vehicleNo || '',
                    warningThreshold: parseFloat(warningThreshold) || 0,
                    status: 1,  // 报停中状态
                    seatId: _pSeat.id, //座席id
                    seatCode: _pSeat.seatId, //座席code
                    seatUserId: _pSeat.userId, //当前座席用户id
                    seatUserName: _pSeat.userName, //当前座席用户工号
                    seatUser: _pSeat.user //当前座席用户姓名
                };
                
                updateVehicleStopRecordById(updateData,
                    function(response) {
                        showProcess(false);
                        $.messager.alert('成功', '车辆报停记录更新成功', 'info', function() {
                            try {
                                window.parent.refreshStopRecordList();
                            } catch (e) {
                                console.error('通知父窗口刷新失败');
                            }
                            closeWindow();
                        });
                    },
                    function(e, url, errMsg) {
                        showProcess(false);
                        console.error('更新失败:', errMsg);
                        $.messager.alert('错误', '车辆报停记录更新失败：' + errMsg, 'error');
                    }
                );
            } else {
                // 新增模式 - 使用原有的stopVehicle接口
                var requestData = {
                    expectedDuration: parseFloat(expectedDuration) || 0,
                    remarks: remarks || '',
                    reminderInterval: parseInt(reminderInterval) || 0,
                    startTime: startTime || '',
                    expectedEndTime: expectedEndTime || '',
                    stopReasonId: stopReasonId || '',
                    vehicleId: selectedVehicle.id || '',
                    warningThreshold: parseFloat(warningThreshold) || 0,
                    seatId: _pSeat.id, //座席id
                    seatCode: _pSeat.seatId, //座席code
                    seatUserId: _pSeat.userId, //当前座席用户id
                    seatUserName: _pSeat.userName, //当前座席用户工号
                    seatUser: _pSeat.user //当前座席用户姓名
                };
                
                stopVehicle(requestData,
                    function(response) {
                        showProcess(false);
                        $.messager.alert('成功', '车辆报停成功', 'info', function() {
                            try {
                                window.parent.refreshStopRecordList();
                            } catch (e) {
                                console.error('通知父窗口刷新失败');
                            }
                            closeWindow();
                        });
                    },
                    function(e, url, errMsg) {
                        showProcess(false);
                        console.error('新增失败:', errMsg);
                        $.messager.alert('错误', '车辆报停失败：' + errMsg, 'error');
                    }
                );
            }
        }
        
        /**
         * 关闭窗口
         */
        function closeWindow() {
            try {
                window.parent.$('#stopRecordEditWindow').window('close');
            } catch (e) {
                window.close();
            }
        }
        
        /**
         * 获取URL参数
         */
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }
    </script>
</body>
</html> 