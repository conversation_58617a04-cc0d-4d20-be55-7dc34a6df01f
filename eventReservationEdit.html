<!--
/* *****************************************************************
* 120EDC急救指挥中心预约编辑页面
* 本页面用于新增和编辑预约记录
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: AI Assistant
* Created: 2024/01/22
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心预约编辑</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <!-- 高德地图API -->
    <script type="text/javascript">
        // 动态加载高德地图API
        function loadGaodeScript(onLoad) {
            const cfg = getMapSysConfig();  // 从localStorage获取地图配置
            const key = cfg.gaode?.key;
            if (!key) {
                console.error('未配置高德地图key');
                return;
            }
            const s = document.createElement('script');
            s.type = 'text/javascript';
            s.src = `https://webapi.amap.com/maps?v=2.0&key=${key}&plugin=AMap.Scale,AMap.ToolBar,AMap.Geocoder,AMap.PlaceSearch,AMap.AutoComplete,AMap.Marker`;
            s.onload = () => {
                // 加载完成后再加载UI组件库
                const ui = document.createElement('script');
                ui.type = 'text/javascript';
                ui.src = 'https://webapi.amap.com/ui/1.1/main.js?v=1.1.1';
                ui.onload = () => {
                    console.log('高德地图API和UI库加载完成');
                    window.onAMapUILoaded = function() {
                        console.log('AMapUI加载完成');
                        initMapFeatures();
                    };
                    onLoad && onLoad();
                };
                document.head.appendChild(ui);
            };
            s.onerror = e => $.messager.alert('错误', '高德地图脚本加载失败');
            document.head.appendChild(s);
        }

        // 获取地图配置
        function getMapSysConfig() {
            try {
                const sysConfigMap = localStorage.getItem('sysConfigMap');
                return sysConfigMap ? JSON.parse(sysConfigMap) : {};
            } catch (e) {
                console.error('获取地图配置失败:', e);
                return {};
            }
        }

        // 初始化地图相关功能
        function initMapFeatures() {
            try {
                // 创建隐藏的地图实例（用于地址自动补全）
                var hiddenMap = new AMap.Map("the-map", {
                    viewMode: '2D',
                    zoom: 13,
                    center: [105.443352, 28.875678]
                });


                // 设置地图显示的城市
                hiddenMap.setCity(cityMap); 
                                // 初始化地理编码服务
                geocoder = new AMap.Geocoder({
                    city: cityMap,
                    radius: 1000
                });

                // 初始化搜索服务
                placeSearch = new AMap.PlaceSearch({
                    pageSize: 10,
                    pageIndex: 1,
                    city: cityMap,
                    citylimit: true
                });

                // 初始化地址自动补全
                initAddressInputAutoComplete();

                console.log('地图相关功能初始化完成');
                



            } catch (e) {
                console.error('初始化地图功能失败:', e);
            }
        }

        // 页面加载完成后初始化
        var cityMap = null;
        $(document).ready(function () {
            // 检查URL参数，判断是否是编辑模式
            var recordId = getUrlParam('id');
            var isEditMode = !!recordId;
            
            querySysConf('map_city',
                    function (data) {
                        // 设置地图显示的城市
                        cityMap = data || '泸州市'; // 默认城市
                        console.log('地图配置的城市:', cityMap);
                        
                        // 初始化页面
                        initializePage();
                        
                        if (isEditMode) {
                            // 编辑模式，加载预约记录
                            loadReservation(recordId);
                        } else {
                            // 新增模式，生成预约编号
                            generateReservationNo();
                            // 设置默认值
                            setDefaultValues();
                        }
                        
                        // 等待页面元素加载完成后初始化地址自动补全
                        setTimeout(function() {
                            if (typeof AMap !== 'undefined') {
                                initAddressInputAutoComplete();
                            } else {
                                console.warn('高德地图API未加载，准备加载...');
                                // 加载地图API
                                loadGaodeScript(function() {
                                    console.log('高德地图API加载完成，初始化地址自动补全');
                                    initAddressInputAutoComplete();
                });
                            }
                        }, 1000);

                        // 绑定搜索按钮点击事件
                        $('#mapSearchBtn').click(function() {
                            searchAddress();
                        });
                        
                        // 绑定搜索输入框回车事件
                        $('#mapSearchInput').keydown(function(e) {
                            if (e.keyCode === 13) {
                                searchAddress();
                                e.preventDefault(); // 阻止默认提交表单行为
                            }
                        });
                    },
                    function(error) {
                        console.error('获取地图城市配置失败:', error);
                        cityMap = '泸州市'; // 默认城市
                        
                        // 继续初始化页面
                        initializePage();
                        if (isEditMode) {
                            loadReservation(recordId);
                        } else {
                            generateReservationNo();
                            setDefaultValues();
                        }
                    }
            );
        });

        /**
         * 初始化地图
         */
        function initMap() {
            try {
                // 如果地图已经初始化，则不重复初始化
                if (map) {
                    map.resize();
                    return;
                }

                // 创建地图实例
                map = new AMap.Map('mapContainer', {
                    resizeEnable: true,
                    zoom: 13,
                    viewMode: '2D'
                });

                // 设置地图显示的城市
                map.setCity(cityMap);

                // 等待地图加载完成
                map.on('complete', function() {
                    console.log('地图加载完成');
                    
                    // 添加地图控件
                    map.addControl(new AMap.Scale());
                    map.addControl(new AMap.ToolBar({
                        position: 'RT'
                    }));

                    // 创建标记点
                    marker = new AMap.Marker({
                        position: map.getCenter(),
                        draggable: true, // 允许拖动标记点
                        map: map,
                        animation: 'AMAP_ANIMATION_DROP'
                    });

                    // 标记点拖动结束事件
                    marker.on('dragend', function(e) {
                        var position = marker.getPosition();
                        getAddressByLocation(position.lng, position.lat);
                    });

                    // 地图移动事件 - 仅更新标记点位置
                    map.on('mapmove', function() {
                        // 获取地图中心点
                        var center = map.getCenter();
                        // 更新标记点位置到地图中心
                        marker.setPosition(center);
                    });
                    
                    // 地图移动结束事件 - 更新地址信息
                    map.on('moveend', function() {
                        // 获取地图中心点
                        var center = map.getCenter();
                        // 获取中心点对应的地址
                        getAddressByLocation(center.lng, center.lat);
                    });
                    
                    // 地图点击事件
                    map.on('click', function(e) {
                        // 获取点击位置经纬度
                        var lnglat = e.lnglat;
                        // 更新标记点位置
                        marker.setPosition(lnglat);
                        // 获取位置对应的地址
                        getAddressByLocation(lnglat.lng, lnglat.lat);
                    });
                    
                    // 初始获取地图中心点位置信息
                    var center = map.getCenter();
                    getAddressByLocation(center.lng, center.lat);
                });

                console.log('地图初始化成功');
            } catch (error) {
                console.error('地图初始化失败:', error);
            }
        }

        /**
         * 初始化地址输入框自动补全
         */
        function initAddressInputAutoComplete() {
            if (typeof AMap === 'undefined') {
                console.error('高德地图API未加载');
                setTimeout(initAddressInputAutoComplete, 1000);
                return;
            }

            try {
                // 获取实际的输入框元素
                var addressInput = $('#form_address').next().find('input.textbox-text');
                var addressWaitInput = $('#form_addressWait').next().find('input.textbox-text');
                
                if (addressInput.length && addressWaitInput.length) {
                    // 设置输入框的id
                    addressInput.attr('id', 'form_address_input');
                    addressWaitInput.attr('id', 'form_addressWait_input');
                    
                    // 初始化自动补全
                    AMap.plugin(['AMap.AutoComplete'], function(){
                        // 预约地址自动补全
                        var addressAutoComplete = new AMap.AutoComplete({
                            input: 'form_address_input',
                            city: cityMap,
                            citylimit: true
                        });
                        
                        addressAutoComplete.on('select', function(e){
                            var poi = e.poi;
                            if (poi && poi.location) {
                                $('#form_address').textbox('setValue', poi.name);
                                $('#form_lng').val(poi.location.lng);
                                $('#form_lat').val(poi.location.lat);
                            }
                        });
                        
                        // 等车地址自动补全
                        var addressWaitAutoComplete = new AMap.AutoComplete({
                            input: 'form_addressWait_input',
                            city: cityMap,
                            citylimit: true
                        });
                        
                        addressWaitAutoComplete.on('select', function(e){
                            var poi = e.poi;
                            if (poi && poi.location) {
                                $('#form_addressWait').textbox('setValue', poi.name);
                                $('#form_waitLng').val(poi.location.lng);
                                $('#form_waitLat').val(poi.location.lat);
                            }
                        });
                    });
                } else {
                    console.error('找不到地址输入框元素');
                    setTimeout(initAddressInputAutoComplete, 1000);
                }
            } catch(e) {
                console.error("初始化地址输入框自动补全失败:", e);
                setTimeout(initAddressInputAutoComplete, 1000);
            }
        }
    </script>
    <!-- 修改CSS样式，减少间距和留白 -->
    <style>
        body {
            overflow-y: auto;
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        input {
            outline: none !important;
        }
        
        .textbox-text {
            outline: none !important;
        }
        
        .combo-text {
            outline: none !important;
        }
        
        .numberbox-text {
            outline: none !important;
        }
        
        .datebox-text {
            outline: none !important;
        }
        
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        
        /* 调整组盒子样式 */
        .group-box {
            min-width: 100px;
            min-height: 100px;
            border: solid #E2E7EA 1px;
            border-radius: 5px;
            margin: 3px 5px 5px 5px;
            padding: 0 16px;
        }
        
        .group-box-title {
            width: calc(100% + 16px);
            height: 30px;
            line-height: 30px;
            background: url(./style/img/grouptitle.png) repeat-x;
            color: #333;
            font-weight: bold;
            font-size: 14px;
            padding-left: 10px;
            margin: 0 -16px 10px -16px;
        }
        
        /* 表单行和项目样式 */
        .form-line {
            display: flex;
            width: 100%;
            height: 40px;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .form-item {
            width: 50%;
            height: 40px;
            display: flex;
            align-items: center;
            padding-right: 20px;
        }
        
        .form-label {
            width: 100px;
            min-width: 100px; /* 确保最小宽度一致 */
            font-size: 14px;
            text-align: right;
            margin-right: 10px;
            color: #333;
            white-space: nowrap;
            display: inline-block; /* 确保块级显示 */
            box-sizing: border-box; /* 确保盒模型计算一致 */
            padding-right: 5px; /* 右侧添加一点内边距 */
        }
        
        .form-control {
            width: calc(100% - 110px); /* 自动适应宽度，减去label宽度和margin */
            min-width: 180px;          /* 最小宽度限制 */
        }
        
        .form-control-wide {
            width: calc(100% - 130px);
        }
        
        /* 底部按钮固定在底部但不占用内容空间 */
        .bottom-buttons {
            text-align: center;
            padding: 10px;
            border-top: 1px solid #E2E7EA;
            background: #f8f9fa;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1000;
        }
        
        /* 确保所有输入框都有白色背景，无论是否只读 */
        input[readonly], textarea[readonly],
        input, textarea, 
        .textbox-text, .combo-text, .numberbox-text, .datebox-text {
            background: #fff !important;
        }
        
        /* 覆盖EasyUI的默认样式，确保输入框是可编辑的 */
        .textbox-readonly {
            opacity: 1 !important;
        }
        
        /* 地图选择相关样式 */
        .map-container {
            width: 100%;
            height: 450px;
            position: relative;
            margin-bottom: 10px;
            border: 1px solid #ccc;
        }
        
        #mapContainer {
            width: 100%;
            height: 100%;
        }
        
        .map-search-box {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 999;
            width: 300px;
            background: #fff;
            padding: 5px;
            border-radius: 3px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            display: flex; /* 使用flex布局 */
            align-items: center; /* 垂直居中 */
            flex-direction: row; /* 水平排列 */
        }
        
        .map-search-input {
            flex: 1; /* 占据剩余空间 */
            height: 30px;
            padding: 0 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            outline: none;
            margin-right: 5px;
        }
        
        .map-search-btn {
            width: 50px;
            height: 30px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .map-result-list {
            position: absolute;
            top: 50px;
            left: 10px;
            z-index: 999;
            width: 300px;
            max-height: 300px;
            overflow-y: auto;
            background: #fff;
            border-radius: 3px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            display: none;
        }
        
        .map-result-item {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .map-result-item:hover {
            background: #f5f5f5;
        }
        
        .map-buttons {
            text-align: center;
            margin-top: 10px;
        }
        
        /* 地址自动补全样式 */
        .address-suggestion-menu {
            position: absolute;
            background: #fff;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 3px 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 9999;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .address-suggestion-item {
            padding: 8px 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .address-suggestion-item:hover {
            background: #f5f5f5;
        }
        
        /* 确保相对定位容器正确显示 */
        .form-item span {
            /* position: relative; */
        }
        
        /* 为底部按钮留出足够的空间 */
        .main-content {
            padding: 5px;
            padding-bottom: 65px; /* 为底部按钮留出空间 */
        }
    </style>
</head>
<body>
    <div class="main-content" style="width:100%; height: auto; min-height: 100%;">
        <!-- 预约信息表单 -->
        <div class="group-box" style="height: auto; margin-bottom: 5px;">
            <div class="group-box-title">
                预约信息
            </div>
            <form id="reservationForm">
                <input type="hidden" id="form_id" name="id" />
                
                <!-- 第一行：预约编号、预约时间 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">预约编号：</label>
                        <input id="form_reservationNo" name="reservationNo" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">预约时间：</label>
                        <input id="form_reservationTime" name="reservationTime" class="easyui-datetimebox form-control" required="true">
                    </div>
                </div>
                
                <!-- 第二行：事件类型、预约类型 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">事件类型：</label>
                        <select id="form_eventType" name="eventType" class="easyui-combobox form-control" required="true"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'0',
                                        text:'普通事件'
                                    },{
                                        value:'1',
                                        text:'重大事件'
                                    }]">
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">预约类型：</label>
                        <select id="form_callType" name="callType" class="easyui-combobox form-control" required="true"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'1',
                                        text:'急救救治（预约）'
                                    },{
                                        value:'2',
                                        text:'转运病人（预约）'
                                    },{
                                        value:'3',
                                        text:'转运物资（预约）'
                                    },{
                                        value:'4',
                                        text:'取血（预约）'
                                    }]">
                        </select>
                    </div>
                </div>
                
                <!-- 第三行：呼叫区域、主诉 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">呼叫区域：</label>
                        <select id="form_callRegionId" name="callRegionId" class="easyui-combobox form-control" required="true"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'1',
                                        text:'江阳区'
                                    },{
                                        value:'2',
                                        text:'龙马潭区'
                                    },{
                                        value:'3',
                                        text:'纳溪区'
                                    },{
                                        value:'4',
                                        text:'泸县'
                                    },{
                                        value:'5',
                                        text:'合江县'
                                    },{
                                        value:'6',
                                        text:'叙永县'
                                    },{
                                        value:'7',
                                        text:'古蔺县'
                                    }]">
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">主诉：</label>
                        <input id="form_majorCall" name="majorCall" class="easyui-textbox form-control" required="true">
                    </div>
                </div>
                
                <!-- 第四行：预约地址（占满一行） -->
                <div class="form-line">
                    <div class="form-item" style="width: 100%;">
                        <label class="form-label">预约地址：</label>
                        <span style="position:relative;display:inline-block;width:calc(100% - 130px);">
                            <input id="form_address" name="address" class="easyui-textbox form-control" style="width:100%;" required="true">
                            <input type="hidden" id="form_lng" name="lng">
                            <input type="hidden" id="form_lat" name="lat">
                        </span>
                        <i class="fa fa-map-marker" style="margin-left:5px;color:#FF5722;font-size:18px;cursor:pointer;" onclick="openMapDialog('address')" title="地图定位"></i>
                        <i id="address_coords_icon" class="fa fa-map-marker" style="margin-left:5px;color:#2196F3;font-size:16px;cursor:help;display:none;" title=""></i>
                    </div>
                </div>
                
                <!-- 第五行：等车地址（占满一行） -->
                <div class="form-line">
                    <div class="form-item" style="width: 100%;">
                        <label class="form-label">等车地址：</label>
                        <span style="position:relative;display:inline-block;width:calc(100% - 130px - 25px);">
                            <input id="form_addressWait" name="addressWait" class="easyui-textbox form-control" style="width:100%;">
                            <input type="hidden" id="form_waitLng" name="waitLng">
                            <input type="hidden" id="form_waitLat" name="waitLat">
                        </span>
                        <i class="fa fa-copy" style="margin-left:5px;margin-right:5px;color:#1E88E5;font-size:16px;cursor:pointer;" onclick="copyFromAddress()" title="复制预约地址"></i>
                        <i class="fa fa-map-marker" style="margin-left:5px;color:#009688;font-size:18px;cursor:pointer;" onclick="openMapDialog('addressWait')" title="地图定位"></i>
                        <i id="addressWait_coords_icon" class="fa fa-map-marker" style="margin-left:5px;color:#2196F3;font-size:16px;cursor:help;display:none;" title=""></i>
                    </div>
                </div>
                
                <!-- 第六行：预约电话、联系电话 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">预约电话：</label>
                        <input id="form_callIn" name="callIn" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">联系电话：</label>
                        <input id="form_contact" name="contact" class="easyui-textbox form-control" required="true">
                    </div>
                </div>
                
                <!-- 第七行：联系人 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">联系人：</label>
                        <input id="form_contacter" name="contacter" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <!-- 空白占位 -->
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 患者信息表单 -->
        <div class="group-box" style="height: auto; margin-bottom: 10px; padding-bottom: 5px;">
            <div class="group-box-title">
                患者信息
            </div>
            <form id="patientForm">
                <!-- 第一行：患者姓名、性别、年龄 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">患者姓名：</label>
                        <input id="form_patientName" name="patientName" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">性别：</label>
                        <select id="form_patientGender" name="patientGender" class="easyui-combobox form-control"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'0',
                                        text:'男'
                                    },{
                                        value:'1',
                                        text:'女'
                                    },{
                                        value:'2',
                                        text:'未知'
                                    }]">
                        </select>
                    </div>
                </div>
                
                <!-- 第二行：年龄、人数 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">年龄：</label>
                        <input id="form_patientAge" name="patientAge" class="easyui-numberbox form-control" 
                               data-options="min:0,max:150">
                    </div>
                    <div class="form-item">
                        <label class="form-label">人数：</label>
                        <input id="form_patientAmount" name="patientAmount" class="easyui-numberbox form-control" 
                               data-options="min:1,max:99,value:1">
                    </div>
                </div>
                
                <!-- 第三行：籍贯、民族、是否外籍 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">籍贯：</label>
                        <input id="form_patientCountry" name="patientCountry" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">民族：</label>
                        <input id="form_patientRace" name="patientRace" class="easyui-textbox form-control">
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">是否外籍：</label>
                        <select id="form_patientIsForeign" name="patientIsForeign" class="easyui-combobox form-control"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'0',
                                        text:'否'
                                    },{
                                        value:'1',
                                        text:'是'
                                    }]">
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">事故等级：</label>
                        <input id="form_accidentGrade" name="accidentGrade" class="easyui-textbox form-control">
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">判断：</label>
                        <input id="form_judgement" name="judgement" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">是否测试：</label>
                        <select id="form_isTest" name="isTest" class="easyui-combobox form-control"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'0',
                                        text:'否'
                                    },{
                                        value:'1',
                                        text:'是'
                                    }]">
                        </select>
                    </div>
                </div>
                
                <!-- 第四行：病情、病状 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">病情：</label>
                        <input id="form_patientCondition" name="patientCondition" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">病状：</label>
                        <input id="form_patientDiagnosis" name="patientDiagnosis" class="easyui-textbox form-control">
                    </div>
                </div>
                
                <!-- 第五行：特殊要求、是否需要担架 -->
                <div class="form-line">
                    <div class="form-item">
                        <label class="form-label">特殊要求：</label>
                        <input id="form_specialRequirement" name="specialRequirement" class="easyui-textbox form-control">
                    </div>
                    <div class="form-item">
                        <label class="form-label">需要担架：</label>
                        <select id="form_isNeedStretcher" name="isNeedStretcher" class="easyui-combobox form-control"
                                data-options="
                                    valueField:'value',
                                    textField:'text',
                                    editable:false,
                                    data:[{
                                        value:'0',
                                        text:'否'
                                    },{
                                        value:'1',
                                        text:'是'
                                    }]">
                        </select>
                    </div>
                </div>
                
                <!-- 第六行：备注 -->
                <div class="form-line">
                    <div class="form-item" style="width: 100%;">
                        <label class="form-label">备注：</label>
                        <input id="form_centerRemark" name="centerRemark" class="easyui-textbox" style="width: 588px;">
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="bottom-buttons">
            <a class="easyui-linkbutton" href="javascript:saveReservation();" 
               style="width:100px;height:32px;line-height: 32px; font-size: 14px; margin: 0 10px;" 
               data-options="iconCls:'icon-save'">保存预约</a>
            <a class="easyui-linkbutton" href="javascript:closeWindow();" 
               style="width:80px;height:32px;line-height: 32px; font-size: 14px; margin: 0 10px;" 
               data-options="iconCls:'icon-cancel'">取消</a>
        </div>
    </div>

    <!-- 地图选择弹窗 -->
    <div id="mapDialog" class="easyui-dialog" title="地图选择位置" style="width:900px;height:550px;padding:5px;"
         data-options="closed:true,modal:true,buttons:'#mapDialogButtons',onResize:function(){setTimeout(function(){if(map)map.resize()},200);}">
        <div class="map-container">
            <div id="mapContainer"></div>
            <div class="map-search-box" style="width:880px;">
                <input type="text" id="mapSearchInput" class="map-search-input" placeholder="输入地址搜索" style="flex:3;margin-right:10px;width:600px;height:36px;font-size:14px;">
                <button id="mapSearchBtn" class="map-search-btn" style="width:120px;height:36px;font-size:14px;">搜索</button>
                <button id="mapLocateBtn" class="map-search-btn" style="width:180px;margin-left:10px;height:36px;font-size:14px;" onclick="locateCurrentPosition()">当前位置</button>
            </div>
            <div id="mapSearchResult" class="map-result-list" style="width:600px;"></div>
            <div class="map-tip" style="position:absolute;bottom:10px;right:10px;background:rgba(255,255,255,0.8);padding:5px 10px;border-radius:3px;font-size:12px;color:#333;box-shadow:0 1px 4px rgba(0,0,0,0.2);z-index:90;">
                <div><i class="fa fa-info-circle"></i> 提示：移动地图或点击地图可以精确选择位置</div>
            </div>
            <div class="map-coordinates" style="position:absolute;bottom:10px;left:10px;background:rgba(255,255,255,0.8);padding:5px 10px;border-radius:3px;font-size:12px;color:#333;box-shadow:0 1px 4px rgba(0,0,0,0.2);z-index:90;display:none;" id="mapCoordinatesBox">
                经度: <span id="mapLng">--</span> 纬度: <span id="mapLat">--</span>
            </div>
        </div>
        <div style="margin-top:10px;color:#666;font-size:13px;">
            <i class="fa fa-map-marker" style="color:#f44336;"></i> 当前位置: <span id="mapCurrentAddress" style="color:#333;font-weight:bold;">正在加载...</span>
        </div>
    </div>
    
    <!-- 地图弹窗按钮 -->
    <div id="mapDialogButtons">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="confirmMapLocation()" style="width:80px">确定</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#mapDialog').dialog('close')" style="width:80px">取消</a>
    </div>
    
    <!-- 隐藏的地图容器，用于地址自动补全 -->
    <div id="the-map" style="display: none;"></div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            try{
                _baseUrl = window.parent.bsProxy.getBaseUrl();
            } catch (e) {
                _baseUrl = window.parent.parent.bsProxy.getBaseUrl();
            }
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            try{
                _pSeat = evalJson(window.parent.bsProxy.getSeatInfo())
            } catch (e) {
                _pSeat = evalJson(window.parent.parent.bsProxy.getSeatInfo());
            };
        }
        
        // 编辑模式下的记录ID
        var editingRecordId = null;
        
        /**
         * 初始化页面
         */
        function initializePage() {
            // 设置预约时间默认值为当前时间
            var currentTime = new Date().formatString('yyyy-MM-dd hh:mm:ss');
            $('#form_reservationTime').datetimebox('setValue', currentTime);
            
            // 确保所有输入框不是只读状态
            setTimeout(function() {
                // 移除所有textbox的readonly属性
                $('.textbox-text').removeAttr('readonly');
                $('.textbox-text').css('background-color', '#fff');
                
                // 移除所有combobox的readonly属性
                $('.combo-text').removeAttr('readonly');
                $('.combo-text').css('background-color', '#fff');
                
                // 移除所有numberbox的readonly属性
                $('.numberbox-text').removeAttr('readonly');
                $('.numberbox-text').css('background-color', '#fff');
                
                // 移除所有datebox的readonly属性
                $('.datebox-text').removeAttr('readonly');
                $('.datebox-text').css('background-color', '#fff');
            }, 500);
        }
        
        /**
         * 生成预约编号
         */
        function generateReservationNo() {
            var now = new Date();
            var reservationNo = 'YY' + now.formatString('yyyyMMdd') + 
                              String(Math.floor(Math.random() * 9000 + 1000));
            $('#form_reservationNo').textbox('setValue', reservationNo);
        }
        
        /**
         * 设置默认值
         */
        function setDefaultValues() {
            $('#form_eventType').combobox('setValue', '0');
            $('#form_callType').combobox('setValue', '1');
            $('#form_callRegionId').combobox('setValue', '1');
            $('#form_patientGender').combobox('setValue', '2');
            $('#form_patientAmount').numberbox('setValue', 1);
            $('#form_patientIsForeign').combobox('setValue', '0');
            $('#form_isNeedStretcher').combobox('setValue', '0');
            $('#form_isTest').combobox('setValue', '0');
        }
        
        /**
         * 加载预约记录
         */
        function loadReservation(recordId) {
            editingRecordId = recordId;
            
            // 模拟加载数据
            var mockData = {
                id: recordId,
                reservationNo: 'YY202401220001',
                reservationTime: '2024-01-23 09:30:00',
                eventType: '0',
                callType: '1',
                callRegionId: '1',
                address: '泸州市江阳区江阳南路16号',
                addressWait: '大门口',
                majorCall: '骨折',
                contact: '13800138000',
                contacter: '张先生',
                patientName: '张三',
                patientGender: '0',
                patientAge: 45,
                patientAmount: 1,
                patientCondition: '清醒',
                patientDiagnosis: '右腿骨折',
                specialRequirement: '需要轮椅',
                isNeedStretcher: '1',
                centerRemark: '患者行动不便',
                callIn: '02830000000',
                patientCountry: '四川泸州',
                patientRace: '汉族',
                patientIsForeign: '0',
                accidentGrade: 'A级',
                judgement: '需要紧急处理',
                isTest: '0',
                lng: 105.443352,
                lat: 28.875678,
                waitLng: 105.442398,
                waitLat: 28.876512
            };
            
            // 填充表单
            $('#form_id').val(mockData.id);
            $('#form_reservationNo').textbox('setValue', mockData.reservationNo);
            // 确保没有设置只读属性
            $('#form_reservationNo').textbox('textbox').removeAttr('readonly');
            $('#form_reservationTime').datetimebox('setValue', mockData.reservationTime);
            $('#form_eventType').combobox('setValue', mockData.eventType);
            $('#form_callType').combobox('setValue', mockData.callType);
            $('#form_callRegionId').combobox('setValue', mockData.callRegionId);
            $('#form_address').textbox('setValue', mockData.address);
            $('#form_addressWait').textbox('setValue', mockData.addressWait);
            $('#form_majorCall').textbox('setValue', mockData.majorCall);
            $('#form_contact').textbox('setValue', mockData.contact);
            $('#form_contacter').textbox('setValue', mockData.contacter);
            $('#form_patientName').textbox('setValue', mockData.patientName);
            $('#form_patientGender').combobox('setValue', mockData.patientGender);
            $('#form_patientAge').numberbox('setValue', mockData.patientAge);
            $('#form_patientAmount').numberbox('setValue', mockData.patientAmount);
            $('#form_patientCondition').textbox('setValue', mockData.patientCondition);
            $('#form_patientDiagnosis').textbox('setValue', mockData.patientDiagnosis);
            $('#form_specialRequirement').textbox('setValue', mockData.specialRequirement);
            $('#form_isNeedStretcher').combobox('setValue', mockData.isNeedStretcher);
            $('#form_centerRemark').textbox('setValue', mockData.centerRemark);
            $('#form_callIn').textbox('setValue', mockData.callIn);
            $('#form_patientCountry').textbox('setValue', mockData.patientCountry);
            $('#form_patientRace').textbox('setValue', mockData.patientRace);
            $('#form_patientIsForeign').combobox('setValue', mockData.patientIsForeign);
            $('#form_accidentGrade').textbox('setValue', mockData.accidentGrade);
            $('#form_judgement').textbox('setValue', mockData.judgement);
            $('#form_isTest').combobox('setValue', mockData.isTest);
            
            // 设置经纬度
            $('#form_lng').val(mockData.lng);
            $('#form_lat').val(mockData.lat);
            $('#form_waitLng').val(mockData.waitLng);
            $('#form_waitLat').val(mockData.waitLat);
            
            // 显示经纬度信息
            if (mockData.lng && mockData.lat) {
                updateCoordsDisplay('address', mockData.lng, mockData.lat);
            }
            if (mockData.waitLng && mockData.waitLat) {
                updateCoordsDisplay('addressWait', mockData.waitLng, mockData.waitLat);
            }
        }
        
        /**
         * 保存预约记录
         */
        function saveReservation() {
            // 验证表单
            var isValid = $('#reservationForm').form('validate') && $('#patientForm').form('validate');
            if (!isValid) {
                $.messager.alert('提示', '请填写完整的预约信息', 'warning');
                return;
            }
            
            // 获取地址和经纬度
            var address = $('#form_address').textbox('getValue');
            var lng = $('#form_lng').val();
            var lat = $('#form_lat').val();
            
            // 检查现场地址是否有经纬度
            if (address && address.trim() !== '') {
                if (!lng || !lat) {
                    $.messager.alert('提示', '请使用地图选择预约地址的准确位置', 'warning', function() {
                        // 打开地图对话框
                        openMapDialog('address');
                    });
                    return;
                }
            } else {
                $.messager.alert('提示', '请填写预约地址并在地图上选择准确位置', 'warning');
                return;
            }
            
            // 显示进度提示
            showProcess(true, '温馨提示', '正在保存预约记录，请稍后...');
            
            // 获取表单数据
            var formData = {
                id: $('#form_id').val(),
                reservationNo: $('#form_reservationNo').textbox('getValue'),
                reservationTime: $('#form_reservationTime').datetimebox('getValue'),
                eventType: $('#form_eventType').combobox('getValue'),
                callType: $('#form_callType').combobox('getValue'),
                callRegionId: $('#form_callRegionId').combobox('getValue'),
                address: address,
                lng: lng,
                lat: lat,
                addressWait: $('#form_addressWait').textbox('getValue'),
                waitLng: $('#form_waitLng').val(),
                waitLat: $('#form_waitLat').val(),
                majorCall: $('#form_majorCall').textbox('getValue'),
                contact: $('#form_contact').textbox('getValue'),
                contacter: $('#form_contacter').textbox('getValue'),
                patientName: $('#form_patientName').textbox('getValue'),
                patientGender: $('#form_patientGender').combobox('getValue'),
                patientAge: $('#form_patientAge').numberbox('getValue'),
                patientAmount: $('#form_patientAmount').numberbox('getValue'),
                patientCondition: $('#form_patientCondition').textbox('getValue'),
                patientDiagnosis: $('#form_patientDiagnosis').textbox('getValue'),
                specialRequirement: $('#form_specialRequirement').textbox('getValue'),
                isNeedStretcher: $('#form_isNeedStretcher').combobox('getValue'),
                centerRemark: $('#form_centerRemark').textbox('getValue'),
                callIn: $('#form_callIn').textbox('getValue'),
                patientCountry: $('#form_patientCountry').textbox('getValue'),
                patientRace: $('#form_patientRace').textbox('getValue'),
                patientIsForeign: $('#form_patientIsForeign').combobox('getValue'),
                accidentGrade: $('#form_accidentGrade').textbox('getValue'),
                judgement: $('#form_judgement').textbox('getValue'),
                isTest: $('#form_isTest').combobox('getValue'),
                // 添加座席信息
                seatId: _pSeat.id,
                seatCode: _pSeat.seatId,
                seatUserId: _pSeat.userId,
                seatUserName: _pSeat.userName,
                seatUser: _pSeat.user
            };
            
            console.log('保存预约数据:', formData);
            
            // 模拟保存操作
            setTimeout(function() {
                showProcess(false);
                $.messager.alert('成功', '预约记录保存成功', 'info', function() {
                    try {
                        window.parent.refreshReservationList();
                    } catch (e) {
                        console.error('通知父窗口刷新失败');
                    }
                    closeWindow();
                });
            }, 1000);
        }
        
        /**
         * 关闭窗口
         */
        function closeWindow() {
            try {
                window.parent.$('#reservationEditWindow').window('close');
            } catch (e) {
                window.close();
            }
        }
        
        /**
         * 获取URL参数
         */
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }
        
        // 地图相关变量
        var map = null;
        var marker = null;
        var geocoder = null;
        var placeSearch = null;
        var currentAddressType = 'address'; // 当前操作的地址类型：address或addressWait
        var addressAc = null; // 预约地址自动完成
        var addressWaitAc = null; // 等车地址自动完成
        
        /**
         * 初始化地图
         */
        function initMap() {
            try {
                // 如果地图已经初始化，则不重复初始化
                if (map) {
                    map.resize();
                    return;
                }

                // 创建地图实例
                map = new AMap.Map('mapContainer', {
                    resizeEnable: true,
                    zoom: 13,
                    viewMode: '2D'
                });

                // 设置地图显示的城市
                map.setCity(cityMap);

                // 等待地图加载完成
                map.on('complete', function() {
                    console.log('地图加载完成');
                    
                    // 添加地图控件
                    map.addControl(new AMap.Scale());
                    map.addControl(new AMap.ToolBar({
                        position: 'RT'
                    }));

                    // 创建标记点
                    marker = new AMap.Marker({
                        position: map.getCenter(),
                        draggable: true, // 允许拖动标记点
                        map: map,
                        animation: 'AMAP_ANIMATION_DROP'
                    });

                    // 标记点拖动结束事件
                    marker.on('dragend', function(e) {
                        var position = marker.getPosition();
                        getAddressByLocation(position.lng, position.lat);
                    });

                    // 地图移动事件 - 仅更新标记点位置
                    map.on('mapmove', function() {
                        // 获取地图中心点
                        var center = map.getCenter();
                        // 更新标记点位置到地图中心
                        marker.setPosition(center);
                    });
                    
                    // 地图移动结束事件 - 更新地址信息
                    map.on('moveend', function() {
                        // 获取地图中心点
                        var center = map.getCenter();
                        // 获取中心点对应的地址
                        getAddressByLocation(center.lng, center.lat);
                    });
                    
                    // 地图点击事件
                    map.on('click', function(e) {
                        // 获取点击位置经纬度
                        var lnglat = e.lnglat;
                        // 更新标记点位置
                        marker.setPosition(lnglat);
                        // 获取位置对应的地址
                        getAddressByLocation(lnglat.lng, lnglat.lat);
                    });
                    
                    // 初始获取地图中心点位置信息
                    var center = map.getCenter();
                    getAddressByLocation(center.lng, center.lat);
                });

                console.log('地图初始化成功');
            } catch (error) {
                console.error('地图初始化失败:', error);
            }
        }

        /**
         * 初始化地图搜索框自动补全
         */
        function initMapSearchAutoComplete() {
            try {
                AMap.plugin(['AMap.AutoComplete', 'AMap.PlaceSearch'], function() {
                    // 初始化搜索服务
                    placeSearch = new AMap.PlaceSearch({
                        city: cityMap,
                        citylimit: true,
                        pageSize: 10,
                        pageIndex: 1
                    });

                    // 初始化自动完成
                    var mapSearchAc = new AMap.AutoComplete({
                        input: 'mapSearchInput',
                        city: cityMap,
                        citylimit: true
                    });
                    
                    mapSearchAc.on('select', function(e) {
                        var poi = e.poi;
                        if (poi && poi.location) {
                            setMapCenterWithZoom(poi.location.lng, poi.location.lat);
                            $('#mapSearchInput').val(poi.name);
                            $('#mapSearchResult').hide();
                        }
                    });
                });
            } catch(e) {
                console.error("初始化地图搜索框自动补全失败:", e);
            }
        }

        /**
         * 初始化地址输入框自动补全
         */
        function initAddressInputAutoComplete() {
            if (typeof AMap === 'undefined') {
                console.error('高德地图API未加载');
                setTimeout(initAddressInputAutoComplete, 1000);
                return;
            }

            try {
                // 获取实际的输入框元素
                var addressInput = $('#form_address').next().find('input.textbox-text');
                var addressWaitInput = $('#form_addressWait').next().find('input.textbox-text');
                
                if (addressInput.length && addressWaitInput.length) {
                    // 设置输入框的id
                    addressInput.attr('id', 'form_address_input');
                    addressWaitInput.attr('id', 'form_addressWait_input');
                    
                    // 初始化自动补全
                    AMap.plugin(['AMap.AutoComplete'], function(){
                        // 预约地址自动补全
                        var addressAutoComplete = new AMap.AutoComplete({
                            input: 'form_address_input',
                            city: cityMap,
                            citylimit: true
                        });
                        
                        addressAutoComplete.on('select', function(e){
                            var poi = e.poi;
                            if (poi && poi.location) {
                                $('#form_address').textbox('setValue', poi.name);
                                $('#form_lng').val(poi.location.lng);
                                $('#form_lat').val(poi.location.lat);
                            }
                        });
                        
                        // 等车地址自动补全
                        var addressWaitAutoComplete = new AMap.AutoComplete({
                            input: 'form_addressWait_input',
                            city: cityMap,
                            citylimit: true
                        });
                        
                        addressWaitAutoComplete.on('select', function(e){
                            var poi = e.poi;
                            if (poi && poi.location) {
                                $('#form_addressWait').textbox('setValue', poi.name);
                                $('#form_waitLng').val(poi.location.lng);
                                $('#form_waitLat').val(poi.location.lat);
                            }
                        });
                    });
                } else {
                    console.error('找不到地址输入框元素');
                    setTimeout(initAddressInputAutoComplete, 1000);
                }
            } catch(e) {
                console.error("初始化地址输入框自动补全失败:", e);
                setTimeout(initAddressInputAutoComplete, 1000);
            }
        }

        /**
         * 打开地图选择弹窗
         */
        function openMapDialog(addressType) {
            currentAddressType = addressType;
            
            // 打开弹窗
            $('#mapDialog').dialog('open');
            
            // 清理可能存在的提示元素
            $('#mapLoadingTip, #mapSearchingTip, #addressTip').remove();
            
            // 添加加载状态提示
            if (!$('#mapLoadingTip').length) {
                $('<div id="mapLoadingTip" style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.7);color:#fff;padding:10px 20px;border-radius:4px;z-index:1000;">地图加载中...</div>')
                    .appendTo('#mapContainer');
            }
            
            // 延迟初始化地图，确保容器已经渲染
            setTimeout(function() {
                // 初始化地图
                if (!map) {
                    try {
                    initMap();
                    } catch (e) {
                        console.error('地图初始化失败:', e);
                        $('#mapLoadingTip').text('地图加载失败，请刷新重试');
                        return;
                    }
                }
                
                // 已初始化则刷新地图尺寸
                if (map) {
                    map.resize();
                    $('#mapLoadingTip').remove();
                }
                
                // 获取当前地址和经纬度
                var address, lng, lat;
                if (addressType === 'address') {
                    address = $('#form_address').textbox('getValue');
                    lng = $('#form_lng').val();
                    lat = $('#form_lat').val();
                } else {
                    address = $('#form_addressWait').textbox('getValue');
                    lng = $('#form_waitLng').val();
                    lat = $('#form_waitLat').val();
                }
                
                // 定位优先级：
                // 1. 如果有经纬度，定位到经纬度位置
                // 2. 如果有地址但没有经纬度，通过地理编码查询定位
                // 3. 如果没有地址也没有经纬度，尝试定位到当前位置
                
                // 1. 如果已有经纬度，则定位到该位置（最高优先级）
                if (lng && lat) {
                    // 使用更高的缩放级别，方便用户查看细节
                    setMapCenterWithZoom(lng, lat, 17); // 17级缩放可以看到建筑物细节
                    $('#mapSearchInput').val(address);
                    
                    // 显示友好的提示
                    $.messager.show({
                        title: '提示',
                        msg: '已定位到选择的地址位置，您可以拖动地图或点击其他位置进行调整',
                        timeout: 3000,
                        showType: 'slide'
                    });
                }
                // 2. 如果有地址文本但没有经纬度，则使用地理编码查询位置
                else if (address && address.trim() !== '') {
                    // 去掉可能包含的省市区前缀
                    var displayAddress = address;
                    
                    // 尝试提取城市名并去掉
                    if (cityMap) {
                        displayAddress = displayAddress.replace(new RegExp(cityMap, 'g'), '');
                        
                        // 可能的区名列表（以泸州为例）
                        var districts = ['江阳区', '龙马潭区', '纳溪区', '泸县', '合江县', '叙永县', '古蔺县'];
                        for (var i = 0; i < districts.length; i++) {
                            displayAddress = displayAddress.replace(new RegExp(districts[i], 'g'), '');
                        }
                    }
                    
                    // 去掉可能残留的前导逗号、空格等
                    displayAddress = displayAddress.replace(/^[,，、\s]+/, '');
                    
                    // 设置搜索框值为处理后的地址
                    $('#mapSearchInput').val(displayAddress);
                    
                    // 清除可能存在的旧提示
                    $('#mapSearchingTip').remove();
                    
                    // 显示搜索中提示
                    var searchingTip = $('<div id="mapSearchingTip" style="position:absolute;top:60px;left:10px;background:rgba(0,0,0,0.7);color:#fff;padding:5px 10px;border-radius:4px;z-index:1000;">地址定位中...</div>')
                        .appendTo('#mapContainer');
                    
                    // 使用原始地址进行地理编码查询，获取更准确的位置
                    // 注意：这里使用原始地址进行查询，而不是处理后的displayAddress
                    AMap.plugin(['AMap.Geocoder'], function() {
                        var geocoder = new AMap.Geocoder({
                            city: cityMap,
                            radius: 1000
                        });
                        
                        // 优先使用城市名+地址进行查询，提高准确性
                        var queryAddress = address;
                        if (cityMap && !address.includes(cityMap)) {
                            queryAddress = cityMap + address;
                        }
                        
                        geocoder.getLocation(queryAddress, function(status, result) {
                            // 不管成功失败，都移除提示
                            $('#mapSearchingTip').remove();
                            
                            if (status === 'complete' && result.info === 'OK' && result.geocodes.length > 0) {
                                var location = result.geocodes[0].location;
                                setMapCenterWithZoom(location.lng, location.lat, 17);
                                
                                // 显示定位成功的提示
                                $.messager.show({
                                    title: '提示',
                                    msg: '已根据地址文本定位到可能位置，请确认是否正确，或调整到准确位置',
                                    timeout: 3000,
                                    showType: 'slide'
                                });
                            } else {
                                console.log('地理编码失败，尝试使用搜索服务');
                                // 如果地理编码失败，使用地点搜索服务作为备选方案
                                searchAddressAndLocate(address);
                            }
                        });
                    });
                }
                // 3. 如果没有地址和经纬度，则尝试定位到当前位置
                else {
                    // 尝试获取当前位置
                    $('#mapSearchInput').val('');
                    $('#mapCurrentAddress').text('正在定位...');
                    
                    // 使用浏览器定位API
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                // 定位成功
                                var lng = position.coords.longitude;
                                var lat = position.coords.latitude;
                                setMapCenterWithZoom(lng, lat, 16);
                                $('#mapCurrentAddress').text('已定位到当前位置');
                                
                                // 显示成功提示
                                $.messager.show({
                                    title: '提示',
                                    msg: '已定位到您的当前位置，请确认或调整到准确地点',
                                    timeout: 3000,
                                    showType: 'slide'
                                });
                            },
                            function(error) {
                                console.error('定位失败:', error.message);
                                // 定位失败，显示城市中心
                                map.setCity(cityMap);
                                map.setZoom(13); // 城市级别的缩放
                                $('#mapCurrentAddress').text('请在地图上选择位置');
                                
                                // 显示需要选择地址的提示
                                $.messager.alert('提示', '无法获取您的当前位置，请在地图上手动选择位置，或输入地址搜索', 'info');
                            },
                            {
                                enableHighAccuracy: true, // 高精度定位
                                timeout: 5000,           // 超时时间
                                maximumAge: 0            // 不使用缓存
                            }
                        );
                    } else {
                        // 浏览器不支持定位
                        map.setCity(cityMap);
                        map.setZoom(13); // 城市级别的缩放
                        $('#mapCurrentAddress').text('请在地图上选择位置');
                        
                        // 显示不支持定位的提示
                        $.messager.alert('提示', '您的浏览器不支持定位功能，请在地图上手动选择位置', 'info');
                    }
                }
            }, 500);
        }
        
        /**
         * 设置地图中心点和缩放级别
         */
        function setMapCenterWithZoom(lng, lat, zoom) {
            if (!map || !marker) return;
            
            var position = new AMap.LngLat(lng, lat);
            
            // 先设置中心点
            map.setCenter(position);
            
            // 设置缩放级别
            map.setZoom(zoom || 16);
            
            // 更新标记点位置
            marker.setPosition(position);
            
            // 移除可能存在的旧提示
            $('#addressTip').remove();
            
            // 显示加载提示
            var addressTip = $('<div id="addressTip" style="position:absolute;top:60px;left:10px;background:rgba(0,0,0,0.7);color:#fff;padding:5px 10px;border-radius:4px;z-index:1000;">获取地址中...</div>')
                .appendTo('#mapContainer');
                
            // 获取位置对应的地址
            getAddressByLocation(lng, lat);
            
            // 3秒后移除提示
            setTimeout(function() {
                $('#addressTip').remove();
            }, 3000);
        }

        /**
         * 通过地址搜索并定位到结果
         */
        function searchAddressAndLocate(address) {
            // 移除可能存在的旧提示
            $('#mapSearchingTip').remove();
            
            // 显示搜索中提示
            var searchingTip = $('<div id="mapSearchingTip" style="position:absolute;top:60px;left:10px;background:rgba(0,0,0,0.7);color:#fff;padding:5px 10px;border-radius:4px;z-index:1000;">搜索中...</div>')
                .appendTo('#mapContainer');
                
            AMap.plugin(['AMap.PlaceSearch'], function() {
                var placeSearch = new AMap.PlaceSearch({
                    city: cityMap,
                    citylimit: true,
                    pageSize: 10,
                    pageIndex: 1
                });
                
                placeSearch.search(address, function(status, result) {
                    // 不管成功失败，都移除提示
                    $('#mapSearchingTip').remove();
                    
                    if (status === 'complete' && result.info === 'OK') {
                        var pois = result.poiList.pois;
                        if (pois && pois.length > 0) {
                            // 显示搜索结果列表
                            var html = '';
                            for (var i = 0; i < Math.min(pois.length, 5); i++) {
                                var poi = pois[i];
                                
                                // 处理地址，去掉省市区前缀
                                var fullAddress = poi.address || '';
                                var displayAddress = fullAddress;
                                
                                // 从adcode中提取省市区信息
                                if (poi.adcode) {
                                    var provinceCode = poi.adcode.substring(0, 2) + '0000';
                                    var cityCode = poi.adcode.substring(0, 4) + '00';
                                    
                                    // 尝试通过行政区划代码提取省市区名称
                                    if (poi.pname) {
                                        displayAddress = displayAddress.replace(poi.pname, '');
                                    }
                                    if (poi.cityname) {
                                        displayAddress = displayAddress.replace(poi.cityname, '');
                                    }
                                    if (poi.adname) {
                                        displayAddress = displayAddress.replace(poi.adname, '');
                                    }
                                }
                                
                                // 去掉可能残留的前导逗号、空格等
                                displayAddress = displayAddress.replace(/^[,，、\s]+/, '');
                                
                                // 如果地址为空，则使用名称
                                if (!displayAddress || displayAddress.trim() === '') {
                                    displayAddress = poi.name;
                                }
                                
                                html += '<div class="map-result-item" onclick="selectMapSearchResult(\'' + 
                                    poi.name.replace(/'/g, '\\\'') + '\', \'' + 
                                    displayAddress.replace(/'/g, '\\\'') + '\', ' + 
                                    poi.location.lng + ', ' + poi.location.lat + ')">' + 
                                    poi.name + '<br><small>' + displayAddress + '</small></div>';
                            }
                            $('#mapSearchResult').html(html).show();
                            
                            // 定位到第一个结果
                            setMapCenterWithZoom(pois[0].location.lng, pois[0].location.lat, 17);
                            
                            // 更新搜索框文本
                            $('#mapSearchInput').val(pois[0].name);
                        } else {
                            // 如果搜索失败，使用城市中心
                            if (map) {
                                map.setCity(cityMap);
                            }
                            // 显示没有找到结果的提示
                            $('#mapSearchResult').html('<div class="map-result-item">未找到相关地址</div>').show();
                            setTimeout(function() {
                                $('#mapSearchResult').hide();
                            }, 3000);
                        }
                    } else {
                        // 如果搜索失败，使用城市中心
                        if (map) {
                            map.setCity(cityMap);
                        }
                        // 显示搜索失败的提示
                        $('#mapSearchResult').html('<div class="map-result-item">搜索失败，请重试</div>').show();
                        setTimeout(function() {
                            $('#mapSearchResult').hide();
                        }, 3000);
                    }
                });
            });
        }
        
        /**
         * 搜索地址
         */
        function searchAddress() {
            var keyword = $('#mapSearchInput').val().trim();
            if (!keyword) return;
            
            // 移除可能存在的旧提示
            $('#mapSearchingTip').remove();
            
            // 显示搜索中提示
            var searchingTip = $('<div id="mapSearchingTip" style="position:absolute;top:60px;left:10px;background:rgba(0,0,0,0.7);color:#fff;padding:5px 10px;border-radius:4px;z-index:1000;">搜索中...</div>')
                .appendTo('#mapContainer');
            
            AMap.plugin(['AMap.PlaceSearch'], function() {
                var placeSearch = new AMap.PlaceSearch({
                    city: cityMap,
                    citylimit: true,
                    pageSize: 10,
                    pageIndex: 1
                });
                
                placeSearch.search(keyword, function(status, result) {
                    // 不管成功失败，都移除提示
                    $('#mapSearchingTip').remove();
                    
                    if (status === 'complete' && result.info === 'OK') {
                        var pois = result.poiList.pois;
                        if (pois && pois.length > 0) {
                            // 显示搜索结果列表
                            var html = '';
                            for (var i = 0; i < Math.min(pois.length, 5); i++) {
                                var poi = pois[i];
                                
                                // 处理地址，去掉省市区前缀
                                var fullAddress = poi.address || '';
                                var displayAddress = fullAddress;
                                
                                // 从adcode中提取省市区信息
                                if (poi.adcode) {
                                    var provinceCode = poi.adcode.substring(0, 2) + '0000';
                                    var cityCode = poi.adcode.substring(0, 4) + '00';
                                    
                                    // 尝试通过行政区划代码提取省市区名称
                                    if (poi.pname) {
                                        displayAddress = displayAddress.replace(poi.pname, '');
                                    }
                                    if (poi.cityname) {
                                        displayAddress = displayAddress.replace(poi.cityname, '');
                                    }
                                    if (poi.adname) {
                                        displayAddress = displayAddress.replace(poi.adname, '');
                                    }
                                }
                                
                                // 去掉可能残留的前导逗号、空格等
                                displayAddress = displayAddress.replace(/^[,，、\s]+/, '');
                                
                                // 如果地址为空，则使用名称
                                if (!displayAddress || displayAddress.trim() === '') {
                                    displayAddress = poi.name;
                                }
                                
                                html += '<div class="map-result-item" onclick="selectMapSearchResult(\'' + 
                                    poi.name.replace(/'/g, '\\\'') + '\', \'' + 
                                    displayAddress.replace(/'/g, '\\\'') + '\', ' + 
                                    poi.location.lng + ', ' + poi.location.lat + ')">' + 
                                    poi.name + '<br><small>' + displayAddress + '</small></div>';
                            }
                            $('#mapSearchResult').html(html).show();
                            
                            // 定位到第一个结果，使用较高的缩放级别
                            setMapCenterWithZoom(pois[0].location.lng, pois[0].location.lat, 17);
                        } else {
                            $('#mapSearchResult').html('<div class="map-result-item">未找到相关地址</div>').show();
                            setTimeout(function() {
                                $('#mapSearchResult').hide();
                            }, 3000);
                        }
                    } else {
                        $('#mapSearchResult').html('<div class="map-result-item">搜索失败，请重试</div>').show();
                        setTimeout(function() {
                            $('#mapSearchResult').hide();
                        }, 3000);
                    }
                });
            });
        }
        
        /**
         * 选择地图搜索结果
         */
        function selectMapSearchResult(name, address, lng, lat) {
            $('#mapSearchInput').val(address);
            $('#mapSearchResult').hide();
            setMapCenterWithZoom(lng, lat, 17);
        }
        
        /**
         * 根据经纬度获取地址
         */
        function getAddressByLocation(lng, lat) {
            // 更新坐标显示
            $('#mapCoordinatesBox').show();
            $('#mapLng').text(lng.toFixed(6));
            $('#mapLat').text(lat.toFixed(6));
            $('#mapCurrentAddress').text('获取地址中...');
            
            AMap.plugin(['AMap.Geocoder'], function() {
                var geocoder = new AMap.Geocoder({
                    city: "全国",
                    radius: 1000
                });
                
                var lnglat = [lng, lat];
                geocoder.getAddress(lnglat, function(status, result) {
                    if (status === 'complete' && result.info === 'OK') {
                        // 获取完整地址
                        var fullAddress = result.regeocode.formattedAddress;
                        // 获取地址组件
                        var addressComponent = result.regeocode.addressComponent;
                        
                        // 提取省市区信息
                        var province = addressComponent.province || '';
                        var city = addressComponent.city || '';
                        var district = addressComponent.district || '';
                        
                        // 去掉省市区的前缀，只保留详细地址
                        var detailAddress = fullAddress;
                        if (province) {
                            detailAddress = detailAddress.replace(province, '');
                        }
                        if (city) {
                            detailAddress = detailAddress.replace(city, '');
                        }
                        if (district) {
                            detailAddress = detailAddress.replace(district, '');
                        }
                        
                        // 去掉可能残留的前导逗号、空格等
                        detailAddress = detailAddress.replace(/^[,，、\s]+/, '');
                        
                        // 如果详细地址为空，则使用街道信息
                        if (!detailAddress || detailAddress.trim() === '') {
                            detailAddress = addressComponent.township || '';
                            if (addressComponent.street) {
                                detailAddress += addressComponent.street;
                            }
                            if (addressComponent.streetNumber) {
                                detailAddress += addressComponent.streetNumber;
                            }
                        }
                        
                        // 更新地址显示
                        $('#mapSearchInput').val(detailAddress);
                        $('#mapCurrentAddress').text(detailAddress);
                        
                        // 完整地址保存到数据属性中，确认时可能需要
                        $('#mapSearchInput').data('fullAddress', fullAddress);
                        $('#mapSearchInput').data('position', {lng: lng, lat: lat});
                    } else {
                        console.error('地理编码查询失败');
                        $('#mapCurrentAddress').text('地址获取失败');
                    }
                    
                    // 确保提示被移除
                    $('#addressTip').remove();
                });
            });
        }
        
        /**
         * 确认地图选择的位置
         */
        function confirmMapLocation() {
            if (!marker) {
                $.messager.alert('提示', '请先在地图上选择一个位置', 'warning');
                return;
            }
            
            var position = marker.getPosition();
            var address = $('#mapSearchInput').val();
            
            if (!address || address.trim() === '') {
                $.messager.alert('提示', '位置信息不完整，请重新选择', 'warning');
                return;
            }
            
            // 根据当前操作的地址类型，填充对应的表单字段
            if (currentAddressType === 'address') {
                $('#form_address').textbox('setValue', address);
                $('#form_lng').val(position.lng);
                $('#form_lat').val(position.lat);
                
                // 显示经纬度信息
                updateCoordsDisplay('address', position.lng, position.lat);
                
                $.messager.show({
                    title: '提示',
                    msg: '预约地址已更新',
                    timeout: 2000,
                    showType: 'slide'
                });
            } else if (currentAddressType === 'addressWait') {
                $('#form_addressWait').textbox('setValue', address);
                $('#form_waitLng').val(position.lng);
                $('#form_waitLat').val(position.lat);
                
                // 显示经纬度信息
                updateCoordsDisplay('addressWait', position.lng, position.lat);
                
                $.messager.show({
                    title: '提示',
                    msg: '等车地址已更新',
                    timeout: 2000,
                    showType: 'slide'
                });
            }
            
            // 关闭弹窗
            $('#mapDialog').dialog('close');
        }
        
        /**
         * 更新坐标显示
         */
        function updateCoordsDisplay(type, lng, lat) {
            if (type === 'address') {
                var coordsText = '经度: ' + lng.toFixed(6) + ', 纬度: ' + lat.toFixed(6);
                $('#address_coords_icon').attr('title', coordsText).show();
                
                // 如果浏览器支持，使用tooltip插件增强显示效果
                if ($.fn.tooltip) {
                    $('#address_coords_icon').tooltip({
                        position: 'right',
                        content: coordsText,
                        showDelay: 200
                    });
                }
            } else if (type === 'addressWait') {
                var coordsText = '经度: ' + lng.toFixed(6) + ', 纬度: ' + lat.toFixed(6);
                $('#addressWait_coords_icon').attr('title', coordsText).show();
                
                // 如果浏览器支持，使用tooltip插件增强显示效果
                if ($.fn.tooltip) {
                    $('#addressWait_coords_icon').tooltip({
                        position: 'right',
                        content: coordsText,
                        showDelay: 200
                    });
                }
            }
        }

        /**
         * 定位到当前位置
         */
        function locateCurrentPosition() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        var lng = position.coords.longitude;
                        var lat = position.coords.latitude;
                        setMapCenterWithZoom(lng, lat);
                        $('#mapSearchInput').val('当前位置');
                        $('#mapCurrentAddress').text('当前位置');
                        $('#mapCoordinatesBox').show();
                        $('#mapLng').text(lng.toFixed(6));
                        $('#mapLat').text(lat.toFixed(6));
                    },
                    function(error) {
                        console.error('定位失败:', error.message);
                        $.messager.alert('提示', '无法获取当前位置，请手动选择或检查浏览器权限', 'warning');
                    }
                );
            } else {
                $.messager.alert('提示', '您的浏览器不支持定位功能', 'warning');
            }
        }
        
        /**
         * 将预约地址和经纬度复制到等车地址
         */
        function copyFromAddress() {
            var address = $('#form_address').textbox('getValue');
            var lng = $('#form_lng').val();
            var lat = $('#form_lat').val();
            
            if (!address || address.trim() === '') {
                $.messager.alert('提示', '预约地址为空，无法复制', 'warning');
                return;
            }
            
            // 复制地址文本
            $('#form_addressWait').textbox('setValue', address);
            
            // 如果有经纬度，也一并复制
            if (lng && lat) {
                $('#form_waitLng').val(lng);
                $('#form_waitLat').val(lat);
                
                // 更新经纬度显示图标
                updateCoordsDisplay('addressWait', lng, lat);
                
                $.messager.show({
                    title: '成功',
                    msg: '已复制预约地址和坐标信息到等车地址',
                    timeout: 2000,
                    showType: 'slide'
                });
            } else {
                $.messager.show({
                    title: '提示',
                    msg: '已复制预约地址到等车地址，但无坐标信息',
                    timeout: 2000,
                    showType: 'slide'
                });
            }
        }

        // 页面加载完成后初始化
        var cityMap = null;
        $(document).ready(function () {
            // 检查URL参数，判断是否是编辑模式
            var recordId = getUrlParam('id');
            var isEditMode = !!recordId;
            
            querySysConf('map_city',
                    function (data) {
                        // 设置地图显示的城市
                        cityMap = data || '泸州市'; // 默认城市
                        console.log('地图配置的城市:', cityMap);
                        
                        // 初始化页面
                        initializePage();
                        
                        if (isEditMode) {
                            // 编辑模式，加载预约记录
                            loadReservation(recordId);
                        } else {
                            // 新增模式，生成预约编号
                            generateReservationNo();
                            // 设置默认值
                            setDefaultValues();
                        }
                        
                        // 等待页面元素加载完成后初始化地址自动补全
                        setTimeout(function() {
                            if (typeof AMap !== 'undefined') {
                                initAddressInputAutoComplete();
                            } else {
                                console.warn('高德地图API未加载，准备加载...');
                                // 加载地图API
                                loadGaodeScript(function() {
                                    console.log('高德地图API加载完成，初始化地址自动补全');
                                    initAddressInputAutoComplete();
                                });
                            }
                        }, 1000);

                        // 绑定搜索按钮点击事件
                        $('#mapSearchBtn').click(function() {
                            searchAddress();
                        });
                        
                        // 绑定搜索输入框回车事件
                        $('#mapSearchInput').keydown(function(e) {
                            if (e.keyCode === 13) {
                                searchAddress();
                                e.preventDefault(); // 阻止默认提交表单行为
                            }
                        });
             })
            
        });
    </script>
</body>
</html> 