# 双击定位功能实现说明

## 功能概述

根据您的需求，我为市县联动系统实现了四个双击定位功能：

1. **医院双击定位** - 数据模块管理中双击医院列表定位到地图位置
2. **血站双击定位** - 数据模块管理中双击血站列表定位到地图位置  
3. **疾控中心双击定位** - 数据模块管理中双击疾控中心列表定位到地图位置
4. **任务双击定位** - 任务信息管理中双击任务列表定位到地图位置

所有功能都包含：自动开启对应图标显示、地图定位、临时高亮标记、关闭当前窗口等完整体验。

## 详细实现

### 1. 医院双击定位功能

**修改位置：** `setupHospitalsGrid()` 函数

**实现代码：**
```javascript
// 添加双击事件处理
$('#hospitalsGrid').datagrid({
    onDblClickRow: function(index, row) {
        console.log('医院表格双击事件:', index, row);
        locateHospitalOnMap(row);
    }
});

// 医院双击定位函数
function locateHospitalOnMap(hospital) {
    if (hospital.longitude && hospital.latitude) {
        // 关闭数据模块窗口
        $('#dataPanel').window('close');
        
        // 开启医院图标显示
        const hospitalBtn = document.getElementById('hospitals-display-btn');
        if (hospitalBtn && !hospitalBtn.classList.contains('active')) {
            hospitalBtn.classList.add('active');
            loadHospitalsData(); // 加载并显示医院数据
        }
        
        // 地图定位到医院
        cityCountyMap.setZoomAndCenter(16, [hospital.longitude, hospital.latitude]);
        
        // 显示成功提示
        $.messager.show({
            title: '定位成功',
            msg: `已在地图上定位到: ${hospital.name}`,
            timeout: 3000,
            showType: 'slide'
        });
        
        // 创建临时标记突出显示医院位置
        const marker = new AMap.Marker({
            position: [hospital.longitude, hospital.latitude],
            icon: 'style/img/hospital_highlight.png',
            animation: 'AMAP_ANIMATION_BOUNCE',
            title: '医院位置: ' + hospital.name
        });
        
        cityCountyMap.add(marker);
        
        // 5秒后移除临时标记
        setTimeout(() => {
            cityCountyMap.remove(marker);
        }, 5000);
    }
}
```

### 2. 血站双击定位功能

**修改位置：** `setupBloodstationsGrid()` 函数

**实现代码：**
```javascript
// 添加双击事件处理
$('#bloodstationsGrid').datagrid({
    onDblClickRow: function(index, row) {
        console.log('血站表格双击事件:', index, row);
        locateBloodstationOnMap(row);
    }
});

// 血站双击定位函数
function locateBloodstationOnMap(bloodstation) {
    if (bloodstation.longitude && bloodstation.latitude) {
        // 关闭数据模块窗口
        $('#dataPanel').window('close');
        
        // 开启血站图标显示
        const bloodstationBtn = document.getElementById('bloodstations-display-btn');
        if (bloodstationBtn && !bloodstationBtn.classList.contains('active')) {
            bloodstationBtn.classList.add('active');
            loadBloodStationsData(); // 加载并显示血站数据
        }
        
        // 地图定位到血站
        cityCountyMap.setZoomAndCenter(16, [bloodstation.longitude, bloodstation.latitude]);
        
        // 显示成功提示和临时标记
        // ... 类似医院的实现
    }
}
```

### 3. 疾控中心双击定位功能

**修改位置：** `setupCdcGrid()` 函数

**实现代码：**
```javascript
// 添加双击事件处理
$('#cdcGrid').datagrid({
    onDblClickRow: function(index, row) {
        console.log('疾控中心表格双击事件:', index, row);
        locateCdcOnMap(row);
    }
});

// 疾控中心双击定位函数
function locateCdcOnMap(cdcCenter) {
    if (cdcCenter.longitude && cdcCenter.latitude) {
        // 关闭数据模块窗口
        $('#dataPanel').window('close');
        
        // 开启疾控中心图标显示
        const cdcBtn = document.getElementById('cdc-display-btn');
        if (cdcBtn && !cdcBtn.classList.contains('active')) {
            cdcBtn.classList.add('active');
            loadCdcCentersData(); // 加载并显示疾控中心数据
        }
        
        // 地图定位到疾控中心
        cityCountyMap.setZoomAndCenter(16, [cdcCenter.longitude, cdcCenter.latitude]);
        
        // 显示成功提示和临时标记
        // ... 类似医院的实现
    }
}
```

### 4. 任务双击定位功能

**修改位置：** 任务表格初始化代码

**实现代码：**
```javascript
// 修改任务表格双击事件
$('#taskGrid').datagrid({
    onDblClickRow: function(index, row) {
        console.log('任务表格双击事件:', index, row);
        locateTaskOnMap(row);
    },
    pagination: true,
    pageSize: 15,
    pageList: [10, 15, 20, 30, 50]
});

// 任务双击定位函数
function locateTaskOnMap(task) {
    // 检查任务坐标信息（支持多种字段格式）
    const lng = task.lng || task.longitude || task.lon;
    const lat = task.lat || task.latitude;
    
    if (lng && lat) {
        // 关闭任务信息窗口
        $('#taskPanel').window('close');
        
        // 开启事故任务图标显示
        const accidentBtn = document.getElementById('accidents-display-btn');
        if (accidentBtn && !accidentBtn.classList.contains('active')) {
            accidentBtn.classList.add('active');
            loadAccidentsData(); // 加载并显示事故任务数据
        }
        
        // 地图定位到任务位置
        cityCountyMap.setZoomAndCenter(16, [lng, lat]);
        
        // 显示成功提示
        $.messager.show({
            title: '定位成功',
            msg: `已在地图上定位到任务: ${task.eventName || task.id}`,
            timeout: 3000,
            showType: 'slide'
        });
        
        // 创建临时标记突出显示任务位置
        const marker = new AMap.Marker({
            position: [lng, lat],
            icon: 'style/img/accident_highlight.png',
            animation: 'AMAP_ANIMATION_BOUNCE',
            title: '任务位置: ' + (task.eventName || task.id)
        });
        
        cityCountyMap.add(marker);
        
        // 5秒后移除临时标记
        setTimeout(() => {
            cityCountyMap.remove(marker);
        }, 5000);
    }
}
```

## 功能特点

### 🎯 统一的用户体验
- **双击触发**：所有列表都支持双击行触发定位
- **自动关闭窗口**：定位后自动关闭当前打开的EasyUI窗口
- **智能图标开启**：自动检查并开启对应的地图资源图标显示

### 🗺️ 精确的地图定位
- **16级缩放**：统一使用16级缩放提供合适的详细程度
- **临时高亮标记**：使用弹跳动画突出显示定位位置
- **5秒自动消失**：临时标记5秒后自动移除，避免地图混乱

### 💬 友好的用户反馈
- **成功提示**：显示定位成功的滑动提示信息
- **错误处理**：缺少坐标信息时显示友好的错误提示
- **控制台日志**：详细的调试信息便于问题排查

### 🔧 灵活的数据支持
- **多字段兼容**：支持多种坐标字段格式（lng/longitude/lon, lat/latitude）
- **数据验证**：完善的数据存在性和有效性检查
- **容错处理**：优雅处理各种异常情况

## 测试指南

### 医院定位测试
1. 点击顶部"数据查询"按钮
2. 切换到"医院管理"标签页
3. 双击任意医院行
4. 验证：数据窗口关闭、医院图标开启、地图定位、临时标记显示

### 血站定位测试
1. 点击顶部"数据查询"按钮
2. 切换到"血站管理"标签页
3. 双击任意血站行
4. 验证：数据窗口关闭、血站图标开启、地图定位、临时标记显示

### 疾控中心定位测试
1. 点击顶部"数据查询"按钮
2. 切换到"疾控中心"标签页
3. 双击任意疾控中心行
4. 验证：数据窗口关闭、疾控图标开启、地图定位、临时标记显示

### 任务定位测试
1. 点击顶部"任务信息"按钮
2. 双击任意任务行
3. 验证：任务窗口关闭、事故图标开启、地图定位、临时标记显示

## 技术要点

### 事件绑定
- 使用EasyUI的`onDblClickRow`事件处理双击
- 在表格初始化时绑定事件处理函数

### 窗口管理
- 使用`$('#windowId').window('close')`关闭EasyUI窗口
- 支持不同类型窗口的统一关闭处理

### 图标状态管理
- 检查按钮的`active`类状态
- 自动添加`active`类并调用对应的数据加载函数

### 地图操作
- 使用`cityCountyMap.setZoomAndCenter()`进行地图定位
- 创建临时标记使用`AMap.Marker`和动画效果
- 使用`setTimeout`实现标记的自动移除

## 兼容性说明

- **数据格式兼容**：支持多种坐标字段命名方式
- **功能独立**：不影响现有的单击定位功能
- **向后兼容**：保持与现有代码的完全兼容
- **错误容错**：完善的错误处理确保系统稳定性

## 部署说明

所有功能已集成到`cityCountyLinkage.html`文件中，无需额外配置。系统启动后即可使用双击定位功能。

建议在部署后进行完整的功能测试，确保所有双击定位功能正常工作。
