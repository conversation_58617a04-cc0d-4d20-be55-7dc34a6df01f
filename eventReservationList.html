<!--
/* *****************************************************************
* 120EDC急救指挥中心预约管理页面
* 本页面用于查看和管理预约记录
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: AI Assistant
* Created: 2024/01/22
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心预约管理</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px !important;
            font-weight: bold !important;
        }
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            height: 28px;
        }
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 0px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover,
        .datagrid-row-selected {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        .datagrid-wrap .datagrid-body tr:hover a,
        .datagrid-row-selected a {
            color: #ffffff !important;
        }

        /* 预约状态样式 */
        .status-pending { color: #ffa500; font-weight: bold; } /* 待确认 */
        .status-reserved { color: #1890ff; font-weight: bold; } /* 已预约 */
        .status-converted { color: #52c41a; font-weight: bold; } /* 已转事件 */
        .status-cancelled { color: #ff4d4f; font-weight: bold; } /* 已取消 */
        .status-expired { color: #d9d9d9; font-weight: bold; } /* 已过期 */

        /* 事件类型样式 */
        .event-normal { color: #1890ff; }
        .event-major { color: #ff4d4f; }

        input[readonly], textarea[readonly] {
            background: #fff !important;
        }
    </style>
</head>
<body>
    <div id="mainPanel_EventReservation" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">预约记录<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="reservationSearch" style="font-size: 14px;">
                    预约时间： <input id="startTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endTime" class="easyui-datetimebox" style="width:160px;" />
                    &nbsp;<a href="javascript:void(0);" class="inToday">今天</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inWeek">本周</a>
                    <span>&nbsp;预约编号：</span>
                    <input id="reservationNo" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;事件ID：</span>
                    <input id="eventId" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;录入人：</span>
                    <input id="createUser" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;状态：</span>
                    <select id="reservationStatus" class="easyui-combobox" style="width:100px;" 
                            data-options="valueField:'value',textField:'text',editable:false">
                        <option value="">全部</option>
                        <option value="0">待确认</option>
                        <option value="1">已预约</option>
                        <option value="2">已转事件</option>
                        <option value="3">已取消</option>
                        <option value="4">已过期</option>
                    </select>
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:searchReservations();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-reservation-list" style="height: Calc(100% - 100px)"
                       data-options="pagination:true,singleSelect:true,rownumbers:true,fit:true,fitColumns:false,toolbar:'#tb'">
                    <thead>
                        <tr>
                            <th data-options="field:'reservationNo',width:120,align:'center'">预约编号</th>
                            <th data-options="field:'eventId',width:120,align:'center'">事件ID</th>
                            <th data-options="field:'reservationTime',width:150,align:'center'">预约时间</th>
                            <th data-options="field:'statusName',width:80,align:'center',formatter:formatStatus">状态</th>
                            <th data-options="field:'eventTypeName',width:100,align:'center',formatter:formatEventType">事件类型</th>
                            <th data-options="field:'eventSrcCodeName',width:100,align:'center'">事件来源</th>
                            <th data-options="field:'callTypeName',width:100,align:'center'">预约类型</th>
                            <th data-options="field:'callRegionName',width:100,align:'center'">呼叫区域</th>
                            <th data-options="field:'patientName',width:100,align:'center'">患者姓名</th>
                            <th data-options="field:'patientGenderName',width:60,align:'center'">性别</th>
                            <th data-options="field:'patientAge',width:60,align:'center'">年龄</th>
                            <th data-options="field:'patientAmount',width:60,align:'center'">人数</th>
                            <th data-options="field:'patientCondition',width:100,align:'center'">病情</th>
                            <th data-options="field:'address',width:200,align:'center'">预约地址</th>
                            <th data-options="field:'addressWait',width:200,align:'center'">等车地址</th>
                            <th data-options="field:'majorCall',width:150,align:'center'">主诉</th>
                            <th data-options="field:'contact',width:120,align:'center'">联系电话</th>
                            <th data-options="field:'contacter',width:100,align:'center'">联系人</th>
                            <th data-options="field:'isNeedStretcherName',width:80,align:'center'">需要担架</th>
                            <th data-options="field:'specialRequirement',width:150,align:'center'">特殊要求</th>
                            <th data-options="field:'centerRemark',width:200,align:'center'">备注</th>
                            <th data-options="field:'createUser',width:100,align:'center'">录入人</th>
                            <th data-options="field:'createTime',width:150,align:'center'">创建时间</th>
                            <th data-options="field:'toEventTime',width:150,align:'center'">转事件时间</th>
                            <th data-options="field:'toEventSeatUser',width:100,align:'center'">转事件人</th>
                        </tr>
                    </thead>
                </table>

                <div id="tb" style="height:45px;">
                    <div>
                        <a id="addReservationBtn" class="easyui-linkbutton" href="javascript:addReservation();" style="width: 100px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-add'">新增预约</a>
                        <a id="viewReservationBtn" class="easyui-linkbutton" href="javascript:viewReservation();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看详情</a>
                        <a id="editReservationBtn" class="easyui-linkbutton" href="javascript:editReservation();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-edit'">编辑</a>
                        <a id="convertToEventBtn" class="easyui-linkbutton" href="javascript:convertToEvent();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-ok'">转为事件</a>
                        <a id="cancelReservationBtn" class="easyui-linkbutton" href="javascript:cancelReservation();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-cancel'">取消预约</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新增/编辑预约窗口 -->
        <div id="reservationEditWindow" class="easyui-window" title="预约" style="width:1000px;height:700px;overflow:hidden;"
             data-options="iconCls:'icon-save',modal:true,closed:true,minimizable:false">
            <iframe id="reservationEditIframe" name="reservationEditIframe" scrolling="auto" frameborder="0" 
                    style="width:100%;height:100%;"></iframe>
        </div>
        
        <!-- 详情查看窗口 -->
        <div id="reservationDetailWindow" class="easyui-window" title="预约详情" style="width:800px;height:600px"
             data-options="iconCls:'icon-info',modal:true,closed:true">
            <div style="padding: 20px;" id="reservationDetail">
                <!-- 详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        // 模拟数据
        var mockReservations = [
            {
                id: '1',
                reservationNo: 'YY202401220001',
                eventId: '',
                reservationTime: '2024-01-23 09:30:00',
                reservationStatus: '1',
                statusName: '已预约',
                eventType: '0',
                eventTypeName: '普通事件',
                eventSrc: '2',
                eventSrcCodeName: '手工制表',
                callType: '1',
                callTypeName: '急救救治（预约）',
                callRegionId: '1',
                callRegionName: '江阳区',
                address: '泸州市江阳区江阳南路16号',
                addressWait: '小区大门口',
                lng: 105.443352,
                lat: 28.875678,
                majorCall: '突发胸痛',
                callIn: '02830000000',
                contact: '13800138000',
                contacter: '张先生',
                patientName: '张三',
                patientGender: '0',
                patientGenderName: '男',
                patientAge: 45,
                patientAmount: 1,
                patientCountry: '四川泸州',
                patientRace: '汉族',
                patientCondition: '清醒',
                patientDiagnosis: '疑似心肌梗塞',
                patientIsForeign: '0',
                centerRemark: '患者有心脏病史',
                accidentGrade: 'A级',
                specialRequirement: '需要心电监护',
                isNeedStretcher: '1',
                isNeedStretcherName: '是',
                createUser: '王调度',
                createTime: '2024-01-22 15:30:00',
                toEventTime: '',
                toEventSeatUser: '',
                cancelReason: ''
            },
            {
                id: '2',
                reservationNo: 'YY202401220002',
                eventId: 'SJ202401220001',
                reservationTime: '2024-01-22 14:00:00',
                reservationStatus: '2',
                statusName: '已转事件',
                eventType: '1',
                eventTypeName: '重大事件',
                eventSrc: '2',
                eventSrcCodeName: '手工制表',
                callType: '2',
                callTypeName: '转运病人（预约）',
                callRegionId: '2',
                callRegionName: '龙马潭区',
                address: '泸州市龙马潭区龙马大道99号',
                addressWait: '医院急诊大楼',
                lng: 105.378965,
                lat: 28.897654,
                majorCall: '转院',
                callIn: '02830000001',
                contact: '13900139000',
                contacter: '李医生',
                patientName: '李四',
                patientGender: '1',
                patientGenderName: '女',
                patientAge: 68,
                patientAmount: 1,
                patientCountry: '四川泸州',
                patientRace: '汉族',
                patientCondition: '需要呼吸机',
                patientDiagnosis: '重症肺炎',
                patientIsForeign: '0',
                centerRemark: '需要专业转运车',
                accidentGrade: 'B级',
                specialRequirement: '需要呼吸机、监护仪',
                isNeedStretcher: '1',
                isNeedStretcherName: '是',
                createUser: '李调度',
                createTime: '2024-01-22 10:00:00',
                toEventTime: '2024-01-22 13:50:00',
                toEventSeatUser: '张调度',
                cancelReason: ''
            }
        ];

        $(document).ready(function () {
            initializePage();
        });

        function initializePage() {
            // 初始化时间范围
            $("#startTime").datetimebox("setValue", "");
            $("#endTime").datetimebox("setValue", "");
            
            // 为时间快速选择设置处理函数
            $('.inToday,.inWeek').on("click", timeClickHandler);
            
            // 初始化数据表格
            $('#dg-reservation-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
                data: mockReservations,
                onClickRow: function (rowIndex, rowData) {
                    updateButtonStates(rowData);
                },
                onDblClickRow: function(rowIndex, rowData) {
                    viewReservation();
                }
            });
        }

        // 格式化状态
        function formatStatus(value, row) {
            var className = '';
            var text = value;
            switch (row.reservationStatus) {
                case '0': className = 'status-pending'; break;
                case '1': className = 'status-reserved'; break;
                case '2': className = 'status-converted'; break;
                case '3': className = 'status-cancelled'; break;
                case '4': className = 'status-expired'; break;
            }
            return '<span class="' + className + '">' + text + '</span>';
        }

        // 格式化事件类型
        function formatEventType(value, row) {
            var className = row.eventType === '1' ? 'event-major' : 'event-normal';
            return '<span class="' + className + '">' + value + '</span>';
        }

        // 更新按钮状态
        function updateButtonStates(rowData) {
            if (!rowData) {
                $('#viewReservationBtn,#editReservationBtn,#convertToEventBtn,#cancelReservationBtn').linkbutton('disable');
                return;
            }

            $('#viewReservationBtn').linkbutton('enable');
            
            // 根据状态控制按钮
            switch (rowData.reservationStatus) {
                case '0': // 待确认
                case '1': // 已预约
                    $('#editReservationBtn,#convertToEventBtn,#cancelReservationBtn').linkbutton('enable');
                    break;
                case '2': // 已转事件
                case '3': // 已取消
                case '4': // 已过期
                    $('#editReservationBtn,#convertToEventBtn,#cancelReservationBtn').linkbutton('disable');
                    break;
            }
        }

        // 时间快速选择处理函数
        function timeClickHandler() {
            var now = new Date();
            var fromTime = new Date();
            var hours = 7 * 24; // 默认一周
            
            if ($(this).hasClass('inToday')) {
                hours = 24; // 今天
                fromTime.setHours(0, 0, 0, 0);
            } else {
                fromTime.setHours(fromTime.getHours() - hours);
            }
            
            $('#startTime').datetimebox('setValue', fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            $('#endTime').datetimebox('setValue', '');
            
            searchReservations();
        }

        // 搜索预约记录
        function searchReservations() {
            var startTime = $('#startTime').datetimebox('getValue');
            var endTime = $('#endTime').datetimebox('getValue');
            var reservationNo = $('#reservationNo').textbox('getValue');
            var eventId = $('#eventId').textbox('getValue');
            var createUser = $('#createUser').textbox('getValue');
            var status = $('#reservationStatus').combobox('getValue');

            // 模拟搜索逻辑
            var filteredData = mockReservations.filter(function(item) {
                if (reservationNo && item.reservationNo.indexOf(reservationNo) === -1) return false;
                if (eventId && item.eventId.indexOf(eventId) === -1) return false;
                if (createUser && item.createUser.indexOf(createUser) === -1) return false;
                if (status && item.reservationStatus !== status) return false;
                return true;
            });

            $('#dg-reservation-list').datagrid('loadData', filteredData);
        }

        // 新增预约
        function addReservation() {
            $('#reservationEditWindow').window({
                title: '新增预约'
            });
            
            var href = "eventReservationEdit.html";
            $('#reservationEditIframe').attr('src', href);
            $('#reservationEditWindow').window('open');
        }

        // 查看预约详情
        function viewReservation() {
            var row = $('#dg-reservation-list').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要查看的记录', 'warning');
                return;
            }
            
            // 构建详情HTML
            var detailHtml = buildDetailHtml(row);
            $('#reservationDetail').html(detailHtml);
            $('#reservationDetailWindow').window('open');
        }

        // 构建详情HTML
        function buildDetailHtml(row) {
            return '<div style="font-size: 14px; line-height: 1.8;">' +
                '<h3 style="margin-bottom: 20px; color: #333;">预约详情</h3>' +
                '<table style="width: 100%; border-collapse: collapse;">' +
                // 基本信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">基本信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="width: 120px; font-weight: bold;">预约编号：</td><td style="width: 250px;">' + row.reservationNo + '</td>' +
                    '<td style="width: 120px; font-weight: bold;">事件ID：</td><td>' + (row.eventId || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">预约时间：</td><td>' + row.reservationTime + '</td>' +
                    '<td style="font-weight: bold;">状态：</td><td>' + formatStatus(row.statusName, row) + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">事件类型：</td><td>' + formatEventType(row.eventTypeName, row) + '</td>' +
                    '<td style="font-weight: bold;">事件来源：</td><td>' + row.eventSrcCodeName + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">预约类型：</td><td>' + row.callTypeName + '</td>' +
                    '<td style="font-weight: bold;">呼叫区域：</td><td>' + row.callRegionName + '</td>' +
                '</tr>' +
                // 地址信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">地址信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">预约地址：</td><td colspan="3">' + row.address + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">等车地址：</td><td colspan="3">' + (row.addressWait || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">经度：</td><td>' + (row.lng || '-') + '</td>' +
                    '<td style="font-weight: bold;">纬度：</td><td>' + (row.lat || '-') + '</td>' +
                '</tr>' +
                // 患者信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">患者信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">患者姓名：</td><td>' + (row.patientName || '-') + '</td>' +
                    '<td style="font-weight: bold;">性别：</td><td>' + (row.patientGenderName || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">年龄：</td><td>' + (row.patientAge || '-') + '</td>' +
                    '<td style="font-weight: bold;">人数：</td><td>' + (row.patientAmount || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">籍贯：</td><td>' + (row.patientCountry || '-') + '</td>' +
                    '<td style="font-weight: bold;">民族：</td><td>' + (row.patientRace || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">是否外籍：</td><td>' + (row.patientIsForeign === '1' ? '是' : '否') + '</td>' +
                    '<td style="font-weight: bold;">病情：</td><td>' + (row.patientCondition || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">病状：</td><td colspan="3">' + (row.patientDiagnosis || '-') + '</td>' +
                '</tr>' +
                // 联系信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">联系信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">联系电话：</td><td>' + row.contact + '</td>' +
                    '<td style="font-weight: bold;">联系人：</td><td>' + (row.contacter || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">预约电话：</td><td colspan="3">' + (row.callIn || '-') + '</td>' +
                '</tr>' +
                // 其他信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">其他信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">主诉：</td><td colspan="3">' + (row.majorCall || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">事故等级：</td><td>' + (row.accidentGrade || '-') + '</td>' +
                    '<td style="font-weight: bold;">需要担架：</td><td>' + (row.isNeedStretcherName || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">特殊要求：</td><td colspan="3">' + (row.specialRequirement || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">备注：</td><td colspan="3">' + (row.centerRemark || '-') + '</td>' +
                '</tr>' +
                // 操作信息
                '<tr><td colspan="4" style="padding: 10px 0;"><b style="color: #1890ff;">操作信息</b></td></tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">录入人：</td><td>' + row.createUser + '</td>' +
                    '<td style="font-weight: bold;">创建时间：</td><td>' + row.createTime + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">转事件时间：</td><td>' + (row.toEventTime || '-') + '</td>' +
                    '<td style="font-weight: bold;">转事件人：</td><td>' + (row.toEventSeatUser || '-') + '</td>' +
                '</tr>' +
                '<tr style="height: 40px;">' +
                    '<td style="font-weight: bold;">取消原因：</td><td colspan="3">' + (row.cancelReason || '-') + '</td>' +
                '</tr>' +
                '</table></div>';
        }

        // 编辑预约
        function editReservation() {
            var row = $('#dg-reservation-list').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要编辑的记录', 'warning');
                return;
            }
            
            if (row.reservationStatus !== '0' && row.reservationStatus !== '1') {
                $.messager.alert('提示', '只能编辑待确认或已预约状态的记录', 'warning');
                return;
            }
            
            $('#reservationEditWindow').window({
                title: '编辑预约'
            });
            
            var href = "eventReservationEdit.html?id=" + row.id;
            $('#reservationEditIframe').attr('src', href);
            $('#reservationEditWindow').window('open');
        }

        // 转为事件
        function convertToEvent() {
            var row = $('#dg-reservation-list').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要转换的预约记录', 'warning');
                return;
            }
            
            if (row.reservationStatus !== '0' && row.reservationStatus !== '1') {
                $.messager.alert('提示', '只能转换待确认或已预约状态的记录', 'warning');
                return;
            }
            
            $.messager.confirm('确认', '确定要将该预约转换为事件吗？', function(r) {
                if (r) {
                    // 模拟转换操作
                    $.messager.alert('成功', '预约已成功转换为事件', 'info');
                    // 更新状态
                    row.reservationStatus = '2';
                    row.statusName = '已转事件';
                    row.eventId = 'SJ' + new Date().formatString('yyyyMMddhhmmss');
                    $('#dg-reservation-list').datagrid('updateRow', {
                        index: $('#dg-reservation-list').datagrid('getRowIndex', row),
                        row: row
                    });
                    updateButtonStates(row);
                }
            });
        }

        // 取消预约
        function cancelReservation() {
            var row = $('#dg-reservation-list').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要取消的预约记录', 'warning');
                return;
            }
            
            if (row.reservationStatus !== '0' && row.reservationStatus !== '1') {
                $.messager.alert('提示', '只能取消待确认或已预约状态的记录', 'warning');
                return;
            }
            
            $.messager.prompt('取消原因', '请输入取消原因：', function(r){
                if (r){
                    // 模拟取消操作
                    $.messager.alert('成功', '预约已取消', 'info');
                    // 更新状态
                    row.reservationStatus = '3';
                    row.statusName = '已取消';
                    row.cancelReason = r; // 保存取消原因
                    $('#dg-reservation-list').datagrid('updateRow', {
                        index: $('#dg-reservation-list').datagrid('getRowIndex', row),
                        row: row
                    });
                    updateButtonStates(row);
                }
            });
        }

        // 刷新预约列表（供子页面调用）
        function refreshReservationList() {
            searchReservations();
        }
    </script>
</body>
</html> 