<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 黄灵
* Created: 2022/4/19
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>公告详情界面</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
      #mainPanel_McWinformMVC{
        height: calc(100% - 1.5em - 10px);
      }
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <div style="margin: 20px">
            <h2 id="noticeTitle" style="margin-bottom: 10px; font-size: 24px; color: #333; text-align: center; width: 100%;"></h2>
            <div style="color: #666; font-size: 14px; margin-bottom: 20px;">
                发布人：<span id="createUser"></span>&nbsp;&nbsp;|&nbsp;&nbsp;
                发布时间：<span id="createTime"></span>
            </div>
            <hr style="border: 1px solid #eee; margin: 20px 0;">
            <div id="noticeContent" style="line-height: 1.8; font-size: 20px; color: #333;">
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _user = null; //用户数据
        var id = null
        var noticeUserId = null
        try {
            _user = evalJson(window.parent.gProxy.getUserInfo());
        } catch (e) {
            _user = evalJson(window.parent.bsProxy.getUserInfo());
        }
        let urlParams = new Map();
        $(document).ready(function () {
            let params = window.location.search.split("?")[1].split("&");
            params.forEach(e => {
               let keyValue = e.split("=");
               if(keyValue[1]){
                   urlParams.set(keyValue[0], keyValue[1]);
               }
            });
            id = urlParams.get("id");
            noticeUserId = urlParams.get("noticeUserId");
            getNotice()
        });
        //刷新车辆信息并显示在车辆监控列表中，点击车辆监控列表的刷新界面
        function getNotice() {
            getNoticeById({id: id}, function (data) {
                $('#noticeTitle').text(data.noticeTitle)
                $('#createUser').text(data.createUser)
                $('#createTime').text(data.createTime)
                $('#noticeContent').html(data.noticeContent)
                if(noticeUserId){
                    receiveNotice({noticeUserId: noticeUserId}, function (data) {
                    }, function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '异常信息：' + errMsg);
                    });
                }
            }, function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        };
    </script>
</body>
</html>