<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/7/12
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <!--<script type="text/javascript" src="http://api.map.baidu.com/getscript?type=webgl&v=1.0&ak=4E2yR7vG9tb8KdjFQ6RRpt80ZdE4p98g"></script>-->
    <script type="text/javascript" src="script/viewcore.js"></script>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false" style="height: 100%;">
        <div id="sec-leftPanel" style="width: 18.75%; height: Calc(100% - 10px); display: inline-block; float: left;">
            <div id="camera1" style="height: 33%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd1" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
            <div id="camera2" style="height: 33%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd2" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
            <div id="camera3" style="height: 34%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd3" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
        </div>
        <div id="sec-mapPanel" style="width: Calc(62.5% - 10px); height:  Calc(100% - 10px); display: inline-block;">
            <div id="the-map" style="width: 100%; height: 100%; overflow: hidden; margin: 0;"></div>
        </div>
        <div id="sec-rightPanel" style="width: 18.75%; height:  Calc(100% - 10px); display: inline-block; float: right;">
            <div id="camera4" style="height: 33%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd4" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
            <div id="camera5" style="height: 33%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd5" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
            <div id="camera6" style="height: 34%; background: black;">
                <span style="color: lime; position: absolute; width: 18.75%;"></span>
                <div id="playWnd6" class="playWnd" style="width: 100%; height: 100%;"></div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/bigScr.js"></script>
    <script type="text/javascript">
        _baseUrl = gProxy.getBaseUrl();
    </script>
</body>
</html>