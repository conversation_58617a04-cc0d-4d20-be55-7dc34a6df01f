<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕电话簿界面文件
* 本窗体专门用于电话簿功能，从second.html中提取
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* Modified: 2024/12/30 - 提取电话簿功能到独立文件
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台电话簿</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            Height: 28px;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        a {
            text-decoration: none !important;
        }

        tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        tr:hover a {
                color: #ffffff !important;
            }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;

        }
        .input-border>.textbox{
            margin: 10px 0;
        }
        .textbox .textbox-prompt {
            font-size: 16px !important;
        }
        .panel-title{
            font-size: 15px;
        }

        #cont-group {
          padding: 18px;
        }

        #cont-group .group-item {
          position: relative;
        }

        #cont-group .group-item input {
          position: absolute;
          opacity: 0;
        }

        #cont-group .group-item input:checked + label {
          background: #0088CC33;
          color: #0088CC;
        }

        #cont-group .group-item label {
          cursor: pointer;
          padding: 1px 6px;
          margin: 0;
          border-radius: 3px;
        }

        #cont-group .group-item label:hover {
          background: #099DFC0D;
        }

        .edc-listbox .title {
          font-size: 14px;
          font-weight: 400;
          position: relative;
          box-sizing: border-box;
          padding: 12px 12px 12px calc(16px + var(--side-pd));
          line-height: 1.5;
          background: rgb(245,245,245);
          display: flex;
          justify-content: space-between;
        }

        .edc-listbox .title::before {
          content: '';
          position: absolute;
          top: 50%;
          left: var(--side-pd);
          transform: translateY(-50%);
          height: 15px;
          width: 5px;
          background: rgb(17,179,248);
        }

        .title .title-toolbar-list {
          display: flex;
          align-items: center;
        }

        .title .title-toolbar {
          display: flex;
          align-items: center;
        }

        .title .title-toolbar.hide {
          display: none;
        }
    </style>
</head>
<body>
    <div id="mainPanel_PhoneBooks" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">电话簿<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="width: 30em; height: 100%; display: inline-block;float: left;">
                <ul id="cont-group" class="edc-listbox input-border" title="单位" style="width: 100%; height: calc(100% - 50px); font-size: 1.2em;">
                    <input id="search-hospital" prompt="输入医院名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;height:35px;font-size: 13px;" />
                </ul>
            </div>
            <div style="display: inline-block; width: Calc(100% - 31em); height: 100%;float: left;margin-left: 1em;">
                <div class="input-border">
                    <input id="search-name" prompt="输入对象名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:300px;height:35px;font-size: 13px;" />
                </div>
                <table cellpadding="0" cellspacing="0" class="edc-table" style="width: 100%;height: calc(100% - 100px);font-size: 1.2em;">
                    <thead>
                        <tr style="width: 100% !important">
                            <th style="width: 20%;">对象名称</th>
                            <th style="width: 80%;" align="left"> &ensp;联系电话(点击号码拨号)</th>
                        </tr>
                    </thead>
                    <tbody id="cont-list-body"></tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.location.protocol + '//' + window.location.host;
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            try{
                _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
            }catch(ex){
                _pSeat = evalJson(window.parent.parent.bsProxy.getSeatInfo());
            }
        }
        
        /** 电话簿离线列表 */
        var _contactList = null;
        /** 当前选中的菜单对象 */
        var menuObj = null;
        
        $(document).ready(function () {
            // 电话簿页面渲染 医院
            getAllContacts(function (data) {
                _contactList = data;
                try {
                    // 兼容C#和B/S模式的更新联系人信息
                    if (typeof gProxy !== "undefined" && gProxy != null) {
                        gProxy.updateContacts(JSON.stringify(data));
                    } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                        bsProxy.updateContacts(JSON.stringify(data));
                    }
                } catch (e) {
                        // 更新联系人信息时出错
                }
                //渲染左侧医院菜单
                renderMenu(_contactList)
                $('#search-hospital').textbox('textbox').bind('input',function(e){
                    let list = JSON.parse(JSON.stringify(_contactList))
                    let filterlist = list.filter(r=>r.groupName.indexOf(e.target.value) !== -1)
                    if(!filterlist || !filterlist[0]){
                        filterlist = list.filter(r=>upperfirst(r.groupName,{upperfirst: true}).indexOf(e.target.value) !== -1)
                    }
                    //根据查询过滤掉的数据渲染左侧医院菜单
                    renderMenu(filterlist)
                })
                $('#search-name').textbox('textbox').bind('input',function(e){
                    if(menuObj != null && Array.isArray(menuObj.phoneBooks)){
                        let list = JSON.parse(JSON.stringify(menuObj.phoneBooks))
                        //过滤数据
                        let filterlist = list.filter(r=>r.contactor.indexOf(e.target.value) !== -1)
                        if(!filterlist || !filterlist[0]){
                            //字母过滤
                            filterlist = list.filter(r=>upperfirst(r.contactor,{upperfirst: true}).indexOf(e.target.value) !== -1)
                        }
                        //右边列表数据渲染
                        renderObj(filterlist)
                    }
                })
            }, function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '获取电话簿数据失败，异常信息：' + errMsg);
            });
        });
        
        /** 电话簿页面渲染左侧医院菜单 */
        function renderMenu(list){
            //删除指定子元素
            $('#cont-group').find('li').remove()
            for (var i = 0; i < list.length; i++) {
                var group = list[i];
                $('#cont-group').append(`
                  <li class="group-item" data="{0}">
                    <input type="radio" name="cont-group" id="{1}" value="{1}"/>
                    <label for="{1}">{1}</label>
                  </li>`
                  .formatStr(
                    group.id,
                    group.groupName
                  ));
            }
            const inputEls = $('#cont-group input[type="radio"]');
            inputEls.on('change', e => {
                var groupId = $(e.target).attr('value');
                menuObj = list.first(function (f) { return f.groupName == groupId; });
                //右边列表数据渲染
                if(menuObj != null){
                    renderObj(menuObj.phoneBooks || [])
                }
            })
        }
        
        /** 右边列表数据渲染 */
        function renderObj(list){
            $('#cont-list-body').empty();
            for (var j = 0; j < list.length; j++) {
                $('#cont-list-body').append('<tr><td style="width: 20%;">{0}</td>\
                      <td style="width: 80%;"  align="left"> &ensp;<a href="javascript:callingPhone(\'{1}\');">{1}</a></td></tr>'
                    .formatStr(
                        list[j].contactor,
                        list[j].number
                    ));
            }
        }

        /** 电话簿拨打电话功能 */
        function callingPhone(phoneNumber) {
            if (!phoneNumber || phoneNumber.trim() === '') {
                $.messager.alert('提示', '电话号码不能为空！', 'warning');
                return;
            }

            $.messager.confirm('确认拨打',
                `确定要拨打电话 ${phoneNumber} 吗？`,
                function(r) {
                    if (r) {
                        if (!_pSeat) {
                            $.messager.alert('提示', '未获取到座席信息，无法拨打电话！', 'error');
                            return;
                        }
                        let dto = {
                            ext: _pSeat.callOut,
                            outerPhone: phoneNumber
                        }
                        callPhone2(dto,
                            function (res) {
                                $.messager.alert('提示', '正在拨打电话，请稍候...', 'info');
                            },
                            function (e, url, errMsg) {
                                $.messager.alert('错误', '拨打电话失败：' + errMsg, 'error');
                            }
                        );
                    }
                }
            );
        }
        
        /**
         * 字符串首字母转换函数
         * @param {string} str - 要转换的字符串
         * @param {object} options - 选项
         * @returns {string} 转换后的字符串
         */
        function upperfirst(str, options) {
            if (!str) return '';
            options = options || {};
            if (options.upperfirst) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }
            return str;
        }
    </script>
</body>
</html>
