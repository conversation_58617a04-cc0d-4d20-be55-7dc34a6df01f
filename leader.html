<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="script/themes/material-teal/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <div id="mainFrame" style="color: #6E6E6E;">
            <div id="header" style="">
                <div class="logo" style=""></div>
                <div class="title" style="background: url(style/img/leaderboard.png) no-repeat;"></div>
                <div class="account">
                    <span id="dispatcher-name"></span>&nbsp;(<span id="dispatcher-code"></span>)
                    <a href="javascript:doLogout();" class="easyui-tooltip logout" title="注销登录并重新登录">换班</a>
                </div>
                <div class="timezone clear-fix">
                    <span style="float: left;">指挥中心</span>
                    <span id="cur-date-time" style="float: left;"></span>
                </div>
            </div>
            <div id="leftFrame" style="width: 40%; height: 100%; padding: 5px; display: inline-block; float: left;">
                <div class="group-box" style="width: 100%; height: 5em; ">
                    <div class="group-box-title"><i class="fa fa-hospital-o"></i> 选择分站</div>
                    <select id="station-list" onchange="onStationSel()" style="margin: 15px 0 0 15px; width: Calc(100% - 30px); font-size: 1.6em; padding: 0 0 5px 0; border-radius: 3px;">
                        <option value="all">全部分站</option>
                    </select>
                </div>
                <div class="group-box" style="width: 100%; height: Calc(100% - 7em - 150px);">
                    <div class="group-box-title"><i class="fa fa-camera-retro"></i> 资产清单<span style="font-size: 9px; margin-left: 5px; font-weight: normal;">(右键投放到大屏幕)</span></div>
                    <div style="height: Calc(100% - 25px - 1.3em); width: 100%; padding: 10px; position: relative;">
                        <table id="car-status" class="edc-table" cellpadding="0" cellspacing="0">
                            <thead>
                                <tr>
                                    <th style="width: 30%;">资产名称</th>
                                    <th style="width: 30%;">所属分站</th>
                                    <th style="width: 30%;">所属车辆</th>
                                    <th style="width: 10%;">车辆状态</th>
                                </tr>
                            </thead>
                            <tbody id="car-status-body"></tbody>
                        </table>
                        <div class="waitBox" id="car-status-waitbox" style="display: none;"><div><i class="fa fa-circle-o-notch fa-spin fa-3x"></i></div></div>
                    </div>
                </div>
            </div>
            <div id="rightFrame" style="width: 59%; height: 100%; padding: 5px; display: inline-block; float: right;">
                <div class="group-box" style="width: Calc(100% - 10px); height: Calc(100% - 150px); position: relative;">
                    <div class="group-box-title"><i class="fa fa-map"></i> 地图视图</div>
                    <div style="display: inline-block; width: 100%; height: 95%;">
                        <div id="map-search" style="width: 80%; height: 3em; position: absolute; left: 70px; top: 70px; z-index: 2;">
                            <input type="text" id="map-search-box" style="width: 60%; height: 100%; font-size: 2em;" />
                            <a href="javascript:manualSearch();" style="display: inline-block; position: absolute; top: 0; right: 40%;">
                                <i class="fa fa-search fa-3x"></i>
                            </a>
                            <div id="searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                        </div>
                        <div id="the-map" style="width: 100%; height: 100%; overflow: hidden; margin: 0;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!--覆盖整个页面的登录框遮罩-->
        <div-- id="loginPanel" style="display: block;background-image: url(style/img/station_background.png); background-size: 100% 100%;">
            <div class="titlebar dragmove" style="visibility: hidden !important;">
                <span></span>
                <ul class="sys-commands">
                    <li n-ui-command="minimize">
                        <i class="title-button min-btn"></i>
                    </li>
                    <!--<li n-ui-command="maximize">
                        <i class="title-button max-btn"></i>
                    </li>-->
                    <li n-ui-command="close">
                        <i class="title-button close-btn"></i>
                    </li>
                </ul>
            </div>
            <div id="login-window" style="width: 600px; height: 350px; left: Calc(50% - 300px); top: Calc(50% - 150px); position: absolute; background: #2fe9f915; border: #FFFFFF50 solid 1px; border-radius: 8px; padding: 0; box-shadow: #09175a33 0 12px 33px 8px; border">
                <div class="clear-fix" style="width: Calc(100% - 18px); height: Calc(100% - 18px); margin: 9px; background: white; border-radius: 6px; padding: 0;">
                    <div style="background-image: url(style/img/logo.png); background-repeat: no-repeat; background-size: 96px 96px; width: 96px; height: 96px; position: absolute; top: Calc(50% - 395px); left: Calc(50% - 48px);"></div>
                    <div style="background-image: url(style/img/leaderboard_title.png);background-repeat: no-repeat; width: 521px; height: 44px; position: absolute; top: Calc(50% - 275px); left: Calc(50% - 260px);"></div>
                    <!--<div id="window-title-bar" style="background:#A64B00; border-radius: 6px 6px 0 0; color: #33CCCC; text-align: center;">调度员登录</div>-->
                    <form action="" onsubmit="return doLogin()" id="loginForm" style="width: 100%; height: 100%; padding: 0; border-radius: 6px; background: white; color: #606266;">
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 85px 5px 19px 0;">账号/工号:</div>
                        <div style="display: inline-block; width: 50%;"><input id="login-un" type="text" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required value="" /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 30px 5px 19px 0;">密码:</div>
                        <div style="display: inline-block; width: 50%;"><input id="login-pwd" type="password" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required value="" /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 72px 5px 0 0;"></div>
                        <div style="display: inline-block; width: 50%; text-align: right;"><a href="javascript:showChangePwd();" style="float: left; margin-top: 0.5em;">修改密码</a><input type="submit" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em;  float: right;" value="登录"></div>
                    </form>
                    <form action="" onsubmit="return doChangePwd()" id="changePwdForm" style="display: none; width: 100%; height: 100%; padding: 0; border-radius: 6px; background: white; color: #606266;">
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 54px 5px 19px 0;">账号/工号:</div>
                        <div style="display: inline-block; width: 50%;"><input id="cp-un" type="text" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">旧密码:</div>
                        <div style="display: inline-block; width: 50%;"><input id="cp-pwd" type="password" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">新密码:</div>
                        <div style="display: inline-block; width: 50%;"><input id="cp-newPwd1" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 38px 0;">确认密码:</div>
                        <div style="display: inline-block; width: 50%;"><input id="cp-newPwd2" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 0 0;"></div>
                        <div style="display: inline-block; width: 50%;"><a href="javascript:showLogin();" style="float: left; margin-top: 0.5em;">返回登录</a><input type="submit" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em;  float: right;" value="确认修改"></div>
                    </form>
                </div>
            </div>
    </div>

    <!--菜单-->
    <div id="menu-mobile" class="easyui-menu" style="width: 120px;" data-options="onClick:menuHandler">
        <div data-options="name:'promote1'">投射到一号位</div>
        <div data-options="name:'promote2'">投射到二号位</div>
        <div data-options="name:'promote3'">投射到三号位</div>
        <div data-options="name:'promote4'">投射到四号位</div>
        <div data-options="name:'promote5'">投射到五号位</div>
        <div data-options="name:'promote6'">投射到六号位</div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/leader.js"></script>
    <script type="text/javascript">
        _baseUrl = gProxy.getBaseUrl();
        querySysConf('mapak',
            function (res) {
                var ohead = document.getElementsByTagName("head")[0];
                var ele = document.createElement('script');
                ele.src = "http://api.map.baidu.com/getscript?v=2.0&ak=" + res;
                ohead.appendChild(ele);
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '获取mapak配置失败，异常信息：' + errMsg);
            });
    </script>
</body>
</html>