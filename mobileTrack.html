<!DOCTYPE html>
<!-- 此类界面请加上这个Class -->
<html class="tabViewHtml">

<head>
    <title>车辆信息</title>
    <!-- 公共头部 -->
    <script type="text/javascript" src="../../components/renderPublickHead/renderPublickHeadEdc.js?v=-7668257"></script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=7af39c825eb7720aede3a5caef928652&plugin=AMap.DistrictSearch,AMap.Scale,Map3D,AMap.Geocoder,AMap.Driving,AMap.DrivingPolicy,AMap,AMap.ToolBar"></script>
    <!-- UI组件库 1.0 -->
    <script src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
    <style type="text/css">
        html,body{
            height: 100%;
            margin: 0 auto;
            padding: 0;
        }
        .mobileTrack{
            height: 100%;
            width: 100%;
            position: relative;
        }
        #map-track{
            height: 100%;
            width: 100%;
        }
        .map-query{
            position: absolute;
            top: 2%;
            left:8%;
            box-shadow: rgb(0 0 0 / 10%) 0px 2px 12px 0px;
            background-color: rgb(255, 255, 255);
            padding: 10px;
            display: flex;
            align-items: baseline;
        }
        .map-query>.query-col{
            display: flex;
            align-items: center;
        }
        .map-query>.query-col>input{
            width: 150px;
            height: 30px;
            padding: 5px;
        }
        .map-button{
            position: absolute;
            top: 2%;
            right: 2%;
            box-shadow: rgb(0 0 0 / 10%) 0px 2px 12px 0px;
            background-color: rgb(255, 255, 255);
            padding: 10px;
        }
        .map-button>.button{
            width: 20%;
        }
        .flex-msg{
            display: flex;
            justify-content: space-between;
        }
        .progress-bar {
            position: relative;
            height: 4px;
            width: 230px;
            background: #ebeef5;
            border-radius: 10px;
            display: inline-block;
            top: -3px;
        }

        .progress-bar .pro-bar {
            position: absolute;
            left: 0;
            height: 4px;
            width: 10px;
            background: #409eff;
        }

        .progress-bar .min-num {
            position: absolute;
            left: -20px;
            top: -5px;
        }

        .progress-bar .max-num {
            position: absolute;
            right: -40px;
            top: -5px;
        }

        .progress-bar .block {
            position: absolute;
            height: 12px;
            width: 12px;
            background: #ccc;
            top: -4px;
            border-radius: 50%;
        }

        .progress-bar .block div {
            position: absolute;
            display: none;
            left: -100%;
            top: -200%;
            text-align: center;
            border-radius: 20px;
            padding: 2px 5px;
            background: #409eff;
            font-size: 10%;
            color: #fff;
        }
        .block:hover .text{
            display: block;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="mobileTrack">
        <div id="map-track"></div>
        <div class="map-query">
            <div class="query-col" style="display:inline-block;">
                <span>起止时间：</span>
                <input type="text" id="deviceStartTime" idName="deviceStartTime" placeholder="起" class="layui-input">
                <div style="margin-top: 10px;">
                    <label>
                        <input type="radio" name="deviceId" value="" id="gpsTerminal"> GPS设备</input>
                    </label>
                    <label>
                        <input type="radio" name="deviceId" value="" id="reserveGpsTerminal"> 备用GPS</input>
                    </label>
                </div>
            </div>
            <div class="query-col">
                <span style="margin-left: 5px;">结束时间：</span>
                <input type="text" id="deviceEndTime" idName="deviceEndTime" placeholder="止" class="layui-input">
            </div>
            <div class="query-col">
                <button id="queryListBtn" class="layui-btn" style="height: 30px;line-height: 30px;">搜索</button>
            </div>
        </div>
        <div class="map-button"></div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/mobileTrack.js"></script>
</body>
</html>