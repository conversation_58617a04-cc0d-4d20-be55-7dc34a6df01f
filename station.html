<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120分站端</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        a {
            color: #007bff; 
            text-decoration: none; 
        }
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }

        .textbox-label {
            display: inline-block;
            width: auto;
            margin-right: 5px;
        }

        .datagrid-cell {
            font-size: 15px;
            font-weight: bold;
        }

        .edc-table {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            border: solid #B3D2E1 1px;
            text-align: center;
            color: #6D6D6E;
            font-size: 14px;
            table-layout: fixed;
            font-weight: bold;
        }

        .datagrid-header-row, .datagrid-row {
            height: 40px;
        }

        .datagrid-cell-rownumber {
            font-size: 15px;
        }

        #print-prev {
            height: 850px !important;
        }

        table td {
            font-size: 15px !important;
            height: 40px !important;
        }

        .datagrid-header .datagrid-cell span {
            font-size: 15px !important;
            color: #013F61 !important;
            font-weight: bold !important;
        }

        #theNumberOfTransfer .panel {
            width: 100% !important;
            height: calc(100% - 40px) !important;
        }

        #theNumberOfTransfer .panel-body {
            width: 100% !important;
            height: 100% !important;
        }

        .none {
            /*禁止点击 */
            pointer-events: none;
        }

        .xiaoxi{
          width: 20px;
          height: 20px;
          position: absolute;
          right: 150px;
          top: 27px;
          cursor: pointer;
        }
        .xiaoxi img{
          width: 20px;
          height: 20px;
          margin-bottom: 13px;
        }
        .xiaoxi_badge{
          position: absolute;
          top: 1px;
          right: -9px;
          display: inline-block;
          width: 9px;
          height: 9px;
          font-size: 10px;
          line-height: 9px;
          text-align: center;
          color: #FFF;
          background-color: #EA0031;
          border-radius: 50%;
        }

        .container {
            display: flex;
        }

        .container span {
           margin-right: 0px; /* 设置每个span右边的间隔 */
        }

       .container span:last-child {
           margin-right: 0; /* 去除最后一个span的右边间隔 */
       }
       #chooseCarNoId .layui-tree-entry,
        #chooseStationId .layui-tree-entry {
            text-align: left;
        }

        /* 事件详情窗口样式统一 */
        #eventDetailRegion input[type="text"],
        #eventDetailRegion input[type="number"],
        #eventDetailRegion input[type="password"],
        #eventDetailRegion select,
        #eventDetailRegion textarea {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 14px;
            background-color: #fff;
            transition: border-color 0.3s ease;
        }

        #eventDetailRegion input[type="text"]:focus,
        #eventDetailRegion input[type="number"]:focus,
        #eventDetailRegion input[type="password"]:focus,
        #eventDetailRegion select:focus,
        #eventDetailRegion textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        #eventDetailRegion input[readonly],
        #eventDetailRegion select[disabled],
        #eventDetailRegion textarea[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        #eventDetailRegion input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0 4px;
            vertical-align: middle;
        }

        /* 事件详情窗口的选择框统一样式 */
        #eventDetailRegion .select-common {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 14px;
            background-color: #fff;
            height: 34px;
        }

        #eventDetailRegion .select-common:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 特殊调整一些特定元素 */
        #eventDetailRegion #det-120remark {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            font-size: 14px !important;
        }
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <!--拒绝派车原因填写-->
        <div id="refuseProcessReasonDialog" class="easyui-dialog" title="拒绝派车，请选择原因" style="width:300px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#refuseProcessReason-buttons">
            <input type="hidden" value="" id="refuse-mobile-process-id" /><!--拒绝派车的任务ID-->
            <form id="refuseProcessReasonForm"></form>
        </div>
        <div id="refuseProcessReason-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:updateRefuseProcessReasonExecBtn($('#change-mobile-process-id').val())" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: cancleRefuseProcessReasonExecBtn();" style="width:80px;">取消</a>
        </div>

        <!--设为待命接口-->
        <div id="updateAwaitMobileDialog" class="easyui-dialog" title="救护车任务设为待命" style="width:350px;height:340px;font-size:14px;padding-left:10px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#uamDlg-buttons">
            <input type="hidden" value="" id="updateAwaitMobileProcessId" /><!--车辆当前任务id-->
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">派车时间：</span>
                <input id="send-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">出车时间：</span>
                <input id="goto-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;" id="cancel-car-times-div">
                <span style="margin-left: 1em;">取消时间：</span>
                <input id="cancel-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">抵达时间：</span>
                <input id="arrive-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">离开现场：</span>
                <input id="leave-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">回院时间：</span>
                <input id="back-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
        </div>
        <div id="uamDlg-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:updateAwaitMobileBtn()" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeUpdateAwaitMobileDialog();" style="width:80px;">取消</a>
        </div>

        <div id="mainFrame" style="color: #6E6E6E;">
            <div id="header" style="">
                <div class="logo" style=""></div>
                <div class="title" style="background: url(style/img/edcstation_title.png);"></div>
                <div class="account">
                    <span id="dispatcher-name"></span>&nbsp;(
                    <span id="dispatcher-code"></span>)
                    <!--<a href="javascript:doLogout();" class="easyui-tooltip logout" title="注销登录并重新登录">换班</a>-->
                </div>
                <div class="timezone clear-fix">
                    <span id="station-name" style="float: left;"></span>
                    <span id="cur-date-time" style="float: left;"></span>
                    <div class="xiaoxi" onclick="noticeDetailStation()">
                        <img src="style/img/xiaoxi.png" alt="">
                        <span class="xiaoxi_badge" id="xiaoxi_badge"></span>
                    </div>
                    <span id="version-number" style="float: right;"></span>
                </div>
            </div>
            <div id="leftFrame" style="width: 30%; height: 100%; padding: 5px; display: inline-block; float: left;">
                <div class="group-box" style="width: 100%; height: 10em; ">
                    <div class="group-box-title">
                        <i class="fa fa-bell-o"></i> 系统状态
                    </div>
                    <div style="font-size: 4em; " id="alert-main" ondblclick="pauseAlert()">
                        <div id="alert-on" style="text-align:center;color: red; cursor: pointer; width: Calc(5em + 15px); height: 1.5em; transform: translate(-50%, 10%);" class="anim-flash easyui-tooltip vertical-center" title="双击关闭警报">
                            <i class="fa fa-bell"></i>
                            <span style="margin-left: 15px;">新调度</span>
                        </div>
                        <div id="alert-pause" style="text-align:center;color: #ff6000; cursor: pointer;  height: 1.5em; transform: translate(-50%, 10%);" class="anim-flash easyui-tooltip vertical-center" title="">
                            <i class="fa fa-bell"></i><span style="margin-left: 15px;" id="alert-pause-text">暂停响铃30秒</span>
                        </div>
                        <div id="alert-off" style="color: green; width: Calc(4em + 15px); height: 1.5em;  transform: translate(-50%, 10%);" class="vertical-center">
                            <i class="fa fa-bell"></i>
                            <span style="margin-left: 15px;">空闲</span>
                        </div>
                    </div>
                </div>
                <div class="group-box" style="width: 100%; height: Calc(100% - 12em - 100px);">
                    <div class="group-box-title">
                        <i class="fa fa-ambulance"></i> 车辆信息
                        <span style="font-size: 9px; margin-left: 5px; font-weight: normal;">(双击接收被指派的事件)</span>
                    </div>
                    <div style="height: Calc(100% - 50px - 1.3em); width: 100%; padding: 10px; position: relative;">
                        <table id="car-status" class="edc-table" cellpadding="0" cellspacing="0">
                            <thead>
                                <tr style="width: 100%;">
                                    <th style="width: 10%">车况</th>
                                    <th style="width: 10%;">别名</th>
                                    <th style="width: 16%;">车牌号</th>
                                    <th style="width: 23%;">电话</th>
                                    <th style="width: 15%;">状态</th>
                                    <th style="width: 26%;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="car-status-body"></tbody>
                        </table>
                    </div>
                    <a style="margin: 0 0 0 10px; padding-bottom: 10px;" href="javascript:openWebCarDetailBtn();"><i class="fa fa-car" aria-hidden="true"></i> 车辆管理</a>
                    <a style="margin: 0 0 0 10px; padding-bottom: 10px;" href="javascript:openWebHistoryDispatchBtn();"><i class="fa fa-car" aria-hidden="true"></i> 历史调度</a>
                    <a style="margin: 0 0 0 10px; padding-bottom: 10px;" href="javascript:openWebDispatchBtn();"><i class="fa fa-map-marker" aria-hidden="true"></i> 调度地图</a>
                    <a style="margin: 0 0 0 10px; padding-bottom: 10px;" href="javascript:openVideoBtn();"><i class="fa fa-video-camera" aria-hidden="true"></i> 视频监控</a>
                    <a style="margin: 0 0 0 10px; padding-bottom: 10px;" href="javascript:openTheNumberOfTransfer();"><i class="fa fa-flask" aria-hidden="true"></i> 统计转院人数</a>
                </div>
            </div>
            <div id="rightFrame" style="width: 70%; height: Calc(100% - 0px); padding: 5px; display: inline-block; float: left;">
                <div class="group-box" style="width: Calc(100% - 10px); height: Calc(100% - 118px);">
                    <div id="main-tabs" class="easyui-tabs" style="width: 101%; height:100%;margin-top:0px;border: solid #E2E7EA 1px;">
                        <div title="任务列表" style="display: none;">
                        <div style="display: inline-block; height: 60px; padding-top: 1em; float: left;">
                            <div style="height: 50px; ">
                                <div id="eventSchCre" style="display: inline-block;  float: left; padding-left: 10px;font-size: 18px;">
                                    调度时间：
                                    <input id="creTimeFrom" class="easyui-datetimebox" style="width:200px;" />
                                    <span>~</span>
                                    <input id="creTimeEnd" class="easyui-datetimebox" style="width:200px;" />
                                    &nbsp;
                                    <a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;
                                    <a href="javascript:void(0);" class="inToday" id="start-toay">一天内</a>
                                    &ensp; &ensp;
                                    <input type="checkbox" id="autoRefreshList" name="autoRefreshList" value="0" checked="checked" style="zoom:120%;position: relative;top: 2px;" />自动刷新
                                </div>
                                <a class="common-button" style="width: 79px; height: 32px; line-height: 32px; margin: -2px 0 0 15px; font-size: 14px;" href="javascript:searchButton();">
                                    <i class="fa fa-search"></i>&nbsp;搜索
                                </a>
                                <a class="common-button" id="refuseSendCarBtn" style="width: 100px; height: 32px; line-height: 32px; margin: -2px 0 0 15px; font-size: 14px;display: none;" href="#" onclick="javascript:refuseSendCarButton()">拒绝派车</a>
                                <a class="common-button" id="abortRefuseSendCarBtn" style="width: 100px; height: 32px; line-height: 32px; margin: -2px 0 0 15px; font-size: 14px;display: none;" href="#" onclick="javascript:abortRefuseSendCar()">撤销拒绝派车</a>
                            </div>
                        </div>
                        <div style="height: Calc(100% - 140px); position: relative; clear: left;">
                            <table class="easyui-datagrid" id="dg-event-process-pages" style="height: Calc(100% - 100px)"
                                   rowStyler="rowStyler" onClickRow="clickRow" pagination="true" singleSelect="true" rownumbers="true" fit="true" toolbar="#tb">
                                   <thead data-options="frozen:true">
                                    <tr>
                                        <th field="viewProcessDetailFormatter" width="100" checkbox="false" align="center" formatter="viewProcessDetailFormatter"></th>
                                    </tr>
                                </thead>
                                <thead>
                                    <tr>
                                        <th field="eventName" width="350" align="center" formatter="eventNameFormatter">事件名称</th>
                                        <!--<th field="address" width="230" checkbox="false" align="center" formatter="addressFormatter">地址</th>-->
                                        <th field="mobileProcessCreateTime" width="170" checkbox="false" align="center">调度时间</th>
                                        <th field="eventContact" width="125" checkbox="false" align="center">联系电话</th>
                                        <th field="dispatchDoctor" width="80" align="center">出车医生</th>
                                        <th field="dispatchNurse" width="80" align="center">出车护士</th>
                                        <th field="dispatchDriver" width="80" align="center">出车司机</th>
                                        <th field="centerRemark" width="200" align="center" formatter="centerRemarkFormatter">备注</th>
                                        <!--<th field="majorCall" width="200" align="center" formatter="eventNameFormatter">事件名称</th>-->
                                        <th field="toStatusStr" width="70" align="center" formatter="setDgProcessStatusColumnFormatter">任务状态</th>
                                        <th field="carName" width="70" align="center">车辆别名</th>
                                        <th field="plateNum" width="80" align="center">车牌号码</th>
                                        <th field="processId" width="220" checkbox="false" align="center">任务编号</th>
                                        <th field="eventId" width="220" checkbox="false" align="center">事件编号</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计转院人数窗口 -->
        <div id="theNumberOfTransfer" class="easyui-window" title="转院人数" style="width:1100px;height:700px"
             data-options="iconCls:'icon-sum',modal:true,closed:true,closable:true">
            <div style="height: 100%;width:100%">
                <div style="padding: 5px 10px;">
                    <span>起止时间：</span>
                    <input id="startCreateTime" class="easyui-datetimebox" style="width:150px;" />
                    <span>~</span>
                    <input id="endCreateTime" class="easyui-datetimebox" style="width:150px;" />
                    <span>&nbsp;转出医院：</span>
                    <input class="easyui-textbox" id="outHospital" style="height:30px;width:150px" />
                    <a class="common-button" style="width: 79px; height: 30px; line-height: 30px; margin: -2px 0 0 10px; font-size: 14px;" href="javascript:searchInfoButton();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
                <table class="easyui-datagrid" id="theNumberOfTransfer-content" style="width:100%;height:calc(100% - 40px)" singleSelect="true" rownumbers="false" fit="true">
                    <thead data-options="frozen:true">
                        <tr>
                            <th field="outHospital1" width="20%" align="center">转出医院名称</th>
                            <th field="transshipment1" width="13%" align="center">转出医院人数</th>
                            <th field="outHospital2" width="20%" align="center">转出医院名称</th>
                            <th field="transshipment2" width="13%" align="center">转出医院人数</th>
                            <th field="outHospital3" width="20%" align="center">转出医院名称</th>
                            <th field="transshipment3" width="13%" align="center">转出医院人数</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!--修改车辆状况-->
        <div id="updateMobileConditionDialog" class="easyui-dialog" title="修改救护车状况" style="width:400px;height:150px;font-size:16px;padding-left:20px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#dlg-buttons">
            <input type="hidden" value="" id="updateConditionMobileId" /><!--车辆id-->
            <input type="hidden" value="" id="mobileCondition" /><!--车辆状况-->
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('0')">
                <input class="easyui-checkbox" id="conditionZc" name="conditionUpdate" value="0" label="正常:">
            </span>
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('1')">
                <input class="easyui-checkbox" id="conditionWh" name="conditionUpdate" value="1" label="维护:">
            </span>
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('2')">
                <input class="easyui-checkbox" id="conditionZd" name="conditionUpdate" value="2" label="驻场:">
            </span>
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('3')">
                <input class="easyui-checkbox" id="conditionWzb" name="conditionUpdate" value="3" label="未值班:">
            </span>
        </div>
        <div id="dlg-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:openMobileConditionBtn($('#updateConditionMobileId').val(),$('#mobileCondition').val())" style="width:80px;">保存</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeMobileConditionWindowBtn();" style="width:80px;">取消</a>
        </div>

        <!--覆盖整个页面的登录框遮罩-->
        <div id="loginPanel" style="display: block;background-image: url(style/img/station_background.png); background-size: 100% 100%;">
            <div class="titlebar dragmove" style="visibility: hidden !important;">
                <span></span>
                <ul class="sys-commands">
                    <li n-ui-command="minimize">
                        <i class="title-button min-btn"></i>
                    </li>
                    <!--<li n-ui-command="maximize">
                        <i class="title-button max-btn"></i>
                    </li-->
                    <li n-ui-command="close">
                        <i class="title-button close-btn"></i>
                    </li>
                </ul>
            </div>
            <div id="login-window" style="width: 600px; height: 350px; left: Calc(50% - 300px); top: Calc(50% - 150px); position: absolute; background: #2fe9f915; border: #FFFFFF50 solid 1px; border-radius: 8px; padding: 0; box-shadow: #09175a33 0 12px 33px 8px;">
                <div class="clear-fix" style="width: Calc(100% - 18px); height: Calc(100% - 18px); margin: 9px; background: white; border-radius: 6px; padding: 0;">
                    <div style="background-image: url(style/img/logo.png); background-repeat: no-repeat; background-size: 96px 96px; width: 96px; height: 96px; position: absolute; top: Calc(50% - 395px); left: Calc(50% - 48px);"></div>
                    <div ondblclick="showVersionInfo();" style="background-image: url(style/img/edcstation_title_login.png);background-repeat: no-repeat; width: 521px; height: 44px; position: absolute; top: Calc(50% - 275px); left: Calc(50% - 260px);"></div>
                    <form action="" onsubmit="return doLogin()" id="loginForm" style="width: 100%; height: 100%; padding-top: 40px;; border-radius: 6px; background: white; color: #606266;">
<!--                        <div id="station-select-label" style="display: none; width: 30%; text-align: right; margin: 20px 5px 19px 0;">选择分站:</div>-->
<!--                        <div id="station-select-container" style="display: none; width: 50%;">-->
<!--                            <select id="login-station" style="width: 100%; font-size: 1.3em; padding: 5px;" required>-->
<!--                                <option value="">请选择分站</option>-->
<!--                            </select>-->
<!--                        </div>-->
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 20px 5px 19px 0;">账号/工号:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="login-un" type="text" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required value="" />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 30px 5px 19px 0;">密码:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="login-pwd" type="password" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required value="" />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 10px 0px 0px 0px;"></div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="is-save-psw" type="checkbox" value="1" />记住密码
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 72px 5px 0 0;"></div>
                        <div style="display: inline-block; width: 50%; text-align: right;">
                            <a href="javascript:showChangePwd();" style="float: left; margin-top: 0.5em;">修改密码</a>
                            <input type="button" id="loginSubmitBtn" onclick="doLogin()" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em;  float: right;" value="登录">
                        </div>
                    </form>
                    <form action="" onsubmit="return doChangePwd()" id="changePwdForm" style="display: none; width: 100%; height: 100%; padding: 0; border-radius: 6px; background: white; color: #606266;">
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 54px 5px 19px 0;">账号/工号:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="cp-un" type="text" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">旧密码:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="cp-pwd" type="password" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">新密码:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="cp-newPwd1" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 38px 0;">确认密码:</div>
                        <div style="display: inline-block; width: 50%;">
                            <input id="cp-newPwd2" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required />
                        </div>
                        <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 0 0;"></div>
                        <div style="display: inline-block; width: 50%;">
                            <a href="javascript:showLogin();" style="float: left; margin-top: 0.5em;">返回登录</a>
                            <input type="button" onclick="doChangePwd()" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em;  float: right;" value="确认修改">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!--事件详情窗口-->
        <div id="eventDetailRegion">
            <div id="eventDetail" style="width:60%; height: 83%; left: 20%;">
                <div style="width: 100%; height: 100%;">
                    <div style="" class="detail-title">
                        <div style="width: 7em; display: inline-block; color: red;"></div>
                        <div style="width: Calc(100% - 18em); display: inline-block; text-align: center;">事件详情</div>
                        <div style="width: 10em; display: inline-block; text-align: right;" id="windowHandlePanel">
                            <a href="javascript:closeDiagle();" class="title-button close-btn"></a>
                        </div>
                    </div>
                    <div id="detail-left" style="width: 100%; height: 100%;">
                        <form action="" style="height: 320px;">
                            <div class="clear-fix">
                                <input type="hidden" value="" id="event-id" />
                                <input type="hidden" value="" id="process-id" />

                                <input type="hidden" value="" id="dispatch-chooseCar" />

                                <input type="hidden" value="" id="dispatch-doctor" />
                                <input type="hidden" value="" id="dispatch-doctorId" />
                                <input type="hidden" value="" id="dispatch-doctorPhone" />

                                <input type="hidden" value="" id="dispatch-driver" />
                                <input type="hidden" value="" id="dispatch-driverId" />
                                <input type="hidden" value="" id="dispatch-driverPhone" />

                                <input type="hidden" value="" id="dispatch-nurse" />
                                <input type="hidden" value="" id="dispatch-nurseId" />
                                <input type="hidden" value="" id="dispatch-nursePhone" />

                                <input type="hidden" value="" id="dispatch-worker" />
                                <input type="hidden" value="" id="dispatch-workerId" />
                                <input type="hidden" value="" id="dispatch-workerPhone" />

                                <span style="float: left;">事件来源：</span>
                                <select id="evd-source" class="select-common" style="width: 10em; float: left; " disabled>
                                    <!--<option value="" selected>普通来电</option>
                                    <option value="">手工制表</option>
                                    <option value="">自动呼救</option>
                                    <option value="">联网联动</option>
                                    <option value="">110联动</option>
                                    <option value="">119联动</option>
                                    <option value="">122联动</option>-->
                                </select>
                                <span style="margin-left: 20px; float: left;">呼叫类型：</span>
                                <select id="evd-call-type" class="select-common" style="width: 10em; float: left;" disabled>
                                    <!--<option value="" selected>救治</option>
                                    <option value="">回家</option>
                                    <option value="">转院</option>
                                    <option value="">特殊事件</option>-->
                                </select>
                                <span style="float: left; margin-left: 20px;">患者人数：</span>
                                <input id="det-amount" type="text" style="width: 4em; text-indent: 0.5em;" value="1" min="0" max="10000" readonly />
                                <!--<a href="javascript:reportBigEvent();" class="easyui-linkbutton" style="background: red; color: white; float: right;">上报重大事故</a>-->
                                <a class="common-button" style="margin-right: 7px;float: right;width: 110px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:showPrintPaper();">
                                    打印120急救单
                                </a>
                                <!--<a id="confirm-receive-button" class="common-button" style="margin-right: 9px;float: right;width: 110px; height: 30px; line-height: 30px; font-size: 14px;" href="javascript:confirmReceiveShow();">
                                    调度确认派车
                                </a>-->
                                <!--<a id="refuse-process-button" class="common-button" style="margin-right: 9px;float: right;width: 110px; height: 30px; line-height: 30px; font-size: 14px; background-color : red" href="javascript:refuseProcessReasonWindowShow();">
                                    分站拒绝派车
                                </a>-->
                                <!--<a id="update-process-button" class="common-button" style="margin-right: 9px;float: right;width: 110px; height: 30px; line-height: 30px; font-size: 14px;" href="javascript:updateProcessShow();">
                                    修改出车信息
                                </a>-->
                            </div>
                            <div>
                                <span>现场地址：</span>
                                <input id="det-address" type="text" style="width: Calc(100% - 87px); " readonly maxlength="200" />
                            </div>
                            <div>
                                <span>呼救原因：</span>
                                <input id="det-major" type="text" style="width: Calc(100% - 87px);" readonly maxlength="200" />
                            </div>
                            <div>
                                <span>主叫号码：</span>
                                <input id="det-callin" type="text" style="width: 12em;" readonly maxlength="20" />
                                <!--<a href="javascript:void(0);" class="easyui-linkbutton"><i class="fa fa-volume-control-phone"></i>&nbsp;拨打</a>-->
                                <span style="margin-left: 3em;">联系电话：</span>
                                <input id="det-contact" type="text" style="width: 12em;" readonly maxlength="20" />
                                <!--<a href="javascript:void(0);" class="easyui-linkbutton"><i class="fa fa-volume-control-phone"></i>&nbsp;拨打</a>-->
                                <span style="margin-left: 3em;">联系人：</span>
                                <input id="det-contacter" type="text" style="width: 12em;" readonly maxlength="20" />
                            </div>
                            <div class="clear-fix">
                                <span style="float: left;">120备注：</span>
                                <textarea maxlength="500" id="det-120remark" style="width: Calc(100% - 85px); resize: none; height: 4em; float: left; font-size: 1.4em;" readonly></textarea>
                            </div>
                            <div>
                                <span>患者姓名：</span>
                                <input id="det-name" type="text" readonly maxlength="20" />
                                <!--<span style="margin-left: 5px;">病情：</span>
                                <select id="det-condition" class="select-common" style="width: 5em;" disabled>-->
                                    <!--<option value="0">轻微</option>
                                    <option value="1">中度</option>
                                    <option value="2">严重</option>
                                    <option value="3">死亡</option>-->
                                <!--</select>-->
                                <span>性别：</span>
                                <select id="det-gender" class="select-common" style="width: 5em;" disabled>
                                    <option value="0">男</option>
                                    <option value="1">女</option>
                                    <option value="2">未知</option>
                                </select>
                                <span style="margin-left: 5px;">年龄：</span>
                                <input id="det-age" type="number" style="width: 4em; text-indent: 0.5em;" min="0" max="999" readonly />
                                <span style="margin-left: 5px;">国籍：</span>
                                <select id="det-country" class="select-common" style="width: 10em;" disabled>
                                    <!--<option value="0">中国</option>-->
                                    <!--<option value="1">海外</option>-->
                                </select>
                                <span style="margin-left: 5px;">民族：</span>
                                <select id="det-race" class="select-common" style="width: 8em;" disabled>
                                    <!--<option value="0">汉族</option>-->
                                    <!--<option value="1">少数民族</option>-->
                                </select>
                                <input type="checkbox" id="det-Foreign" style="height: 1em;width: 1em;margin-left: 0.5em;margin-bottom: .4em;" disabled />
                                <span style="margin-left: 5px;">外籍人员</span>
                            </div>
                            <!--出车信息-->
                            <div id="exitInformation">
                                <div class="group-box-title">出车信息:</div>
                                <div style="margin-left:6%">
                                    <div class="container" style="margin-bottom:10px">
                                        <span>
                                            选择车辆：
                                            <select id="chooseCar" class="easyui-combobox" style="width:230px;line-height:30px;border:1px"></select>
                                        </span>

                                        <span style="margin-left: 5px;">
                                            选择班次：
                                            <select id="dispatchClasses" name="dispatchClasses" class="easyui-combobox" style="width:230px;line-height:10px;border:1px"></select>
                                        </span>

                                        <span style="margin-left: 5px;">
                                            出车司机：
                                            <select id="dispatchDriver" name="dispatchDriver" class="easyui-combobox" style="width:230px;line-height:30px;border:1px"></select>
                                        </span>

                                    </div>
                                    <div class="container">
                                        <span>
                                            出车医生：
                                            <select id="dispatchDoctor" name="dispatchDoctor" class="easyui-combobox" style="width:230px;line-height:30px;border:1px"></select>
                                        </span>

                                        <span style="margin-left: 5px;">
                                            出车护士：
                                            <select id="dispatchNurse" name="dispatchNurse" class="easyui-combobox" style="width:230px;line-height:30px;border:1px"></select>
                                        </span>

                                        <span style="margin-left: 5px;">
                                            出车护工：
                                            <select id="dispatchWorker" name="dispatchWorker" class="easyui-combobox" style="width:230px;line-height:30px;border:1px"></select>
                                        </span>

                                    </div>
                                </div>

                            </div>
                            <!--<div class="datagrid-toolbar"></div>
                            <div id="dis-cars-button" class="clear-fix" style="text-align: center !important;line-height:  50px;height: 50px;align-content: center;margin:  0px auto;">
                                <a href="javascript:showPrint(_currentEvent.id, null, false);" class="common-button" style="height: 30px; line-height: 30px; width: 8em;">
                                    打印
                                </a>
                                <a href="javascript:showPrint(_currentEvent.id, null, false);" class="common-button" style="height: 30px; line-height: 30px; width: 8em;">
                                    出车
                                </a>
                            </div>-->
                            <!--<div class="clear-fix">
                                <span style="float: left;">分站备注：</span>
                                <textarea maxlength="500" id="det-stationRemark" style="width: Calc(100% - 85px); resize: none; height: 4em; float: left; font-size: 1.4em;"></textarea>

                            </div>-->
                            <input type="hidden" value="" id="dispatchCarId" />
                            <input type="hidden" value="" id="dispatchEventId" />
                            <input type="hidden" value="" id="mobileProcessId" />
                            <div id="button_car" style="display:flex;justify-content: center; align-items: center;">
                                <a id="confirmReceiveSubmitBtn" class="common-button" style="margin-right: 9px;float: left;width: 150px; height: 40px; line-height: 40px; font-size: 15px; display: none" href="javascript:confirmReceiveSubmit();">确认接收（派车）</a>
                                <a id="updateDispatchPeopleBtn" class="common-button" style="margin-right: 9px;float: left;width: 150px; height: 40px; line-height: 40px; font-size: 15px; display: none" href="javascript:confirmReceiveSubmit();">修改出车人员</a>
                                <a id="CancelDiagle" class="common-button" style="margin-right: 9px;float: right;width: 110px; height: 40px; line-height: 40px; font-size: 15px;" href="javascript:closeDiagle();">
                                    取消
                                </a>
                            </div>

                        </form>
                        <!--调度-->
                        <div class="group-box" style="width: Calc(100% - 20px); height: Calc(100% - 560px);margin: 213px 5px 5px 5px">
                            <div class="group-box-title">车辆记录</div>
                            <table id="dis-cars" class="edc-table" cellpadding="0" cellspacing="0" style="width: Calc(100% - 20px); height: Calc(100% - 55px); margin-top: 10px; margin-left: 10px;">
                                <thead>
                                    <tr style="width:100%">
                                        <th style="width: 30%;">时间</th>
                                        <th style="width: 20%;">状态</th>
                                        <th style="width: 50%;">备注</th>
                                    </tr>
                                </thead>
                                <tbody id="dis-cars-body"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--车辆详情界面-->
        <div id="mobile-editor" class="easyui-window" title="车辆详情" style="width: 400px; height: 300px;"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <form action="" onsubmit="return false;" style="font-size: 1.5em; padding: 10px;">
                <div style="padding-bottom: 5px;">
                    <div style="width: 5em; text-align: right; display: inline-block;">车牌号：</div>
                    <input id="med-platenum" style="width: Calc(100% - 5em - 5px); font-size: 1.4em;" />
                </div>
                <div style="padding-bottom: 5px;">
                    <div style="width: 5em; text-align: right; display: inline-block;">车辆名称：</div>
                    <input id="med-name" style="width: Calc(100% - 5em - 5px); font-size: 1.4em;" />
                </div>
                <div style="padding-bottom: 5px;">
                    <div style="width: 5em; text-align: right; display: inline-block;">联系方式：</div>
                    <input id="med-contact" style="width: Calc(100% - 5em - 5px); font-size: 1.4em;" />
                </div>
                <div style="padding-bottom: 5px;">
                    <div style="width: 5em; text-align: right; display: inline-block;">可载伤员：</div>
                    <input id="med-ability" type="number" style="width: Calc(100% - 5em - 5px); font-size: 1.4em;" value="1" min="1" />
                </div>
                <div style="padding-top: 20px; height: 1.3em;">
                    <div style="width: 5em; text-align: right; display: inline-block;"></div>
                    <input class="common-button" type="submit" value="添加" id="med-submit-btn" style="width: 5em; font-size: 1.2em; cursor: pointer;" />
                </div>
            </form>
        </div>

        <!--打印预览界面-->
        <div id="print-prev" class="easyui-window" title="手动打印" style="width: 60%;height:850px; padding: 10px 20px;"
             data-options="resizable:true,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <input type="hidden" value="" id="printConfig" />
            <!-- 一份急救单 -->
            <div id="print-one-copies">
                <table id="print-zone-one" style="width: 100%; font-size: 14px;">
                    <thead>
                        <tr>
                            <td colspan="4" style="text-align: center;">
                                <span id="prt-stationName">.</span>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="title" style="width: 166px;">出车医生：</td>
                            <td>
                                <span id="prt-dispatchDoctor">.</span>
                            </td>
                            <td class="title" style="width: 166px;">出车护士：</td>
                            <td>
                                <span id="prt-dispatchNurse">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">出车司机：</td>
                            <td colspan="3">
                                <span id="prt-dispatchDriver">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">收单时刻：</td>
                            <td colspan="3">
                                <span id="prt-createTime">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">事件编号：</td>
                            <td colspan="3">
                                <span id="prt-id">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">呼叫类型：</td>
                            <td colspan="3">
                                <span id="prt-call-type">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">呼救原因：</td>
                            <td colspan="3">
                                <span id="prt-major">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">调派车辆：</td>
                            <td colspan="3">
                                <span id="prt-car">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">地址：</td>
                            <td colspan="3">
                                <span id="prt-addr">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">联系人：</td>
                            <td colspan="3">
                                <span id="prt-contacter">.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">呼救电话：</td>
                            <td colspan="1" style="width: 35%;">
                                <span id="prt-callIn">.</span>
                            </td>
                            <td class="title" style="width: 166px;">联系电话：</td>
                            <td colspan="1" style="width: 35%;">
                                <span id="prt-contact"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">患者姓名：</td>
                            <td colspan="1">
                                <span id="prt-name">.</span>
                            </td>
                            <td class="title" style="width: 166px;">患者性别：</td>
                            <td colspan="1">
                                <span id="prt-gender"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">患者人数：</td>
                            <td colspan="1">
                                <span id="prt-amount">.</span>
                            </td>
                            <td class="title" style="width: 166px;">患者年龄：</td>
                            <td colspan="1">
                                <span id="prt-age"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">患者国籍：</td>
                            <td colspan="1">
                                <span id="prt-country">.</span>
                            </td>
                            <td class="title" style="width: 166px;">
                                <span>患者民族：</span>
                            </td>
                            <td colspan="1">
                                <span id="prt-race"></span>
                            </td>
                        </tr>
                        <!--<tr>
                            <td class="title">病情初判：</td>
                            <td colspan="3">
                                <span id="prt-condition">.</span>
                            </td>
                        </tr>-->
                        <tr style="height: 4em; vertical-align: top;">
                            <td class="title">备注：</td>
                            <td colspan="3">
                                <span id="prt-remark">.</span>
                            </td>
                        </tr>
                        <tr style="vertical-align: top;">
                            <td class="title">送院确认：</td>
                            <td colspan="3" style="width:84%">
                                <div style="white-space:normal;word-break:break-all;width: 100%;">
                                    因患者病情需送院诊治，但救护车上急救条件有限，在送往医院途中可能出现病情变化而危及生命。承诺送医途中出现任何意外责任自负，请签字为证。
                                </div>
                                <div style="height: 32px;line-height: 32px;margin-top: 15px;">
                                    <span style="display: inline-block;height: 32px;width: 45%;padding-left: 50px;float: left;">患者/家属/送院者签名：</span>
                                    <span style="display: inline-block;height: 32px;width: 45%;float: right;">与患者关系：</span>
                                </div>
                            </td>
                        </tr>
                        <tr style="height: 38px;;line-height: 38px;">
                            <td class="title">取消原因：</td>
                            <td></td>
                            <td colspan="2" style="padding: 0px;">
                                <span style="display: inline-block;width: 51%;height: 38px;padding-left: 5px;float: left;">家属或患者签名：</span>
                                <span style="display: inline-block;width: 45%;height: 38px;padding-left: 5px;float: left;">与患者关系：</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 一式两份急救单  -->
            <div id="print-two-copies">
                <table id="print-zone-two" style="width: 100%; font-size: 14px;">
                    <thead>
                        <tr>
                            <td colspan="8" style="text-align: center;">
                                <span id="prt-stationName2"></span>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="title">收单时刻</td>
                            <td colspan="3">
                                <span id="prt-receiptTime"></span>
                            </td>
                            <td class="title">流水号</td>
                            <td colspan="3">
                                <span id="prt-flowNo"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">派车人员</td>
                            <td colspan="3">
                                <span id="prt-seatUser"></span>
                            <td class="title">事件编号</td>
                            <td colspan="3">
                                <span id="prt-eventId"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title" style="width: 10%">患者姓名</td>
                            <td style="width: 15%">
                                <span id="prt-patientName"></span>
                            </td>
                            <td class="title" style="width: 10%">性别</td>
                            <td style="width: 15%">
                                <span id="prt-patientGender"></span>
                            </td>
                            <td class="title" style="width: 10%">年龄</td>
                            <td style="width: 15%">
                                <span id="prt-patientAge"></span>
                            </td>
                            <td class="title" style="width: 10%">派车类型</td>
                            <td style="width: 15%">
                                <span id="prt-dispatchType"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">呼救电话</td>
                            <td colspan="3">
                                <span id="prt-callIn2"></span>
                            </td>
                            <td class="title">联系电话</td>
                            <td colspan="3">
                                <span id="prt-contact2"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">呼救原因</td>
                            <td colspan="3">
                                <span id="prt-majorCall"></span>
                            </td>
                            <td class="title">车牌号码</td>
                            <td colspan="3">
                                <span id="prt-plateNum"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">现场地址</td>
                            <td colspan="7">
                                <span id="prt-address"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">送往地址</td>
                            <td colspan="7">
                                <span id="prt-destination"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">备注</td>
                            <td colspan="7">
                                <span id="prt-remark2"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">司机</td>
                            <td>
                                <span id="prt-dispatchDriver2"></span>
                            </td>
                            <td class="title">医生</td>
                            <td>
                                <span id="prt-dispatchDoctor2"></span>
                            </td>
                            <td class="title">护士</td>
                            <td>
                                <span id="prt-dispatchNurse2"></span>
                            </td>
                            <td class="title">担架员</td>
                            <td>
                                <span id="prt-dispatchWorker2"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">车费</td>
                            <td>
                                <span id="prt-carFare"></span>
                            </td>
                            <td class="title">出诊费</td>
                            <td>
                                <span id="prt-visitFare"></span>
                            </td>
                            <td class="title">抢救费</td>
                            <td>
                                <span id="prt-rescueFare"></span>
                            </td>
                            <td class="title">监护费</td>
                            <td>
                                <span id="prt-custodyFare"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">治疗费</td>
                            <td>
                                <span id="prt-treatmentFare"></span>
                            </td>
                            <td class="title">氧气费</td>
                            <td>
                                <span id="prt-oxygenFare"></span>
                            </td>
                            <td class="title">药费</td>
                            <td>
                                <span id="prt-medicineFare"></span>
                            </td>
                            <td class="title">材料费</td>
                            <td>
                                <span id="prt-materialFare"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">其他费</td>
                            <td>
                                <span id="prt-otherFare"></span>
                            </td>
                            <td class="title">合计</td>
                            <td>
                                <span id="prt-totalFare"></span>
                            </td>
                            <td class="title">欠费</td>
                            <td colspan="3">
                                <span id="prt-arrears"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="title">发票号</td>
                            <td>
                                <span id="prt-invoiceNumber"></span>
                            </td>
                            <td class="title">住院号</td>
                            <td>
                                <span id="prt-inpatientNumber"></span>
                            </td>
                            <td class="title">里程</td>
                            <td colspan="3">
                                <span id="prt-dispatchMiles"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <a href="javascript:doPrint($('#printConfig').val());" class="common-button" style="height: 30px; line-height: 30px; width: 8em; position: relative; top: 20px; left: Calc(50% - 15px);">
                打印
            </a>
        </div>

        <!--车辆确认接收派车界面-->
        <div id="confirm-receive-editor" class="easyui-window" title="任务出车人员设置" style="width: 400px; height: 320px; text-align: center; padding-top:20px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <table tableframe=void style="padding-top:30px">
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;">
                        <label>选择班次:</label>
                    </td>
                    <td style="border-style:none;">
                        <select id="dispatchClasses" name="dispatchClasses" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font>
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;">
                        <label>出车医生:</label>
                    </td>
                    <td style="border-style:none;">
                        <select id="dispatchDoctor" name="dispatchDoctor" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font>
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;">
                        <label>出车护士:</label>
                    </td>
                    <td style="border-style:none;">
                        <select id="dispatchNurse" name="dispatchNurse" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></tdstyle="border-style:none;">
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;">
                        <label>出车司机:</label>
                    </td>
                    <td style="border-style:none;">
                        <select id="dispatchDriver" name="dispatchDriver" class="easyui-combobox" style="width:250px;line-height:20px;border:1px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;">
                        <label>出车护工:</label>
                    </td>
                    <td style="border-style:none;">
                        <select id="dispatchWorker" name="dispatchWorker" class="easyui-combobox" style="width:250px;line-height:20px;border:1px">
                    </td>
                </tr>

            </table>
            <input type="hidden" value="" id="dispatchCarId" />
            <input type="hidden" value="" id="dispatchEventId" />
            <input type="hidden" value="" id="mobileProcessId" />
            <a href="javascript:confirmReceiveSubmit();" id="confirmReceiveSubmitBtn" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 20px 10px 0 0;">确认接收（派车）</a>
        </div>

        <!--出车菜单-->
        <div id="menu-mobile" class="easyui-menu" style="width: 120px;">
            <div>出车</div>
        </div>

        <!--取消派车窗口-->
        <div id="cancelDispatchedDialog" class="easyui-dialog" title="取消派车，请填写原因" style="width:360px;"
            data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#qxpc-buttons">
            <input type="hidden" value="" id="cancel-process-id" /><!--取消派车的任务ID-->
            <form id="cancelDispatchedForm">
                <div style="height:60px; text-align:center;line-height: 60px;font-size: 15px;">
                    原因：
                    <textarea id="cancel-reason" class="easyui-textbox" style="height:30px; width:260px;" maxlength="500" required></textarea>
                </div>
            </form>
        </div>
        <div id="qxpc-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:cancelDispatchedBtn()" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closecancelDispatchedBtn();"
                style="width:80px;">取消</a>
        </div>
    </div>
    <!-- 音频元素 -->
    <audio id="audioaAlert" src="./wav/alert.wav" controls="controls" loop="true" hidden="true"></audio>
    
    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>

    <script type="text/javascript" src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/station.js"></script>
    
</body>
</html>