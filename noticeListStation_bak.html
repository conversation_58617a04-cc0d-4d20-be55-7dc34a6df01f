<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 黄灵
* Created: 2022/4/19
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>消息列表界面(分站端)</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <style>
      .notice-main{
        width: 99%;
        height: 100%;
        border: solid #E2E7EA 1px;
        margin: 5px;
        border-radius: 5px;
      }
      html, body {
          height: 99%;
      }
      .notice-header{
        display: flex;
        padding: 12px 15px 8px 15px;
        background-color: #F5F5F5;
        border-bottom: 1px solid lightgray;
      }
      .cjsj, .timeStart , .refresh, .details {
        margin-right: 15px;
      }
      .refresh{
        margin-left: 15px;
      }
      .zhi {
        margin: 0 15px;
      }
      #mainPanel_McWinformMVC{
        height: calc(100%);
      }
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <div class="notice-main">
            <div class="notice-header">
                <div class="cjsj">创建时间</div>
                <input id="startTime" class="easyui-datetimebox timeStart" style="width:200px;text-indent: 0.5em;" />
                <div class="zhi">至</div>
                <input id="endTime" class="easyui-datetimebox timeEnd" style="width:200px;text-indent: 0.5em;" />
                <a class="easyui-linkbutton refresh" id="refresh-event-btn" href="javascript:refreshlist();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" data-options="iconCls:'icon-reload'">刷新</a>
                <a class="easyui-linkbutton details" id="show-event-btn" href="javascript:clear();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" data-options="iconCls:'icon-show'">重置</a>
            </div>
            <table class="easyui-datagrid" id="dg-notice-list" style="width:100%;font-weight: bold;height: 100%;" title=""
                   pagination="false" singleSelect="true" rownumbers="false" fit="true">
                <thead>
                    <tr>
                        <th field="index" width="6%" align="center">编号</th>
                        <th field="noticeTitle" width="60%" align="center">公告标题</th>
                        <th field="sender" width="16%" align="center">发布人</th>
                        <th field="createTime" width="18%" align="center">发布时间</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">

        var _user = null; //用户数据
        try {
            _user = evalJson(window.parent.gProxy.getUserInfo());
        } catch (e) {
            _user = evalJson(window.parent.bsProxy.getUserInfo());
        }
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var noticeList = []
        var startTime = ''
        var endTime = ''
        //刷新
        function refreshlist() {
            startTime = $('#startTime').datetimebox('getValue') || '',
                endTime = $('#endTime').datetimebox('getValue') || '',
                getList()
        }
        $(document).ready(function () {
            getList()
        });
        function clear() {
            $('#startTime').datetimebox('setValue', '');
            $('#endTime').datetimebox('setValue', '');
            startTime = ''
            endTime = ''
            getList()
        }
        function getList() {
            clearTimeout(window.noticeInterval);
            window.noticeInterval = null;
            $('#dg-notice-list').datagrid('loading'); //打开进度条：打开等待div
            $('#dg-notice-list').datagrid({
                rowStyler: function (index, row) {
                    if (row.isReceive == '0') return 'background-color: #b8ec9a;';
                }
            });
            $('#dg-notice-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
                data: refreshNoticeDatalist(), //这一步是加载ajax查询的数据，非常重要
                onDblClickRow: function (rowIndex) { //鼠标双击事件
                    let id = noticeList[rowIndex].id
                    openTab('公告详情' + id, "noticeDetailStation.html?id=" + id, id);
                    updateNoticeUserRead({ noticeId: id }, function (data) {
                        getList()
                    }, function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '异常信息：' + errMsg);
                    });
                },
            });
            window.noticeInterval = setTimeout(getList, 3000);
        }
        /** 刷新通知 */
        function refreshNoticeDatalist() {
            var params = {
                userId: _user.Id,
                startTime: startTime,
                endTime: endTime,
                pageSize: 999,
                pageNum: 1,
                stationCode: _user.Station.id
            };
            //获取通知
            getNoticelists(params, function (data) {
                $('#dg-notice-list').datagrid('loadData', {
                    total: 0,
                    rows: []
                }); //清理数据
                for (var i = 0; i < data.length; i++) {
                    setNoticeList('insertRow', i, data[i])
                }
                noticeList = data
                $('#dg-notice-list').datagrid('loaded'); //关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-notice-list').datagrid('loaded'); //关闭loding进度条；
                //失败才做处理
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        };
        //设置添加列表数据
        function setNoticeList(type, i, row) {
            $('#dg-notice-list').datagrid(type, {
                index: i, // 索引从0开始
                row: {
                    index: i + 1,
                    noticeTitle: row.noticeTitle,
                    sender: row.sender,
                    createTime: row.createTime,
                    isReceive: row.isReceive,
                }
            })
        }
        function openTab(title, href, iframeName) {
            // 在 iframe 页面中调用父页面的元素或 id
            var parentElement = window.parent.$('#main-tabs');
            var e = parentElement.tabs('exists', title);
            if (e) {
                parentElement.tabs('select', title);
                var tab = parentElement.tabs('getSelected');
                parentElement.tabs('update', {
                    tab: tab,
                    options: {
                        id: iframeName,
                        title: title,
                        content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                        closable: true,
                        selected: true
                    }
                });
            } else {
                parentElement.tabs('add', {
                    id: iframeName,
                    title: title,
                    content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                    iconCls: '',
                    closable: true
                });
            }
        }
    </script>
</body>
</html>