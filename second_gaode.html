<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <!--<script type="text/javascript" src="http://api.map.baidu.com/getscript?v=2.0&ak=4E2yR7vG9tb8KdjFQ6RRpt80ZdE4p98g"></script>-->
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        #second-screen-tabs .tabs-title {
            font-size: 14px;
            font-weight: bold;
        }
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row { 
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        #second-screen-tabs .tabs-panels > .panel > .panel-body {
            overflow: hidden;
        }

        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        a {
            text-decoration: none !important;
        }

        tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        tr:hover a {
                color: #ffffff !important;
            }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            
        }
        .input-border>.textbox{
            border: none !important;
        }
        .panel-title{
            font-size: 15px;
        }

        .isFollow,.noFollow,.configs,.show_guiji ,.hide_guiji,.changeTilt{
          position: absolute;
          top: 3.5em;
          left: -4.3em;
          box-shadow: rgba(0, 0, 0, 0.35) 2px 2px 3px;
          border-width: 1px;
          border-style: solid;
          border-color: rgb(139, 164, 220);
          background: rgb(142, 168, 224);
          padding: 2px 6px;
          font-style: normal;
          font-variant: normal;
          font-weight: bold;
          font-stretch: normal;
          font-size: 12px;
          line-height: 1.3em;
          font-family: arial, sans-serif;
          text-align: center;
          white-space: nowrap;
          border-radius: 3px;
          color: rgb(255, 255, 255);
          cursor: pointer;
        }
        .changeTilt{
          top: .9em;
          left: -5.4em;
        }
        .noFollow {
          top: 3.5em;
          left: -4.3em;
          border-color: #099DFC;
          background: #099DFC;
        }
        .configs{
          top: 8.9em;
          left: -4.3em;
          border-color: rgb(139, 164, 220);
          background: rgb(142, 168, 224);
        }
        .show_guiji{
          top: 6.2em;
          left: -4.3em;
          border-color: rgb(139, 164, 220);
          background: rgb(142, 168, 224);
        }
        .hide_guiji{
          top: 6.2em;
          left: -4.3em;
          border-color: #099DFC;
          background: #099DFC;
        }
        /* .BMap_noprint{
          display: none;
        } */
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false" style="height: 100%;">
        <div id="hangUpDialog" class="easyui-dialog" title="座席挂起" style="width:300px;height:150px;"
             data-options="resizable:false,modal:true,closed:false,closable:false">
            <div style="text-align:center;font-size:16px;color:brown;">
                <div style="height:40px;font-size:16px;color:brown;margin-top: 20px;">本座席已被挂起或处于未登陆状态，副屏幕将不能操作，请输入密码解除此状态！！！</div>
            </div>
        </div>
        <div id="second-screen-tabs" class="easyui-tabs" style="width: 100%; height: 100%;">
            <div title="地图调度222222" style="display: none;" iconCls="icon-map">
                <div style="display: inline-block; width: Calc(100% - 31em); height: 100%;">
                    <div id="map-search" style="width: 50%; height: 3em; position: absolute; left: 70px; top: 70px; z-index: 2;">
                        <input type="text" id="map-search-box" style="width: 60%; height: 100%; font-size: 2em;" />
                        <a href="javascript:manualSearch();" style="display: inline-block; position: absolute; top: 0; right: 40%;">
                            <i class="fa fa-search fa-3x"></i>
                        </a>
                        <div id="searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                    </div>
                    <div id="the-map" style="width: 100%; height: 100%; overflow: hidden; margin: 0;"></div>
                </div>
                <div style="width: 30em; height: 100%; position: relative; right: 0; top: 0; display: inline-block;">
                    <!-- <div id="changeTilt" class="changeTilt" style="display: block;">切换地图</div> -->
                    <div id="isFollow" class="isFollow" style="display: block;">跟随</div>
                    <div id="noFollow" class="noFollow" style="display: none;">取消</div>
                    <div id="show_guiji" class="show_guiji">轨迹</div>
                    <div id="hide_guiji" class="hide_guiji">隐藏</div>
                    <div id="configs" class="configs">配置</div>
                    <div id="config-dialog" class="easyui-dialog" title="显示配置" style="width:160px;height:170px;left: 1365px;top: 170px;"
                         data-options="">
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="fenzhan-checkbox" name="fenzhan-checkbox" checked value="1" label="所属分站">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="chepai-checkbox" name="config2" checked value="2" label="车牌号">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="sudu-checkbox" name="config3" checked value="3" label="速度">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="status-checkbox" name="config4" checked value="4" label="状态">
                        </div>
                    </div>
                    <ul id="map-station" style="width: 100%; float: left; height: Calc(100% - 0.2em); display: inline-block; font-size: 1.6em;" class="edc-listbox"></ul>
                </div>
            </div>
            <div title="电话簿" style="display: none;" iconCls="icon-phonebooks">
                <div style="width: 30em; height: 100%; display: inline-block;float: left;">
                    <ul id="cont-group" class="edc-listbox input-border" title="单位" style="width: 100%; height: 100%; font-size: 1.5em;">
                        <input id="search-hospital" prompt="输入医院名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;height:35px;font-size: 13px;"></input>
                    </ul>
                </div>
                <div style="display: inline-block; width: Calc(100% - 31em); height: 100%;float: left;margin-left: 1em;">
                    <div class="input-border">
                        <input id="search-name" prompt="输入对象名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:300px;height:35px;font-size: 13px;"></input>
                    </div>
                    <table cellpadding="0" cellspacing="0" class="edc-table" style="width: 100%;height: calc(100% - 35px);font-size: 1.5em;">
                        <thead>
                            <tr>
                                <th style="width: 20%;">对象名称</th>
                                <th style="width: 80%;" align="left"> &ensp;联系电话(点击号码拨号)</th>
                            </tr>
                        </thead>
                        <tbody id="cont-list-body"></tbody>
                    </table>
                </div>
            </div>
            <div title="事件列表" style="display: none;" iconCls="icon-tasklist">
                <iframe name="eventList" id="eventListIframe" scrolling="auto" src="eventList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="通话列表" style="display: none;" iconCls="icon-calllist">
                <iframe name="callList" id="callListIframe" scrolling="auto" src="callList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="街道列表" style="display: none;" iconCls="icon-search">
                <iframe name="streetList" id="streetListIframe" scrolling="auto" src="streetList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>

    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/secondScr_gaode.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript">
        //兼容web，如果非C#中执行，那么gProxy是未定义的，使用自定义的gProxy
        _baseUrl = "";
        bsProxy = null;
        //读取服务器接口地址
        try {
            _baseUrl = gProxy.getBaseUrl();
            console.log("C/S模式执行");
        } catch (e) {
            console.log("B/S模式执行");
            bsProxy = new gBsProxy(document.body);
            _baseUrl = bsProxy.getBaseUrl();
        }
        var tabs = ['', '', 'eventListIframe', 'callListIframe', 'streetListIframe', 'seatsListIframe']
        querySysConf('map_config',
            function (res) {
                var ohead = document.getElementsByTagName("head")[0];
                var ele = document.createElement('script');
                let jsonConfig = JSON.parse(res)
                //ele.src = "http://api.map.baidu.com/getscript?v=2.0&ak=" + jsonConfig.baidu.ak; // 百度
                //ele.src = "https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652"; // 高德
                ele.src = "https://webapi.amap.com/maps?v=2.0&key=" + jsonConfig.gaode.key; // 高德
                ohead.appendChild(ele);
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '获取地图配置map_config失败，异常信息：' + errMsg);
            });
        //页面加载完成后执行
        window.onload = function () {
            $('#second-screen-tabs').tabs({
                onSelect: function (title, index) {
                    //判断是否有iframe
                    if (tabs[index]) {
                        let iframe = document.getElementById(tabs[index])
                        //判断是否有子方法 如果有就尝试调用
                        iframe && iframe.contentWindow && iframe.contentWindow.childFnt && iframe.contentWindow.childFnt()
                    }
                }
            })
        }

    </script>
</body>
</html>