<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification: 添加TreeGrid修复方案
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .l-btn-left l-btn-icon-left {
            margin-top: -1px;
        }
        /* 只针对非TreeGrid的DataGrid应用样式 */
        .datagrid:not(.treegrid) .datagrid-cell, 
        .datagrid:not(.treegrid) .tree-title {
            font-size: 14px !important;
            font-weight: bold !important;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }
        .window-body{
            overflow-y:hidden;
        }
        /*只针对非TreeGrid的DataGrid应用行高设置*/
         .datagrid:not(.treegrid) .datagrid-row {
            height: 28px;
        }
         .datagrid:not(.treegrid) .datagrid-btable tr {
             height: 0px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover,
        .datagrid-row-selected {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        .datagrid-wrap .datagrid-body tr:hover a,
        .datagrid-row-selected a {
            color: #ffffff !important;
        }

        /* 确保选中行的背景色优先级最高 */
        .datagrid-row-selected,
        .treegrid-row-selected {
            background: #12B5F8 !important;
                color: #ffffff !important;
        }
        
        /* ========== TreeGrid 修复样式 开始 ========== */
        
        /* 1. 强制表格布局为固定模式，确保列宽同步 */
        .treegrid .datagrid-view table {
            table-layout: fixed !important;
            width: 100% !important;
        }
        
        .treegrid .datagrid-header table,
        .treegrid .datagrid-body table {
            table-layout: fixed !important;
            width: 100% !important;
        }
        
        /* 2. 确保单元格宽度可控 */
        .treegrid .datagrid-header .datagrid-cell,
        .treegrid .datagrid-body .datagrid-cell {
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            box-sizing: border-box !important;
        }
        
        /* 3. TreeGrid行高和字体设置 */
        .treegrid .datagrid-row {
            height: 28px !important;
        }
        
        .treegrid .datagrid-cell {
            font-size: 14px !important;
            font-weight: bold !important;
            line-height: 28px !important;
            padding: 0 4px !important;
        }
        
        /* 4. 树形节点图标对齐 */
        .treegrid .tree-icon,
        .treegrid .tree-hit {
            vertical-align: middle !important;
            margin-top: 0 !important;
        }
        
                 /* 5. 强制隐藏折叠的子节点 - 关键修复 */
         .treegrid .datagrid-body tr[style*="display: none"] {
             display: none !important;
         }
         
         .treegrid .tree-node-collapsed {
             display: none !important;
         }
         
         /* 确保EasyUI原生的隐藏机制正常工作 */
         .treegrid .datagrid-body tr.treegrid-row-collapsed {
             display: none !important;
         }
        
        /* 6. 修复列调整时的视觉效果 */
        .treegrid .datagrid-header .datagrid-cell {
            border-right: 1px solid #ddd;
        }
        
        /* 7. 确保分页器正常显示 */
        .treegrid .pagination {
            border-top: 1px solid #ddd !important;
            background: #fafafa;
        }
        
        /* 8. 修复冻结列问题 */
        .treegrid .datagrid-view1 .datagrid-body table,
        .treegrid .datagrid-view2 .datagrid-body table {
            table-layout: fixed !important;
        }
        
        /* 9. 确保树形结构线条正确显示 */
        .treegrid .tree-line {
            height: 28px !important;
        }
        
        /* ========== TreeGrid 修复样式 结束 ========== */
        
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
        }
        input[type="checkbox"]:checked::before {
            content: "\2713";
            background-color: #fff;
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            color: #000;
            font-size: 20px;
            font-weight: bold;
        }
        
    </style>
</head>
<body>
    <div id="mainPanel_EventList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">事件列表<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="eventSearch" style="font-size: 14px;">
                    创建时间： <input id="startCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endCreateTime" class="easyui-datetimebox" style="width:160px;" />
                    &nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>
                    <span>&nbsp;事件名称：</span>
                    <input id="majorCall" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;指派分站：</span>
                    <input id="stationName" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;事故地址：</span>
                    <input id="address" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;呼救电话：</span>
                    <input id="callPhone" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;任务状态：</span>
                    <select id="eventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                        <option value="0">全部</option>
                        <option value="7">未调度-预约</option>
                        <option value="1">未调度-落单</option>
                        <option value="6">未调度-待派</option>
                        <option value="2">已调度</option>
                        <option value="3">已完成</option>
                        <option value="4">已撤销</option>
                    </select>
                    &ensp;<input type="checkbox" id="autoRefreshList" name="autoRefreshList" value="0" checked="checked" />自动刷新
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getDgEventListDatas();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-treegrid" id="dg-event-pages" style="height: Calc(100% - 100px)"
                       data-options="idField:'eventId',treeField:'eventName',parentIdField:'_parentId',pagination:true,singleSelect:true,rownumbers:true,fit:true,fitColumns:false,toolbar:'#tb'">
                    <thead data-options="frozen:true">
                        <tr>
                            <th data-options="field:'eventStatusIcon',width:30,align:'center',formatter:setDgEventStatusIconColumnFormatter"></th>
                        </tr>
                    </thead>
                    <thead>
                        <tr>
                            <th data-options="field:'eventName',width:400,align:'left'">事件名称</th>
                            <th data-options="field:'serialNo',width:100,align:'center',formatter:formatSerialNo">受理次数</th>
                            <th data-options="field:'createTime',width:160,align:'center'">创建时间</th>
                            <th data-options="field:'taskStation',width:200,align:'center'">指派分站</th>
                            <th data-options="field:'callIn',width:120,align:'center'">呼救电话</th>
                            <th data-options="field:'callInTimes',width:160,align:'center'">来电时间</th>
                            <th data-options="field:'eventStatusStr',width:70,align:'center'">状态</th>
                            <th data-options="field:'eventTypeStr',width:80,align:'center'">事件类型</th>
                            <th data-options="field:'eventSrcName',width:80,align:'center'">来源</th>
                            <th data-options="field:'callTypeName',width:80,align:'center'">类型</th>
                            <th data-options="field:'contact',width:120,align:'center'">联系电话</th>
                            <th data-options="field:'contacter',width:100,align:'center'">联系人</th>
                            <th data-options="field:'processCount',width:100,align:'center'">任务总数(派车)</th>
                            <th data-options="field:'runProcessCount',width:80,align:'center'">运行任务数</th>
                            <th data-options="field:'finishedProcessCount',width:80,align:'center'">完成任务数</th>
                            <th data-options="field:'cancelProcessCount',width:100,align:'center'">撤销任务数(中止)</th>
                            <th data-options="field:'revocationSchedulingReason',width:160,align:'center'">撤销原因</th>
                            <th data-options="field:'eventCreateSeatUser',width:80,align:'center'">创建人</th>
                            <th data-options="field:'eventCreateSeatCode',width:80,align:'center'">创建座席</th>
                            <th data-options="field:'centerRemark',width:200,align:'center'">备注</th>
                            <th data-options="field:'firstSeatUser',width:100,align:'center'">首次调度员</th>
                            <th data-options="field:'firstCreateTime',width:150,align:'center'">首次受理时刻</th>
                            <th data-options="field:'firstCarDispatchedTime',width:150,align:'center'">首次派车时刻</th>
                            <th data-options="field:'eventId',width:210,align:'center'">编号</th>
                        </tr>
                    </thead>
                </table>

                <div id="tb" style="height:45px;">
                    <div>
                        <a id="sendToMapBtn" class="easyui-linkbutton" href="javascript:sendToMapFromButton();" style="width: 0px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-map'">发送到地图</a>
                        <a id="openEventInfoBtn" class="easyui-linkbutton" href="javascript:openEventInfo();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看详情</a>
                        <a id="updateRecoverEventBtn" class="easyui-linkbutton" href="javascript:updateRecoverEventInfo();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-back'">恢复调度</a>
                        <a id="lockEventBtn" class="easyui-linkbutton" href="javascript:lockEventFromButton();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-event-lock'">锁定事件</a>
                        <a id="unlockEventBtn" class="easyui-linkbutton" href="javascript:unlockEventFromButton();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-event-unlock'">解锁事件</a>
                        <a id="relevanceFirstEventBtn" class="easyui-linkbutton" href="javascript:relevanceFirstEventBtn();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-filter'">关联首次事件</a>
                        <a id="redirectAllEventBtn" class="easyui-linkbutton" href="javascript:redirect();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-redo'">查看全部事件</a>
                        

                    </div>
                </div>
            </div>
        </div>
        
        <!-- 其他窗口保持不变 -->
        <div id="eventInfoWindow" class="easyui-window" title="" style="width:600px;height:400px"
             data-options="iconCls:'icon-save',modal:true, closed: true">
            <iframe id="eventInfoIframe" name="eventInfoIframe" scrolling="auto" frameborder="0" style="width:100%;height:100%;"></iframe>
        </div>
        
        <div id="firstEventSelectWindow" class="easyui-window" title="选择关联首次事件" style="width:1240px;height:700px;overflow:hidden;"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="margin-bottom:15px;height:40px;">
                <div id="firstEventSearch" style="display: inline-block; width: Calc(100% - 50px); float: left; padding-left: 10px;padding-top: 10px; font-size: 14px;">
                    创建时间： <input id="firstStartCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="firstEndCreateTime" class="easyui-datetimebox" style="width:160px;" />
                    <span>&nbsp;呼叫原因：</span>
                    <input id="firstMajorCall" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;地址：</span>
                    <input id="firstAddress" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;任务状态：</span>
                    <select id="firstEventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                        <option value="0">全部</option>
                        <option value="7">未调度-预约</option>
                        <option value="1">未调度-落单</option>
                        <option value="6">未调度-待派</option>
                        <option value="2">已调度</option>
                        <option value="3">已完成</option>
                        <option value="4">已撤销</option>
                    </select>
                    <a id="getFirstEventListDatasBtn" class="easyui-linkbutton" href="javascript:getFirstEventListDatas();" style="width: 80px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;vertical-align: middle;">搜 索</a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 55px); position: relative;font-size: 14px; margin-top: 15px;">
                <table class="easyui-datagrid" id="dg-first-event-pages" style="width: 1230px; height: 600px;" pagination="true" singleSelect="true" rownumbers="true" fit="false"
                       data-options="onDblClickRow:selectFirstEvent">
                    <thead>
                        <tr>
                            <th field="eventId" width="180" checkbox="false" align="center">编号</th>
                            <th field="createTime" width="150" checkbox="false" align="center">创建时间</th>
                            <th field="majorCall" width="180" checkbox="false" align="center">呼叫原因</th>
                            <th field="address" width="320" align="center">地址</th>
                            <th field="callIn" width="100" align="center">呼救电话</th>
                            <th field="callInTimes" width="150" align="center">来电时间</th>
                            <th field="eventStatusStr" width="80" align="center">状态</th>
                            <th field="eventSrcName" width="80" align="center">来源</th>
                            <th field="callTypeName" width="80" align="center">类型</th>
                            <th field="contact" width="100" align="center">联系电话</th>
                            <th field="contacter" width="100" align="center">联系人</th>
                            <th field="processCount" width="80" align="center">任务总数</th>
                            <th field="runProcessCount" width="80" align="center">运行的任务数</th>
                            <th field="finishedProcessCount" width="80" align="center">完成任务数</th>
                            <th field="cancelProcessCount" width="80" align="center">撤销任务数</th>
                            <th field="eventCreateSeatUser" width="80" align="center">创建人</th>
                            <th field="eventCreateSeatCode" width="80" align="center">创建座席</th>
                            <th field="centerRemark" width="200" align="center">备注</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
        
        //关联事件页码
        var page = 1;
        var size = 20;
        
                 // ========== TreeGrid 修复函数 开始 ==========
         
         /**
          * TreeGrid列宽同步修复函数
          */
         function syncTreeGridColumnWidths() {
             setTimeout(function() {
                 try {
                     var $tg = $('#dg-event-pages');
                     
                     // 简单有效的方法：调用EasyUI自带的fixColumnSize方法
                     if ($tg.length > 0) {
                         $tg.treegrid('fixColumnSize');
                         $tg.treegrid('resize');
                         console.log('TreeGrid列宽同步完成');
                     }
                 } catch (e) {
                     console.error('TreeGrid列宽同步失败:', e);
                     // 如果fixColumnSize方法不存在，使用备用方案
                     try {
                         $tg.treegrid('resize');
                         console.log('TreeGrid resize完成');
                     } catch (e2) {
                         console.error('TreeGrid resize也失败:', e2);
                     }
                 }
             }, 50);
         }
        
                 
        
                 /**
          * 强制修复TreeGrid显示问题
          */
         function forceFixTreeGridDisplay() {
             console.log('强制修复TreeGrid显示问题');
             try {
                 var $tg = $('#dg-event-pages');
                 
                 // 简单的修复方式：重新加载当前数据
                 $tg.treegrid('reload');
                 
                 // 延时同步列宽
                 setTimeout(function() {
                     syncTreeGridColumnWidths();
                 }, 500);
         
             } catch (e) {
                 console.error('强制修复TreeGrid显示失败:', e);
             }
         }
        

        
        // ========== TreeGrid 修复函数 结束 ==========
        
        $(document).ready(function () {
            //按钮格式调整,自动计算出来的可能会出现问题。直接设置
            $("#openEventInfoBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            $("#updateRecoverEventBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            
            // 初始化时禁用锁定/解锁按钮
            $('#lockEventBtn').linkbutton('disable');
            $('#unlockEventBtn').linkbutton('disable');
            //获得开始时间,默认显示前一天
            var curDate = new Date();
            var fromTime = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
            $("#startCreateTime").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            //为所有快速时间操作设置处理函数
            $('.inAnHour,.inToday').on("click", timeClickHandler);
            
            // ========== 修改后的TreeGrid初始化 ==========
            $('#dg-event-pages').treegrid({
                loadMsg: '数据加载，请稍等.....',
                idField: 'eventId',
                treeField: 'eventName',
                parentIdField: '_parentId',
                animate: false,
                lines: false,
                nowrap: true,
                rownumbers: true,
                singleSelect: true,
                fit: true,
                fitColumns: false,
                rowStyler: function (row) {
                    // 父节点样式
                    if (row.isFirstEvent) {
                        return 'background-color:#e8f4f8; font-weight: bold;';
                    }
                    // 子节点根据状态设置样式
                    switch (row.eventStatus) {
                        case '1':
                        case '7':
                        case '6':
                        case '3':
                        case '4':
                            return 'background-color:#dad6d557;';//灰色
                        case "2":
                            return 'background-color:#48c400;';//绿色
                    }
                },
                onClickRow: function (rowData) {
                    if (rowData.eventStatus == '1' || rowData.eventStatus == '7' || rowData.eventStatus == '6') {
                        $('#updateRecoverEventBtn').linkbutton("disable");
                    } else {
                        $('#updateRecoverEventBtn').linkbutton("enable");
                    }
                    
                    // 控制锁定/解锁按钮状态
                    updateLockButtonState(rowData);
                },
                onDblClickRow: function(row) {
                    // 检查是否有经纬度
                    if (!row.lng || !row.lat) {
                        $.messager.alert('提示', '该事件没有经纬度信息，无法在地图上显示', 'warning');
                        return;
                    }
                    
            
                    try {
                        window.parent.gProxy.setPlaceBylonAndLat(row.eventId); //地图显示事件位置
                    } catch (e) {
                        try {
                            window.parent.setPlaceBylonAndLat(row.eventId); //地图显示事件位置
                        } catch (err) {
                            $.messager.alert('错误', '发送到地图失败: ' + err.message, 'error');
                        }
                
                    }
                },
                onBeforeExpand: function(row) {
                    return true;
                },
                onExpand: function(row) {
                    setTimeout(function() {
                        syncTreeGridColumnWidths();
                    }, 100);
                },
                onBeforeCollapse: function(row) {
                    return true;
                },
                onCollapse: function(row) {
                    setTimeout(function() {
                        syncTreeGridColumnWidths();
                    }, 100);
                },
                onLoadSuccess: function(data) {
                    syncTreeGridColumnWidths();
                },
                onResizeColumn: function(field, width) {
                    syncTreeGridColumnWidths();
                }
            });
            // 分页控件将在首次数据加载成功后初始化
            // 页面加载完成后立即加载第一页数据
            getDgEventListDatas();
            eventListAutoRefreshIntervalTimeout();
        });

        /**
         * 格式化受理次数
         * @param {string} value - 受理次数值
         * @param {Object} row - 行数据
         * @returns {string} 格式化后的受理次数
         */
        function formatSerialNo(value, row) {
            if (value === 0 || value === '0' || !value) {
                return '首次受理';
            } else {
                return '第' + value + '次受理';
            }
        }

        /**
         * 将 firstEventId 数据重组为 TreeGrid 需要的 _parentId 结构
         * @param {Array} flatData - 原始平铺数据
         * @returns {Array} 重组后的树形数据
         */
        function buildTreeDataFromFirstEventId(flatData) {
            // 如果没有数据，返回空数组
            if (!flatData || flatData.length === 0) {
                return [];
            }
            
            var treeData = [];
            var firstEventMap = {}; // 存储首次事件作为父节点
            var childEvents = []; // 存储关联事件作为子节点
            
            // 第一遍：分离首次事件和关联事件
            for (var i = 0; i < flatData.length; i++) {
                var item = flatData[i];
                
                // 构建基本事件数据
                var eventData = {
                    eventId: item.eventId,
                    serialNo: item.serialNo || 0, // 受理次数
                    eventStatus: item.eventStatus,
                    eventStatusStr: item.eventStatusStr,
                    callIn: item.callIn,
                    callInTimes: item.callInTimes,
                    createTime: item.createTime,
                    majorCall: item.majorCall ? item.majorCall.replace("_", " ") : "",
                    // 修复eventName构建逻辑，确保有完整信息
                    eventName: (item.address || "未知地址") + " + " + (item.patientName || "无名氏") + " + " + (item.majorCall ? item.majorCall.replace("_", " ") : "未知原因"),
                    taskStation: item.taskStation,
                    eventSrcCode: item.eventSrcCode,
                    eventSrcName: item.eventSrcName,
                    callTypeCode: item.callTypeCode,
                    lng: item.lng,
                    lat: item.lat,
                    callTypeName: item.callTypeName,
                    contact: item.contact,
                    contacter: item.contacter,
                    addressWait: item.addressWait,
                    address: item.address,
                    createUser: item.createUser,
                    eventCreateSeatCode: item.eventCreateSeatCode,
                    eventCreateSeatUserId: item.eventCreateSeatUserId,
                    eventCreateSeatUserName: item.eventCreateSeatUserName,
                    eventCreateSeatUser: item.eventCreateSeatUser,
                    processCount: item.processCount,
                    runProcessCount: item.runProcessCount,
                    finishedProcessCount: item.finishedProcessCount,
                    cancelProcessCount: item.cancelProcessCount,
                    centerRemark: item.centerRemark,
                    revocationSchedulingReason: item.revocationSchedulingReason,
                    eventType: item.eventType,
                    eventTypeStr: item.eventType == '1' ? '重大事件' : '普通事件',
                    firstSeatUser: item.firstSeatUser,
                    firstCreateTime: item.firstCreateTime,
                    firstCarDispatchedTime: item.firstCarDispatchedTime,
                    isLock: item.isLock,
                    lockSeatId: item.lockSeatId,
                    lockSeatCode: item.lockSeatCode,
                    lockSeatUserId: item.lockSeatUserId,
                    lockSeatUserName: item.lockSeatUserName,
                    firstEventId: item.firstEventId,
                    patientName: item.patientName // 添加患者姓名字段
                };
                

                
                if (item.firstEventId && item.firstEventId !== item.eventId) {
                    // 这是关联事件，设置 _parentId 指向首次事件
                    eventData._parentId = item.firstEventId;
                    childEvents.push(eventData);
                } else {
                    // 这是首次事件或独立事件
                    eventData._parentId = null;
                    if (item.firstEventId) {
                        // 标记为首次事件
                        eventData.isFirstEvent = true;
                        eventData.eventName = "[首次事件] " + eventData.eventName;
                    }
                    firstEventMap[item.eventId] = eventData;
                }
            }
            
            // 第二遍：将首次事件添加到结果数组
            for (var eventId in firstEventMap) {
                treeData.push(firstEventMap[eventId]);
            }
            
            // 第三遍：将关联事件添加到结果数组
            for (var j = 0; j < childEvents.length; j++) {
                var childEvent = childEvents[j];
                // 检查父节点是否存在，如果不存在则作为独立事件
                if (firstEventMap[childEvent._parentId]) {
                    childEvent.eventName = "[关联受理] " + childEvent.eventName;
                    treeData.push(childEvent);
                } else {
                    // 父节点不存在，作为独立事件处理
                    childEvent._parentId = null;
                    treeData.push(childEvent);
                }
            }
            

            return treeData;
        }

        //获得事件列表数据
        function getDgEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }
            var startCreateTimeVal = $('#startCreateTime').datetimebox('getValue');
            var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");
            var endCreateTimeVal = $('#endCreateTime').datetimebox('getValue');
            var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");
            //打开进度条：
            $('#dg-event-pages').treegrid('loading');//打开等待div
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "stationName": $('#stationName').val(),
                "callPhone": $('#callPhone').val(),
                "eventStatusList": $('#eventStatus').combobox('getValues'),
                "majorCall": $('#majorCall').val(),
                "address": $('#address').val(),
            };
            getFirstEventPages(params, function (data) {
                // 重组数据：将 firstEventId 转换为 _parentId 结构
                var treeData = buildTreeDataFromFirstEventId(data.content);
                
                // 加载树形数据
                $('#dg-event-pages').treegrid('loadData', { 
                    total: data.total, 
                    rows: treeData 
                });
                
                // ========== 添加修复调用 ==========
                // 数据加载完成后，同步列宽
                syncTreeGridColumnWidths();
                
                // 初始化或更新分页控件
                var pg = $("#dg-event-pages").treegrid("getPager");
                if (pg && pg.length > 0) {
                    // 检查是否已经初始化过分页控件
                    var paginationExists = pg.find('.pagination').length > 0;
                    if (!paginationExists) {
                        // 首次初始化分页控件
                        console.log('首次初始化TreeGrid分页控件');
                        pg.pagination({
                            total: data.total,
                            pageSize: data.pageSize,
                            pageNumber: data.pageNum,
                            pageList: [10, 20, 30, 40, 50],
                            onBeforeRefresh: function () {
                                var opts = $(this).pagination('options');
                                console.log('TreeGrid before refresh, pageNumber:', opts.pageNumber, 'pageSize:', opts.pageSize);
                                getDgEventListDatas(opts.pageNumber, opts.pageSize);
                            },
                            onRefresh: function (pageNumber, pageSize) {
                                console.log('TreeGrid onRefresh pageNumber:', pageNumber, 'pageSize:', pageSize);
                                getDgEventListDatas(pageNumber, pageSize);
                            },
                            onChangePageSize: function (pageSize) {
                                var opts = $(this).pagination('options');
                                console.log('TreeGrid pagesize changed to:', pageSize, 'pageNumber:', opts.pageNumber);
                                getDgEventListDatas(opts.pageNumber, pageSize);
                            },
                            onSelectPage: function (pageNumber, pageSize) {
                                console.log('TreeGrid onSelectPage pageNumber:', pageNumber, 'pageSize:', pageSize);
                                getDgEventListDatas(pageNumber, pageSize);
                            }
                        });
                    } else {
                        // 已初始化，只更新数据
                        console.log('更新TreeGrid分页控件数据');
                        pg.pagination({
                            total: data.total,
                            pageSize: data.pageSize,
                            pageNumber: data.pageNum
                        });
                    }
                }
                $('#dg-event-pages').treegrid('loaded');//关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-event-pages').treegrid('loaded');//关闭loding进度条；
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }

        // ========== 以下保持原有函数不变 ==========

        function openEventInfo() {
            //获得当前选中行
            var rows = $('#dg-event-pages').treegrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件进行查看!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件进行查看!', 'error');
                return;
            }
            var title = "事件详情" + "(" + rows[0].eventId + ")"
            $('#eventInfoWindow').window({
                width: 1440,
                height: 950,
                modal: true,
                title: title,
                collapsible: false,
                minimizable: false,
                maximizable: false,
                draggable: false,//不能拖拽
                resizable: false,
            });
            href = "eventInfoTabs.html?eventId=" + rows[0].eventId;
            $('#eventInfoIframe').attr('src', href);
            $('#eventInfoWindow').window('open');
        }

        function updateRecoverEventInfo() {
            var rows = $('#dg-event-pages').treegrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件恢复调度!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件恢复调度!', 'error');
                return;
            }
            if(!canOperateEvent(rows[0].isLock,rows[0].lockSeatUserId,rows[0].lockSeatId)){
                $.messager.alert('提示', '事件被 ' + (rows[0].lockSeatCode || '') + ' (' + (rows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                return;
            }
            $.messager.confirm("提示", "是否恢复调度进行调度操作？", function (r) {
                if (r) {
                    showProcess(true, '温馨提示', '正在进行恢复事件调度操作，请稍后...');
                    updateRecoverEvent(rows[0].eventId, function (data) {
                        showProcess(false);
                    }, function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('异常', '异常信息：' + errMsg);
                    });
                }
            });
        }

        function timeClickHandler() {
            var from = $(this).parent().children('input:nth-of-type(1)');
            var to = $(this).parent().children('input:nth-of-type(2)');
            var now = new Date();
            var fromTime = new Date();
            var hours = 24;
            if ($(this).hasClass('inAnHour')) {
                hours = 1;
            }
            fromTime.setHours(fromTime.getHours() - hours);
            from.val(fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            to.val(null);
            var parentId = $(this).parent().attr('id');
            easyUiDateTimeSet();
            //自动搜索
            getDgEventListDatas(1, 20);
        }

        function easyUiDateTimeSet() {
            $('.easyui-datetimebox').each(function () {
                var e = $(this);
                e.datetimebox('setValue', e.val());
            });
        }

        //定时刷新任务开关
        var _eventListAutoRefreshInterval = true;
        /** 设置事件列表刷新计时器,定时刷新事件列表 */
        function eventListAutoRefreshIntervalTimeout() {
            clearTimeout(window.eventListAutoRefreshInterval);
            if (_eventListAutoRefreshInterval) {
                window.eventListAutoRefreshInterval = null;
                if (_token != "") {
                    if ($('#autoRefreshList').is(':checked')) {//选中才自动刷新
                        getDgEventListDatas(null, null);
                    }
                }
                window.eventListAutoRefreshInterval = setTimeout(eventListAutoRefreshIntervalTimeout, 60000);
            }
        }

        /** 重定向到事件记录 */
        function redirect() {
            querySysConf('event_list_url',
                function (data) {
                    try {
                        window.parent.gProxy.openBrowser(data);
                    } catch (e) {
                        window.parent.parent.gProxy.openBrowser(data);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取事件记录页面配置失败，异常信息：' + errMsg);
                });
            }

         /** 事件监控表格的状态颜色格 */
        function setDgEventStatusIconColumnFormatter(value, row, index) {
            if(row.isLock && row.isLock == '1'){//锁了，那么只能这个座席操作，其他坐席不能操作
                var lockInfo = '已锁定';
                if(row.lockSeatCode || row.lockSeatUserName) {
                    lockInfo = '由座席 ' + (row.lockSeatCode || '') + ' (' + (row.lockSeatUserName || '') + ') 锁定';
                }
                //如果锁定事件的座席id和当前座席id一致，且锁定事件的座席用户id和当前座席用户id一致，那么显示绿色的锁定的图片
                if((row.lockSeatId == _pSeat.id && row.lockSeatUserId == _pSeat.userId)){
                    return '<img src="./style/img/lock_event_green.png" title="' + lockInfo + '" alt="事件已锁定"/>';
                }else if(row.lockSeatId == _pSeat.id && row.lockSeatUserId != _pSeat.userId){
                    //如果锁定事件的座席id和当前座席id一致，且锁定事件的座席用户id不一致，那么显示蓝色的锁定的图片
                    return '<img src="./style/img/lock_event_blue.png" title="' + lockInfo + '" alt="事件已锁定"/>';
                }else{  
                    //如果锁定事件的座席id和当前座席id不一致，那么显示红色的锁定的图片
                    return '<img src="./style/img/lock_event_red.png" title="' + lockInfo + '" alt="事件已锁定"/>';
                }
            }else{
                //0-普通事件 1-重大事件
                switch (row.eventType) {
                    case '1':
                        return '<img src="./style/img/120_chexiao_ic.png"/>';
                    case '0':
                        switch (row.eventStatus) {
                            case '1'://1：落单-未调度（接警后初步处理，接警员接听急救电话，完成现场信息采集（地址、病情、联系方式）后，将派车任务转交至调度组的状态。）
                                return '<img src="./style/img/120_weidiaodu_ic.png"/>';
                            case '7'://预约（事件尚未进入实时调度流程，处于 "预登记" 状态）；
                                return '<img src="./style/img/120_chexiao_ic.png"/>';
                            case '6'://挂起-未调度（临时搁置状态，因特殊原因（无可用车辆、地址不明确、需二次确认）暂时暂停处理的事件状态。）
                                return '<img src="./style/img/120_chexiao_ic.png"/>';
                            case "2"://已调度
                                return '<img src="./style/img/120_yidiaodu_ic.png"/>';
                            case "3"://已完成
                                return '<img src="./style/img/120_yiwancheng_ic.png"/>';
                            case "4"://已撤销
                                return '<img src="./style/img/120_yiwancheng_ic.png"/>';
                        }
                }
            }
        }

        // ========== 锁定/解锁事件按钮相关函数 ==========

        /**
         * 更新锁定按钮状态
         * @param {Object} rowData - 行数据
         */
        function updateLockButtonState(rowData) {
            if (!rowData) {
                // 没有选中行，禁用所有按钮
                $('#lockEventBtn').linkbutton('disable');
                $('#unlockEventBtn').linkbutton('disable');
                return;
            }
            
            // 根据锁定状态控制按钮
                if (rowData.isLock && rowData.isLock == '1') {
                // 事件已锁定
                $('#lockEventBtn').linkbutton('disable');
                
                // 检查是否有权限解锁
                if (_pSeat && _pSeat.directorPermission && _pSeat.directorPermission == '1') {
                    // 主任座席可以解锁所有事件
                    $('#unlockEventBtn').linkbutton('enable');
                } else if (canOperateEvent(rowData.isLock, rowData.lockSeatUserId, rowData.lockSeatId)) {
                    // 有权限解锁
                    $('#unlockEventBtn').linkbutton('enable');
                } else {
                    // 无权限解锁
                    $('#unlockEventBtn').linkbutton('disable');
                }
            } else {
                // 事件未锁定
                $('#lockEventBtn').linkbutton('enable');
                $('#unlockEventBtn').linkbutton('disable');
            }
        }

        /**
         * 从按钮锁定事件
         */
        function lockEventFromButton() {
            var rowData = $('#dg-event-pages').treegrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请先选择要锁定的事件', 'warning');
                return;
            }
            
            // 检查事件是否已锁定
            if (rowData.isLock && rowData.isLock == '1') {
                $.messager.alert('提示', '该事件已经被锁定', 'warning');
                return;
            }
            
            $.messager.confirm('确认', '是否锁定事件"' + rowData.eventName + '"？', function(r) {
                if (r) {
                    updateEventLock({
                        eventId: rowData.eventId,
                        isLock: '1',
                        lockSeatId: _pSeat.id,
                        lockSeatCode: _pSeat.seatId
                    }, function(data) {
                        $.messager.alert('提示', '锁定事件成功', 'info');
                        // 刷新事件列表
                        getDgEventListDatas();
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', '锁定事件失败: ' + errMsg, 'error');
                    });
                }
            });
        }

        /**
         * 从按钮解锁事件
         */
        function unlockEventFromButton() {
            var rowData = $('#dg-event-pages').treegrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请先选择要解锁的事件', 'warning');
                return;
            }
            
            // 检查事件是否已锁定
            if (!rowData.isLock || rowData.isLock != '1') {
                $.messager.alert('提示', '该事件未被锁定', 'warning');
                return;
            }
            
            if (!(_pSeat && _pSeat.directorPermission && _pSeat.directorPermission == '1')) {
                // 非主任座席需要检查权限
                if (!canOperateEvent(rowData.isLock, rowData.lockSeatUserId, rowData.lockSeatId)) {
                    $.messager.alert('提示', '事件被 ' + (rowData.lockSeatCode || '') + ' (' + (rowData.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                    return;
                }
            }
            
            $.messager.confirm('确认', '是否解锁事件"' + rowData.eventName + '"？', function(r) {
                if (r) {
                    updateEventLock({
                        eventId: rowData.eventId,
                        isLock: '0',
                        lockSeatId: _pSeat.id,
                        lockSeatCode: _pSeat.seatId
                    }, function(data) {
                        $.messager.alert('提示', '解锁事件成功', 'info');
                        // 刷新事件列表
                        getDgEventListDatas();
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', '解锁事件失败: ' + errMsg + '，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                    });
                }
            });
        }
        
        /**
         * 关联首次事件按钮点击事件
         */
        function relevanceFirstEventBtn() {
            // 检查是否选中了事件
            var rows = $('#dg-event-pages').treegrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件进行关联首次事件!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件进行关联首次事件!', 'error');
                return;
            }
            
            // 保存当前选中的事件ID
            window._currentEventForFirstEvent = rows[0];
            
            //打开首次事件选择窗口
            $('#firstEventSelectWindow').window('open');
            getFirstEventListDatas(1, 20);
        }
        
        /**
         * 获得首次事件列表数据
         * @param pageNum
         * @param pageSize
         */
        function getFirstEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = size;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = size;
            }
            var startCreateTimeVal = $('#firstStartCreateTime').datetimebox('getValue');
            var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");
            var endCreateTimeVal = $('#firstEndCreateTime').datetimebox('getValue');
            var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");
            //打开进度条：
            $('#dg-first-event-pages').datagrid('loading');//打开等待div
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "majorCall": $("#firstMajorCall").val(),
                "address": $('#firstAddress').val(),
                "eventStatusList": $('#firstEventStatus').combobox('getValues'),
            };
            getFirstEventPages(params, function (data) {
                $('#dg-first-event-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-first-event-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            eventId: data.content[i].eventId,
                            eventStatus: data.content[i].eventStatus,
                            eventStatusStr: data.content[i].eventStatusStr,
                            callIn: data.content[i].callIn,
                            callInTimes: data.content[i].callInTimes,
                            createTime: data.content[i].createTime,
                            majorCall: data.content[i].majorCall.replace("_", " "),
                            eventSrcCode: data.content[i].eventSrcCode,
                            eventSrcName: data.content[i].eventSrcName,
                            callTypeCode: data.content[i].callTypeCode,
                            lng: data.content[i].lng,
                            lat: data.content[i].lat,
                            callTypeName: data.content[i].callTypeName,
                            contact: data.content[i].contact,
                            contacter: data.content[i].contacter,
                            addressWait: data.content[i].addressWait,
                            address: data.content[i].address,
                            createUser: data.content[i].createUser,
                            eventCreateSeatCode: data.content[i].eventCreateSeatCode,
                            eventCreateSeatUserId: data.content[i].eventCreateSeatUserId,
                            eventCreateSeatUserName: data.content[i].eventCreateSeatUserName,
                            eventCreateSeatUser: data.content[i].eventCreateSeatUser,
                            processCount: data.content[i].processCount,
                            runProcessCount: data.content[i].runProcessCount,
                            finishedProcessCount: data.content[i].finishedProcessCount,
                            cancelProcessCount: data.content[i].cancelProcessCount,
                            centerRemark: data.content[i].centerRemark,
                            callRegionId: data.content[i].callRegionId,
                            callRegionName: data.content[i].callRegionName
                        }
                    });
                }
                $("#dg-first-event-pages").datagrid({
                    rowStyler: function (index,row) {
                            switch (row.eventStatus) {
                                case '1':
                                    return 'background-color:#dad6d557;';//灰色
                                case "2":
                                    return 'background-color:#f9a296ad;';//红色
                                case "3":
                                    return 'background-color:#dad6d557;';//灰色
                                case "4":
                                    return 'background-color:#dad6d557;';//灰色
                            }
                        },
                });
                setFirstEventPage(data.total || 0, pageNum)
                $('#dg-first-event-pages').datagrid('loaded');//关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-first-event-pages').datagrid('loaded');//关闭loding进度条；
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }

        /**
         * 设置首次事件分页
         * @param total
         * @param pageNum
         */
        function setFirstEventPage(total, pageNum) {
            var pg = $("#dg-first-event-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total,
                    pageSize: size,
                    pageNumber: pageNum,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function (pageNumber, pageSize) {
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function (pageSize) {
                        page = 1
                        size = pageSize
                        getFirstEventListDatas(page, pageSize);
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getFirstEventListDatas(pageNumber, pageSize);
                    }
                });
            }
        }

        /**
         * 选择首次事件（双击事件）
         * @param index 行索引
         * @param row 行数据
         */
        function selectFirstEvent(index, row) {
            // 检查是否有当前选中的事件
            if (!window._currentEventForFirstEvent) {
                $.messager.alert('错误', '未找到要关联的事件', 'error');
                return;
            }

            // 判断不能关联自己
            if (window._currentEventForFirstEvent.eventId === row.eventId) {
                $.messager.alert('提示', '不能将事件关联为自身，请选择其他事件！', 'warning');
                return;
            }
            
            // 调用绑定首次事件接口
            bindFirstEvent({
                eventId: window._currentEventForFirstEvent.eventId,
                firstEventId: row.eventId
            }, function(data) {
                // 关闭窗口
                $('#firstEventSelectWindow').window('close');
                // 清空当前选中的事件
                window._currentEventForFirstEvent = null;
                // 刷新事件列表
                getDgEventListDatas();
                $.messager.alert('提示', '关联首次事件成功', 'info');
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '关联首次事件失败：' + errMsg, 'error');
            });
        }
        
        /**
         * 关联首次事件到当前事件
         * @param currentEventId 当前事件ID
         * @param firstEventId 首次事件ID
         */
        function associateFirstEventToCurrentEvent(currentEventId, firstEventId) {
            // 这里需要调用后端接口来关联首次事件
            // 假设有一个 updateEventFirstEventId 接口
            var params = {
                eventId: currentEventId,
                firstEventId: firstEventId
            };
            
            // 调用更新事件接口，添加首次事件ID字段
            updateEventFirstEventId(params, function(data) {
                $.messager.alert('提示', '关联首次事件成功', 'info');
                // 刷新事件列表
                getDgEventListDatas();
                // 清空当前选中的事件
                window._currentEventForFirstEvent = null;
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '关联首次事件失败：' + errMsg, 'error');
            });
        }

        /**
         * 从按钮发送事件到地图
         */
        function sendToMapFromButton() {
            var rowData = $('#dg-event-pages').treegrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请先选择要在地图上显示的事件', 'warning');
                return;
            }
            
            // 检查是否有经纬度
            if (!rowData.lng || !rowData.lat) {
                $.messager.alert('提示', '该事件没有经纬度信息，无法在地图上显示', 'warning');
                return;
            }
            
            try {
                window.parent.gProxy.setPlaceBylonAndLat(rowData.eventId); //地图显示事件位置
            } catch (e) {
                try {
                    window.parent.setPlaceBylonAndLat(rowData.eventId); //地图显示事件位置
                } catch (err) {
                    $.messager.alert('错误', '发送到地图失败: ' + err.message, 'error');
                }
          
            }

        }
    </script>
</body>
</html>
