<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/eventReport.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
                color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important;
        }
        #wavPlayWindow{
            overflow: initial;
        }
        .datebox {
            height: 1.7rem; /* 设置 DateTimeBox 输入框的高度 */
        }
        .clear-fix .datebox{
            height: 2.1rem; /* 设置 DateTimeBox 输入框的高度 */
        }
    </style>
</head>
<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">满意度回访<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;">
                    受理时间： <input id="eventTimeStart" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="eventTimeEnd" class="easyui-datetimebox" style="width:200px;" />
                    回访时间： <input id="visitTimeStart" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="visitTimeEnd" class="easyui-datetimebox" style="width:200px;" />
                    <span>&nbsp;回访类型：</span>
                    <select id="visitTypeSelect" class="easyui-combobox" name="visitTypeSelect" style="width:120px;">
                        <option value="">全部</option>
                        <option value="-1">未回访</option>
                        <option value="1">短信回访</option>
                        <option value="2">电话回访</option>
                        <option value="3">无效任务</option>
                    </select>
                    <span>&nbsp;回访状态：</span>
                    <select id="visitStatusSelect" class="easyui-combobox" name="visitStatusSelect" style="width:150px;">
                        <option value="">全部</option>
                        <option value="1">已填写</option>
                        <option value="2">未填写/未接通</option>
                        <option value="3">短信发送失败</option>
                        <option value="4">短信发送中</option>
                    </select>
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getEventList();">
                        <i class="fa fa-search"></i>&nbsp;查询
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="visit-event-list" style="height: Calc(100% - 100px)"
                       pagination="true" singleSelect="true" checkOnSelect="false" rownumbers="true" fit="true" toolbar="#tb">
                    <thead>
                        <tr>
                            <th field="ck" checkbox="true"></th>
                            <th field="eventTime" width="9%" align="center">受理时间</th>
                            <th field="eventId" width="12%" align="center">事件编号</th>
                            <th field="callType" width="6%" align="center">呼叫类型</th>
                            <th field="patientName" width="6%" align="center">患者姓名</th>
                            <th field="callIn" width="7%" align="center">主叫号码</th>
                            <th field="majorCall" width="12%" align="center">呼叫原因</th>
                            <th field="visitTypeName" width="6%" align="center">回访类型</th>
                            <th field="visitTime" width="9%" align="center">回访时间</th>
                            <th field="visitStatusName" width="6%" align="center">回访状态</th>
                            <th field="address" width="12%" align="center">地址</th>
                            <th field="duration" width="5%" align="center">通话时长(秒)</th>
                            <th field="seatCode" width="8%" align="center">座席号</th>
                            <th field="seatUser" width="8%" align="center">座席调度员</th>
                        </tr>
                    </thead>
                </table>
                <div id="tb" style="height:45px;">
                    <div>
                        <input id="multiSelect" type="checkbox"/><span style="font-size: 14px; font-weight: bold;"> 多选</span>
                        <a id="openEventInfoBtn" class="easyui-linkbutton" href="javascript:openEventInfo();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看事件</a>
                        <a id="selectVisitBtn" class="easyui-linkbutton" href="javascript:doSelectVisit();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-search'">查看回访</a>
                        <a id="callVisitBtn" class="easyui-linkbutton" href="javascript:doCallVisit();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-softphone-dialcall'">电话回访</a>
                        <a id="msgVisitBtn" class="easyui-linkbutton" href="javascript:doMsgVisit();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-save'">短信回访</a>
                        <a id="invalidVisitBtn" class="easyui-linkbutton" href="javascript:doInvalidVisit();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-clear'">无效回访</a>
                        <a id="delInvalidVisitBtn" class="easyui-linkbutton" href="javascript:doDelInvalidVisit();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-undo'">解除无效</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- 回访单窗口 -->
        <div id="visitWindow" class="easyui-window" title="电话回访" style="width:850px; height:800px; overflow:auto;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <div style="padding: 10px 0 0 10px; display: flex; justify-content: space-between;">
                <div style="width: 33%;">
                    <label style="display: inline-block;">事件编码：</label>
                    <span id="visitEventCode"></span>
                </div>
                <div style="width: 33%;">
                    <label style="display: inline-block;">事件时间：</label>
                    <span id="visitEventTime"></span>
                </div>
                <div style="width: 33%;">
                    <label style="display: inline-block;">呼叫类型：</label>
                    <span id="visitCallType"></span>
                </div>
            </div>
            <div style="padding: 10px 0 0 10px; display: flex; justify-content: space-between;">
                <div style="width: 33%;">
                    <label style="display: inline-block;">呼叫原因：</label>
                    <span id="visitCallReason"></span>
                </div>
                <div style="width: 33%;">
                    <label style="display: inline-block;">患者姓名：</label>
                    <span id="visitPatientName"></span>
                </div>
                <div style="width: 33%;">
                    <label style="display: inline-block;">主叫号码：</label>
                    <span id="visitPhone"></span>
                </div>
            </div>
            <div style="padding: 10px 0 0 10px;" id="visitIsNotThroughShow">
                <div style="width: 33%;" id="">
                    <label style="display: inline-block;">是否未接通电话：</label>
                    <input type="checkbox" id="visitIsNotThrough"/>
                </div>
            </div>
            <hr/>
            <input id="visitFormId" hidden>
            <div id="visitForm" style="margin-bottom: 50px">
            </div>
            <p style="position: absolute; bottom: 10px; width: 100%; text-align: center; margin: 0;">
                <a class="easyui-linkbutton" id="visitRecordSaveBtn" href="javascript:void(0)" onclick="saveVisitRecord()"
                   style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">提交</a>
            </p>
        </div>
    </div>
    <div id="eventInfoWindow" class="easyui-window" title="" style="width:600px;height:400px"
         data-options="iconCls:'icon-save',modal:true, closed: true">
        <iframe id="eventInfoIframe" name="eventInfoIframe" scrolling="auto" frameborder="0" style="width:100%;height:100%;"></iframe>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat;
        var page = 1;
        var size = 20;
        var largerList = [] // 重大事件列表
        $(document).ready(function () {
            $('#visit-event-list').datagrid('hideColumn', 'ck');
            $('#visit-event-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
                onClickRow: function(index, row) {
                    if(!$("#multiSelect").is(":checked")){
                        $("#openEventInfoBtn").linkbutton("enable");
                        if(row.visitStatus === "1"){
                            $("#selectVisitBtn").linkbutton("enable");
                            $("#callVisitBtn").linkbutton("disable");
                            $("#msgVisitBtn").linkbutton("disable");
                            $("#invalidVisitBtn").linkbutton("disable");
                            $("#delInvalidVisitBtn").linkbutton("disable");
                        }else{
                            if(row.visitType === "3"){
                                $("#selectVisitBtn").linkbutton("disable");
                                $("#callVisitBtn").linkbutton("disable");
                                $("#msgVisitBtn").linkbutton("disable");
                                $("#invalidVisitBtn").linkbutton("disable");
                                $("#delInvalidVisitBtn").linkbutton("enable");
                            }else{
                                $("#selectVisitBtn").linkbutton("disable");
                                $("#callVisitBtn").linkbutton("enable");
                                $("#msgVisitBtn").linkbutton("enable");
                                $("#invalidVisitBtn").linkbutton("enable");
                                $("#delInvalidVisitBtn").linkbutton("disable");
                            }
                        }
                    }
                }
            });
            //分页控件
            var pg = $("#visit-event-list").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    size: 20,
                    pageNumber: 1,
                    pageList: [5, 10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getEventList();
                    }
                });
            }
            $("#visitForm").on("change", "input[name$='_option']", function() {
                let text = $(this).attr("text");
                let _textarea = $(this).closest("div").find("textarea");
                console.info("_textarea", _textarea)
                if(text == "true"){
                    _textarea.show();
                }else{
                    _textarea.hide();
                }
            });
            $("#visitIsNotThrough").on("change", function () {
                if ($(this).is(":checked")) {
                    $("#visitForm").hide();
                } else {
                    $("#visitForm").show();
                }
            });
            $("#multiSelect").on("change", function () {
                if ($(this).is(":checked")) {
                    $("#openEventInfoBtn").linkbutton("disable");
                    $("#selectVisitBtn").linkbutton("disable");
                    $("#callVisitBtn").linkbutton("disable");
                    $("#invalidVisitBtn").linkbutton("enable");
                    $("#delInvalidVisitBtn").linkbutton("enable");
                    $("#msgVisitBtn").linkbutton("enable");
                    $('#visit-event-list').datagrid({
                        singleSelect: false,
                        checkOnSelect: true
                    });
                    $('#visit-event-list').datagrid('showColumn', 'ck');
                } else {
                    $('#visit-event-list').datagrid({
                        singleSelect: true,
                        checkOnSelect: true
                    });
                    $('#visit-event-list').datagrid('hideColumn', 'ck');
                }
                $('#visit-event-list').datagrid('clearSelections');
            });
            getEventList();
        })
        //获得列表数据
        function getEventList() {
            //打开进度条：
            $('#visit-event-list').datagrid('loading');//打开等待div
            var params = {
                "page": page,
                "size": size,
                'eventStartTime': $("#eventTimeStart").val(),
                'eventEndTime': $("#eventTimeEnd").val(),
                'visitStartTime': $("#visitTimeEnd").val(),
                'visitEndTime': $("#visitTimeEnd").val(),
                'visitType': $("#visitTypeSelect").val(),
                'visitStatus': $("#visitStatusSelect").val()
            };
            selectEventVisitPage(params, function (data) {
                largerList = data.records
                $('#visit-event-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.records.length; i++) {
                    let visitStatusName = "";
                    let visitTypeName = "";
                    let visitStatus = data.records[i].visitStatus;
                    let visitType = data.records[i].visitType;
                    if(visitStatus === "1"){
                        visitStatusName = "已填写";
                    }else if(visitStatus === "2"){
                        if(visitType === "1"){ // 短信回访
                            visitStatusName = "未填写";
                        }else if(visitType === "2"){ // 电话回访
                            visitStatusName = "未接通";
                        }
                    }else if(visitStatus === "3"){
                        visitStatusName = "发送失败";
                    }else if(visitStatus === "4"){
                        visitStatusName = "发送中";
                    }
                    if(visitType === "1"){
                        visitTypeName = "短信回访";
                    }else if(visitType === "2"){
                        visitTypeName = "电话回访";
                    }else if(visitType === "3"){
                        visitTypeName = "无效任务";
                    }
                    $('#visit-event-list').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            eventId: data.records[i].eventId,
                            eventTime: data.records[i].eventTime, // 上报时间
                            callType: data.records[i].callType, // 上报类别
                            patientName: data.records[i].patientName, // 上报类别
                            callIn: data.records[i].callIn,// 汇报内容
                            address: data.records[i].address,//伤员人数
                            majorCall: data.records[i].majorCall,// 发生时间
                            duration: data.records[i].duration, //事件类型
                            seatCode: data.records[i].seatCode, // 事件类型
                            seatUser: data.records[i].seatUser, // 事件等级
                            eventCode: data.records[i].eventCode,// 事件编码
                            visitRecordId: data.records[i].visitRecordId,
                            visitStatus: visitStatus,
                            visitStatusName: visitStatusName,
                            visitType: visitType,
                            visitTypeName: visitTypeName,
                            visitTime: data.records[i].visitTime
                        }
                    });
                }
                $("#visit-event-list").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    size: data.size,
                    pageNumber: data.current,
                });
                $('#visit-event-list').datagrid('loaded');//关闭loding进度条；

            }, function (e, url, errMsg) {
                $('#visit-event-list').datagrid('loaded');//关闭loding进度条；
            });
        }
        
        /**
         * 无效回访任务
         */
        function doInvalidVisit() {
            let rows = $('#visit-event-list').treegrid('getSelections');
            rows = rows.filter(e => e.visitType !== "3" && e.visitStatus !== "1");
            if(rows.length === 0){
                return;
            }
            let eventIds = rows.map(e => e.eventId);
            $.messager.confirm('确认', '是否设置为无效任务？', function(r) {
                if (r) {
                    invalidVisit({
                        eventIds: eventIds
                    }, function(data) {
                        $.messager.alert('提示', '操作成功', 'info');
                        // 刷新事件列表
                        getEventList();
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', errMsg, 'error');
                    });
                }
            });
        }

        /**
         * 解除无效回访任务
         */
        function doDelInvalidVisit() {
            let rows = $('#visit-event-list').treegrid('getSelections');
            rows = rows.filter(e => e.visitType === "3");
            if(rows.length === 0){
                return;
            }
            let eventIds = rows.map(e => e.eventId);
            $.messager.confirm('确认', '是否解除无效任务？', function(r) {
                if (r) {
                    delInvalidVisit({
                        eventIds: eventIds
                    }, function(data) {
                        $.messager.alert('提示', '操作成功', 'info');
                        // 刷新事件列表
                        getEventList();
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', errMsg, 'error');
                    });
                }
            });
        }

        /**
         * 短信回访
         */
        function doMsgVisit() {
            let rows = $('#visit-event-list').treegrid('getSelections');
            rows = rows.filter(e => e.visitType !== "3" && e.visitStatus !== "1");
            if(rows.length === 0){
                $.messager.alert('提示', '请至少选中一条可短信回访的事件', 'warning');
                return;
            }
            let eventIds = rows.map(e => e.eventId);
            $.messager.confirm('确认', '是否发送短信回访？', function(r) {
                if (r) {
                    msgVisit({
                        eventIds: eventIds
                    }, function(data) {
                        $.messager.alert('提示', '操作成功', 'info');
                        // 刷新事件列表
                        getEventList();
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', errMsg, 'error');
                    });
                }
            });
        }

        /**
         * 查看回访
         */
        function doSelectVisit(){
            let rowData = $('#visit-event-list').datagrid('getSelected');
            if(!rowData){
                $.messager.alert('提示', '请先选择要查看回访的事件', 'warning');
                return;
            }
            if(!rowData.visitType && rowData.visitType != "无效任务"){
                $.messager.alert('提示', '该事件没有回访记录', 'warning');
                return;
            }
            openVisitWindow(rowData, "1");
            // api
            getVisitRecord({
                id: rowData.visitRecordId
            }, function(data) {
                createVisitForm(data.visitForm.questions);
                if(data.visitRecord.type == "2" && data.visitRecord.status == "2"){
                    $("#visitIsNotThrough").prop("checked", true).trigger("change");
                }
                let questions = JSON.parse(data.visitForm.questions);
                let answerList = JSON.parse(data.visitRecord.answerList);
                for (let i = 0; i < answerList.length; i++) {
                    let _div = $("div[name$='_q" + i + "']");
                    let answer = answerList[i];
                    let type = questions[i].type;
                    if(type == "1"){
                        if(answer.value){
                            let _input = _div.find("input[value='" + answer.value + "']");
                            if(_input){
                                _input.prop("checked", true);
                                if(_input.attr("text") == "true" && answer.text){
                                    _div.find("textarea").val(answer.text);
                                    _div.find("textarea").show();
                                }
                            }
                        }
                    }else if(type == "2"){
                        if(answer.text){
                            _div.find("textarea").val(answer.text);
                        }
                    }
                }
            }, function(e, url, errMsg) {
                $.messager.alert('错误', errMsg, 'error');
            });
        }

        /**
         * 电话回访单
         */
        function doCallVisit(){
            let rowData = $('#visit-event-list').datagrid('getSelected');
            if(!rowData){
                $.messager.alert('提示', '请先选择要回访的事件', 'warning');
                return;
            }
            if(rowData.visitStatus === "1"){
                $.messager.alert('提示', '该事件已回访完成', 'warning');
                return;
            }
            let pSeat = evalJson(window.localStorage.getItem("_pSeat"));
            if(!pSeat){
                $.messager.alert('提示', '座席信息为空', 'warning');
                return;
            }
            if(!pSeat.callOut){
                $.messager.alert('提示', '呼出分机号为空', 'warning');
                return;
            }
            let callIn = rowData.callIn;
            if(!callIn){
                $.messager.alert('提示', '事件主叫号码为空', 'warning');
                return;
            }
            $.messager.confirm('确认', '是否拨打电话进行回访？', function(r) {
                if (r) {
                    let dto = {
                        eventId: rowData.eventId,
                        ext: pSeat.callOut, 
                        outerPhone: callIn,
                        type: "19"
                    }
                    callPhone2(dto,
                        function (res) {
                            //正在拨打电话
                            openVisitWindow(rowData, "2");
                            $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');
                            getVisitForm({}, function(data) {
                                $("#visitFormId").val(data.id);
                                createVisitForm(data.questions);
                            }, function(e, url, errMsg) {
                                $.messager.alert('错误', errMsg, 'error');
                            });
                        },
                        function (e, url, errMsg) {
                            $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                        }
                    );
                }
            });
        }

        function openVisitWindow(rowData, opt){
            $('#visitWindow').window('setTitle', '电话回访');
            $("#visitWindow").scrollTop(0);
            $("#visitEventCode").text(rowData.eventId);
            $("#visitEventTime").text(rowData.eventTime);
            $("#visitCallType").text(rowData.callType);
            $("#visitCallReason").text(rowData.majorCall);
            $("#visitPatientName").text(rowData.patientName);
            $("#visitPhone").text(rowData.callIn);
            $("#visitIsNotThrough").prop("checked", false).trigger("change");
            let title = "";
            if(opt === "1"){
                title = "查看回访记录";
                if(rowData.visitType == "2"){
                    $("#visitIsNotThroughShow").show();
                }else{
                    $("#visitIsNotThroughShow").hide();
                }
                $("#visitRecordSaveBtn").hide();
            }else if(opt === "2"){
                title = "电话回访";
                $("#visitIsNotThroughShow").show();
                $("#visitRecordSaveBtn").show();
            }
            $('#visitWindow').window('setTitle', title);
            $('#visitWindow').window('open');
        }

        function createVisitForm(questionsStr){
            let questions = JSON.parse(questionsStr);
            let str = "";
            for (let i = 0; i < questions.length; i++) {
                let q = questions[i];
                let questionType = "";
                if(q.type == "1"){
                    questionType = "option";
                }else if(q.type == "2"){
                    questionType = "text";
                }
                str += "<div style='padding: 0 0 10px 10px' name='" + questionType + "_q" + i + "'>";
                str += "<p style='margin: 14px 0 14px 0'><span style='color:red;'>*</span>" + q.question + "</p>";
                if(q.type == "1"){
                    str += "<p style='margin: 14px 0 14px 0'>";
                    q.options.forEach(o => {
                        str += "<input style='margin: 3px' type='radio' name='q" + i + "_option' text='" + o.text + "' value='" + o.value + "'/><span style='margin: 3px'>" + o.value + "</span>"
                    });
                    str += "</p>";
                    str += "<textarea name='q" + i + "_text' style='width: 50vh; height: 50px' hidden></textarea>";
                }else if(q.type == "2"){
                    str += "<textarea name='q" + i + "_text' style='width: 50vh; height: 50px'></textarea>";
                }
                str += "</div>";
            }

            $("#visitForm").html(str);
        }

        /**
         * 保存回访记录
         */
        function saveVisitRecord(){
            let rowData = $('#visit-event-list').datagrid('getSelected');
            if(!rowData){
                $.messager.alert('提示', '请先选择要回访的事件', 'warning');
                return;
            }
            let answerList = [];
            let isNotThrough = "0";
            if (!$("#visitIsNotThrough").is(":checked")) {
                $("#visitForm > div").each(function (index, _div){
                    let value = "";
                    let text = "";
                    let divName = $(_div).attr("name");
                    if(divName.startsWith("option")){
                        let optionEl = $(_div).find("input").filter(":checked");
                        if(optionEl){
                            value = optionEl.val();
                            if(optionEl.attr("text") == "true"){
                                text = $(_div).find("textarea").val();
                            }
                        }
                    }else if(divName.startsWith("text")){
                        text = $(_div).find("textarea").val();
                    }
                    answerList.push({
                        value: value,
                        text: text
                    });
                });
            } else {
                isNotThrough = "1";
            }
            callVisit({
                eventId: rowData.eventId,
                isNotThrough: isNotThrough,
                answerList: answerList,
                visitFormId: $("#visitFormId").val()
            }, function(data) {
                $.messager.alert('提示', '提交成功', 'info');
                $('#visitWindow').window('close');
                getEventList();
            }, function(e, url, errMsg) {
                $.messager.alert('错误', errMsg, 'error');
            });
        }

        function openEventInfo() {
            var rowData = $('#visit-event-list').datagrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请先选择要回访的事件', 'warning');
                return;
            }
            var title = "事件详情" + "(" + rowData.eventId + ")"
            $('#eventInfoWindow').window({
                width: 1440,
                height: 900,
                modal: true,
                title: title,
                collapsible: false,
                minimizable: false,
                maximizable: false,
                draggable: false,//不能拖拽
                resizable: false,
            });
            href = "eventInfoTabs.html?eventId=" + rowData.eventId;
            $('#eventInfoIframe').attr('src', href);
            $('#eventInfoWindow').window('open');
        }
    </script>
</body>
</html>
