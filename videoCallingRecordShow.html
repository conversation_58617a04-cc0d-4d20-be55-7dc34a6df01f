<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>   
    <title>视频呼救通话混录回放</title>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />	
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    

    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f0f2f5;
        }

        .container {
            display: flex;
            height: 100vh;
            background: #fff;
        }

        /* 左侧聊天区域 */
        .chat-section {
            width: 30%;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }

        /* 右侧视频区域 */
        .video-section {
            width: 70%;
            background: #000;
        }

        /* 聊天头部 */
        .chat-header {
            padding: 15px;
            background: #fff;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header h2 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }

        .phone-container {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .phone-text {
            font-size: 14px;
            color: #666;
            font-weight: normal;
        }

        /* 聊天消息区域 */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #f0f2f5;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .message.received {
            align-items: flex-start;
        }

        .message.sent {
            align-items: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            font-size: 14px;
            margin: 5px 0;
        }

        .message.received .message-content {
            background: #fff;
            color: #333;
        }

        .message.sent .message-content {
            background: #1890ff;
            color: #fff;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin: 2px 5px;
        }

        .message-sender {
            font-size: 12px;
            color: #666;
            margin: 2px 5px;
        }

        /* 聊天输入区域 */
        .chat-input {
            padding: 15px;
            background: #fff;
            border-top: 1px solid #ddd;
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        

        /* 视频播放器样式 */
        .video-player {
            width: 100%;
            height: calc(100% - 60px);
            background: #000;
            object-fit: contain;
        }

        .video-info {
            height: 60px;
            background: #2c3e50;
            color: #fff;
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .video-title {
            font-size: 16px;
            font-weight: bold;
        }

        .video-status {
            font-size: 12px;
            color: #bdc3c7;
        }
        
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧聊天区域 -->
        <div class="chat-section">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <h2>视频呼救聊天</h2>
                <div class="phone-container">
                    <span class="phone-text" id="phoneText">呼救者手机号</span>
                </div>
            </div>
            
            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <!-- 消息将通过JavaScript动态添加 -->
            </div>
            
            <!-- 聊天输入区域 -->
            <div class="chat-input">
                <div class="input-container">
                    <div style="text-align: center; padding: 0px; color: #999; font-size: 14px;">
                        此为视频呼救混录回放界面，仅用于查看历史消息记录
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧视频播放区域 -->
        <div class="video-section">
            <video class="video-player" id="videoPlayer" controls preload="metadata" autoplay>
                <source id="videoSource" src="" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
            <div class="video-info" id="videoInfo">
                <div class="video-title" id="videoTitle">视频混录回放</div>
                <div class="video-status" id="videoStatus">准备播放...</div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            try{
                _baseUrl = window.parent.parent.gProxy.getBaseUrl();
            }catch(e){
                _baseUrl = window.parent.parent.parent.gProxy.getBaseUrl();
            }   
        } catch (e) {
            try{
                _baseUrl = window.parent.parent.bsProxy.getBaseUrl();
            }catch(e){
                _baseUrl = window.parent.parent.parent.bsProxy.getBaseUrl();
            }
        }
        
        //获取当前登录用户信息
        var _userInfo = "";
        //获取当前座席信息
        var _seatInfo = "";
        //获取视频呼救详情
        var _currentInCallDetails = "";
        
        //视频播放器元素
        var _videoPlayer = null;

        // 页面加载完成后初始化
        window.onload = function() {
            // 初始化视频播放器
            initVideoPlayer();
                       
            //获取url参数信息
            var urlParams = queryURLParams(window.location.search);
            console.log("videoCallId:" + urlParams.videoCallId);
            //获取视频呼救详情，并打开加载视频页面
            initVideoCalling(urlParams.videoCallId);

        };

        // 获取视频呼救详情通过ID
        function initVideoCalling(videoCallId){
            getVideoCallById({id: videoCallId}, 
                function(data) {
                    if(data) {
                        console.log('获取视频呼救详情成功:', data);

                        //加载历史消息
                        loadHistoryMessages(data.chatId, data.toSeatId);
                        
                        // 显示呼救者手机号码和加载视频文件
                        displayCallerPhone(data.phoneNumber);
                        // 加载视频文件并播放，测试用
                        loadVideoFile(data.recordHost + data.recordAddr);
                    }
                },  
                function(e, url, errMsg) {
                    // 错误处理
                    console.error('获取视频呼救记录失败:', errMsg);
                }
            );
        }

        // 添加消息到聊天区域
        function appendMessage(message) {
            // 只处理文本消息
            if (message.type !== '1') {
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.role === 'seat' ? 'sent' : 'received'}`;//接受者还是发送者
            messageDiv.setAttribute('data-message-id', message.id);
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = message.content;//消息内容
            
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = formatTimeForDisplay(message.time);//消息发送时间
            
            const messageSender = document.createElement('div');
            messageSender.className = 'message-sender';
            messageSender.textContent = message.sender;//发送者名称

            // 如果消息被撤回
            if (message.recallTime) {
                messageContent.className = 'message-content recalled';
                messageContent.textContent = '此消息已被撤回';
            }
            
            messageDiv.appendChild(messageSender);
            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageTime);
            
            document.getElementById('chatMessages').appendChild(messageDiv);
            scrollToBottom();
        }

        // 滚动到底部
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 加载历史消息
        function loadHistoryMessages(sessionId, userId) {
            if(!sessionId || !userId) {
                console.warn('sessionId或userId为空，跳过加载历史消息');
                return;
            }
            
            try {
                getNewSessionMessagesByUserId({sessionId: sessionId, userId: userId},
                function (data) {
                    if(data && data.length > 0) {
                        data.forEach(msg => {
                            appendMessage({
                                id: msg.id,
                                type: msg.msgType,//会话类型（1-单聊，2-群聊）
                                content: msg.content,//消息内容
                                senderId: msg.senderId,//消息发送者ID
                                time: msg.sendTime,//消息发送时间
                                sender:  msg.senderId === userId ? '调度座席' :'呼救人',//消息发送者昵称
                                role: msg.senderId === userId ? 'seat' : 'caller',//消息发送者角色（seat-座席，caller-呼救者）
                                status: msg.msgStatus === 1 ? 'read' : 'unread',//消息状态（0-未读，1-已读）
                                attachment: msg.attachment,//消息附件
                                recallTime: msg.recallTime,//消息撤回时间
                                recallUserId: msg.recallUserId//消息撤回者ID    
                            });
                        });
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    console.error('加载历史消息失败:', errMsg);
                });
            } catch (error) {
                console.error('加载历史消息失败:', error);
            }
        }

        // 时间格式转换函数：将时间字符串转换为YYYYMMddHHmmss格式
        function formatTimeToYYYYMMddHHmmss(timeStr) {
            try {
                var date = new Date(timeStr);
                if (isNaN(date.getTime())) {
                    // 如果无法解析，尝试其他格式
                    return timeStr.replace(/[^\d]/g, '').substring(0, 14);
                }
                
                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var day = String(date.getDate()).padStart(2, '0');
                var hour = String(date.getHours()).padStart(2, '0');
                var minute = String(date.getMinutes()).padStart(2, '0');
                var second = String(date.getSeconds()).padStart(2, '0');
                
                return year + month + day + hour + minute + second;
            } catch (error) {
                console.error('时间格式转换失败:', error);
                return '';
            }
        }

        // 时间格式化函数：将YYYYMMddHHmmss格式转换为友好显示格式
        function formatTimeForDisplay(timeStr) {
            try {
                if (!timeStr) return '';
                
                // 如果是YYYYMMddHHmmss格式（14位数字）
                if (typeof timeStr === 'string' && /^\d{14}$/.test(timeStr)) {
                    var year = timeStr.substring(0, 4);
                    var month = timeStr.substring(4, 6);
                    var day = timeStr.substring(6, 8);
                    var hour = timeStr.substring(8, 10);
                    var minute = timeStr.substring(10, 12);
                    var second = timeStr.substring(12, 14);
                    
                    var date = new Date(year, month - 1, day, hour, minute, second);
                    var now = new Date();
                    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    var messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                    
                    // 判断是否是今天
                    if (messageDate.getTime() === today.getTime()) {
                        // 今天的消息只显示时分
                        return hour + ':' + minute;
                    } else {
                        // 不是今天的消息显示月日 时分
                        return parseInt(month) + '月' + parseInt(day) + '日 ' + hour + ':' + minute;
                    }
                } else {
                    // 尝试解析其他格式
                    var date = new Date(timeStr);
                    if (isNaN(date.getTime())) {
                        return timeStr; // 无法解析就返回原值
                    }
                    
                    var now = new Date();
                    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    var messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                    
                    // 判断是否是今天
                    if (messageDate.getTime() === today.getTime()) {
                        // 今天的消息只显示时分
                        return String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0');
                    } else {
                        // 不是今天的消息显示月日 时分
                        return (date.getMonth() + 1) + '月' + date.getDate() + '日 ' + 
                               String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0');
                    }
                }
            } catch (error) {
                console.error('时间格式化失败:', error);
                return timeStr; // 出错时返回原值
            }
        }

        // 显示呼救者手机号码
        function displayCallerPhone(phone) {
            const phoneText = document.getElementById('phoneText');
            if (phone && phone.trim() !== '') {
                phoneText.textContent = '呼救者电话：' + phone;
            } else {
                phoneText.textContent = '呼救者电话：未知';
            }
        }

        /**
         * 返回参数对象
         * @param urlParams
         */
         function queryURLParams(urlParams) {
            // const url = location.search; // 项目中可直接通过search方法获取url中"?"符后的字串
            let url = urlParams.split("?")[1];
            let obj = {}; // 声明参数对象
            let arr = url.split("&"); // 以&符号分割为数组
            for (let i = 0; i < arr.length; i++) {
                let arrNew = arr[i].split("="); // 以"="分割为数组
                obj[arrNew[0]] = arrNew[1];
            }
            return obj;
        }

        // 初始化视频播放器
        function initVideoPlayer() {
            _videoPlayer = document.getElementById('videoPlayer');
            
            // 添加视频事件监听器
            _videoPlayer.addEventListener('loadstart', function() {
                updateVideoStatus('正在加载视频...');
            });
            
            _videoPlayer.addEventListener('loadedmetadata', function() {
                updateVideoStatus('视频已加载，时长：' + formatDuration(_videoPlayer.duration));
            });
            
            _videoPlayer.addEventListener('canplay', function() {
                updateVideoStatus('准备就绪，可以播放');
            });
            
            _videoPlayer.addEventListener('play', function() {
                updateVideoStatus('正在播放');
            });
            
            _videoPlayer.addEventListener('pause', function() {
                updateVideoStatus('已暂停');
            });
            
            _videoPlayer.addEventListener('ended', function() {
                updateVideoStatus('播放完毕');
            });
            
            _videoPlayer.addEventListener('error', function() {
                updateVideoStatus('视频加载失败');
            });
            
            _videoPlayer.addEventListener('timeupdate', function() {
                // 可以在这里添加播放进度的处理
                if(_videoPlayer.duration) {
                    var progress = (_videoPlayer.currentTime / _videoPlayer.duration * 100).toFixed(1);
                    updateVideoStatus('正在播放 (' + progress + '%)');
                }
            });
        }

        // 加载视频文件
        function loadVideoFile(videoUrl) {
           
            if(videoUrl) {
                document.getElementById('videoSource').src = videoUrl;
                document.getElementById('videoTitle').textContent = '视频混录回放';
                _videoPlayer.load(); // 重新加载视频
                updateVideoStatus('正在加载视频文件...');
                console.log('加载视频文件:', videoUrl);
            } else {
                updateVideoStatus('未找到视频文件');
                console.warn('未找到视频文件URL');
            }
        }

        // 更新视频状态显示
        function updateVideoStatus(status) {
            document.getElementById('videoStatus').textContent = status;
        }

        // 格式化视频时长
        function formatDuration(seconds) {
            var hours = Math.floor(seconds / 3600);
            var minutes = Math.floor((seconds % 3600) / 60);
            var secs = Math.floor(seconds % 60);
            
            if(hours > 0) {
                return hours + ':' + String(minutes).padStart(2, '0') + ':' + String(secs).padStart(2, '0');
            } else {
                return minutes + ':' + String(secs).padStart(2, '0');
            }
        }
    </script>
</body>
</html>