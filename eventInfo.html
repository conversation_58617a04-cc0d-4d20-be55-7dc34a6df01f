<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 黄灵
* Created: 2022/4/19
* -----------------------------------------------------------------
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>事件显示界面</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/eventReport.js"></script>
    <style>
        .tangram-suggestion-main {
            display: none !important;
        }

        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }

        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }

        .group-box {
            min-width: 100px;
            min-height: 100px;
            border: solid #E2E7EA 1px;
            border-radius: 5px;
            margin: 3px 5px 5px 5px;
            padding: 0 16px;
        }

        .anchorBL {
            display: none;
        }

        /* 文字闪烁 定义keyframe动画，命名为blink */
        @keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        /* 添加兼容性前缀 */
        @-webkit-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-moz-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-ms-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-o-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        /* 定义红色闪动blink类*/
        .redblink {
            font-size: 2.0em;
            color: #f10404;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        .blueblink {
            font-size: 2.0em;
            color: #099DFC;
            margin-left: 10px;
        }

        .greenblink {
            font-size: 2.0em;
            color: #1e8804;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        #eventDetail-tabel.tabs-header {
            border-width: 0px;
            background: url(./style/img/grouptitle.png) repeat-x;
        }

        #eventDetail-tabel .detail-title {
            background: none !important;
            background: url(./style/img/grouptitle.png) repeat-x !important;
        }

        #eventDetail-tabel {
            font-size: 14px;
            /*overflow: auto;*/
        }

        #eventDetail-tabel #detail-left {
            width: 74.5%;
            padding-right: 0;
        }

        #eventDetail-tabel #region-call-voices {
            width: 25%;
        }

        .easyui-linkbutton {
            position: relative;
            top: -2px;
        }

        /*
            按钮高度修改
        */
        /* #eventDetail-tabel .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        } */

        /*
            下拉框字体高度修改
        */
        #eventDetail-tabel .select-common {
            /*font-size: 14px;*/
            /*height: 2.1em;*/
            margin-left: 0;
        }

        /*
            下拉框样式修改
        
        select {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        }*/

        /*
            联级输入框样式修改
        
        textarea {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        }*/

        /*
            树型下拉框样式修改
        */
        input[readonly],
        textarea[readonly] {
            background: transparent !important;
        }
        /*
        .textbox {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
            font-size: 1em !important;
        }

        .textbox .textbox-text {
            height: 2.1em !important;
            line-height: 2.1 !important;
            padding: 5px !important;
        }

        .textbox-icon {
            height: 1.5em !important;
        }*/

        .tree-title {
            font-size: 1.3em !important;
        }

        .combo-panel {
            overflow: auto !important;
        }

        .panel-tool-close {
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            line-height: 18px;
            position: relative;
        }

        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
            /* 未选中时的边框颜色 */
        }

        input[type="checkbox"]:checked::before {
            content: "\2713";
            /* 对号的转义符 √ */
            background-color: #fff;
            /* 选中时的背景颜色 */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            /* 选中后的边框颜色 */
            color: #000;
            /* 选中后的文字颜色   */
            font-size: 17px;
            font-weight: bold;
        }

        #morbidity .checked-input::before {
            top: -2px !important;
        }

        .checkSpan {
            cursor: pointer;
        }

        /* .panel-title {
            font-size: 15px;
            opacity: ;
        } */

        .window {
            overflow: initial !important;
        }

        #wavPlayWindow {
            overflow: initial;
        }

        .larger-event-message {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50px;
            background-color: rgb(242, 242, 242);
            line-height: 50px;
        }

        .larger-event-btns {
            height: 100%;
            display: flex;
            align-items: center;
        }

        .larger-event-title {
            width: calc(100% - 210px);
            white-space: nowrap;
            color: rgb(245, 154, 35);
            font-size: 16px;
            padding-left: 10px;
            text-align: left;
            overflow: hidden;
        }

        .larger-form-line1 {
            display: flex;
            width: 100%;
            height: 40px;
            align-items: center;
        }

        .larger-form-item {
            width: 33.3%;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-code {
            width: 100%;
            font-size: 1.1em;
            height: 2.1em;
            padding: 5px;
            border-radius: 3px;
        }

        .larger-form-label {
            margin-left: 5px;
            width: 140px;
            font-size: 15px;
            text-align: right;
        }

        #update-evd-form div{
            margin-bottom: 6px;
        }

        .datebox {
            height: 1.7rem;
            /* 设置 DateTimeBox 输入框的高度 */
        }

        .clear-fix .datebox {
            height: 2rem;
            /* 设置 DateTimeBox 输入框的高度 */
        }

        .larger-form-line2 {
            height: 40px;
            display: flex;
            align-items: center;
            width: 100%;
            padding-left: 20px;
        }

        .larger-form-line2-item {
            width: 16.6%;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-line3 {
            width: 100%;
            margin-bottom: 6px;
        }

        .larger-form-line3-item {
            width: 100%;
            display: flex;
        }

        .larger-event-flowOfCasualties {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .larger-event-uploadList,
        .larger-event-flowOfCasualties {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 35px;
            background-color: rgb(242, 242, 242);
            line-height: 35px;
        }

        .larger-event-flowOfCasualties {
            margin-top: 6px;
        }

        .larger-event-uploadList-title,
        .larger-event-flowOfCasualties-title {
            text-align: left;
            color: rgb(245, 154, 35);
            font-size: 15px;
            padding-left: 10px;
        }

        .larger-form-line4 {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
        }

        .larger-form-line4-item {
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-line5 {
            width: 100%;
        }

        .larger-form-line5-item {
            display: flex;
            width: 100%;
        }

        .uploadPicture {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            min-height: 438px;
            max-height: 638px;
            overflow: auto;
        }

        .amap-logo {
            display: none !important;
        }

        #on-site-photo {
            width: 1000px;
            text-align: center;
            padding: 20px;
            box-sizing: border-box;
        }

        #onSitePhoto {
            height: 30px;
            line-height: 30px;
            color: white;
            background: #39c;
            width: 140px;
            font-size: 1em;
            margin: 20px 10px 0 0;
        }

        #showPictureDialog {
            width: 700px;
            height: 500px;
            text-align: center;
        }

        #mobileChangeRecordWindow {
            width: 650px;
            height: 300px
        }

        #mobileOperationRecordWindow {
            width: 650px;
            height: 300px
        }

        #chooseCarNoId .layui-tree-entry,
        #chooseStationId .layui-tree-entry {
            text-align: left;
        }
    </style>
</head>

<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <!--事件详情窗口-->
        <div id="eventDetailRegion">
            <div id="eventDetail" style="width:100%; height: 100%;left: 0%;top: 0%;border-width:0;">
                <div class="easyui-layout" id="eventDetail-tabel" data-options="fit:true">
                    <div class="detail-title">
                        <div style="display:inline-block; color: #0088CC;font-weight: bold;">
                            <div style="display: inline-block;" id="event_title_num">事件详情</div>
                        </div>
                        <div style="display: inline-block;float: right;" id="windowHandlePanel">
                            <a href="javascript:printDetails();" class="easyui-linkbutton" data-options="iconCls:'icon-print'" >打 印</a>&nbsp;
                        </div>
                        <div style="display: inline-block;float: right;display: none;margin-right: 5px;"
                            id="windowHandlePanel" class="sendMessageDom">
                            <a href="javascript:sendMessage();" class="easyui-linkbutton" id="sendMessageBtn" style="min-width: 9.5em;font-size: 14px; width:auto;margin: 0px;">
                                <img src="style/img/sendMessage.png" style="width:16px;height: 16px;" alt="">保存并发送指导短信
                            </a>
                        </div>
                        <div style="display: inline-block;float: right;display: none" id="windowHandlePanel" class="getAdlsUrl">
                            <a href="javascript:redirectAdlsUrl();" class="easyui-linkbutton" style="font-size: 14px; width:80px;margin-right: 0px;">ADLS</a>&nbsp;
                        </div>
                        <div style="display: inline-block; float: right; display: none" id="windowHandlePanel" class="keepEvent">
                            <span></span>
                            <a href="javascript:updateEventBtn();" class="easyui-linkbutton" data-options="iconCls:'icon-save'" style="font-size: 14px; min-width:100px;margin-right:0px;">保存事件</a>&nbsp;
                        </div>

                        <div style="display: inline-block; float: right;margin-right: 0px;" id="windowHandlePanel">
                            <div id="largerEventUploadP">
                                <a href="javascript:updateEventBtn(true);" id="largerEventUpload" class="easyui-linkbutton" data-options="iconCls:'icon-redo'"
                                    style="font-size: 14px; width:130px;margin-right: 5px;background-color: #EC0808;color: #ffffff;">上传重大事件</a>
                            </div>
                        </div>
                        <div style="display: inline-block; float: right;margin-right: 0px;" id="windowHandlePanel">
                            <a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:refreshEvent();"
                                style="width: 80px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;"
                                data-options="iconCls:'icon-reload'">刷新</a>
                        </div>
                        <div style="display: inline-block; float: right;margin-right: 0px;" id="windowHandlePanel">
                            <a class="easyui-linkbutton" id="picture-event-btn" href="javascript:openPictures();"
                                style="width: 100px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;"
                                data-options="iconCls:'icon-picture'">现场照片</a>
                        </div>
                        <div style="display: inline-block; float: right;margin-right: 0px;" id="windowHandlePanel">
                            <a class="easyui-linkbutton" id="picture-event-btn" href="javascript:openCallRecord();"
                                style="width: 100px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;">通话录音</a>
                        </div>
                        <div style="display: inline-block; float: right;margin-right: 0px;" id="windowHandlePanel">
                            <a class="easyui-linkbutton" id="relevance-first-event-btn" href="javascript:updateRelevanceFirstEventBtn();"
                                style="width: 120px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;"
                                data-options="iconCls:'icon-filter'">关联首次事件</a>
                        </div>
                    </div>
                    <div id="detail-left" style="height:calc(67% - 5px);overflow: auto;">
                        <form id="update-evd-form" action="" onsubmit="return false">
                            <div class="clear-fix">
                                <input type="submit" id="update-evd-btn-submit" style="display: none;" />
                                <input type="hidden" value="" id="update-event-id" />
                                <input type="hidden" value="" id="update-event-status" />
                                <input type="hidden" value="" id="update-call-r-id" />
                                <input type="hidden" value="" id="update-event-lat" />
                                <input type="hidden" value="" id="update-event-lng" />
                                <input type="hidden" value="" id="upload-event-lat" />
                                <input type="hidden" value="" id="upload-event-lng" />
                                <input type="hidden" value="" id="update-pickup-lat" />
                                <input type="hidden" value="" id="update-pickup-lng" />
                                <input type="hidden" value="" id="update-destination-lat" />
                                <input type="hidden" value="" id="update-destination-lng" />
                                <input type="hidden" value="" id="update-first-event-id" />
                                <span style="float: left;height: 2em;line-height: 2em;">事件来源：</span>
                                <select id="update-evd-source" class="select-common" style="width: 7em; float: left;">
                                    <!--<option value="" selected>普通来电</option>
                                    <option value="">手工制表</option>
                                    <option value="">自动呼救</option>
                                    <option value="">联网联动</option>
                                    <option value="">110联动</option>
                                    <option value="">119联动</option>
                                    <option value="">122联动</option>-->
                                </select>
                                <span style="margin-left: 1em; float: left;height: 2em;line-height: 2em;">呼叫类型：</span>
                                <select id="update-evd-call-type" class="select-common" style="width: 7em; float: left;" disabled>
                                    <!--<option value="" selected>救治</option>
                                    <option value="">回家</option>
                                    <option value="">转院</option>
                                    <option value="">特殊事件</option>-->
                                </select>

                                <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;">所属区域：</span>
                                <select id="update-det-area" class="select-common" style="width: 7em; float: left;">
                                    <option value="">请选择</option>
                                </select>



                                <span style="margin-left: 1em;">重大事件：</span>
                                <input type="checkbox" id="updateEventType" name="updateEventType" style="margin-left: 0;vertical-align: middle;" />
                      

                                <span style="margin-left: 1em;float: left;height: 2em;line-height: 2em;">事件类型：</span>
                                <select id="updateMainEventType" class="select-common" style="width: 7em; float: left;" disabled>
                                    <option value="" selected>请选择</option>
                                    <option value="01">自然灾害</option>
                                    <option value="02">事故灾难</option>
                                    <option value="03">公共卫生事件</option>
                                    <option value="04">社会安全事件</option>
                                </select>
                                
                                <span style="margin-left: 1em;; float: left;height: 2em;line-height: 2em;">事故等级：</span>
                                <select id="updateMainEventLevel" class="select-common" style="width:7em; float: left;" disabled>
                                  <option value="">请选择</option>
                                  <option value="01">特大（Ⅰ级）</option>
                                  <option value="02">重大（Ⅱ级）</option>
                                  <option value="03">较大（Ⅲ级）</option>
                                  <option value="04">一般（Ⅳ级）</option>
                              </select>
                            </div>
 
                            <div class="clear-fix">
                                <span style="color: red;" id="update-required-address">现场地址：</span><span id="update-not-required-address">现场地址：</span>
                                <input id="update-det-address" required type="text" style="width: Calc(100% - 180px);" maxlength="200" />
                                <a href="javascript:queryLocationFntsss($('#update-det-callin').val());" class="easyui-linkbutton" style="position: relative;top: -0.2em;" id="updateIsPhone" data-options="iconCls:'icon-show'" title="电话号码查询地址">
                                </a>
                                <a href="javascript:setAddress($('#update-det-address').val());" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                                <a href="javascript:getUpdateMapLocation('scene');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                                <div id="searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                            </div>
                            
                            <div class="clear-fix">
                                <span>等车地址：</span>
                                <input id="update-pickup-address" type="text" style="width: Calc(100% - 180px);" maxlength="200" />
                                <a href="javascript:syncUpdateSceneToPickupAddress();" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="同步现场地址"><i class="fa fa-refresh"></i></a>
                                <a href="javascript:confirmUpdatePickupAddress('发送到地图');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                                <a href="javascript:getUpdateMapLocation('pickup');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                                <div id="update-pickup-searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                            </div>

                            <div class="clear-fix">
                                <span style="float: left; margin-left: 0em;height: 2em;line-height: 2em;">主叫号码：</span>
                                <input id="update-det-callin" type="text" style="width: 8em;" maxlength="20"
                                       onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                       onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                       onblur="fillPersonInfoByPhoneNumber(this.value)" />
                                <a href="javascript:callingPhone($('#update-det-callin').val());" class="easyui-linkbutton" title="拨打电话">
                                    <i class="fa fa-volume-control-phone"></i>
                                </a>
                                <span style="margin-left: 1em; color: red;" id="update-required-contact">联系电话：</span>
                                <span style="margin-left: 1em; " id="update-not-required-contact">联系电话：</span>
                                <input id="update-det-contact" type="text" style="width: 8em;" required maxlength="20"
                                       onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                       onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                       onblur="fillPersonInfoByPhoneNumber(this.value)" />
                                <a href="javascript:callingPhone($('#update-det-contact').val());" class="easyui-linkbutton" title="拨打电话">
                                    <i class="fa fa-volume-control-phone"></i>
                                </a>
                                <span style="float: left; margin-left: 1em;color: red;height: 2em;line-height: 2em;">来电时间：</span>
                                <input id="update-det-call-in-times" required class="easyui-datetimebox" style="width:170px;text-indent: 0.5em;" />

                                <span style="margin-left: 1em;">联系人：</span>
                                <input id="update-det-contacter" type="text" style="width: 8em;" maxlength="20" />

                                <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;">患者人数：</span>
                                <input id="update-det-amount" type="number" style="width: 4em; float: left;" value="1" min="0" max="100" />
                            </div>

                            <div class="clear-fix">
                                <span>送往地址：</span>
                                <input id="update-destination-address" type="text" style="width: Calc(100% - 150px);" maxlength="200" />
                                
                                <a href="javascript:confirmUpdateDestinationAddress('发送到地图');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                                <a href="javascript:getUpdateMapLocation('destination');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                                <div id="update-destination-searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                            </div>
                            
                            <div class="clear-fix">
                                <span style="color: red;" id="update-required-major">呼叫原因：</span><span id="update-not-required-major">呼叫原因：</span>
                                <input id="update-det-major" type="text" style="width: Calc(60% - 111px);" maxlength="200" />

                                <span style="margin-left: 1em;">病状判断：</span>
                                <!--<div style="flex:1">
                                    <div class="layui-form" id="patientDiagnosislevel1" style="width: calc(100% - 67px);min-height: 63px;padding-top: 2px;display: flex;flex-wrap: wrap;align-items: flex-start;"></div>
                                    <div class="layui-form" id="patientDiagnosislevel2" style="width: calc(100% - 67px);border-top: solid 1px #cccccc;padding-top: 6px;min-height: 63px;display: flex;flex-wrap: wrap;align-items: flex-start;"></div>
                                    <div class="layui-form" id="patientDiagnosislevel3" style="width: calc(100% - 67px);"></div>
                                </div>-->
                                <!--更改为下拉选择框-->
                                <select name="symptom" lay-verify="" class="select-common" id="updateSelect1" style="width: 10em;margin-left: 0;">
                                </select>
                                <select name="symptom" lay-verify="" class="select-common" id="updateSelect2"  style="width: 15em;margin-left: 5px">
                                </select>

                                <span style="display:inline-block;width:85px;"></span>
                                <span id="updateMainSuit" style="display:inline-block;width: Calc(100% - 111px);"></span>
                            </div>
 

                            <div class="clear-fix">
                                <span>患者姓名：</span>
                                <input id="update-det-name" type="text" maxlength="20" style="width: 10em;margin-left: 0;" />
                                <!-- <span style="margin-left: 1em;">病情：</span>
                                <select id="update-det-condition" class="select-common" style="width: 5em;">
                                    <option value="0">轻微</option>
                                    <option value="1">中度</option>
                                    <option value="2">严重</option>
                                    <option value="3">死亡</option>
                                </select> -->
                                <span style="margin-left: 10px;">性别：</span>
                                <select id="update-det-gender" class="select-common" style="width: 5em;">
                                </select>
                                <span style="margin-left: 10px;">年龄：</span>
                                <input id="update-det-age" type="number" style="width: 5em; text-indent: 0.5em;" min="0" max="999" />
                                <span style="margin-left: 10px;">国籍：</span>
                                <select id="update-det-country" class="select-common" style="width: 10em;margin-left: 0;">
                                    <!--<option value="0">中国</option>-->
                                    <!--<option value="1">海外</option>-->
                                </select>
                                <span style="margin-left: 1em;" class="field-create-foreign">外籍人员：</span>
                                <input type="checkbox" id="updateCreateForeign" name="updateCreateForeign" class="field-create-foreign" style="margin-left: 0;vertical-align: middle;" />

                                <span style="margin-left: 1em;">民族：</span>
                                <select id="update-det-race" class="select-common" style="width: 8em;">
                                    <!--<option value="0">汉族</option>-->
                                    <!--<option value="1">少数民族</option>-->
                                </select>
                                <span style="margin-left: 1em;">需要担架：</span>
                                <input type="checkbox" id="update-need-stretcher" style="margin-left: 0;vertical-align: middle;" />
                            </div>
                            <div class="clear-fix">
                                <span>特殊要求：</span>
                                <input id="update-det-special-req" type="text" style="width: calc(50% - 6em);margin-left: 0;" maxlength="500" />
                                <span style="margin-left: 10px;">120备注：</span>
                                <input id="update-det-120remark" type="text" style="width: calc(50% - 6em);" maxlength="500" />
                            </div>
                            <div class="clear-fix">
                                <span style="float: left;" class="field-det-alds-summary">ALDS汇总：</span>
                                <input id="update-det-alds-summary" type="text" class="field-det-alds-summary" style="width: Calc(70% - 6px);" maxlength="500" />

                                <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;" class="field-emergency-level">紧急程度：</span>
                                <select id="update-emergency-level" class="select-common field-emergency-level" style="width: 10em; float: left;">
                                    <option value="O">O级-非紧急</option>
                                    <option value="E">E级-急诊</option>
                                    <option value="D">D级-较紧急</option>
                                    <option value="C">C级-紧急</option>
                                    <option value="B">B级-高危</option>
                                    <option value="A">A级-最高紧急</option>
                                </select>
                            </div>
                            <div class="clear-fix">
                                <span id="update-patient-info-lable" style="float: left;">上次来电：</span>
                                <!-- <input id="update-patient-info" type="text" style="width: Calc(100% - 111px);" maxlength="512" /> -->
                                <textarea id="update-patient-info" maxlength="512" style="width: Calc(100% - 100px); resize: none; height: 4em; float: left; font-size: 1em;"></textarea>
                            </div>
                            <div class="clear-fix"  id="other-info-panel">
                                <span style="margin-left:0em;" class="field-push-outer-type">接收中心：</span>
                                <select id="update-push-outer-type" class="select-common field-push-outer-type" style="width: 10em;"></select>

                                <span style="margin-left: 1em;" class="field-is-test">测试：</span>
                                <input type="checkbox" id="update-is-test" class="field-is-test" style="margin-left: 0;vertical-align: middle;" />
                            </div>
                            <!-- 关联首次事件显示面板 -->
                            <div class="clear-fix" id="updateRelevanceFirstEventPanel" style="display: none;">

                            </div>
                            <!-- 调度员信息显示面板 -->
                            <div class="clear-fix" id="dispatcherInfoPanel" style="border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin: 5px 0; background-color: #f9f9f9; width: calc(100% - 22px);">
                                <div style="font-size: 14px; display: flex; justify-content: center; align-items: center;margin-bottom: 0px">
                                    <div style="margin-left: 10px; margin-right: 20px;margin-bottom: 0px">
                                        <i class="fa fa-user" style="margin-right: 5px; color: #2c5aa0;"></i>
                                        <strong>受理台：</strong><span id="dispatcherSeatCode">-</span>
                                    </div>
                                    <div style="margin-right: 20px;">
                                        <i class="fa fa-id-card" style="margin-right: 5px; color: #2c5aa0;"></i>
                                        <strong>受理员：</strong><span id="dispatcherSeatUser">-</span>
                                    </div>
                                    <div>
                                        <i class="fa fa-clock-o" style="margin-right: 5px; color: #2c5aa0;"></i>
                                        <strong>创建时间：</strong><span id="dispatcherCreateTime">-</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div id="region-call-voices" style="height:calc(67% - 60px);">
                        <!--地图-->
                        <div id="the-map" style="width: 100%; height: 75%; margin-top: 5px;margin-right:5px;"></div>
                        <div style="width: 100%;overflow-y: auto;margin-top: 5px;">
                            <div style="width: 355px;height: 130px;">
                                <table class="easyui-datagrid" style="width: 100%" id="dg-uploadAdress" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                    <thead>
                                        <tr>
                                            <th field="operation" width="40px" align="center" formatter="uploadOperationFormatter">同步</th>
                                            <th field="address" width="180px" align="left" formatter="uploadAddressFormatter">上报地址</th>
                                            <th field="createTime" width="130px" align="left" formatter="uploadTimeFormatter">上报时间</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!--调度任务-->
                    <div class="group-box"
                        style="width:Calc(100% - 10px); height: Calc(30% - 6px); display: inline-block; float: left; padding: 0 0px;position: relative;">
                        <div class="group-box-title" style="padding-left: 15px;padding: 0px 0px; margin: 0px 0px;width: 100%;font-size:12px;">
                            调度任务
                        </div>
                        <!-- <div style="width: 8.2em;display: inline-block;float: right;position: absolute; right: 10px;top:5px" id="windowHandlePanel">
                            <a href="javascript:sendMessage();" class="easyui-linkbutton" style="font-size: 14px; width:100px;margin: 0px;">发送指导短信</a>&nbsp;
                        </div> -->
                        <div style="height: Calc(100% - 30px); position: relative;padding: 3px;">
                            <table class="easyui-datagrid" id="dg-mobiel-process-list" style="width: 100%" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                <thead>
                                    <tr>
                                        <th field="stationName" width="150" align="center">分站名称</th>
                                        <th field="carName" width="80" align="center">车辆别名</th>
                                        <th field="plateNum" width="100" checkbox="false" align="center">车牌号码</th>
                                        <th field="id" width="80" align="center" formatter="mobileOperationRecordFormatter">运行情况</th>
                                        <th field="isFinishdStr" width="80" align="center">任务状态</th>
                                        <th field="toStatusStr" width="80" align="center">车辆状态</th>
                                        <th field="callTypeCodeName" width="80" align="center">出动方式</th>
                                        <th field="emergencyRadius" width="80" align="center">急救半径(km)</th>
                                        <th field="mobileProcessCreateTime" width="150" align="center">调度时间</th>
                                        <th field="seatCode" width="120" align="center">调度座席</th>
                                        <th field="seatUser" width="120" align="center">调度员</th>
                                        <th field="processId" width="200" align="center" formatter="processIdFormatter">任务编码</th>
                                        <th field="address" width="150" align="center" formatter="addressFormatter">出车地址</th>
                                        <th field="changeSendCarNumber" width="80" align="center"formatter="changeSendCarNumberFormatter">改派情况</th>
                                        <th field="stationRefuseStatus" width="80" align="center" formatter="stationRefuseStatusFormatter">分站拒绝</th>
                                        <th field="stationRefuseReason" width="120" align="center">分站拒绝原因</th>
                                        <th field="dispatchDoctor" width="120" checkbox="false" align="center">出车医生</th>
                                        <th field="dispatchDoctorPhone" width="120" checkbox="false" align="center" formatter="doctorPhoneFormatter">出车医生电话</th>
                                        <th field="dispatchNurse" width="120" checkbox="false" align="center">出车护士</th>
                                        <th field="dispatchNursePhone" width="120" checkbox="false" align="center" formatter="nursePhoneFormatter">出车护士电话</th>
                                        <th field="dispatchDriver" width="120" checkbox="false" align="center">出车司机</th>
                                        <th field="dispatchDriverPhone" width="120" checkbox="false" align="center" formatter="dispatchDriverPhoneFormatter">出车司机电话</th>
                                        <th field="dispatchMedicalStaff" width="100" align="center">出车救护员</th>
                                        <th field="dispatchMedicalStaffPhone" width="120" align="center">出车救护员电话</th>
                                        <th field="dispatchWorker" width="100" align="center">出车担架工</th>
                                        <th field="dispatchWorkerPhone" width="120" align="center">出车担架工电话</th>
                                        <th field="centerRemark" width="200" align="center" formatter="centerRemarkFormatter">备注</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <!--通话录音-->
                    <div id="on-call-record" class="easyui-window" class="easyui-window" title="通话录音"
                        style="background-color:#FAFAFA; width: 1320px; height: 300px; text-align: center; padding-top:5px"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <table class="easyui-datagrid" id="dg-call-list" style="width:100%" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                <thead>
                                    <tr>
                                        <th field="callTime" width="180" align="center">来/去电时间</th>
                                        <th field="number" width="160" checkbox="false" align="center">主叫号码</th>
                                        <th field="toNumber" width="160" align="center">被叫号码</th>
                                        <th field="callType" width="100" align="center" formatter="callTypeFormatter">呼叫类型</th>
                                        <th field="type" width="100" align="center" formatter="typeFormatter">类型</th>
                                        <th field="endTime" width="180" align="center">挂断时间</th>
                                        <th field="handleStatus" width="80" align="center"formatter="handleStatusFormatter">处理结果</th>
                                        <th field="duration" width="100" align="center">通话时长（秒）</th>
                                        <th field="isAnswer" width="100" align="center" formatter="isAnswerFormatter">是否接听</th>
                                        <th field="recordAddr" width="70" align="center" formatter="wavPlayFormatter">录音/视频</th>
                                        <th field="callbackTel" width="70" align="center" formatter="callbackPhoneFormatter">回拨</th>
                                    </tr>
                                </thead>
                        </table>
                    </div>
                    <div id="wavPlayWindow" class="easyui-window" title="录音回放"
                        style="background-color:#FAFAFA; width: 600px; height: 100px; text-align: center; padding-top:5px"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div style="position: relative;">
                            <audio id="playWavAudio" src="" style="width: 100%;height: 42px;" autostart="false" controls></audio>
                            <!--<img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="http://preview.qiantucdn.com/58pic/20220321/00S58PIC5Xkc6mdzM5xU3_PIC2018_PIC2018.jpg!w290_290" />-->
                            <img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="style/img/download.jpg" />
                        </div>
                        <!-- <audio id="playWavAudio" src="" autostart="false" style="width: 100%;height: 42px;" controls></audio> -->
                    </div>
                    <!--视频混录回放播放窗口-->
                    <div id="videoRecordWindow" class="easyui-window" title="" style="width:1320px;height:700px" data-options="iconCls:'icon-save',modal:true, closed: true">
                        <iframe id="videoRecordIframe" name="videoRecordIframe" scrolling="no" frameborder="0" style="width:100%;height:100%;"></iframe>
                    </div>
                    <!--关联首次事件窗口-->
                    <div id="updateFirstEventSelectWindow" class="easyui-window" title="选择关联首次事件" style="width:1240px;height:700px;overflow:hidden;"
                         data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div style="margin-bottom:15px;height:40px;">
                            <div id="updateFirstEventSearch" style="display: inline-block; width: Calc(100% - 50px); float: left; padding-left: 10px;padding-top: 10px; font-size: 14px;">
                                创建时间： <input id="updateFirstStartCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                                <span>至</span>
                                <input id="updateFirstEndCreateTime" class="easyui-datetimebox" style="width:160px;" />
                                <span>&nbsp;呼叫原因：</span>
                                <input id="updateFirstMajorCall" class="easyui-textbox" style="width:150px">
                                <span>&nbsp;地址：</span>
                                <input id="updateFirstAddress" class="easyui-textbox" style="width:150px">
                                <span>&nbsp;任务状态：</span>
                                <select id="updateFirstEventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                                    <option value="0">全部</option>
                                    <option value="7">未调度-预约</option>
                                    <option value="1">未调度-落单</option>
                                    <option value="6">未调度-待派</option>
                                    <option value="2">已调度</option>
                                    <option value="3">已完成</option>
                                    <option value="4">已撤销</option>
                                </select>
                                <a id="getUpdateFirstEventListDatasBtn" class="easyui-linkbutton" href="javascript:getUpdateFirstEventListDatas();" style="width: 80px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;vertical-align: middle;">搜 索</a>
                            </div>
                        </div>
                        <div style="width:100%;height: Calc(100% - 55px); position: relative;font-size: 14px; margin-top: 15px;">
                            <table class="easyui-datagrid" id="dg-update-first-event-pages" style="width: 1230px; height: 600px;" pagination="true" singleSelect="true" rownumbers="true" fit="false"
                                   data-options="onDblClickRow:updateSelectFirstEvent">
                                <thead>
                                    <tr>
                                        <th field="eventId" width="180" checkbox="false" align="center">编号</th>
                                        <th field="createTime" width="150" checkbox="false" align="center">创建时间</th>
                                        <th field="majorCall" width="180" checkbox="false" align="center">呼叫原因</th>
                                        <th field="address" width="320" align="center">地址</th>
                                        <th field="callIn" width="100" align="center">呼救电话</th>
                                        <th field="callInTimes" width="150" align="center">来电时间</th>
                                        <th field="eventStatusStr" width="80" align="center">状态</th>
                                        <th field="eventSrcName" width="80" align="center">来源</th>
                                        <th field="callTypeName" width="80" align="center">类型</th>
                                        <th field="contact" width="100" align="center">联系电话</th>
                                        <th field="contacter" width="100" align="center">联系人</th>
                                        <th field="processCount" width="80" align="center">任务总数</th>
                                        <th field="runProcessCount" width="80" align="center">运行的任务数</th>
                                        <th field="finishedProcessCount" width="80" align="center">完成任务数</th>
                                        <th field="cancelProcessCount" width="80" align="center">撤销任务数</th>
                                        <th field="eventCreateSeatUser" width="80" align="center">创建人</th>
                                        <th field="eventCreateSeatCode" width="80" align="center">创建座席</th>
                                        <th field="centerRemark" width="200" align="center">备注</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <!--改派记录-->
                    <div id="mobileChangeRecordWindow" class="easyui-window" title="改派记录"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <table class="easyui-datagrid" id="dg-change-mobile-list" pagination="false" singleSelect="true" rownumbers="true" fit="true">
                            <thead>
                                <tr>
                                    <th field="times" width="180" checkbox="false" align="center">改派时间</th>
                                    <th field="oldMobilePlatnum" width="100" checkbox="false" align="center">改派前救护车</th>
                                    <th field="newMobilePlatnum" width="100" checkbox="false" align="center">改派后救护车</th>
                                    <th field="remark" width="220" align="center">改派原因</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <!--车辆运行情况记录-->
                    <div id="mobileOperationRecordWindow" class="easyui-window" title="车辆运行记录"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <table class="easyui-datagrid" id="dg-mobile-operation-list" pagination="false" singleSelect="true" rownumbers="true" fit="true">
                            <thead>
                                <tr>
                                    <th field="times" width="180" checkbox="false" align="center">时间</th>
                                    <th field="toStatusStr" width="100" checkbox="false" align="center">状态</th>
                                    <th field="remark" width="335" align="center">备注</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <!-- 查看现场照片 -->
                    <div id="on-site-photo" class="easyui-window" title="现场情况" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div class="uploadPicture" id="carousel"></div>
                        <a href="javascript:closePicture();" id="onSitePhoto" class="easyui-linkbutton">关闭</a>
                    </div>
                    <div id="showPictureDialog" class="easyui-window" title="现场照片" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div class="showPicture" id="showPicture" style="width: 100%;height: 100%;"></div>
                    </div>
                    <!-- 上传重大事件的弹窗 -->
                    <div id="large-event-upload" class="easyui-window" title="上传重大事件" style="width: 1200px; height: 850px; text-align: center;"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div style="width: 100%;height: calc(100% - 50px);padding: 10px 10px 0;box-sizing: border-box;">
                            <div class="larger-event-message">
                                <div class="larger-event-title" id="largerEventTitle">
                                    <div style="height: 25px;line-height: 25px;">地址：<span id="largerEventAddress"></span></div>
                                    <div style="height: 25px;line-height: 25px;">呼叫原因：<span id="largerEventZS"></span></div>
                                    <input type="hidden" id="largerEvent-lat">
                                    <input type="hidden" id="largerEvent-lng">
                                </div>
                            </div>
                            <div class="larger-event-form">
                                <div class="larger-form-line1">
                                    <div class="larger-form-item">
                                        <span class="larger-form-label">事件编号：</span>
                                        <input class="larger-form-code" disabled id="largerFormEventId" type="text" value="" />
                                    </div>
                                    <div class="larger-form-item">
                                        <span class="larger-form-label">事件类型<span style="color:red;">*</span>：</span>
                                        <select id="largeEventType" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                            <option value=""> </option>
                                            <option value="01">自然灾害</option>
                                            <option value="02">事故灾难</option>
                                            <option value="03">公共卫生事件</option>
                                            <option value="04">社会安全事件</option>
                                        </select>
                                    </div>
                                    <div class="larger-form-item">
                                        <span class="larger-form-label">事件等级<span style="color:red;">*</span>：</span>
                                        <select id="largeEventLevel" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                            <option value=""> </option>
                                            <option value="01">特大（Ⅰ级）</option>
                                            <option value="02">重大（Ⅱ级）</option>
                                            <option value="03">较大（Ⅲ级）</option>
                                            <option value="04">一般（Ⅳ级）</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- 上报列表 -->
                            <div class="larger-event-uploadList">
                                <div class="larger-event-uploadList-title">上报列表</div>
                                <div class="larger-event-btns">
                                    <input id="largeEventId" type="hidden" value="" />
                                    <!-- <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton" -->
                                    <a href="javascript: phoneList(false);" class="easyui-linkbutton"
                                        style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                                    <!-- <a href="javascript:largeEventAddUpload(true);" class="easyui-linkbutton" -->
                                    <a href="javascript:phoneList(true);" class="easyui-linkbutton"
                                        style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                                </div>
                            </div>
                            <div class="larger-event-form">
                                <div class="larger-form-line1">
                                    <div class="larger-form-item">
                                        <span class="larger-form-label">上报类别<span style="color:red;">*</span>：</span>
                                        <select id="reportTypeCode" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                            <option value=""> </option>
                                            <option value="01">首报</option>
                                            <option value="02">追报</option>
                                            <option value="03">终报</option>
                                        </select>
                                    </div>
                                    <div class="larger-form-item">
                                        <span class="larger-form-label">上报时间<span style="color:red;">*</span>：</span>
                                        <input id="largeEventReportTime" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                                    </div>
                                    <div class="larger-form-item">
                                    </div>
                                </div>
                                <div class="larger-form-line2">
                                    <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label" style="min-width: 81px;">出动车辆<span style="color:red;">*</span>：</span>
                                        <input id="vehicleDispatchedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">辆</span>
                                    </div>
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label">伤员<span style="color:red;">*</span>：</span>
                                        <input id="woundedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                                    </div>
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label">死亡<span style="color:red;">*</span>：</span>
                                        <input id="deadNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                                    </div>
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label">重伤<span style="color:red;">*</span>：</span>
                                        <input id="woundedSevereNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                                    </div>
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label">轻伤<span style="color:red;">*</span>：</span>
                                        <input id="woundedSlightNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                                    </div>
                                    <div class="larger-form-line2-item">
                                        <span class="larger-form-label">转送<span style="color:red;">*</span>：</span>
                                        <input id="transferNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                                    </div>
                                </div>
                                <div class="larger-form-line3">
                                    <div class="larger-form-line3-item">
                                        <span class="larger-form-label" style="width: 101px;">汇报内容<span style="color:red;">*</span>：</span>
                                        <textarea rows="3" id="reportContent" style="width:calc(100% - 106px); resize: none; font-size: 1.4em;"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div style="height: 140px;width: 100%">
                                <table class="easyui-datagrid" id="larger-event-upload-list" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                    <thead>
                                        <tr>
                                            <th field="reportTime" width="150" align="center">上报时间</th>
                                            <th field="reportTypeName" width="70" align="center">类别</th>
                                            <th field="reportUserName" width="80" checkbox="false" align="center">上报人</th>
                                            <!--<th field="eventTypeName" width="100" align="center">事件类型</th>-->
                                            <th field="eventLevelName" width="100" align="center">事件等级</th>
                                            <th field="vehicleDispatchedNum" width="80" checkbox="false" align="center">出动车辆</th>
                                            <th field="woundedNum" width="60" checkbox="false" align="center">伤员</th>
                                            <th field="deadNum" width="60" checkbox="false" align="center">死亡</th>
                                            <th field="woundedSevereNum" width="60" checkbox="false" align="center">重伤</th>
                                            <th field="woundedSlightNum" width="60" checkbox="false" align="center">轻伤</th>
                                            <th field="transferNum" width="60" checkbox="false" align="center">转送</th>
                                            <th field="reportContent" width="280" align="center" formatter="reportContentFormatter">汇报内容</th>
                                            <th field="operation" width="100" align="center" formatter="operation">操作
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <!-- 伤员流向 -->
                            <div class="larger-event-flowOfCasualties">
                                <div class="larger-event-flowOfCasualties-title">伤员流向</div>
                                <div class="larger-event-btns">
                                    <a href="javascript:flowOfCasualtiesAddUpload();" class="easyui-linkbutton"
                                        style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                                    <a href="javascript:flowOfCasualtiesAddUpload(true);" class="easyui-linkbutton"
                                        style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                                </div>
                            </div>
                            <div class="larger-form-line4">
                                <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                                <div class="larger-form-line4-item" style="width: 21%;">
                                    <span class="larger-form-label" style="min-width: 82px;">发车时间<span style="color:red;">*</span>：</span>
                                    <input id="eventWoundedTransferId" type="hidden" value="" />
                                    <input id="vehicleDispatchedTime_liudong" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                                </div>
                                <div class="larger-form-line4-item">
                                    <span class="larger-form-label" style="width: 84px;">车牌号<span style="color:red;">*</span>：</span>
                                    <input id="vehiclePlateNo_liudong" maxlength="8" type="text" value="" style="width: 6rem;height: 1.7rem;margin-right: 5px;" />
                                    <a href="javascript:chooseCar();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                                </div>
                                <div class="larger-form-line4-item">
                                    <span class="larger-form-label" style="width: 100px;">转送人数<span style="color:red;">*</span>：</span>
                                    <input id="transferNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                                </div>
                                <div class="larger-form-line4-item">
                                    <span class="larger-form-label" style="width: 74px;">死亡<span style="color:red;">*</span>：</span>
                                    <input id="deadNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                                </div>
                                <div class="larger-form-line4-item">
                                    <span class="larger-form-label" style="width: 74px;">重伤<span style="color:red;">*</span>：</span>
                                    <input id="woundedSevereNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                                </div>
                                <div class="larger-form-line4-item">
                                    <span class="larger-form-label" style="width: 74px;">轻伤<span style="color:red;">*</span>：</span>
                                    <input id="woundedSlightNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                                </div>
                                <div class="larger-form-line4-item" style="width: 100%">
                                    <span class="larger-form-label" style="width: 82px;">送往地点<span style="color:red;">*</span>：</span>
                                    <input id="transferAddress_liudong" type="text" value="" style="width: calc(100% - 154px);height: 1.7rem;margin-right: 5px;" />
                                    <a href="javascript:chooseStation();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                                </div>
                            </div>
                            <div class="larger-form-line5">
                                <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                                <div class="larger-form-line5-item">
                                    <span class="larger-form-label" style="width: 81px;">伤情备注 ：</span>
                                    <textarea rows="3" id="injuryRemark" style="width:calc(100% - 81px); resize: none; font-size: 1.4em;"></textarea>
                                </div>
                            </div>
                            <div style="height: 140px;margin-top: 6px;width: 100%">
                                <table class="easyui-datagrid" id="larger-event-flowOfCasualties-list" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                    <thead>
                                        <tr>
                                            <th field="vehicleDispatchedTime" width="160" align="center">发车时间</th>
                                            <th field="vehiclePlateNo" width="80" align="center">车牌号</th>
                                            <th field="transferNum" width="80" align="center">转送人数</th>
                                            <th field="transferAddress" width="274" checkbox="false" align="center" formatter="transferAddressFormatter">送往地点</th>
                                            <th field="deadNum" width="80" checkbox="false" align="center">死亡</th>
                                            <th field="woundedSevereNum" width="80" checkbox="false" align="center">重伤</th>
                                            <th field="woundedSlightNum" width="80" checkbox="false" align="center">轻伤</th>
                                            <th field="injuryRemark" width="330" checkbox="false" align="center" formatter="injuryRemarkFormatter">伤情备注</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                        <!-- <a href="javascript:largeEventSubmit();" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;">上传</a> -->
                        <a href="javascript:largeEventCancel();" class="easyui-linkbutton"
                            style="height: 30px;line-height: 30px;color: #000; background: #fff; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;border: 1px solid lightgray;">关闭</a>
                    </div>
                    <!-- 通信录弹窗 -->
                    <div id="phone-list" class="easyui-window" title="选择通讯录" style="width: 600px;"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <div id="phone-book" style="height: 300px;"></div>
                        <div style="text-align: center;margin-bottom: 20px;">
                            <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton"
                            style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">保存并发送短信</a>
                            <a href="javascript:cancelPhoneList();" class="easyui-linkbutton"
                            style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">取消</a>
                        </div>

                    </div>
                    <!-- 点击伤员流动选择车牌号 -->
                    <div id="chooseCarNoId" class="easyui-window" title="选择车辆" style="width: 350px; height: 400px; text-align: center;;padding:10px"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <input id="chooseCarInput" oninput="chooseCarInput()" type="text" value="" placeholder="请输入车牌号筛选" style="height: 1.7rem;width: 100%;" />
                        <div style="width: 100%;">
                            <div id="car-number-tree"></div>
                        </div>
                    </div>
                    <!-- 点击伤员流动选择分站 -->
                    <div id="chooseStationId" class="easyui-window" title="选择分站" style="width: 280px; height: 400px; text-align: center;"
                        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                        <input id="chooseStationInput" oninput="chooseStationInput()" type="text" value="" placeholder="请输入分站名称筛选" style="height: 1.7rem;width: 100%;" />
                        <div style="width: 100%;">
                            <div id="station-tree"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript" src="script/jQuery.print.js"></script>
    <script type="text/javascript" src="script/symptomData.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.parent.gProxy.getBaseUrl();
        } catch (e) {
            try {
                _baseUrl = window.parent.parent.bsProxy.getBaseUrl();
            } catch (e) {
                _baseUrl = window.parent.parent.parent.bsProxy.getBaseUrl();
            }
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.parent.gProxy.getSeatInfo());
        } catch (e) {
            try {
                _pSeat = evalJson(window.parent.parent.bsProxy.getSeatInfo());
            } catch (e) {
                _pSeat = evalJson(window.parent.parent.parent.bsProxy.getSeatInfo());
            }
        }
        console.log("事件详情页，获取到的座席信息：", _pSeat)
        //获得详情
        var eventId = null;
        var sendCarMarker = null;//事件地点
        var updateMap;//百度地图
        var mobileProcessId;//调度任务id
        var updateAc;//自动完成对象
        var currentEventPr = null;//事件的经纬度
        var currentEventInfo = null;//当前事件信息
        var currentCity = null;//城市
        //多选框选中参数
        var checkedList = []
        var morbidity = []
        var largerEventList = [] //重大事件的列表
        var flowOfCasualtiesList = [] //伤员流向的列表
        var dispatcherTask = [{}, {}, {}] //存储调度任务列表数据
        var callLog = [{}, {}, {}]//存储通话记录
        var localSearch = null;//中文查询对象
        var localSearchStata = true//中文查询状态 避免重复查询多次
        var uploadAddress = []
        var hasLargerEventEdit = false // 判断是否有正在编辑的重大事件
        var hasFlowOfCasualtiesEdit = false // 判断是否有正在编辑的伤员流向表单
        var uploadPictureVideoList = [] //上报现场照片和视频
        var opts = {
            width: 250,     //信息窗口宽度
            height: 100,   //信息窗口高度
            title: "事件"  //信息窗口标题
        }
        var playWavAudio = null //语音播放插件
        var downloadUrl = ''

        //病状数据
        var symptomData = getSymptomData();

        var phoneTree =null
        var isEdit = false
        var _dics = new Array();//EDC字典表数组

        var callTypeList = [] //呼叫类型
        var callTypeShouldCreateEventList = [] // 呼叫类型创建事件判断列表

        //是否展示发送指导短信按钮，0否1是
        var btnSendH5Msg = '0'
        
        //关联事件页码
        var page = 1;
        var size = 20;
        // 页面加载完成后执行
        $(function () {
            // 应用受理页面配置
            function applyAcceptPageConfig() {
                const config = getAcceptPageConfig();
                
                // 紧急程度
                if (!config.emergency_level) {
                    $('.field-emergency-level').hide();
                    // 调整同行的alds汇总的宽度
                    $('#update-det-alds-summary').css('width', 'calc(100% - 100px)');
                }
                
                // 外籍人员
                if (!config.create_foreign) {
                    $('.field-create-foreign').hide();
                }
                
                // 接收中心 - 默认隐藏，只在callType为11时显示
                $('.field-push-outer-type').hide(); // 默认隐藏接收中心
                // 调整同行的测试的左边距
                $('.field-is-test').css('margin-left', '0');
                
                // 测试
                if (!config.is_test) {
                    $('.field-is-test').hide();
                }
                
                // ALDS汇总
                if (!config.det_alds_summary) {
                    $('.field-det-alds-summary').hide();
                    // 调整紧急程度的样式
                    $('.field-emergency-level').first().css('margin-left', '0');
                    $('#update-emergency-level').css('width', 'calc(100% - 100px)');
                }


                // 如果接收中心和外籍成员都隐藏,调整padding
                if (!config.push_outer_type && !config.is_test) {
                    $('#other-info-panel').hide();
                    $('#detail-left > form > div').css('padding', '2px');
                }
            }

            // 页面加载时应用配置
            applyAcceptPageConfig();
        });

        //获取传参
        function getURLParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                value: params.get('value'),
                eventId: params.get('eventId'),
            };
        }

        //参数赋值 
        function setInputState() {
            const params = getURLParams();
            eventId = params.eventId
           // 获取需要禁用的元素
            const checkbox = document.getElementById('updateCreateForeign');
            const select1 = document.getElementById('updateSelect1');
            const select2 = document.getElementById('updateSelect2');
        
            if (params.value == 'secondScr') {
                checkbox.disabled = true;
                select1.disabled = true;
                select2.disabled = true;
            }else {
                checkbox.disabled = false;
                select1.disabled = false;
                select2.disabled = false; 
            }
        }
        
        //默认隐藏非必填地址
        $("#update-not-required-address").hide()
        //默认隐藏非必填呼叫原因
        $("#update-not-required-major").hide()
        //默认隐藏非必填联系电话
        $("#update-not-required-contact").hide()
        // 呼叫类型选择为咨询、投诉、骚扰；地址、呼叫原因、联络电话非必填保存
        $('#update-evd-call-type').on('click', function (ev) {
            let callType = $('#update-evd-call-type').val()
            if (callType == '5' || callType == '6' || callType == '7') {
                $('#update-not-required-address').show();
                $('#update-not-required-major').show();
                $('#update-not-required-contact').show();
                $('#update-required-address').hide();
                $('#update-required-major').hide();
                $('#update-required-contact').hide();
            } else {
                $('#update-not-required-address').hide();
                $('#update-not-required-major').hide();
                $('#update-not-required-contact').hide();
                $('#update-required-address').show();
                $('#update-required-major').show();
                $('#update-required-contact').show();
            }
        });
        function validateInput(input) {
            const value = input.value;
            const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
            if (!regex.test(value)) {
                input.value = ""; // 清空输入框
            }
        }
        
        //控制按钮展示
        querySysConfSync('button_show', function (data) {
            let obj = JSON.parse(data)
            // 判断重大事件按钮是否展示
            if (obj.event.btn_event_report == '1') {
                //$("#largerEvent").show()
                $("#largerEventUploadP").show()
            } else {
                //$("#largerEvent").hide()
                $("#largerEventUploadP").hide()
            }

            //发送指导短信按钮是否展示
            if (obj.event.btn_send_h5_msg == '1') {
                btnSendH5Msg = '1'
                $('.sendMessageDom').css('display', 'inline-block')
            } else {
                btnSendH5Msg = '0'
                $('.sendMessageDom').css('display', 'none')
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '异常信息：' + errMsg);
        });

        initMapAndEvent()
        /** 初始化事件和地图 */
        function initMapAndEvent() {
            //查询百度地图的key
            var ohead = document.getElementsByTagName("head")[0];
            var ele = document.createElement('script');
            ele.type = 'text/javascript';
            ele.onload = ele.onreadystatechange = function () {
                //动态加载完百度地图后操作
                if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
                    // Handle memory leak in IE
                    ele.onload = ele.onreadystatechange = null;
                    //显示相关信息
                    setInputState()
                    getEventByIdShow();
                    //默认隐藏上传重大事件按钮
                    $("#uploadEvent").hide()
                    //加载事件任务列表和车辆再地图上的路线显示
                    $('#dg-mobiel-process-list').datagrid({
                        loadMsg: '数据加载，请稍等.....',
                        data: getMobileProcessByEventIdShow() //这一步是加载ajax查询的数据，非常重要
                    });
                    $('#dg-uploadAdress').datagrid({
                        loadMsg: '数据加载，请稍等.....',
                        data: getUploadAdress() //这一步是加载ajax查询的数据，非常重要
                    });
                    //getMobileProcessByEventIdShow();
                    // $('#dg-call-list').datagrid({
                    //     loadMsg: '数据加载，请稍等.....',
                    //     data: getCallPageListShow() //这一步是加载ajax查询的数据，非常重要
                    // });

                    //关闭播放wav录音的窗口时
                    $('#wavPlayWindow').window({
                        onBeforeClose: function () {
                            var playWavAudio = document.getElementById('playWavAudio');
                            playWavAudio.pause(); //暂停播放
                        }
                    });
                }
            };
            const cfg = getMapSysConfig();
            const key = cfg.gaode?.key;
            if (!key) {
                console.error('未配置高德地图 key');
                return;
            }
            // ele.src = "https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652";
            ele.src = `https://webapi.amap.com/maps?v=2.0&key=${key}`
            ohead.appendChild(ele);
        }
        /** 重大事件弹窗关闭按钮 */
        function largeEventCancel() {
            $('#large-event-upload').window('close')
        }
        /** 查询重大事件模块的信息 */
        function getLargerEventInfo() {
            let idEvent = $('#update-event-id').val()
            eventReportItem({ eventCode: idEvent, page: 1, size: 9999 },
                function (data) {
                    largerEventList = data
                    //设置默认上报类型
                    let defaultReportTypeCode = getDefaultReportTypeCode(data);
                    $("#reportTypeCode").val(defaultReportTypeCode);
                    selectLargerEvent() // 回显汇报内容

                    resetReportContentIfAbsent(idEvent)

                    $('#larger-event-upload-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].reportTime) {
                            data[i].reportTime = convertTimeToRead(data[i].reportTime)
                        }
                        $('#larger-event-upload-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                reportTime: data[i].reportTime,
                                reportTypeName: data[i].reportTypeName,
                                reportTypeCode: data[i].reportTypeCode,
                                reportUserName: data[i].reportUserName,
                                eventTypeName: data[i].eventTypeName,
                                eventLevelName: data[i].eventLevelName,
                                vehicleDispatchedNum: data[i].vehicleDispatchedNum,
                                woundedNum: data[i].woundedNum,
                                deadNum: data[i].deadNum,
                                woundedSevereNum: data[i].woundedSevereNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                transferNum: data[i].transferNum,
                                reportContent: data[i].reportContent,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-upload-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#largeEventId').val(rowData.id)
                            $('#reportTypeCode').val(rowData.reportTypeCode)
                            $('#largeEventReportTime').datetimebox('setValue', rowData.reportTime)
                            $('#vehicleDispatchedNum').val(rowData.vehicleDispatchedNum)
                            $('#woundedNum').val(rowData.woundedNum)
                            $('#deadNum').val(rowData.deadNum)
                            $('#woundedSevereNum').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum').val(rowData.woundedSlightNum)
                            $('#transferNum').val(rowData.transferNum)
                            setTimeout(() => { // 为了防止select 事件覆盖此条记录 不能去掉延时器
                                $('#reportContent').val(rowData.reportContent)
                            }, 0);
                            // $('#larger-event-upload-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasLargerEventEdit = true // 表示有正在编辑的数据不让再双击其它数据
                            selectLargerEvent()
                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                });
        }
        /** 查询流动伤员的列表详情 */
        function getEventWoundedTransfer() {
            let idEvent = $('#update-event-id').val()
            eventWoundedTransfer({ eventCode: idEvent },
                function (data) {
                    flowOfCasualtiesList = data
                    $('#larger-event-flowOfCasualties-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].vehicleDispatchedTime) {
                            data[i].vehicleDispatchedTime = convertTimeToRead(data[i].vehicleDispatchedTime)
                        }
                        $('#larger-event-flowOfCasualties-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                vehiclePlateNo: data[i].vehiclePlateNo,
                                vehicleDispatchedTime: data[i].vehicleDispatchedTime,
                                transferNum: data[i].transferNum,
                                transferAddress: data[i].transferAddress,
                                woundedSevereNum: data[i].woundedSevereNum,
                                deadNum: data[i].deadNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                injuryRemark: data[i].injuryRemark,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-flowOfCasualties-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#eventWoundedTransferId').val(rowData.id)
                            $('#vehiclePlateNo_liudong').val(rowData.vehiclePlateNo)
                            $('#transferNum_liudong').val(rowData.transferNum)
                            $('#transferAddress_liudong').val(rowData.transferAddress)
                            $('#woundedSevereNum_liudong').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum_liudong').val(rowData.woundedSlightNum)
                            $('#deadNum_liudong').val(rowData.deadNum)
                            $('#injuryRemark').val(rowData.injuryRemark)
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', rowData.vehicleDispatchedTime)
                            // $('#larger-event-flowOfCasualties-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasFlowOfCasualtiesEdit = true // 表示有正在编辑的数据不让再双击其它数据
                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                })

        }

        function resetuploadEventPage() {
            $('#largerFormEventId').val('')
            $('#largeEventType').val('')
            $('#largeEventLevel').val('')
            $('#reportTypeCode').val('')
            $('#vehicleDispatchedNum').val('')
            $('#woundedNum').val('')
            $('#deadNum').val('')
            $('#woundedSevereNum').val('')
            $('#woundedSlightNum').val('')
            $('#transferNum').val('')
            $('#reportContent').val('')
            $('#eventWoundedTransferId').val('')
            $('#vehiclePlateNo_liudong').val('')
            $('#transferNum_liudong').val('')
            $('#transferAddress_liudong').val('')
            $('#woundedSevereNum_liudong').val('')
            $('#woundedSlightNum_liudong').val('')
            $('#deadNum_liudong').val('')
            $('#injuryRemark').val('')
        }
        // 保存座席人员电话，保存时需要传参
        let seatUserPhone = ''
        /** 上传重大事件页面信息初始化 */
        function largerEventUploadDialog() {
            $('#largeEventLevel').val('')
            $('#largeEventType').val('')
            // 查询重大事件的信息
            let idEvent = $('#update-event-id').val()
            // 获取事件基本信息的接口
            getImportantEventInfo({ id: idEvent },
                function (res) {
                    seatUserPhone = res.seatUserPhone
                    $('#large-event-upload').window({
                        title: `<div style="display:flex;justify-content: space-between;align-items: center;">
                                        <div>
                                            <span style="margin-right:20px">重大事件上报</span>
                                            <span>来电时间：<span id='largeEventCallInTimes'>${res.callInTimes ? res.callInTimes : ''}</span></span>
                                        </div>
                                        <div style="margin-right: 60px">
                                            调度员：${res.seatUser}
                                        </div>
                                    </div>`, // 替换 esay ui的title内容
                        onClose: function () {
                            resetuploadEventPage()
                        }
                    });
                    $('#largerEvent-lat').val(res.lat) // 重大事件的标题设置回显
                    $('#largerEvent-lng').val(res.lng) // 重大事件的标题设置回显
                    $('#largerEventAddress').text(res.address) // 重大事件的标题设置回显
                    $('#largerEventZS').text(res.majorCall) // 重大事件的标题设置回显
                    $('#largerFormEventId').val(res.eventId) // 重大事件的编号回显
                    // 事件类型和事件等级回显
                    // $('#largeEventType').val(res.largeEventType)
                    // $('#largeEventLevel').val(res.largeEventLevel)

                    $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                    $('#woundedNum').val(res.woundedNum)// 伤员人数
                    $('#deadNum').val(res.deadNum)// 死亡人数
                    $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                    $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                    $('#transferNum').val(res.transferNum)// 转送人数

                    $('#large-event-upload').window('open'); //打开上报重大事件页面
                    //查询重大事件的上报列表
                    getLargerEventInfo()
                    //查询重大事件的伤员流向列表
                    getEventWoundedTransfer()
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                });
            // 直接从主页面的表单字段获取事故类型和等级
            var mainEventTypeValue = $('#updateMainEventType').val();
            var mainEventLevelValue = $('#updateMainEventLevel').val();
            
            // 将主页面的值赋给弹窗字段
            $('#largeEventType').val(mainEventTypeValue || '');
            $('#largeEventLevel').val(mainEventLevelValue || '');
            
            // 回显汇报内容的默认话术
            selectLargerEvent();
            let nowTimeStr = getNowTimeStr();
            //上报时间默认取当前时间
            $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
            //出动车辆时间默认取当前时间
            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)

            // 之前上传重大事件是给领导发短信先注释掉，现在改为弹窗 填写信息
            // $.messager.confirm("提示", "是否确认上传重大事件", function (r) {
            //     if (r) {
            //         importantEvent(eventId,function(res){
            //             let data = res.sendStatusSet || []
            //             let status = true
            //             data.forEach(r=>{
            //                 if(r.message !== 'send success'){
            //                     if(status){
            //                         status = false
            //                     }
            //                     $.messager.alert('提示', r.phoneNumber+'上传重大事件失败！！！', 'error');
            //                 }
            //             })
            //             if(status){
            //                 $.messager.alert('提示', '上传重大事件成功', 'info');
            //             }
            //         },
            //         function (e, url, errMsg) {
            //             $.messager.alert('提示', '获取事件信息异常：' + errMsg, 'error');
            //         }
            //         )
            //     }
            // });
        }
        /** 重大事件表单保存接口 */
        function largeEventAddUpload() {
            let selectPhone = phoneTree.getChecked('phone-list'); // 获取选中节点的数据
            let sendMsgList =[]
            for(let s=0;s<selectPhone.length;s++){
                let item = selectPhone[s]
                for(let c=0;c<item.children.length;c++){
                    let child = item.children[c]
                    let obj = {
                        "name": child.title,
                        "phone": child.number
                    }
                    sendMsgList.push(obj)
                }
            }


            var id = $('#largeEventId').val();
            var reportTypeCodeSelected = document.getElementById("reportTypeCode");
            var reportTypeCode = reportTypeCodeSelected.value; //上报类别编码
            var reportTypeName = reportTypeCodeSelected.options[reportTypeCodeSelected.selectedIndex].text;//上报类别名称
            var reportTimeStr = $('#largeEventReportTime').val(); //上报时间
            var reportTime = reportTimeStr.replace(/[\s:-]/g, "");
            var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()// 出动车辆
            var woundedNum = $('#woundedNum').val()// 伤员人数
            var deadNum = $('#deadNum').val()// 死亡人数
            var woundedSevereNum = $('#woundedSevereNum').val()// 重伤人数
            var woundedSlightNum = $('#woundedSlightNum').val()// 轻伤人数
            var transferNum = $('#transferNum').val()// 转送人数
            var reportContent = $('#reportContent').val()// 汇报内容
            var objLE = {
                reportTypeCode,
                reportTypeName,
                reportTime,
                vehicleDispatchedNum,
                woundedNum,
                deadNum,
                woundedSevereNum,
                woundedSlightNum,
                transferNum,
                reportContent,
                reportUserName: _pSeat.user,
                reportUserPhone: seatUserPhone
            }
            if (isEdit) {
                objLE.id = id
                var largeEventTypeSelected = document.getElementById("largeEventType");
                var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
                var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
                var largeEventLevelSelected = document.getElementById("largeEventLevel");
                var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
                var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
                let objValue = {
                    eventTypeCode,
                    eventTypeName,
                    eventLevelCode,
                    eventLevelName,
                }
                const values = Object.values(objLE).map(item => {
                    return item.trim();
                })
                if (objLE.id == '') {
                    return $.messager.alert('提示', '如需修改事件请先双击表格内已上报的事件，否则请先添加并上报新事件。', 'error');
                }
                if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                    return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
                }

                $.messager.confirm("提示", '是否修改并立即上报重大事件？', function (r) {
                    if (r) {
                        largeUpdateById({ ...objLE, ...objValue,users:sendMsgList },
                            function (res) {
                                $.messager.alert('提示', '修改成功', 'success');
                                // 表单编辑完全部清空
                                $('#largeEventId').val('')
                                $('#reportTypeCode').val('')
                                $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                                $('#vehicleDispatchedNum').val('')
                                $('#woundedNum').val('')
                                $('#deadNum').val('')
                                $('#woundedSevereNum').val('')
                                $('#woundedSlightNum').val('')
                                $('#transferNum').val('')
                                $('#reportContent').val('')
                                // 表格重新渲染一下
                                getLargerEventInfo()
                                cancelPhoneList()
                            },
                            function (e, url, errMsg) {
                                //失败做处理
                                $.messager.alert('提示', errMsg);
                            }
                        );
                    }
                });
            } else {
                // 新增重大事件，调用的接口
                largeEventSubmit('1', objLE,sendMsgList)
            }
        }
        /**
         * 伤员流向表单新增
         * @param isEdit
         */
        function flowOfCasualtiesAddUpload(isEdit) {
            // 伤员的流动数据
            var id = $('#eventWoundedTransferId').val()// id
            var vehicleDispatchedTime = $('#vehicleDispatchedTime_liudong').val().replace(/[\s:-]/g, "")// 出动车辆时间
            var vehiclePlateNo = $('#vehiclePlateNo_liudong').val()// 出动车辆数量
            var transferNum = $('#transferNum_liudong').val()// 转送人数
            var transferAddress = $('#transferAddress_liudong').val()// 送往地点
            var woundedSevereNum = $('#woundedSevereNum_liudong').val()// 重伤
            var woundedSlightNum = $('#woundedSlightNum_liudong').val()// 轻伤
            var deadNum = $('#deadNum_liudong').val()// 轻伤
            var injuryRemark = $('#injuryRemark').val()// 伤情备注
            let objFOC = {
                vehicleDispatchedTime,
                vehiclePlateNo,
                transferNum,
                transferAddress,
                woundedSevereNum,
                woundedSlightNum,
                deadNum,
                injuryRemark,
            }
            if (isEdit) {
                objFOC.id = id
                flowOfCasualtiesUpdate(objFOC)
            } else {
                largeEventSubmit('2', objFOC)
            }
        }
        /**
         * 伤员流向更新
         * @param obj
         */
        function flowOfCasualtiesUpdate(obj) {
            const values = Object.keys(obj).filter(val => {
                if (val != 'injuryRemark' && obj[val] == '') {
                    return val
                }
            })
            if (obj.id == '') {
                return $.messager.alert('提示', '如需修改伤员流向请先双击表格内已添加的伤员流向，否则请先添加一条新的伤员流向。', 'error');
            }
            if (values.length > 0) {
                return $.messager.alert('提示', '流动伤员必要信息未填写，无法上报。', 'error');
            }
            $.messager.confirm("提示", '是否修改并立即上报伤员流向？', function (r) {
                if (r) {
                    let params = {
                        ...obj
                    }
                    eventWoundedTransferUpdateById(params,
                        function (res) {
                            $.messager.alert('提示', '修改成功', 'success');
                            // 表单编辑完全部清空
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                            $('#vehiclePlateNo_liudong').val('')
                            $('#transferNum_liudong').val('')
                            $('#transferAddress_liudong').val('')
                            $('#woundedSevereNum_liudong').val('')
                            $('#woundedSlightNum_liudong').val('')
                            $('#deadNum_liudong').val('')
                            $('#injuryRemark').val('')
                            // 表格重新渲染一下
                            getEventWoundedTransfer()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        /**
         * 重大事件保存按钮提交数据
         * @param type 1-上报列表，2-伤员流向
         * @param obj
         * @param sendMsgList
         */
        function largeEventSubmit(type, obj,sendMsgList) {
            if(!canOperateEvent(currentEventInfo.isLock,currentEventInfo.lockSeatUserId,currentEventInfo.lockSeatId)){
                $.messager.alert('提示', '事件被 ' + (currentEventInfo.lockSeatCode || '') + ' (' + (currentEventInfo.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                return;
            }
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            let objValue = {}
            if (type == '2') {
                objValue.injuryRemark = obj.injuryRemark
                delete obj.injuryRemark
            }
            const values = Object.values(obj).map(item => {
                return item.trim();
            })
            if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
            }
            $.messager.confirm("提示", `${type == '1' ? '是否新增并立即上报重大事件？' : '是否新增并立即上报伤员流向'}`, function (r) {
                if (r) {
                    let params = {
                        eventInfo: {
                            eventTypeCode,
                            eventTypeName,
                            eventLevelCode,
                            eventLevelName,
                            eventCode: $('#update-event-id').val(),
                            address: $('#largerEventAddress').text(),
                            latitude: $('#largerEvent-lat').val(),
                            longitude: $('#largerEvent-lng').val(),
                            occurrenceTime: $('#update-det-call-in-times').val().replace(/[\s:-]/g, ""),
                            seatUserName: _pSeat.user
                        },
                        reportItems: type == '1' ? [obj] : [],
                        woundedTransfers: type == '2' ? [{ ...obj, ...objValue }] : [],
                        users:sendMsgList
                    }
                    eventInfoReport(params,
                        function (res) {
                            $.messager.alert('提示', '上报成功', 'success');
                            // 上报成功表单全清空
                            if (type == '1') {
                                // 表单编辑完全部清空
                                $('#reportTypeCode').val('')
                                $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                                $('#vehicleDispatchedNum').val('')
                                $('#woundedNum').val('')
                                $('#deadNum').val('')
                                $('#woundedSevereNum').val('')
                                $('#woundedSlightNum').val('')
                                $('#transferNum').val('')
                                $('#reportContent').val('')
                                // 表格重新渲染一下
                                getLargerEventInfo()
                                // 查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasLargerEventEdit = false //事件保存后 允许再双击其它事件 目前不存在编辑，先注释 后续可能会加此功能
                            } else {
                                // 表单编辑完全部清空
                                $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                                $('#vehiclePlateNo_liudong').val('')
                                $('#transferNum_liudong').val('')
                                $('#transferAddress_liudong').val('')
                                $('#woundedSevereNum_liudong').val('')
                                $('#woundedSlightNum_liudong').val('')
                                $('#deadNum_liudong').val('')
                                $('#injuryRemark').val('')
                                // 查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasFlowOfCasualtiesEdit = false //事件保存后 允许再双击其它事件/目前不存在编辑，先注释 后续可能会加此功能
                            }
                            // $('#large-event-upload').window('close')
                            cancelPhoneList()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }

        /**
         * 重置汇报内容
         * @param idEvent 120事件id
         */
        function resetReportContentIfAbsent(idEvent) {
            if (!$('#reportContent').val()) {
                // 获取事件基本信息的接口
                getImportantEventInfo({ id: idEvent },
                    function (res) {
                        let nowTimeStr = getNowTimeStr();
                        //上报时间默认取当前时间
                        $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
                        //出动车辆时间默认取当前时间
                        $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)

                        $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                        $('#woundedNum').val(res.woundedNum)// 伤员人数
                        $('#deadNum').val(res.deadNum)// 死亡人数
                        $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                        $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                        $('#transferNum').val(res.transferNum)// 转送人数

                        selectLargerEvent()
                    },
                    function (e, url, errMsg) {
                        //失败做处理
                        $.messager.alert('提示', errMsg);
                    });
            }
        }

        /** 重大事件类型等级选择时触发，第一次打开页面或事件等级和事件类型选择时，回显汇报内容 */
        function selectLargerEvent() {
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            var reportTypeCode = $('#reportTypeCode').val()
            var reportTime = $('#largeEventReportTime').val()
            var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()
            var woundedNum = $('#woundedNum').val()
            var deadNum = $('#deadNum').val()
            var woundedSevereNum = $('#woundedSevereNum').val()
            var woundedSlightNum = $('#woundedSlightNum').val()
            var transferNum = $('#transferNum').val()
            if (eventTypeName && eventLevelName) {
                //汇报内容模板
                var largeEventCallInTimes = $('#largeEventCallInTimes').text().replace(/[\s:-]/g, "");// 来电时间
                let timeArr = largeEventCallInTimes.split('')
                let y = timeArr.slice(0, 4).join('')
                let m = timeArr.slice(4, 6).join('')
                let d = timeArr.slice(6, 8).join('')
                let hh = timeArr.slice(8, 10).join('')
                let mm = timeArr.slice(10, 12).join('')
                let ss = timeArr.slice(12, 14).join('')
                var callInTimes = y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
                var largerEventAddress = $('#largerEventAddress').text() // 地点
                var largerEventZS = $('#largerEventZS').text() // 呼叫原因
                if (reportTypeCode == '01') {
                    //var str = `在【${callInTimes}】接到来电，在【${largerEventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
                    var str = createEventFirstReportContent(callInTimes, largerEventAddress, eventLevelName, eventTypeName);
                    $('#reportContent').val(str)
                } else if ((reportTypeCode == '02' || reportTypeCode == '03') && vehicleDispatchedNum && woundedNum && deadNum && woundedSevereNum && woundedSlightNum && transferNum && reportTime) {
                    //追报默认汇报内容模板
                    var str = createEventReportContent(convertTime(reportTime.replace(/[\s:-]/g, "")), vehicleDispatchedNum, woundedNum, deadNum, woundedSevereNum, woundedSlightNum, transferNum);
                    $('#reportContent').val(str)
                }

            }
        }
        function operation(value, row, index) {
            let node = '';
            if (row.reportTypeCode === '02') {
                node += `
                            <a href="javascript:largeEventDel(${index});" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;padding: 3px 8px;border-radius: 5px;">删 除</a>
                            `
            }
            return node
        }
        function largeEventDel(index) {
            $.messager.confirm("提示", '是否删除此条重大事件？', function (r) {
                if (r) {
                    var id = largerEventList[index].id
                    let params = {
                        id
                    }
                    largeDeleteById(params,
                        function (res) {
                            $.messager.alert('提示', '删除成功', 'success');
                            getLargerEventInfo() // 表格数据重新渲染下
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        /**
         * 重大事件的汇报内容气泡弹窗
         * @param value
         * @param row
         * @param index
         */
        function reportContentFormatter(value, row, index) {
            let node = `
                        <div id="reportContentFormatter${index}" onmouseout="closeAllTips()" onmouseover="reportContentHover(${index})">${row.reportContent}</div>
                      `
            return node
        }
        function reportContentHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + largerEventList[index].reportContent + "</span>", `#reportContentFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /**
         * 重大事件的伤情备注气泡
         * @param value
         * @param row
         * @param index
         */
        function injuryRemarkFormatter(value, row, index) {
            let node = `
                        <div id="injuryRemarkFormatter${index}" onmouseout="closeAllTips()" onmouseover="injuryRemarkHover(${index})">${row.injuryRemark}</div>
                      `
            return node
        }
        function injuryRemarkHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].injuryRemark + "</span>", `#injuryRemarkFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /**
         * 重大事件的送往地点气泡
         * @param value
         * @param row
         * @param index
         */
        function transferAddressFormatter(value, row, index) {
            let node = `
                        <div id="transferAddressFormatter${index}" onmouseout="closeAllTips()" onmouseover="transferAddressHover(${index})">${row.transferAddress}</div>
                      `
            return node
        }
        function transferAddressHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].transferAddress + "</span>", `#transferAddressFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        function closeAllTips() {
            layui.use('layer', function () {
                var layer = layui.layer;
                layer.closeAll('tips');
            });
        }
        function showCheck(ev) {
            ev = ev || ''
            $('#mainSuit').html(ev)
        }
        /** 点击伤员流动的车牌号选择弹窗打开 */
        function chooseCar() {
            $('#chooseCarNoId').window('open');
            chooseCarInput()
        }
        /** 查询车辆 */
        function chooseCarInput() {
            let params = {
                plateNum: $('#chooseCarInput').val(),// 车牌号
                stationName: '' // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item,
                        spread: $('#chooseCarInput').val() ? true : false,
                        type: '1',
                        children: []
                    }
                    if (data[item].length > 0) {
                        data[item].forEach(val => {
                            obj.children.push({
                                spread: $('#chooseCarInput').val() ? true : false,
                                type: '2',
                                title: val.plateNum
                            })
                        })

                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#car-number-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            if (obj.data.type == '2') {
                                $('#chooseCarNoId').window('close');
                                // 输入框赋值
                                $('#vehiclePlateNo_liudong').val(obj.data.title)
                            }
                        }
                    });
                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        /** 点击伤员流动的车牌号选择弹窗打开 */
        function chooseStation() {
            $('#chooseStationId').window('open');
            chooseStationInput()
        }
        /** 查询车辆 */
        function chooseStationInput() {
            let params = {
                plateNum: '',// 车牌号
                stationName: $('#chooseStationInput').val() // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item
                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#station-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            // 输入框赋值
                            $('#chooseStationId').window('close');
                            $('#transferAddress_liudong').val(obj.data.title)
                        }
                    });

                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        //查询相关字典
        queryAllDic(["event_source", "call_type", "patient_status", "country", "race", "push_outer_type","patient_gender"],
            function (data) {
                _dics = data
                //window.parent.gProxy.updateDic(JSON.stringify(data));
                //事件来源
                insertIntoSelect('update-evd-source', 'event_source', data);
                //呼叫类型
                callTypeList = insertIntoSelect('update-evd-call-type', 'call_type', data);
                //病情
                //insertIntoSelect('update-det-condition', 'patient_status', data);
                //性别
                insertIntoSelect('update-det-gender', 'patient_gender', data);
                //国家
                insertIntoSelect('update-det-country', 'country', data);
                //民族
                insertIntoSelect('update-det-race', 'race', data);
                //推送类型
                insertIntoSelect2('update-push-outer-type', 'push_outer_type', data);
                
                // 初始化所属区域下拉框
                initUpdateAreaSelect();
                
                // 初始化重大事件联动功能
                initUpdateEventTypeLinkage();
                
                // 初始化国籍和外籍人员联动功能
                initUpdateCountryForeignLinkage();
                
                // 初始化地址自动匹配功能
                initUpdateAddressAutoMatch();
                
                // 初始化症状判断和呼叫原因的联动
                initUpdateSymptomCallReasonLinkage();
                
                // 初始化呼叫类型创建事件判断列表
                initUpdateCallTypeShouldCreateEventList();

            }, function (e, url, errMsg) { });//查询所有字典
        /** 刷新事件信息 */
        function refreshEvent() {
            initMapAndEvent()
        }
        function getEventByIdShow() {
            //不再调用isUpdateEventCallType方法判断是否可以修改呼叫类型
            //isUpdateEventCallType(eventId);
            //获得事件信息
            getEventById(eventId,
                function (data) {
                    if (data == null) {
                        $.messager.alert('提示', '查询不到此事件编号', 'error');
                    }
                    currentEventInfo = data;
                    if (data.status == '3' || data.status == '4') {
                        DetailsAboutTheDisabled(true)//禁用表单
                        // 发送指导短信按钮隐藏掉
                        $('.sendMessageDom').css('display', 'none')
                        $('.keepEvent').css('display', 'none')
                        // ADLS系统按钮隐藏
                        $('.getAdlsUrl').css('display', 'none')
                    } else {
                        //只要在发送指导短信按钮展示的情况下，才展示发送指导短信按钮
                        if (btnSendH5Msg === '1') {
                            $('.sendMessageDom').css('display', 'inline-block')
                        }
                        $('.keepEvent').css('display', 'inline-block')
                        // ADLS系统按钮显示
                        $('.getAdlsUrl').css('display', 'inline-block')
                    }
                    // 根据事件状态显示不同的标题信息
                    let titleText = "&nbsp;&nbsp;事件详情&nbsp;[" + getEventStatusText(data.status);
                    
                    if (data.status == '6') { // 挂起状态
                        if (data.pendingTime) {
                            titleText += "&nbsp;&nbsp;挂起时间：" + data.pendingTime;
                        }
                    } else if (data.status == '1') { // 落单状态
                        if (data.createTime) {
                            titleText += "&nbsp;&nbsp;落单时间：" + data.createTime;
                        }
                    }
                    titleText = titleText + "]";
                    $('#event_title_num').html(titleText);
                    //获取事件信息
                    $('#update-event-id').val(data.id);
                    $('#update-event-status').val(data.status);
                    $('#update-evd-source').val(data.eventSrc);
                    
                    // 直接禁用事件来源、呼叫类型和接收中心输入框
                    $('#update-evd-source').attr('disabled', true);
                    $('#update-evd-call-type').attr('disabled', true);
                    $('#update-push-outer-type').attr('disabled', true);
                    
                    // 设置呼叫类型
                    if (callTypeShouldCreateEventList.length > 0) {
                        safeSetCallTypeValue(data.callType);
                    } else {
                        $('#update-evd-call-type').val(data.callType);
                    }
                    
                    //$('#update-det-condition').val(data.patientCondition);
                    $('#update-det-gender').val(data.patientGender || '');
                    $('#update-det-country').val(data.patientCountry);
                    $('#update-det-amount').val(data.patientAmount);
                    $('#upload-det-address').val(data.addressWait);
                    $('#update-det-call-in-times').datetimebox('setValue', data.callInTimes);
                    let index = data.majorCall.indexOf('_')
                    if (index !== -1) {
                        let majorCall = data.majorCall.split('_')
                        $('#update-det-major').val(majorCall[0]);
                        $('#mainSuit').html(majorCall[1])
                    } else {
                        $('#update-det-major').val(data.majorCall);
                    }
                    $('#update-det-callin').val(data.callIn);
                    $('#update-det-contact').val(data.contact);
                    $('#update-det-contacter').val(data.contacter);
                    $('#update-det-120remark').val(data.centerRemark);
                    $('#update-det-name').val(data.patientName);
                    $('#update-det-race').val(data.patientRace);
                    $('#update-det-age').val(data.patientAge);
                    $('#update-event-lat').val(data.lat);
                    $('#update-event-lng').val(data.lng);
                    $('#upload-event-lat').val(data.addressWaitLat);
                    $('#upload-event-lng').val(data.addressWaitLng);
                    if (data.patientDiagnosis) {
                        checkedList = data.patientDiagnosis.split(' ') // 处理回显多选框数据
                    }
                    if (data.eventType == '1') {
                        $("#updateEventType").prop("checked", true);
                        $("#largerEventUpload").show()
                        // 启用事故类型和事故等级下拉框
                        $('#updateMainEventType').prop('disabled', false);
                        $('#updateMainEventLevel').prop('disabled', false);
                        
                        // 回显事故类型和事故等级
                        if (data.eventTypeCode) {
                            $('#updateMainEventType').val(data.eventTypeCode);
                        }
                        if (data.eventLevelCode) {
                            $('#updateMainEventLevel').val(data.eventLevelCode);
                        }
                    } else {
                        $("#largerEventUpload").hide();
                        // 禁用并清空事故类型和事故等级下拉框
                        $('#updateMainEventType').prop('disabled', true).val('');
                        $('#updateMainEventLevel').prop('disabled', true).val('');
                    }
                    //回显下拉框数据    获取用于显示下拉框的 DOM 元素 ----开始
                    var categorySelect = document.getElementById("updateSelect1");
                    categorySelect.innerHTML = '<option value="">请选择分类</option>'
                    var selectedData = []
                    var selectedIndex = 0
                    // 动态生成外科类别下拉框
                    for (var i = 0; i < symptomData.length; i++) {
                        var categoryOption = document.createElement("option");
                        categoryOption.value = i;
                        categoryOption.innerHTML = symptomData[i].t;
                        // 检查是否选中该类别
                        if (checkedList.includes(symptomData[i].t)) {
                            categoryOption.selected = true;
                            selectedIndex = i
                            if (symptomData[i].c) {
                                selectedData = symptomData[i].c
                            }
                        }
                        categorySelect.appendChild(categoryOption);
                    }
                    if (selectedData.length > 0) {
                        //回显第二个下拉框
                        var secondSelect = document.getElementById("updateSelect2");
                        secondSelect.innerHTML = '<option value="">请选择分类</option>';
                        for (var j = 0; j < symptomData[selectedIndex].c.length; j++) {
                            var selectedOption = document.createElement("option");
                            selectedOption.value = j;
                            selectedOption.innerHTML = symptomData[selectedIndex].c[j].t;
                            // 检查是否选中该类别
                            if (checkedList.includes(symptomData[selectedIndex].c[j].t)) {
                                selectedOption.selected = true;
                            }
                            secondSelect.appendChild(selectedOption);
                        }
                        // 显示第二个下拉框
                        document.getElementById('updateSelect2').style.display = '';
                    } else {
                        document.getElementById('updateSelect2').style.display = 'none';
                    }
                    // 根据呼叫类型判断是否显示接收中心
                    if (data.callType === "11") {
                        // 如果是区域联动事件(callType=11)，显示接收中心并获取区域联动信息
                        $('.field-push-outer-type').show();
                        //$('#update-push-outer-type').val(data.pushOuterType);
                        
                        getEventRegionLinkageByExternalBizId(data.id, function(regionLinkageData) {
                            if (regionLinkageData && regionLinkageData.receiveRegionCode) {
                                // 设置接收中心下拉框的值为区域联动信息中的接收区域编码
                                $('#update-push-outer-type').val(regionLinkageData.receiveRegionCode);
                                console.log('区域联动事件接收中心回显成功:', regionLinkageData.receiveRegionCode);
                            } else {
                                console.log('未找到区域联动信息或接收区域编码为空');
                            }
                        }, function(e, url, errMsg) {
                            console.error('获取区域联动信息失败:', errMsg);
                        });
                    } else {
                        // 非区域联动事件，隐藏接收中心
                        $('.field-push-outer-type').hide();
                    }
                    
                    //回显数据----结束
                    if (currentEventInfo.patientIsForeign == 1) {
                        // currentEventInfo.patientIsForeign = 1
                        $("#updateCreateForeign").prop("checked", true);
                    } else {
                        // currentEventInfo.patientIsForeign = 0
                        $("#updateCreateForeign").prop("checked", false);
                    }
                    
                    // 回显所属区域
                    if (data.callRegionId) {
                        $('#update-det-area').val(data.callRegionId);
                    }
                    
                    // 回显新增字段
                    if (data.isNeedStretcher == 1) {
                        $("#update-need-stretcher").prop("checked", true);
                    }
                    if (data.isTest == 1) {
                        $("#update-is-test").prop("checked", true);
                    }
                    if (data.mpdsSummary) {
                        $('#update-det-alds-summary').val(data.mpdsSummary);
                    }
                    if (data.specialRequirement) {
                        $('#update-det-special-req').val(data.specialRequirement);
                    }
                    if (data.patientInfoHistory) {
                        $('#update-patient-info').val(data.patientInfoHistory);
                    }
                    // 回显紧急程度
                    if (data.emergencyDegree) {
                        $('#update-emergency-level').val(data.emergencyDegree);
                    }
                    
                    // 回显关联首次事件
                    if (data.firstEventId) {
                        $('#update-first-event-id').val(data.firstEventId);
                        // 获取首次事件详情用于显示
                        getEventById(data.firstEventId, function(firstEventData) {
                            if (firstEventData) {
                                updateShowFirstEventInfo(firstEventData);
                            }
                        }, function(e, url, errMsg) {
                            console.error('获取关联首次事件详情失败：', errMsg);
                        });
                    }
                    
                    //查询诊断字典
                    /*getPatientDiagnosis(function (data) {
                        let list = data[0].val
                        //加载诊断树字典
                        loadRegionTree(list, "#patientDiagnosislevel1")
                    }, function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('提示', '查询诊断字典失败，异常信息：' + errMsg);
                    })*/
                    //当前事件的经纬度
                    if (data.lng && data.lat) {
                        currentEventPr = new AMap.LngLat(data.lng, data.lat);
                    }
                    // 填充调度员信息
                    $('#dispatcherSeatCode').text(data.seatCode || '-');
                    $('#dispatcherSeatUser').text(data.seatUserName || '-');
                    $('#dispatcherCreateTime').text(data.createTime || '-');

                    //初始化地图
                    initMap(data);
                    // //检测当前号码和系统配置是否是同一个，是则提示未检测到来电号码，请询问来电人 并且清空主叫号码和联系电话
                    // if(data.callIn){
                    //     querySysConf("site_out_lines_nums",res=>{
                    //         res = res || {}
                    //         if(res.val){
                    //             let val = JSON.parse(res.val) || []
                    //             if(Array.isArray(val) && val.indexOf(data.callIn) !== -1){
                    //                 $('#update-det-callin').val('');
                    //                 $('#update-det-contact').val('');
                    //                 $.messager.alert('提示', '未检测到来电号码，请询问来电人', 'error');
                    //             }
                    //         }
                    //     },
                    //     (e, url, errMsg)=> {
                    //         $.messager.alert('提示', '获取配置异常：' + errMsg, 'error');
                    //     })
                    // }
                },
                function (e, url, errMsg) {
                    $.messager.alert('提示', '获取事件信息异常：' + errMsg, 'error');
                });
        }
        function uploadOperationFormatter(value, row, index) {
            let node = `
                        <img style="width:22px;height:22px;cursor: pointer;" src="style/img/tongbuSelect.png" onclick="syncAddress(${index})">
                      `
            let eventStatus = $("#update-event-status").val()
            if (eventStatus === '3' || eventStatus === '4') {//已完成和已撤销不可以改事件地址
                node = ''
            }
            return node
        }
        function uploadAddressFormatter(value, row, index) {
            let node = `
                        <div id="uploadAddress${index}" onmouseout="closeAllTips()" onmouseover="uploadAddressHover(${index})">${row.address}</div>
                      `
            return node
        }
        function uploadAddressHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + uploadAddress[index].address + "</span>", `#uploadAddress${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        function uploadTimeFormatter(value, row, index) {
            let y = new Date(row.createTime).getFullYear()
            let m = new Date(row.createTime).getMonth() + 1
            let d = new Date(row.createTime).getDate()
            let h = new Date(row.createTime).getHours()
            let mm = new Date(row.createTime).getMinutes()
            // 分别对月、日、时、分补零
            m = m < 10 ? "0" + m : m;
            d = d < 10 ? "0" + d : d;
            h = h < 10 ? "0" + h : h;
            mm = mm < 10 ? "0" + mm : mm;
            let node = `
                      <div>${y}-${m}-${d} ${h}:${mm}</div>
                      `
            return node
        }
        function processIdFormatter(value, row, index) {
            return `<div id='processId${index}' onmouseout="closeAllTips()" onmouseover="showProcessId(${index})">${row.processId}</div>`
        }
        function addressFormatter(value, row, index) {
            return `<div id='address${index}' onmouseout="closeAllTips()" onmouseover="showAddress(${index})">${row.address}</div>`
        }
        function centerRemarkFormatter(value, row, index) {
            return `<div id='centerRemark${index}' onmouseout="closeAllTips()" onmouseover="showCenterRemark(${index})">${row.centerRemark}</div>`
        }
        function showProcessId(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + dispatcherTask[index].processId + "</span>", `#processId${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        function showAddress(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + dispatcherTask[index].address + "</span>", `#address${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        function showCenterRemark(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + dispatcherTask[index].centerRemark + "</span>", `#centerRemark${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /** 打开现场照片的弹窗 */
        function openPictures() {
            let params = {
                type: '0',
                taskId: $("#update-event-id").val()
            }
            getPhoto(params, function (data) {
                uploadPictureVideoList = data
                if (data && data.length > 0) {
                    querySysConf('fileserver_download_url',
                        function (dataurl) {
                            //dataurl值例子：http://*************:7707/doFile/download
                            let html = ''
                            data.forEach((item, index) => {
                                if (item.fileType == '1') {
                                    html += `
                                            <div style="position:relative;margin:0 15px 15px 0">
                                                <img onclick='showUploadPicture(${index})'
                                                    src="${dataurl}?attachmentId=${item.fileId}"
                                                    style="object-fit: contain;width:200px;height:200px;background:lightgray;">
                                                <div style="position:absolute;left:0;bottom:0;width:200px;line-height: 35px;font-size: 14px;font-weight: bold; height: 35px; background-color: #fff;opacity: 0.6;"><span style="line-height: 35px">上传时间：${convertTimeToRead2(item.createdTime)}</span></div>
                                            </div>
                                           `;
                                }
                                if (item.fileType == '2') {
                                    html += `
                                            <div onclick='showUploadVideoH5(${index})'
                                                style='margin:0 15px 15px 0;height:200px;width:200px;display:flex;align-items:center;justify-content: center;border:1px solid lightgrey;    position: relative;'>
                                                <div style="width:200px;height:200px;background-image: url('style/img/uploadPicture.jpg');background-repeat: no-repeat;background-size: contain;background-position: center;">
                                                </div>
                                                <div style="position:absolute;left:0;bottom:0;width:200px;line-height: 35px;font-size: 14px;font-weight: bold; height: 35px; background-color: #fff;opacity: 0.6;"><span style="line-height: 35px">上传时间：${convertTimeToRead2(item.createdTime)}</span></div>
                                            </div>
                                            `;
                                }
                            })
                            $('#carousel').html(html)
                        },
                        function (e, url, errMsg) {
                            //失败才做处理
                            $.messager.alert('异常', '获取文件下载链接失败，异常信息：' + errMsg);
                        });
                }
                $('#on-site-photo').window('open')
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        /** 打开童话记录的弹窗 */
        function openCallRecord() {
            $('#on-call-record').window('open')
            getCallPageListShow();
        }
        /**
         * 现场视频跳转到H5
         * @param index
         */
        function showUploadVideoH5(index) {
            querySysConf('pphi_h5_url',
                function (data) {
                    //data值例子：https://corsyn.uicp.net:18086/noteUrl/
                    let url = data + '?&fileId=' + uploadPictureVideoList[index].fileId + '#/uploadPictureShow'
                    try {
                        window.parent.gProxy.openBrowser(url);
                    } catch (e) {
                        try {
                            window.parent.parent.gProxy.openBrowser(url);
                        } catch (e) {
                            window.parent.parent.parent.gProxy.openBrowser(url);
                        }
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取文件下载链接失败，异常信息：' + errMsg);
                });

        }
        /**
         * 现场图片放大看
         * @param index
         */
        function showUploadPicture(index) {
            querySysConf('fileserver_download_url',
                function (data) {
                    //data值例子：http://*************:7707/doFile/download
                    let html = `<img src="${data}?attachmentId=${uploadPictureVideoList[index].fileId}" style="object-fit: contain;width:100%;height:100%;">`
                    $('#showPicture').html(html)
                    $('#showPictureDialog').window('open')
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取文件下载链接失败，异常信息：' + errMsg);
                });
        }
        /** 关闭现场照片 */
        function closePicture() {
            $('#on-site-photo').window('close')
        }
        /**
         * 同步地址走接口
         * @param index
         */
        function syncAddress(index) {
            let uploadAddressParams = {
                address: uploadAddress[index].address,
                eventId: uploadAddress[index].eventId,
                lat: uploadAddress[index].lat,
                lng: uploadAddress[index].lng
            }
            updateEventAddress(uploadAddressParams,
                function (data) {
                    $('#update-det-address').val(uploadAddress[index].address);
                    // 地址设置后自动匹配区域
                    autoMatchUpdateRegionByAddress(uploadAddress[index].address);
                    $('#update-event-lng').val(uploadAddress[index].lng);
                    $('#update-event-lat').val(uploadAddress[index].lat);
                    sendToMap2(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
                    $.messager.alert('提示', '同步地址成功', 'success');
                },
                function (e, url, errMsg) {
                    $.messager.alert('提示', '同步地址信息异常：' + errMsg, 'error');
                });
        }
        /**
         * 发送到地图不走接口
         * @param index
         */
        function sendToMap(index) {
            if (uploadAddress[index] && uploadAddress[index].lat && uploadAddress[index].lng) {
                sendToMap2(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
            }
            uploadAddress[index]
        }
		function sendToMap2(address, lng, lat) {
            try {
                try{
                    window.parent.parent.gProxy.sendToMap(address, lng, lat);
                    console.log("sendToMap 1")
                } catch (e) {
                    window.parent.parent.parent.gProxy.sendToMap(address, lng, lat);
                    console.log("sendToMap 1")
                }
            } catch (e) {
                try{
                    window.parent.parent.bsProxy.sendToMap(address, lng, lat);
                    console.log("sendToMap 2")
                } catch (e) {
                    window.parent.parent.parent.bsProxy.sendToMap(address, lng, lat);
                    console.log("sendToMap 2")
                }
            }
        }
        //重大事件勾选按钮事件
        $('#updateEventType').on("click", function (ev) {
            if ($('#updateEventType').is(':checked')) {
                $("#updateEventType").prop("checked", true);
                $("#largerEventUpload").show();
                // 启用事故类型和事故等级下拉框
                $('#updateMainEventType').prop('disabled', false);
                $('#updateMainEventLevel').prop('disabled', false);
            } else {
                let hasEventId = $("#update-event-id").val()
                if (!hasEventId) { // 如果没有id说明事件还没有生成，不存在上报重大事件，可以隐藏
                    $("#largerEventUpload").hide();
                    // 禁用并清空事故类型和事故等级下拉框
                    $('#updateMainEventType').prop('disabled', true).val('');
                    $('#updateMainEventLevel').prop('disabled', true).val('');
                } else {
                    isReported({ eventCode: hasEventId },
                        function (res) {
                            //是否上传过
                            if (res.isReported == '0') {
                                $("#largerEventUpload").hide();
                                // 禁用并清空事故类型和事故等级下拉框
                                $('#updateMainEventType').prop('disabled', true).val('');
                                $('#updateMainEventLevel').prop('disabled', true).val('');
                            } else {
                                // 上传过 不准取消
                                $.messager.alert('注意', '已上报过重大事件，不可取消勾选', false);
                                $("#largerEventUpload").show();
                                $("#updateEventType").prop("checked", true);
                                // 保持启用状态
                                $('#updateMainEventType').prop('disabled', false);
                                $('#updateMainEventLevel').prop('disabled', false);
                            }
                        },
                        function (e, url, errMsg) {
                            //失败不做处理
                        });
                }
            }
        });
        /** 获取事件任务信息 */
        function getMobileProcessByEventIdShow() {
            $('#dg-mobiel-process-list').datagrid('loading');//打开进度条：打开等待div
            //获取事件任务信息
            var getMobileProcessByEventIdParams = {
                "eventId": eventId,
                "beginTime": '',
                "endTime": '',
                "stationId": '',
                "processId": '',
                "stationCode": ''
            };
            getMobileProcessByEventId(getMobileProcessByEventIdParams,
                function (data) {
                    $('#dg-mobiel-process-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    dispatcherTask = data || [{}, {}, {}]
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.length; i++) {
                        $('#dg-mobiel-process-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                stationName: data[i].stationName,
                                carName: data[i].carName,
                                plateNum: data[i].plateNum,
                                dispatchDoctor: data[i].dispatchDoctor,
                                dispatchDoctorPhone: data[i].dispatchDoctorPhone,
                                dispatchNurse: data[i].dispatchNurse,
                                dispatchNursePhone: data[i].dispatchNursePhone,
                                dispatchDriver: data[i].dispatchDriver,
                                dispatchDriverPhone: data[i].dispatchDriverPhone,
                                isFinishdStr: data[i].isFinishdStr,
                                toStatusStr: data[i].toStatusStr,
                                callTypeCodeName: data[i].callTypeCodeName,
                                emergencyRadius: data[i].emergencyRadius,
                                dispatchMedicalStaff: data[i].dispatchMedicalStaff,
                                dispatchMedicalStaffPhone: data[i].dispatchMedicalStaffPhone,
                                dispatchWorker: data[i].dispatchWorker,
                                dispatchWorkerPhone: data[i].dispatchWorkerPhone,
                                mobileProcessCreateTime: data[i].mobileProcessCreateTime,
                                seatCode: data[i].seatCode,
                                seatUserName: data[i].seatUserName,
                                seatUser: data[i].seatUser,
                                processId: data[i].processId,
                                address: data[i].address,
                                centerRemark: data[i].centerRemark,
                                changeSendCarNumber: data[i].changeSendCarNumber,
                                stationRefuseStatus: data[i].stationRefuseStatus,
                                stationRefuseReason: data[i].stationRefuseReason
                            }
                        });
                        if (data[i].isFinishd == '1') {
                            // 检查地图是否已初始化
                            if (updateMap && typeof updateMap.plugin === 'function') {
                                // 创建起点和终点
                                updateMap.plugin("AMap.Driving", function () {
                                    var driving = new AMap.Driving({
                                        map: updateMap,
                                        autoFitView: true,
                                        policy: AMap.DrivingPolicy.LEAST_TIME,
                                    });
                                    var startPoint = [data[i].lng, data[i].lat];
                                    const startIcon = new AMap.Icon({
                                        size: new AMap.Size(20, 38),
                                        imageSize: new AMap.Size(20, 38),
                                        image: 'style/img/ambul.png' // 图标路径
                                    });
                                    const markerStart = new AMap.Marker({
                                        position: startPoint,
                                        offset: new AMap.Pixel(0, 0),
                                        icon: startIcon, //添加 icon 图标 URL
                                    });

                                    updateMap.add(markerStart);
                                    var endPoint = [data[i].destinationLng, data[i].destinationLag];

                                    driving.search(startPoint, endPoint, function (status, result) {
                                        if (result.routes && result.routes.length) {
                                            var path = parseRouteToPath(result.routes[0])
                                            var polyline = new AMap.Polyline({
                                                path: path,
                                                showDir: true, // 显示箭头指示
                                                strokeColor: '#67C23A',
                                                strokeOpacity: 1,
                                                strokeWeight: 5
                                            });
                                            polyline.setMap(updateMap);
                                        }
                                        //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
                                        //查询成功时，result 即为对应的驾车导航信息
                                    });
                                });
                            } else {
                                console.warn('地图尚未初始化，跳过路线绘制');
                            }
                            // var myIcon = new AMap.Icon("style/img/ambul.png", new AMap.Size(48, 48));
                        }
                    }

                    //回显任务信息的结果
                    $('#dg-mobiel-process-list').datagrid('loaded');//关闭loding进度条；
                    $('#dg-mobiel-process-list').datagrid({
                        onClickRow: function (rowIndex, rowData) {
                            mobileProcessId = rowData.processId
                        },
                    });

                },
                function (e, url, errMsg) {
                    $('#dg-mobiel-process-list').datagrid('loaded');//关闭loding进度条
                    $.messager.alert('提示', '获取事件任务信息异常：' + errMsg, 'error');
                });
        }
        function parseRouteToPath(route) {
            let path = []
            for (let i = 0, l = route.steps.length; i < l; i++) {
                let step = route.steps[i]
                for (let j = 0, n = step.path.length; j < n; j++) {
                    path.push(step.path[j])
                }
            }

            return path
        }
        /** 上报地址 */
        function getUploadAdress() {
            let _uploadAddIntervals
            try {
                _uploadAddIntervals = window.parent._uploadAddInterval
            } catch (e) {
                try {
                    _uploadAddIntervals = window.parent.parent._uploadAddInterval;
                } catch (e) {
                    _uploadAddIntervals = window.parent.parent.parent._uploadAddInterval;
                }
            }
            clearTimeout(window.getUploadAddressTimeout);
            // $('#dg-uploadAdress').datagrid('loading');//打开进度条：打开等待div
            //获取最新的事件地址
            var getuploadAddressParams = {
                eventId: eventId,
                maxSize: 2,
            };
            getUploadAddress(getuploadAddressParams,
                function (data) {
                    uploadAddress = data
                    $('#dg-uploadAdress').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.length; i++) {
                        // 如果上报的地址其一和 地址栏上的地址时相同的，默认就是启用了当前上报地址给一个背景颜色
                        let useLng = $('#update-event-lng').val()
                        let useLat = $('#update-event-lat').val()
                        $('#dg-uploadAdress').datagrid({
                            rowStyler: function (index, row) {
                                if (data[index].lng == useLng && data[index].lat == useLat) {
                                    return 'background-color: #48c400; color: #fff;';
                                }
                            }
                        });
                        $('#dg-uploadAdress').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                address: data[i].address,
                                createTime: data[i].createTime,
                            }
                        });
                    }
                },
                function (e, url, errMsg) {
                    // $('#dg-mobiel-process-list').datagrid('loaded');//关闭loding进度条
                    $.messager.alert('提示', '获取最新的事件地址异常：' + errMsg, 'error');
                });
            if (_uploadAddIntervals) {
                window.getUploadAddressTimeout = setTimeout(getUploadAdress, 3000);
            }
        }
        /** 发送指导短信 */
        function sendMessage() {
            if(!canOperateEvent(currentEventInfo.isLock,currentEventInfo.lockSeatUserId,currentEventInfo.lockSeatId)){
                $.messager.alert('提示', '事件被 ' + (currentEventInfo.lockSeatCode || '') + ' (' + (currentEventInfo.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                return;
            }
            if (hasDisabled) return // 十秒倒计中，不执行任何操作
            let eventId = $("#update-event-id").val()
            // 没有事件id则 表示没有保存事件，必须先保存事件id 才能发送指导短信
            updateEventBtn('', () => {
                countSendH5Message({ eventId: eventId }, function (res) {
                    if (res > 0) {
                        $.messager.confirm("提示", "已发送过指导视频短信，是否重新发送", function (r) {
                            if (r) {
                                sendH5Message({ eventId: eventId }, function (resp) {
                                    useCountdown()
                                    $.messager.alert('提示', '短信发送成功', 'info');
                                }, function (e2, url, errMsg2) {
                                    //失败才做处理
                                    $.messager.alert('提示', '请求错误：' + errMsg2);
                                })
                            }
                        });
                    } else {
                        sendH5Message({ eventId: eventId }, function (resp) {
                            useCountdown()
                            $.messager.alert('提示', '短信发送成功', 'info');
                        }, function (e2, url, errMsg2) {
                            //失败才做处理
                            $.messager.alert('提示', '请求错误：' + errMsg2);
                        })
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '请求错误：' + errMsg);
                })
            })
        }
        // 10秒倒计时方法
        var hasDisabled = false;// 点击按钮发送短信成功后进入10秒倒计时的标记
        var disTime = 10; // 10秒倒计时
        function useCountdown() {
            hasDisabled = true // 启动10秒倒计时
            let countdownId = setInterval(() => {
                $("#sendMessageBtn").css({ "background-color": "#f4f4f5", "color": "#bcbec2", "line-height": "28px", "padding": "0 12px" });
                $("#sendMessageBtn").html("<img src='style/img/sendMessageDisabled.png' style='width:16px;height: 16px;'' alt=''> 保存并发送指导短信" + ' ' + disTime);
                disTime--
                if (disTime < 0) {
                    hasDisabled = false
                    $("#sendMessageBtn").css({ "background-color": "white", "color": "#099DFC" });
                    $('#sendMessageBtn').removeProp('border');
                    $("#sendMessageBtn").html("<img src='style/img/sendMessage.png' style='width:16px;height: 16px;'' alt=''> 保存并发送指导短信");
                    clearInterval(countdownId)
                    disTime = 10
                }
            }, 1000)
        }
        /** 获取打开ADLS系统的链接 */
        function redirectAdlsUrl() {
            let eventId = $('#update-event-id').val();//事件id
            if (eventId != null && eventId != "") {
                getEventDetailById(eventId,
                    function (data) {
                        if (data.status == '1' || data.status == '2') {
                            var getAdlsUrlDTO = {};
                            getAdlsUrlDTO.eventId = eventId;//事件id
                            getAdlsUrlDTO.contactPhone = data.contact;//联系人电话
                            getAdlsUrlDTO.exactAddress = data.address;
                            getAdlsUrlDTO.reason = data.majorCall;//呼叫原因
                            getAdlsUrlDTO.seatCode = _pSeat.seatId; //座席code
                            getAdlsUrlDTO.seatId = _pSeat.id; //座席id
                            getAdlsUrlDTO.username = _pSeat.userName;//座席用户工号
                            getAdlsUrlDTO.displayName = _pSeat.user;//座席用户姓名
                            getAdlsUrlDTO.gpsType = "02";//经纬度类型 01:gps 02:高德 03:百度
                            getAdlsUrlDTO.latitude = data.lat;//纬度
                            getAdlsUrlDTO.longitude = data.lng;//经度
                            getAdlsUrlDTO.patientGender = data.patientGender == null || data.patientGender == '' ? '2' : data.patientGender;//患者性别
                            getAdlsUrlDTO.alarmPhone = data.contact;//报警人电话
                            getAdlsUrlDTO.patientAge = data.patientAge;//患者年龄，单位：年
                            getAdlsUrlDTO.patientAmount = data.patientNumber;//患者人数
                            getAdlsUrl(getAdlsUrlDTO,
                                function (url) {
                                    try {
                                        window.parent.parent.gProxy.openBrowser(url);
                                    } catch (e) {
                                        try {

                                            if (window.parent.parent.opener && typeof window.parent.parent.opener.windowOpenUrl === 'function') {
                                                window.parent.parent.opener.windowOpenUrl(url);
                                            } else {
                                                // 如果opener不存在，尝试直接打开链接
                                                window.open(url, '_blank');
                                            }
                                        } catch (e2) {
                                            // 最后的备选方案：直接打开链接
                                            window.open(url, '_blank');
                                        }
                                    }
                                },
                                function (e, url, errMsg) {
                                    $.messager.alert('提示', "获取ADLS系统链接失败" + errMsg, 'error');
                                }
                            );
                        }else{
                            $.messager.alert('提示', '事件已结束，无法获取ADLS系统链接', 'info');
                        }
                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('提示', "事件查询失败：" + errMsg, 'error');
                    }
                );
            } else {
                $.messager.alert('提示', '事件未保存，无法获取ADLS系统链接', 'info');
            }
        }
        /** 获取通话记录 */
        function getCallPageListShow() {
            $('#dg-call-list').datagrid('loading');//打开进度条：打开等待div
            // //获取通话记录（未加入视频呼救的代码）
            // var getCallPageListParams = {
            //     "eventId": eventId,
            //     "pageSize": 100,
            //     "pageNum": 1
            // };
            // getCallPageList(getCallPageListParams,
            //     function (data) {
            //         $('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
            //         //回显通话记录
            //         //获取正在进行的事件的列表
            //         for (var i = 0; i < data.content.length; i++) {
            //             $('#dg-call-list').datagrid('insertRow', {
            //                 index: i,  // 索引从0开始
            //                 row: {
            //                     id: data.content[i].id,
            //                     number: data.content[i].number,
            //                     toNumber: data.content[i].toNumber,
            //                     callType: data.content[i].callType,
            //                     callTime: data.content[i].callTime,
            //                     handleTime: data.content[i].handleTime,
            //                     endTime: data.content[i].endTime,
            //                     handleStatus: data.content[i].handleStatus,
            //                     eventId: data.content[i].eventId,
            //                     recordAddr: data.content[i].recordAddr,
            //                     callId: data.content[i].callId,
            //                     visitorId: data.content[i].visitorId,
            //                     outerId: data.content[i].outerId,
            //                     duration: data.content[i].duration,
            //                     createTime: data.content[i].createTime,
            //                     createUser: data.content[i].createUser,
            //                     lastUpdateTime: data.content[i].lastUpdateTime,
            //                     lastUpdateUser: data.content[i].lastUpdateUser,
            //                     isAnswer: data.content[i].isAnswer,
            //                     recordName: data.content[i].recordName,
            //                     type: data.content[i].type
            //                 }
            //             });
            //         }
            //         callLog = data.content || [{}, {}, {}]
            //         //回显任务信息的结果
            //         $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
            //     },
            //     function (e, url, errMsg) {
            //         //回显任务信息的结果
            //         $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
            //         $.messager.alert('提示', '获取通话记录异常：' + errMsg, 'error');

            //     });
            //获取电话和视频通话记录
            //获取电话和视频通话记录
            var params = {
                "eventId": eventId
            };
            getPhoneAndVideoCallListByEventId(params,
                function (data) {
                    $('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //回显通话记录
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.length; i++) {
                        var row = {
                            id: data[i].id,
                            type: data[i].type,
                            number: data[i].number,
                            toNumber: data[i].toNumber,
                            callType: data[i].callType,
                            callTime: data[i].callTime,
                            handleTime: data[i].handleTime,
                            endTime: data[i].endTime,
                            handleStatus: data[i].handleStatus,
                            eventId: data[i].eventId,
                            recordAddr: data[i].recordAddr,
                            callId: data[i].callId,
                            visitorId: data[i].visitorId,
                            outerId: data[i].outerId,
                            duration: data[i].duration,
                            isAnswer: data[i].isAnswer,
                            seatId: data[i].seatId,
                            seatUserId: data[i].seatUserId,
                            seatUserName: data[i].seatUserName,
                            seatUser: data[i].seatUser,
                            isNewDevice: data[i].isNewDevice,
                            returnVisitStatus: data[i].returnVisitStatus,
                            callOpenId: data[i].callOpenId,
                            address: data[i].address,
                            lng: data[i].lng,
                            lat: data[i].lat,
                            nickName: data[i].nickName,
                            avatarUrl: data[i].avatarUrl,
                            gender: data[i].gender,
                            callCurentStatus: data[i].callCurentStatus,
                            createTime: data[i].createTime,
                            createUserId: data[i].createUserId,
                            createUser: data[i].createUser,
                            lastUpdateTime: data[i].lastUpdateTime,
                            lastUpdateUserId: data[i].lastUpdateUserId,
                            lastUpdateUser: data[i].lastUpdateUser,
                            disabled: data[i].disabled,
                            callInType: data[i].callInType, //呼入类型 1:视频呼救 2:120电话呼入
                            recordName: data[i].recordName //录音文件的名称，实际没有用途
                        };
                        $('#dg-call-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: row
                        });
                    }
                    callLog = data || [{}, {}, {}]
                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                },
                function (e, url, errMsg) {
                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                    $.messager.alert('提示', '获取通话记录异常：' + errMsg, 'error');
                });
        }
        function insertIntoSelect(id, typeCode, dicts) {
            var ui = $('#' + id);
            var list = getDicList(typeCode, dicts);
            ui.empty();
            
            // 对于呼叫类型下拉框，先加载所有选项，等callTypeShouldCreateEventList加载完成后再过滤
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
            }
            
            if (list != null && list.length > 0) {
                ui.val(list[0].codeName);
            }
            return list;
        }
        /**
         * 插入下拉框选项，首项为空
         * @param id
         * @param typeCode
         */
         function insertIntoSelect2(id, typeCode, dicts) {
            var ui = $('#' + id);
            var list = getDicList(typeCode, dicts);
            ui.empty();
            ui.append('<option value="{0}">{1}</option>'.formatStr("", ""));
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
            }
            //默认选中首项为空
            ui.val("");   
        }
        /**
         * 修改事件
         * @param type
         * @param callback
         */
        function updateEventBtn(type, callback) {
            if(!canOperateEvent(currentEventInfo.isLock,currentEventInfo.lockSeatUserId,currentEventInfo.lockSeatId)){
                $.messager.alert('提示', '事件被 ' + (currentEventInfo.lockSeatCode || '') + ' (' + (currentEventInfo.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
                return;
            }
            //检查必填项
            var pass = true;
            $('#update-evd-form input[required]').each(function (e) {
                var v = $(this).val();
                if (v == undefined || v == null || v == "") {
                    pass = false;
                    return false;
                }
                return true;
            });
            if (pass) {
                //二次检测
                $('#update-evd-form input[type=number]').each(function (e) {
                    var v = $(this).val();
                    var min = 0;
                    var max = parseInt($(this).attr('max'));
                    if (v > max || v < min) {
                        pass = false;
                        return false;
                    }
                    return true;
                });
            }
            // 呼叫原因和病状二选一 必填一项检测
            let major = $('#update-det-major').val()
            if (!major && !$('#update-evd-form #mainSuit').text()) {
                pass = false
            }
            // 呼叫类型为咨询电话、投诉电话、骚扰电话，不用填地址，呼叫原因，患者姓名等信息
            if ($('#update-evd-call-type').val() == '5' || $('#update-evd-call-type').val() == '6' || $('#update-evd-call-type').val() == '7') {
                pass = true
            }
            if (pass) {
                checkedList = []
                var select1Value = $('#updateSelect1').val();
                var select2Value = $('#updateSelect2').val();
                if (select1Value) {
                    checkedList.push(symptomData[select1Value].t)
                    if (symptomData[select1Value].c && select2Value) {
                        checkedList.push(symptomData[select1Value].c[select2Value].t)
                    }
                }
                //检测通过，更新事件
                var newEve = {};
                let patientDiagnosis = checkedList.join(' ')
                newEve.id = $('#update-event-id').val();
                newEve.eventType = "0";
                newEve.eventSrc = $('#update-evd-source').val();
                newEve.eventSrcCodeName = $('#update-evd-source option:selected').text();
                newEve.callType = $('#update-evd-call-type').val();
                newEve.callTypeCodeName = $('#update-evd-call-type option:selected').text();
                newEve.address = $('#update-det-address').val();
                newEve.majorCall = major + filterPatientDiagnosis(patientDiagnosis); // 呼叫原因+病症
                newEve.callIn = $('#update-det-callin').val();
                newEve.contact = $('#update-det-contact').val();
                newEve.contacter = $('#update-det-contacter').val();

                newEve.patientName = $('#update-det-name').val() || '未知';
                newEve.patientGender = $('#update-det-gender').val() || '';
                newEve.patientAge = $('#update-det-age').val();
                newEve.patientAmount = $('#update-det-amount').val();
                newEve.callInTimes = $('#update-det-call-in-times').val();

                newEve.patientCountry = $('#update-det-country').val();
                newEve.patientRace = $('#update-det-race').val();
                //newEve.patientCondition = $('#update-det-condition').val();

                newEve.centerRemark = $('#update-det-120remark').val();

                newEve.lat = $('#update-event-lat').val();
                newEve.lng = $('#update-event-lng').val();
                // 新增地址字段映射
                newEve.pickupAddress = $('#update-pickup-address').val();
                newEve.pickupLat = $('#update-pickup-lat').val();
                newEve.pickupLng = $('#update-pickup-lng').val();
                newEve.addressWait = $('#update-pickup-address').val();
                newEve.addressWaitLat = $('#update-pickup-lat').val() ? parseFloat($('#update-pickup-lat').val()).toFixed(6) : null;
                newEve.addressWaitLng = $('#update-pickup-lng').val() ? parseFloat($('#update-pickup-lng').val()).toFixed(6) : null;
                newEve.addressDelivery = $('#update-destination-address').val();
                newEve.addressDeliveryLat = $('#update-destination-lat').val() ? parseFloat($('#update-destination-lat').val()).toFixed(6) : null;
                newEve.addressDeliveryLng = $('#update-destination-lng').val() ? parseFloat($('#update-destination-lng').val()).toFixed(6) : null;
                newEve.patientDiagnosis = patientDiagnosis // 病症
                
                // 所属区域
                newEve.callRegionId = $('#update-det-area').val(); // 所属区域ID
                newEve.callRegionName = $('#update-det-area option:selected').text(); // 所属区域名称
                
                // 新增字段
                newEve.isNeedStretcher = $('#update-need-stretcher').is(':checked') ? 1 : 0; // 是否需要担架
                newEve.isTest = $('#update-is-test').is(':checked') ? 1 : 0; // 是否测试
                newEve.mpdsSummary = $('#update-det-alds-summary').val(); // ALDS汇总
                newEve.specialRequirement = $('#update-det-special-req').val(); // 特殊要求
                newEve.patientInfoHistory = $('#update-patient-info').val(); // 上次来电
                
                // 外籍人员（使用正确的ID）
                if ($('#updateCreateForeign').is(':checked')) {
                    newEve.patientIsForeign = '1'
                } else {
                    newEve.patientIsForeign = '0'
                }

                // 重大事件
                if ($('#updateEventType').is(':checked')) {
                    newEve.eventType = '1'
                    // 重大事件类型和等级信息
                    newEve.eventTypeCode = $('#updateMainEventType').val(); // 事故类型编码
                    newEve.eventTypeName = $('#updateMainEventType option:selected').text(); // 事故类型名称
                    newEve.eventLevelCode = $('#updateMainEventLevel').val(); // 事故等级编码
                    newEve.eventLevelName = $('#updateMainEventLevel option:selected').text(); // 事故等级名称
                } else {
                    newEve.eventType = '0'
                }
                newEve.pushOuterType = $('#update-push-outer-type').val();
                newEve.emergencyDegree = $('#update-emergency-level').val(); // 紧急程度
                newEve.firstEventId = $('#update-first-event-id').val(); // 关联首次事件ID
                // showProcess(true, '温馨提示', '正在处理保存数据，请稍后...');
                showProcess(false)
                updateEventData(newEve,
                    function (res) {
                        showProcess(false);
                        //成功不做任何处理
                        if (!type) {
                            if (callback) {
                                callback()
                            } else {
                                $.messager.alert('提示', '事件保存成功', 'info');
                            }
                        }
                        if (newEve.eventType == '1' && type) {
                            largerEventUploadDialog() // 重大事件上报页面的弹窗
                        }
                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('提示', '事件保存失败，异常信息：' + errMsg);
                    });

            } else {
                $('#update-evd-btn-submit').click();
                $.messager.alert('提示', '事件必要信息未填写或填写有误，无法保存事件。', 'error');
            }
        }
        function filterPatientDiagnosis(str) {
            if (str) {
                return '_' + str
            }
            return ''
        }
        /** 上报重大事件 */
        /*function reportBigEvent() {
            $.messager.confirm('',
                '确定要将此事件作为重大事件上报吗？\n请谨慎操作。',
                function (r) {
                    if (r) {
                        reportEvent(eventId,
                            function (res) {
                                if (res.success) {
                                    $.messager.alert('', '上报成功。');
                                } else {
                                    $.messager.alert('', '上报失败，发生错误。', 'error');
                                }
                            });
                    }
                });
        }*/
        /**
         * 事件地图
         * @param eventInfo
         */
        function initMap(eventInfo) {
            //高德地图
            updateMap = new AMap.Map("the-map", {
                enableMapClick: false,
                minZoom: 1,
                maxZoom: 100,
            });    // 创建Map实例,关闭底图可点功能
            querySysConf('map_city', // 查询配置的哪个城市
                function (data) {
                    currentCity = data;
                    updateMap.setCity(data); // 设置地图显示的城市 此项是必须设置的
                    // 设置控件
                    updateMap.plugin(["AMap.Scale"], function () {
                        updateMap.addControl(new AMap.Scale());
                    });
                    AMap.plugin('AMap.AutoComplete', function () {
                        updateAc = new AMap.AutoComplete(    //建立一个自动完成的对象
                            {
                                "input": "update-det-address",
                                "location": updateMap,
                                "city":currentCity
                            });
                        var myValue;
                        updateAc.on("select", function (e) {     //鼠标点击下拉列表后的事件
                            var _value = e.poi;
                            myValue = _value.name;
                            setAddress(myValue, 'hasSelect');
                        });
                        if (!eventInfo.lat || !eventInfo.lng) {
                            $.messager.alert('提示', '事件无经纬度信息，无法在地图上显示位置！！', 'info');
                        }
                        setAddress(eventInfo.address);
                    })

                },
                function (e, url, errMsg) {
                    $('#update-det-address').val(eventInfo.address);
                    //失败才做处理
                    $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
                });
        }
        function setAddress(address, hasSelect) {
            let position = null
            if ($('#update-event-lng').val() && $('#update-event-lat').val()) {
                position = JSON.stringify([$('#update-event-lng').val(), $('#update-event-lat').val()])
            }
            var inputElement = document.getElementById('update-det-address');
            // 设置输入框的值
            inputElement.value = address;
            currentEventInfo.address = address
            setPlace(address, position, hasSelect);
        }
        function setAddressUpload(address) {
            if ($('#upload-event-lng').val() && $('#upload-event-lat').val()) {
                sendToMap2(address, $('#upload-event-lng').val(), $('#upload-event-lat').val());
            }
        }

        function setPlace(value, position, hasSelect) {
            // 中文搜索定位
            handleSearch(value, position, hasSelect)
        }
        /**
         * 中文搜索定位
         * @param value
         * @param position
         * @param hasSelect
         */
        function handleSearch(value, position, hasSelect) {
            if (hasSelect) {
                if (value) {
                    AMap.plugin('AMap.PlaceSearch', function () {
                        placeSearch = new AMap.PlaceSearch(updateMap);
                        placeSearch.search(value, function (status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                // 获取搜索结果的范围
                                var bounds = result.bounds;
                                // 将地图视图调整为搜索结果的范围
                                updateMap.setBounds(bounds);
                                var firstPOI = result.poiList.pois[0];
                                //自动选中
                                if (firstPOI) {
                                    currentEventPr = firstPOI;
                                    //将经纬度写入到当前弹出框
                                    $('#update-event-lat').val(currentEventPr.location.lat);
                                    $('#update-event-lng').val(currentEventPr.location.lng);
                                    //处理加载两次问题
                                    if (localSearchStata) {
                                        // updateAc.hide();
                                        localSearchStata = false
                                        //显示地址信息窗口
                                        showLocationInfo();
                                    }
                                } else {
                                    _currentLocCreateEvent = null;
                                }
                            } else {
                                // 查询失败或没有结果的处理逻辑
                                console.log('查询失败或没有结果');
                            }
                        });
                    })
                }
            } else {
                if (position) {
                    let pos = JSON.parse(position)
                    sendToMap2(value, String(pos[0]), String(pos[1]));
                }
            }
        }
        /**
         * 显示地址信息窗口
         * @param res
         */
        function showLocationInfo(res) {
            updateMap.clearMap();    //清除地图上所有覆盖物
            sendCarMarker = null
            //绘制图标
            if (!sendCarMarker) {
                sendCarMarker = new AMap.Marker(currentEventPr.location);
                updateMap.add(sendCarMarker); //添加标注
            }
            var addr = null;
            if (res) {
                var addComp = res.addressComponents;
                let address = [addComp.province, addComp.city, addComp.district, addComp.street, addComp.streetNumber].filter(r => r).join(',')
                addr = "事件: " + currentEventInfo.majorCall.replace("_", " ") + "<br/>";
                addr += "位置：" + address + "<br/>";
                addr += "纬度: " + currentEventPr.location.lat + ", " + "经度：" + currentEventPr.location.lng;
                var inputElement = document.getElementById('update-det-address');
                // 设置输入框的值
                inputElement.value = address;
                $('#update-event-lat').val(currentEventPr.location.lat);
                $('#update-event-lng').val(currentEventPr.location.lng);
            } else {
                addr = "事件: " + currentEventInfo.majorCall.replace("_", " ") + "<br/>";
                addr += "位置：" + currentEventInfo.address + "<br/>";
                addr += "纬度: " + currentEventPr.location.lat + ", " + "经度：" + currentEventPr.location.lng;
            }
            setTimeout(() => {
                localSearchStata = true
            }, 1500)
            var infoWindow = new AMap.InfoWindow({
                content: addr, //传入字符串拼接的 DOM 元素
                anchor: "bottom-center",
                offset: [0, -35]
            });  //创建信息窗口对象
            infoWindow.open(updateMap, [currentEventPr.location.lng, currentEventPr.location.lat]);
            //移动派车地点
            sendCarMarker.setPosition([currentEventPr.location.lng, currentEventPr.location.lat]);
            //跳转到那个中心点
            updateMap.setCenter(currentEventPr.location);
            //加入拖拽
            sendCarMarker.setDraggable();
            // //监听标注的dragend事件来捕获拖拽后标注的最新位置
            // sendCarMarker.addEventListener("dragend", function (e) {
            //     if (sendCarMarker != null) {
            //         var gc = new AMap.Geocoder();//地址解析类
            //         gc.getLocation(e.point, function (res) {
            //             currentEventPr = e.point;
            //             showLocationInfo(res);
            //         });
            //     }
            // });
        }
        /**
         * 播放录音
         * @param value
         * @param row
         * @param index
         */
        function wavPlayFormatter(value, row, index) {
            if(row.callInType == '1'){
                var address = "";
                if (row.recordAddr != null) {
                    address = row.recordAddr;
                    if (row.number) {
                        return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#"  style="color: #099DFC;" onclick="playWav(\'' + address + '\',\'' + row.recordName + '\')">播放</a>';
                    }
                } else {
                    return '';
                }
            }else{
                return '<a href="#"  style="color: #099DFC;" onclick="openVideoRecord(\'' + row.id + '\')">回放</a>';
            }
        }
        let throttle = null;
        /**
         * 录音窗口打开
         * @param address
         * @param name
         */
        function playWav(address, name) {
            $('#playWavAudio').attr('src', address);
            $('#wavPlayWindow').window('open');
            downloadUrl = address
            $('#download').off('click').on('click', () => {
                if (!throttle) {
                    throttle = true
                    downloadURL(downloadUrl, downloadUrl.substring(downloadUrl.lastIndexOf("/") + 1))
                    setTimeout(() => {
                        throttle = null;
                    }, 1000);
                }
            })
            // $('#playWavAudio').attr('src', address);
        }

        /**
         * 打开视频混录回放
         * @param videoCallId
         */
        function openVideoRecord(videoCallId) {
            var title = "视频呼救通话混录" + "(" + videoCallId + ")"
            //获得当前选中行
            $('#videoRecordWindow').window({
                width: 1320,
                height: 700,
                modal: true,
                title: title,
                collapsible: false,
                minimizable: false,
                maximizable: false,
                draggable: false,//不能拖拽
                resizable: false,
                //left: ($(window).width() - 650) * 0.5,
                //top: ($(window).width() - 650) * 0.5,
            });

            href = "videoCallingRecordShow.html?videoCallId=" + videoCallId;
            $('#videoRecordIframe').attr('src', href);
            $('#videoRecordWindow').window('open');
        }
        function downloadURL(url, name) {
            try {
                window.parent.gProxy.download(url, name)
            } catch (e) {
                try {
                    window.parent.parent.gProxy.download(url, name)
                } catch (e) {
                    window.parent.parent.parent.gProxy.download(url, name)
                }
            }
        }
        
        /**
         * 处理结果
         * @param value
         * @param row
         * @param index
         */
        function handleStatusFormatter(value, row, index) {
            switch (row.handleStatus) {
                case "0":
                    return "无效";
                case "1":
                    return "已处理";
                case "2":
                    return "已合并";
                case "3":
                    return "未处理";
            }
        }

        function isAnswerFormatter(value, row, index) {
            switch (row.isAnswer) {
                case "0":
                    return "未接听";
                case "1":
                    return "已接听";
            }
        }

        function typeFormatter(callType) {
            let obj = callTypeList.filter(r => r.codeName == callType)[0]
            if (obj) {
                return obj.codeVale
            }
            return ''
        }
        /**
         * 拨打电话
         * @param value
         * @param row
         * @param index
         */
        function callbackPhoneFormatter(value, row, index) {
            if(row.callInType && row.callInType == '2'){
                return ''; 
            }else{
                switch (row.callType) {
                    case "IN":
                        if (row.number) {
                            return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.number + '\')">拨打</a>';
                        }
                    case "OU":
                        if (row.number) {
                            return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.toNumber + '\')">拨打</a>';
                        }
                    case "LO":
                        if (row.number) {
                            return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.number + '\')">拨打</a>';
                        }
                }
            }
        }

        /**
         * 医生号码显示格式-点击打电话
         * @param value
         * @param row
         * @param index
         */
        function doctorPhoneFormatter(value, row, index) {
            if (row.dispatchDoctorPhone != null && row.dispatchDoctorPhone != "") {
                return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.dispatchDoctorPhone + '\')">' + row.dispatchDoctorPhone + '</a>';
            }
        }
        /**
         * 护士号码显示格式-点击打电话
         * @param value
         * @param row
         * @param index
         */
        function nursePhoneFormatter(value, row, index) {
            if (row.dispatchNursePhone != null && row.dispatchNursePhone != "") {
                return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.dispatchNursePhone + '\')">' + row.dispatchNursePhone + '</a>';
            }
        }
        /**
         * 出车司机电话
         * @param value
         * @param row
         * @param index
         */
        function dispatchDriverPhoneFormatter(value, row, index) {
            if (row.dispatchDriverPhone && row.dispatchDriverPhone !== "") {
                return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.dispatchDriverPhone + '\')">' + row.dispatchDriverPhone + '</a>';
            }
            return ''
        }
        /**
         * 是否改派车辆
         * @param value
         * @param row
         * @param index
         */
        function changeSendCarNumberFormatter(value, row, index) {
            if (row.changeSendCarNumber != null && row.changeSendCarNumber != "" && row.changeSendCarNumber > 0) {
                return '<a href="#" style="color: #099DFC;" onclick="changeSendWindowOpen(\'' + row.processId + '\')">' + row.changeSendCarNumber + '次</a>';
            } else {
                return '否';
            }
        }

        /**
         * 显示改派记录窗口
         * @param processId
         */
        function changeSendWindowOpen(processId) {
            $('#mobileChangeRecordWindow').window('open');
            var params = {
                "processId": processId,
            };
            getMobileProcessChangeMobileList(params,
                function (data) {
                    $('#dg-change-mobile-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //回显通话记录
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.length; i++) {
                        $('#dg-change-mobile-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                mobileProcessId: data[i].mobileProcessId,
                                times: data[i].times,
                                remark: data[i].remark,
                                oldMobileId: data[i].oldMobileId,
                                oldMobilePlatnum: data[i].oldMobilePlatnum,
                                newMobilePlatnum: data[i].newMobilePlatnum,
                                newMobileId: data[i].newMobileId,
                            }
                        });
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取派车记录错误，异常信息：' + errMsg);
                });
        }
        /**
         * 是否车辆运行情况记录
         * @param value
         * @param row
         * @param index
         */
        function mobileOperationRecordFormatter(value, row, index) {
            return '<a href="#" style="color: #099DFC;" onclick="mobileOperationRecordWindowOpen(\'' + row.processId + '\')">运行情况</a>';
        }
        /**
         * 是否分站拒绝过滤显示
         * @param value
         * @param row
         * @param index
         */
        function stationRefuseStatusFormatter(value, row, index) {
            return `<span>${value == '1' ? '是' : value == '0' ? '否' : ''}</span>`
        }
        /**
         * 显示车辆运行记录窗口
         * @param processId
         */
        function mobileOperationRecordWindowOpen(processId) {
            $('#mobileOperationRecordWindow').window('open');
            var params = {
                "processId": processId,
            };
            getMobileOperationRecordList(params,
                function (data) {
                    $('#dg-mobile-operation-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        $('#dg-mobile-operation-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                mobileProcessId: data[i].mobileProcessId,
                                times: data[i].times,
                                remark: data[i].remark,
                                toStatus: data[i].toStatus,
                                toStatusStr: data[i].toStatusStr,
                            }
                        });
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取派车记录错误，异常信息：' + errMsg);
                });
        }
        /**
         * 是否容许修改事件呼叫类型 - 该方法已不再使用，事件来源、呼叫类型和接收中心均不可修改
         * @param eventIdParam
         * @deprecated 已废弃，查看事件页面不允许修改这些字段
         */
        function isUpdateEventCallType(eventIdParam) {
            // 该方法已不再使用
            console.log('isUpdateEventCallType方法已废弃，事件来源、呼叫类型和接收中心均不可修改');
            $('#update-evd-call-type').attr("disabled", true);
            $('#update-evd-source').attr("disabled", true);
            $('#update-push-outer-type').attr("disabled", true);
        }
        function callTypeFormatter(value, row, index) {
            switch (row.callType) {
                case "IN":
                    return "呼入";
                case "OU":
                    return "呼出";
                case "LO":
                    return "内部呼叫";
            }
        }
        /**
         * 是否禁用事件详情输入框信息
         * @param type
         */
        function DetailsAboutTheDisabled(type) {
            if (type) {
                // 事件来源、呼叫类型和接收中心在其他位置已经设置为禁用，这里不需要再设置
                // $('#update-evd-call-type').attr("disabled", true);
                // $('#update-evd-source').attr("disabled", true);
                // $('#update-push-outer-type').attr("disabled", true);
                
                $('#update-det-amount').attr("disabled", true);
                $('#update-det-call-in-times').datetimebox({ disabled: true });
                $('#update-det-address').attr("disabled", true);
                $('#update-det-major').attr("disabled", true);
                $('#update-det-callin').attr("disabled", true);
                $('#update-det-contact').attr("disabled", true);
                $('#update-det-contacter').attr("disabled", true);
                $('#update-det-120remark').attr("disabled", true);
                $('#update-det-name').attr("disabled", true);
                // $('#det-condition').attr("disabled", true);
                $('#update-det-gender').attr("disabled", true);
                $('#update-det-age').attr("disabled", true);
                $('#update-det-country').attr("disabled", true);
                $('#update-det-race').attr("disabled", true);
                $('#eventType').attr("disabled", true);
                //延时一秒是为了防止拿不到元素
                setTimeout(() => {
                    $('.checked-input').attr("disabled", true);
                    fontEventStatas = false
                }, 1000);
            } else {
                // 事件来源、呼叫类型和接收中心始终保持禁用状态，不启用它们
                // $('#update-evd-call-type').removeAttr("disabled");
                // $('#update-evd-source').removeAttr("disabled");
                // $('#update-push-outer-type').removeAttr("disabled");
                
                $('#update-det-amount').removeAttr("disabled");
                $('#update-det-call-in-times').datetimebox({ disabled: false });
                $('#update-det-address').removeAttr("disabled");
                $('#update-det-major').removeAttr("disabled");
                $('#update-det-callin').removeAttr("disabled");
                $('#update-det-contact').removeAttr("disabled");
                $('#update-det-contacter').removeAttr("disabled");
                $('#update-det-120remark').removeAttr("disabled");
                $('#update-det-name').removeAttr("disabled");
                // $('#det-condition').removeAttr("disabled");
                $('#update-det-gender').removeAttr("disabled");
                $('#update-det-age').removeAttr("disabled");
                $('#update-det-country').removeAttr("disabled");
                $('#update-det-race').removeAttr("disabled");
                $('#eventType').removeAttr("disabled");
                //延时一秒是为了防止拿不到元素
                setTimeout(() => {
                    $('.checked-input').attr("disabled", false);
                    fontEventStatas = true
                }, 1000);
            }
        }
        /** 打印详情 */
        function printDetails() {
            querySysConf("site_print_title", (data) => {
                data = data || {}
                if (dispatcherTask.length <= 5) {
                    do {
                        dispatcherTask.push({})
                    } while (dispatcherTask.length <= 5)
                }
                if (callLog.length <= 5) {
                    do {
                        callLog.push({})
                    } while (callLog.length <= 5)
                }
                var html = `
                            <!DOCTYPE html>
                                <html lang="en">
                                <head>
                                    <title>详情页打印</title>
                                    <style>
                                        table {
                                            border-top: 1px solid #999;
                                            border-left: 1px solid #999;
                                        }
                                        td {
                                            text-align: center;
                                            padding: 3px;
                                            height: 30px;
                                            border-bottom: 1px solid #999;
                                            border-right: 1px solid #999;
                                        }
                                        th {
                                            padding: 3px;
                                            height: 30px;
                                            border-bottom: 1px solid #999;
                                            border-right: 1px solid #999;
                                        }
                                        #information{
                                            border: none;
                                        }
                                        #information td{
                                            border: none;
                                            text-align: left;
                                        }
                                        #dispatcherTask td{
                                            font-size:12px;
                                        }
                                        #callLog td{
                                            font-size:12px;
                                        }
                                    </style>
                                </head>
                                <body>
                                    <div class="printDetails">
                                        <h3 style="width:100%;text-align: center;margin-bottom:3px;">${data.val || '泸州市120调度指挥中心'}</h3>
                                        <div style="width:100%;text-align: center;">事件详情</div>
                                        <table cellspacing="0px" cellpadding="0px" width="100%" id="information">
                                            <tr>
                                                <td style="width:9%;">患者姓名</td>
                                                <td style="width: 9%;">${$('#update-det-name').val() || '&nbsp;'}</td>
                                                <td style="width:5%;">性别</td>
                                                <td style="width: 6%;">${showGender($('#update-det-gender').val())}</td>
                                                <td style="width:5%;">年龄</td>
                                                <td style="width: 6%;">${$('#update-det-age').val() || '&nbsp;'}</td>
                                                <td style="width:5%;">国籍</td>
                                                <td style="width: 10%;">${querySelect('country', $('#update-det-country').val())}</td>
                                                <td style="width:5%;">民族</td>
                                                <td style="width: 10%;">${querySelect('race', $('#update-det-race').val())}</td>
                                            </tr>
                                        </table>
                                        <div style="min-height: 30px;">呼叫原因&nbsp;${$('#update-det-major').val() + '&nbsp;' + $('#mainSuit').html() || '&nbsp;'}</div>
                                        <div style="min-height: 30px;">地址&nbsp;${$('#update-det-address').val() || '&nbsp;'}</div>
                                        <h4 style="margin: 0;padding: 10px 0;">调度任务</h4>
                                        <table cellspacing="0px" cellpadding="0px" width="100%" id="dispatcherTask">
                                            <tr>
                                                <th style="width: 15%;">分站名称</th>
                                                <th style="width: 10%;">车牌号码</th>
                                                <th style="width: 10%;">出车医生</th>
                                                <th style="width: 10%;">出车护士</th>
                                                <th style="width: 10%;">出车司机</th>
                                                <th style="width: 10%;">派车时间</th>
                                                <th style="width: 10%;">出车时间</th>
                                                <th style="width: 15%;">车辆备注</th>
                                            </tr>
                                            ${dispatcherTask.map((r, index) => {
                                                return `
                                                      <tr>
                                                          <td>${r.stationName || '&nbsp;'}</td>
                                                          <td>${r.plateNum || '&nbsp;'}</td>
                                                          <td>${r.dispatchDoctor || '&nbsp;'}</td>
                                                          <td>${r.dispatchNurse || '&nbsp;'}</td>
                                                          <td>${r.dispatchDriver || '&nbsp;'}</td>
                                                          <td>${r.eventCreateTime || '&nbsp;'}</td>
                                                          <td>${r.mobileProcessCreateTime || '&nbsp;'}</td>
                                                          <td>${r.centerRemark || '&nbsp;'}</td>
                                                      </tr>
                                                  `
                                                }).join(' ')
                                            }
                                        </table>
                                        <h4 style="margin: 0;padding: 10px 0;">通话记录</h4>
                                        <table cellspacing="0px" cellpadding="0px" width="100%" id="callLog">
                                            <tr>
                                                <th>来/去电时间</th>
                                                <th>主叫号码</th>
                                                <th>被叫号码</th>
                                                <th>呼叫类型</th>
                                                <th>挂断时间</th>
                                                <th>是否接听</th>
                                            </tr>
                                            ${callLog.map(r => {
                                                return `
                                                        <tr>
                                                            <td>${r.callTime || '&nbsp;'}</td>
                                                            <td>${r.number || '&nbsp;'}</td>
                                                            <td>${r.toNumber || '&nbsp;'}</td>
                                                            <td>${callTypeFormatter(null, r) || '&nbsp;'}</td>
                                                            <td>${r.endTime || '&nbsp;'}</td>
                                                            <td>${isAnswerFormatter(null, r) || '&nbsp;'}</td>
                                                        </tr>
                                                    `
                                                }).join(' ')
                                            }
                                        </table>
                                    </div>
                                </body>
                                </html>
                            `
                try {
                    window.parent.gProxy.printDetails(html)
                } catch (e) {
                    // 调用打印
                    $.print(html, {
                        debug: false,
                        globalStyles: true,     // 是否使用父文档的样式表
                        iframe: true,
                        importCSS: true,
                        printContainer: false,
                        operaSupport: false
                    });
                }
            },
                (e, url, errMsg) => {
                    $.messager.alert('异常', '获取打印信息标头错误，异常信息：' + errMsg);
                })
        }
        /**
         * 回显性别中文
         * @param str
         */
        function showGender(str) {
            if (str == '0') {
                return '男'
            } else if (str == '1') {
                return '女'
            } else if (str == '2') {
                return '未知'
            }
            return '&nbsp;'
        }
        /**
         * 回显字典中文
         * @param typeCode
         * @param value
         */
        function querySelect(typeCode, value) {
            var obj = (getDicList(typeCode, _dics) || []).filter(r => r.codeName == value)[0];
            if (obj) {
                return obj.codeVale
            }
            return '&nbsp;'
        }
        initSelect()
        /** 初始化病状数据，渲染下拉框 */
        function initSelect() {
            // 根据返回的数据生成第一个下拉框的选项
            var select1 = document.getElementById('updateSelect1');
            select1.innerHTML = '<option value="">请选择分类</option>';
            for (var i = 0; i < symptomData.length; i++) {
                var option = document.createElement('option');
                option.value = i;
                option.innerHTML = symptomData[i].t;
                select1.appendChild(option);
            }

            // 监听第一个下拉框的选择事件
            $('#updateSelect1').on('change', function () {
                var selectedValue = $(this).val();
                // 判断选择的值，决定是否显示第二个下拉框
                if (selectedValue !== '' && symptomData[selectedValue].c) {
                    // 清空第二个下拉框的选项
                    var select2 = document.getElementById('updateSelect2');
                    select2.innerHTML = '';

                    // 根据返回的数据生成第二个下拉框的选项
                    for (var i = 0; i < symptomData[selectedValue].c.length; i++) {
                        var option = document.createElement('option');
                        option.value = i;
                        option.innerHTML = symptomData[selectedValue].c[i].t;
                        select2.appendChild(option);
                    }

                    // 显示第二个下拉框
                    document.getElementById('updateSelect2').style.display = '';
                } else {
                    // 隐藏第二个下拉框
                    document.getElementById('updateSelect2').style.display = 'none';

                    // 清空第二个下拉框的选项
                    document.getElementById('updateSelect2').innerHTML = '';
                }
            })
        }

        /**
         * 通信录弹窗
         * @param flag  
         */
        function phoneList(flag) {
            isEdit = flag
            $('#phone-list').window({
                title: `<div>选择通讯录</div>`
            });
            let phoneArr = []
            getPhoneList({}, function (res) {
                for(let i=0;i<res.length;i++){
                    let parent = {
                        title:res[i].groupName,
                        id:res[i].id,
                        children:[]
                    }
                    for(let c=0;c<res[i].phoneBooks.length;c++){
                        let itemChild = res[i].phoneBooks[c]
                        let child = {
                            title:itemChild.contactor + ' ' + itemChild.number,
                            id:itemChild.id,
                            number:itemChild.number
                        }

                        parent.children.push(child)
                    }
                    phoneArr.push(parent)
                }

                layui.use(function () {
                    phoneTree = layui.tree;
                var layer = layui.layer;
                var util = layui.util;
                // 渲染
                phoneTree.render({
                    elem: '#phone-book',
                    data: phoneArr,
                    id: 'phone-list',
                    showCheckbox: true,
                    edit: [], // 开启节点的右侧操作图标

                });
            });
            $('#phone-list').window('open')
            }, function (e, url, errMsg) {
                //失败做处理
                $.messager.alert('提示', errMsg);
            })
        }

        /** 关闭通讯录弹窗 */
        function cancelPhoneList(){
            $('#phone-list').window('close')
        }

        /**
         * 将yyyyMMddHHmmss转换为yyyy年MM月dd日 HH时mm分
         * @param time
         */
        function convertTime(time) {
            let timeArr = time.split('')
            let y = timeArr.slice(0, 4).join('')
            let m = timeArr.slice(4, 6).join('')
            let d = timeArr.slice(6, 8).join('')
            let hh = timeArr.slice(8, 10).join('')
            let mm = timeArr.slice(10, 12).join('')
            let ss = timeArr.slice(12, 14).join('')
            return y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
        }
        
        /**
         * 初始化所属区域下拉框 (eventInfo版本)
         * 通过getAllStationRegionList接口获取区域数据
         */
        function initUpdateAreaSelect() {
            getAllStationRegionList(
                function (data) {
                    var $detArea = $('#update-det-area');
                    // 清空现有选项，保留默认的"请选择"
                    $detArea.html('<option value="">请选择</option>');
                    
                    // 遍历返回的区域数据，添加选项
                    if (data && Array.isArray(data)) {
                        data.forEach(function(region) {
                            var option = '<option value="' + region.id + '">' + region.regionName + '</option>';
                            $detArea.append(option);
                        });
                    }
                },
                function (e, url, errMsg) {
                    console.error('获取区域数据失败:', errMsg);
                }
            );
        }
        
        /**
         * 根据地址自动匹配所属区域 (eventInfo版本)
         * @param {string} address - 地址字符串
         * @param {boolean} autoSelect - 是否自动选择匹配的区域
         */
        function autoMatchUpdateRegionByAddress(address, autoSelect = true) {
            if (!address || address.trim() === '') {
                return;
            }
            
            getAllStationRegionList(
                function (data) {
                    if (!data || !Array.isArray(data)) {
                        return;
                    }
                    
                    var bestMatch = null;
                    var highestScore = 0;
                    
                    // 遍历所有区域，计算匹配度
                    data.forEach(function(region) {
                        var score = calculateAddressMatchScore(address, region);
                        if (score > highestScore) {
                            highestScore = score;
                            bestMatch = region;
                        }
                    });
                    
                    // 如果找到匹配度较高的区域且启用自动选择
                    if (bestMatch && highestScore > 0 && autoSelect) {
                        $('#update-det-area').val(bestMatch.id);
                    }
                },
                function (e, url, errMsg) {
                    console.error('获取区域数据失败:', errMsg);
                }
            );
        }
        
        /**
         * 初始化地址自动匹配功能 (eventInfo版本)
         */
        function initUpdateAddressAutoMatch() {
            // 为地址输入框添加失焦和回车事件
            $('#update-det-address').on('blur keydown', function(e) {
                // 回车键或失焦时触发
                if (e.type === 'blur' || e.keyCode === 13) {
                    var address = $(this).val();
                    if (address && address.trim() !== '') {
                        // 使用防抖机制，避免频繁触发
                        clearTimeout(window.updateAddressMatchTimeout);
                        window.updateAddressMatchTimeout = setTimeout(function() {
                            autoMatchUpdateRegionByAddress(address);
                        }, 500);
                    }
                }
            });
        }
        
        /**
         * 初始化重大事件联动功能 (eventInfo版本)
         */
        function initUpdateEventTypeLinkage() {
            // 重大事件复选框变化事件
            $('#updateEventType').on('change', function() {
                if ($(this).is(':checked')) {
                    // 启用事故类型和事故等级下拉框
                    $('#updateMainEventType').prop('disabled', false);
                    $('#updateMainEventLevel').prop('disabled', false);
                } else {
                    // 禁用并清空事故类型和事故等级下拉框
                    $('#updateMainEventType').prop('disabled', true).val('');
                    $('#updateMainEventLevel').prop('disabled', true).val('');
                }
            });
        }
        
        /**
         * 初始化国籍和外籍人员的联动功能 (eventInfo版本)
         */
        function initUpdateCountryForeignLinkage() {
            // 国籍选择变化时的处理
            $('#update-det-country').on('change', function() {
                var selectedCountry = $(this).val();
                var selectedText = $(this).find('option:selected').text();
                
                // 如果选择了海外相关的国籍，自动勾选外籍人员
                if (selectedCountry === '1' || selectedText.includes('海外')) {
                    $('#updateCreateForeign').prop('checked', true);
                } else if (selectedCountry === '0' || selectedText.includes('中国')) {
                    // 如果选择了中国，取消勾选外籍人员
                    $('#updateCreateForeign').prop('checked', false);
                }
            });
            
            // 外籍人员复选框变化时的处理
            $('#updateCreateForeign').on('change', function() {
                var isChecked = $(this).is(':checked');
                
                if (isChecked) {
                    // 如果勾选了外籍人员，自动选择海外
                    var $countrySelect = $('#update-det-country');
                    var found = false;
                    
                    // 查找海外选项
                    $countrySelect.find('option').each(function() {
                        var optionValue = $(this).val();
                        var optionText = $(this).text();
                        if (optionValue === '1' || optionText.includes('海外')) {
                            $countrySelect.val(optionValue);
                            found = true;
                            return false; // 跳出循环
                        }
                    });
                    
                    // 如果没找到海外选项，尝试选择非中国的第一个选项
                    if (!found) {
                        $countrySelect.find('option').each(function() {
                            var optionValue = $(this).val();
                            var optionText = $(this).text();
                            if (optionValue !== '0' && !optionText.includes('中国') && optionValue !== '') {
                                $countrySelect.val(optionValue);
                                return false; // 跳出循环
                            }
                        });
                    }
                } else {
                    // 如果取消勾选外籍人员，自动选择中国
                    var $countrySelect = $('#update-det-country');
                    $countrySelect.find('option').each(function() {
                        var optionValue = $(this).val();
                        var optionText = $(this).text();
                        if (optionValue === '0' || optionText.includes('中国')) {
                            $countrySelect.val(optionValue);
                            return false; // 跳出循环
                        }
                    });
                }
            });
        }
        
        /**
         * 根据症状判断的选择更新呼叫原因 (eventInfo版本)
         */
        function updateUpdateMajorCallFromSymptom() {
            var select1Value = $('#updateSelect1').val();
            var select2Value = $('#updateSelect2').val();
            var majorCallText = '';
            
            if (select1Value && select1Value !== '') {
                // 获取第一个下拉框的文本
                var select1Text = symptomData[select1Value].t;
                majorCallText = select1Text;
                
                // 如果第二个下拉框也有选择
                if (select2Value && select2Value !== '' && symptomData[select1Value].c && symptomData[select1Value].c[select2Value]) {
                    var select2Text = symptomData[select1Value].c[select2Value].t;
                    majorCallText += ',' + select2Text;
                }
            }
            
            // 更新呼叫原因输入框
            if (majorCallText) {
                $('#update-det-major').val(majorCallText);
            }
        }
        
        /**
         * 初始化症状判断下拉框 (eventInfo版本)
         */
        function initUpdateSelect() {
            // 根据返回的数据生成第一个下拉框的选项
            var select1 = document.getElementById('updateSelect1');
            var select2 = document.getElementById('updateSelect2');
            select1.innerHTML = '<option value="">请选择分类</option>';
            select2.innerHTML = '<option value="">请选择分类</option>';
            for (var i = 0; i < symptomData.length; i++) {
                var option = document.createElement('option');
                option.value = i;
                option.innerHTML = symptomData[i].t;
                select1.appendChild(option);
            }

            // 监听第一个下拉框的选择事件
            $('#updateSelect1').on('change', function() {
                var selectedValue = $(this).val();
                // 判断选择的值，决定是否显示第二个下拉框
                if (selectedValue !== '' && symptomData[selectedValue].c) {
                    // 清空第二个下拉框的选项
                    var select2 = document.getElementById('updateSelect2');
                    select2.innerHTML = '<option value="">请选择分类</option>';

                    // 根据返回的数据生成第二个下拉框的选项
                    for (var i = 0; i < symptomData[selectedValue].c.length; i++) {
                        var option = document.createElement('option');
                        option.value = i;
                        option.innerHTML = symptomData[selectedValue].c[i].t;
                        select2.appendChild(option);
                    }

                    // 显示第二个下拉框
                    document.getElementById('updateSelect2').style.display = '';
                } else {
                    // 隐藏第二个下拉框
                    document.getElementById('updateSelect2').style.display = 'none';

                    // 清空第二个下拉框的选项
                    document.getElementById('updateSelect2').innerHTML = '';
                }
                
                // 更新呼叫原因
                updateUpdateMajorCallFromSymptom();
            });

            // 监听第二个下拉框的选择事件
            $('#updateSelect2').on('change', function() {
                // 更新呼叫原因
                updateUpdateMajorCallFromSymptom();
            });
        }
        
        /**
         * 初始化症状判断和呼叫原因的联动 (eventInfo版本)
         */
        function initUpdateSymptomCallReasonLinkage() {
            // 初始化症状判断下拉框
            initUpdateSelect();
        }
        
        /**
         * 提取区域名称的关键词 (eventInfo版本)
         * @param {string} regionName - 区域名称
         * @returns {array} 关键词数组
         */
        function extractRegionKeywords(regionName) {
            if (!regionName) return [];
            
            var keywords = [];
            var name = regionName.trim();
            
            // 去除常见的行政区划后缀
            var suffixes = ['市', '区', '县', '镇', '街道', '乡', '村'];
            for (var i = 0; i < suffixes.length; i++) {
                if (name.endsWith(suffixes[i])) {
                    name = name.substring(0, name.length - suffixes[i].length);
                    break;
                }
            }
            
            // 添加完整名称作为主要关键词
            keywords.push(name);
            keywords.push(regionName); // 也保留原始名称
            
            // 如果名称较长，尝试提取子关键词
            if (name.length > 2) {
                // 添加2-3字的子串作为关键词
                for (var j = 0; j <= name.length - 2; j++) {
                    keywords.push(name.substring(j, j + 2));
                    if (j <= name.length - 3) {
                        keywords.push(name.substring(j, j + 3));
                    }
                }
            }
            
            return keywords;
        }
        
        /**
         * 计算地址与区域的匹配度 (eventInfo版本)
         * @param {string} address - 清理后的地址
         * @param {object} region - 区域信息对象
         * @returns {number} 匹配分数 (0-1)
         */
        function calculateAddressMatchScore(address, region) {
            var score = 0;
            var keywords = region.keywords || extractRegionKeywords(region.regionName || region.name || '');
            
            for (var i = 0; i < keywords.length; i++) {
                var keyword = keywords[i];
                if (!keyword) continue;
                
                // 完全匹配得分最高
                if (address.indexOf(keyword) !== -1) {
                    var keywordLength = keyword.length;
                    var addressLength = address.length;
                    
                    // 根据关键词长度和在地址中的位置计算得分
                    var baseScore = keywordLength / addressLength;
                    
                    // 如果关键词出现在地址开头，额外加分
                    if (address.indexOf(keyword) === 0) {
                        baseScore *= 1.5;
                    }
                    
                    // 如果是完整的区域名称匹配，额外加分
                    var regionName = region.regionName || region.name || '';
                    if (keyword === regionName || keyword === regionName.replace(/[市区县镇]$/, '')) {
                        baseScore *= 2;
                    }
                    
                    score = Math.max(score, baseScore);
                }
            }
            
            return Math.min(score, 1); // 确保分数不超过1
        }
        
        /**
         * 判断呼叫类型是否为直接完成且不保存事件，只保存通话记录，不需要派车的事件类型
         * 使用动态获取的呼叫类型配置数据进行判断
         * @param {string} callType 呼叫类型代码
         * @returns {boolean} 是否为直接完成类型（不需要创建事件的类型）
         */
        function isDirectCompleteCallType(callType) {
            
            // 在呼叫类型配置列表中查找对应的配置
            var callTypeConfig = callTypeShouldCreateEventList.find(function(item) {
                return item.eventCallType === callType;
            });
            
            if (callTypeConfig) {
                // 如果找到配置，返回相反的值：shouldCreateEvent为false表示直接完成
                var isDirectComplete = !callTypeConfig.shouldCreateEvent;
                console.log('呼叫类型判断结果:', {
                    callType: callType,
                    callTypeName: callTypeConfig.eventCallTypeName,
                    shouldCreateEvent: callTypeConfig.shouldCreateEvent,
                    isDirectComplete: isDirectComplete
                });
                return isDirectComplete;
            } else {
                // 如果未找到配置，默认认为需要创建事件（不是直接完成类型）
                console.warn('未找到呼叫类型配置:', callType, '默认认为需要创建事件');
                return false;
            }
        }

                 /**
          * 初始化获取呼叫类型创建事件判断列表 (eventInfo版本)
          * 页面加载时调用接口获取所有呼叫类型的创建事件判断配置
          */
         function initUpdateCallTypeShouldCreateEventList() {
             console.log('开始初始化呼叫类型创建事件判断列表...');
             
             // 调用接口获取呼叫类型创建事件判断列表
             getCallTypeShouldCreateEventList(
                 function(response) {
                     // 成功回调
                     if (response) {
                         callTypeShouldCreateEventList = response;
                         
                         // 数据加载完成后，立即过滤呼叫类型下拉框
                         filterCallTypeSelect();
                         
                     } else {
                         console.warn('接口返回数据格式异常:', response);
                         callTypeShouldCreateEventList = [];
                     }
                 },
                 function(error, url, errorMessage) {
                     // 失败回调
                     console.error('获取呼叫类型创建事件判断列表失败:', {
                         error: error,
                         url: url,
                         errorMessage: errorMessage
                     });
                     
                     // 失败时设置为空数组，确保不影响其他功能
                     callTypeShouldCreateEventList = [];
                     
                     // 可选：显示用户友好的错误提示
                     $.messager.alert('提示', '获取呼叫类型配置失败：' + errorMessage, 'warning');
                 }
             );
         }
         
                   /**
           * 过滤呼叫类型下拉框，移除直接完成类型
           */
          function filterCallTypeSelect() {
              var $callTypeSelect = $('#update-evd-call-type');
              var currentValue = $callTypeSelect.val(); // 保存当前选中值
              
              console.log('开始过滤呼叫类型下拉框，当前选中值:', currentValue);
              
              // 获取所有选项
              var options = [];
              $callTypeSelect.find('option').each(function() {
                  var $option = $(this);
                  var value = $option.val();
                  var text = $option.text();
                  
                  // 如果不是直接完成类型，则保留
                  if (!isDirectCompleteCallType(value)) {
                      options.push({
                          value: value,
                          text: text
                      });
                  } else {
                      console.log('过滤掉直接完成类型:', value, text);
                  }
              });
              
              // 清空下拉框并重新添加过滤后的选项
              $callTypeSelect.empty();
              for (var i = 0; i < options.length; i++) {
                  var option = options[i];
                  $callTypeSelect.append('<option value="' + option.value + '">' + option.text + '</option>');
              }
              
              // 尝试恢复之前选中的值（如果该值没有被过滤掉）
              if (currentValue && !isDirectCompleteCallType(currentValue)) {
                  $callTypeSelect.val(currentValue);
              } else if (options.length > 0) {
                  // 如果之前的值被过滤掉了，选择第一个可用选项
                  $callTypeSelect.val(options[0].value);
              }
              
              console.log('呼叫类型下拉框过滤完成，保留选项数:', options.length);
              
              // 如果当前页面有事件信息且该事件的呼叫类型被过滤掉了，需要重新设置
              if (currentEventInfo && currentEventInfo.callType && isDirectCompleteCallType(currentEventInfo.callType)) {
                  safeSetCallTypeValue(currentEventInfo.callType);
              }
          }
          
          /**
           * 安全地设置呼叫类型值，如果值被过滤掉则给出提示
           * @param {string} callTypeValue - 要设置的呼叫类型值
           */
          function safeSetCallTypeValue(callTypeValue) {
              var $callTypeSelect = $('#update-evd-call-type');
              
              // 检查该值是否在下拉框中存在
              var optionExists = $callTypeSelect.find('option[value="' + callTypeValue + '"]').length > 0;
              
              if (optionExists) {
                  // 如果存在，直接设置
                  $callTypeSelect.val(callTypeValue);
                  console.log('成功设置呼叫类型:', callTypeValue);
              } else {
                  // 如果不存在，说明被过滤掉了，给出提示
                  console.warn('呼叫类型被过滤:', callTypeValue, '(直接完成类型)');
                  
                  // 设置为第一个可用选项
                  var firstOption = $callTypeSelect.find('option:first');
                  if (firstOption.length > 0) {
                      $callTypeSelect.val(firstOption.val());
                      console.log('已设置为第一个可用呼叫类型:', firstOption.val());
                  }
                  
                  // 给用户提示
                  $.messager.alert('提示', '该事件的呼叫类型为直接完成类型，无法在查看界面中修改', 'info');
              }
          }

        /** 关联首次事件功能 - eventInfo页面专用 */
        function updateRelevanceFirstEventBtn() {
            //打开首次事件选择窗口
            $('#updateFirstEventSelectWindow').window('open');
            getUpdateFirstEventListDatas(1, 20);
        }

        /**
         * 获得首次事件列表数据 - eventInfo页面专用
         * @param pageNum
         * @param pageSize
         */
        function getUpdateFirstEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = size;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = size;
            }

            var startCreateTimeVal = $('#updateFirstStartCreateTime').datetimebox('getValue');
            var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endCreateTimeVal = $('#updateFirstEndCreateTime').datetimebox('getValue');
            var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            //打开进度条：
            $('#dg-update-first-event-pages').datagrid('loading');//打开等待div

            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "majorCall": $("#updateFirstMajorCall").val(),
                "address": $('#updateFirstAddress').val(),
                "eventStatusList": $('#updateFirstEventStatus').combobox('getValues'),
            };
            getEventPages(params, function (data) {
                $('#dg-update-first-event-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-update-first-event-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            eventId: data.content[i].eventId,
                            eventStatus: data.content[i].eventStatus,
                            eventStatusStr: data.content[i].eventStatusStr,
                            callIn: data.content[i].callIn,
                            callInTimes: data.content[i].callInTimes,
                            createTime: data.content[i].createTime,
                            majorCall: data.content[i].majorCall.replace("_", " "),
                            eventSrcCode: data.content[i].eventSrcCode,
                            eventSrcName: data.content[i].eventSrcName,
                            callTypeCode: data.content[i].callTypeCode,
                            lng: data.content[i].lng,
                            lat: data.content[i].lat,
                            callTypeName: data.content[i].callTypeName,
                            contact: data.content[i].contact,
                            contacter: data.content[i].contacter,
                            addressWait: data.content[i].addressWait,
                            address: data.content[i].address,
                            createUser: data.content[i].createUser,
                            eventCreateSeatCode: data.content[i].eventCreateSeatCode,
                            eventCreateSeatUserId: data.content[i].eventCreateSeatUserId,
                            eventCreateSeatUserName: data.content[i].eventCreateSeatUserName,
                            eventCreateSeatUser: data.content[i].eventCreateSeatUser,
                            processCount: data.content[i].processCount,
                            runProcessCount: data.content[i].runProcessCount,
                            finishedProcessCount: data.content[i].finishedProcessCount,
                            cancelProcessCount: data.content[i].cancelProcessCount,
                            centerRemark: data.content[i].centerRemark,
                            callRegionId: data.content[i].callRegionId,
                            callRegionName: data.content[i].callRegionName
                        }
                    });
                }
                $("#dg-update-first-event-pages").datagrid({
                    rowStyler: function (index,row) {
                            switch (row.eventStatus) {
                                case '1':
                                    return 'background-color:#dad6d557;';//灰色
                                case "2":
                                    return 'background-color:#f9a296ad;';//红色
                                case "3":
                                    return 'background-color:#dad6d557;';//灰色
                                case "4":
                                    return 'background-color:#dad6d557;';//灰色
                            }
                        },
                });
                setUpdateFirstEventPage(data.total || 0, pageNum)
                $('#dg-update-first-event-pages').datagrid('loaded');//关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-update-first-event-pages').datagrid('loaded');//关闭loding进度条；
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }

        /**
         * 设置首次事件分页 - eventInfo页面专用
         * @param total
         * @param pageNum
         */
        function setUpdateFirstEventPage(total, pageNum) {
            var pg = $("#dg-update-first-event-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total,
                    pageSize: size,
                    pageNumber: pageNum,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function (pageNumber, pageSize) {
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function (pageSize) {
                        page = 1
                        size = pageSize
                        getUpdateFirstEventListDatas(page, pageSize);
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getUpdateFirstEventListDatas(pageNumber, pageSize);
                    }
                });
            }
        }

        /**
         * 选择首次事件（双击事件） - eventInfo页面专用
         * @param index 行索引
         * @param row 行数据
         */
        function updateSelectFirstEvent(index, row) {
            console.log('选择首次事件:', row);
            
            // 关闭窗口
            $('#updateFirstEventSelectWindow').window('close');
            
            // 获取首次事件的详细信息用于回填
            getEventById(row.eventId, function(eventData) {
                if (eventData) {
                    // 回填数据
                    updateFillFirstEventData(eventData);
                    
                    // 显示关联信息
                    updateShowFirstEventInfo(eventData);
                    
                    // 存储首次事件ID
                    $('#update-first-event-id').val(eventData.id);
                    
                } else {
                    $.messager.alert('错误', '获取首次事件详情失败', 'error');
                }
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '获取首次事件详情失败：' + errMsg, 'error');
            });
        }

        /**
         * 回填首次事件数据 - eventInfo页面专用
         * @param eventData 首次事件数据
         */
        function updateFillFirstEventData(eventData) {
            // 回填呼救类型
            if (eventData.callType) {
                safeSetCallTypeValue(eventData.callType);
            }
            
            // 回填所属区域
            if (eventData.callRegionId) {
                $('#update-det-area').val(eventData.callRegionId);
            }
            
            // 回填现场地址
            if (eventData.address) {
                $('#update-det-address').val(eventData.address);
                // 如果有经纬度也回填
                if (eventData.lat && eventData.lng) {
                    $('#update-event-lat').val(eventData.lat);
                    $('#update-event-lng').val(eventData.lng);
                }
            }
            
            console.log('已回填首次事件数据:', {
                callType: eventData.callType,
                callRegionId: eventData.callRegionId,
                address: eventData.address
            });
        }

        /**
         * 显示首次事件关联信息 - eventInfo页面专用
         * @param eventData 首次事件数据
         */
        function updateShowFirstEventInfo(eventData) {
            var html = '<div style="border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin: 5px 0; background-color: #f9f9f9; display: flex; justify-content: space-between; align-items: center; width: calc(100% - 22px);">';
            
            // 左边信息内容
            html += '<div style="font-size: 13px;">';
            html += '<i class="fa fa-link" style="margin-right: 5px; color: #2c5aa0;"></i>';
            html += '<strong>已关联首次事件：</strong>';
            html += '<span style="margin-left: 10px;"><strong>现场地址：</strong>' + (eventData.address || '-') + '</span>';
            html += '<span style="margin-left: 15px;"><strong>呼叫原因：</strong>' + (eventData.majorCall ? eventData.majorCall.replace("_", " ") : '-') + '</span>';
            html += '<span style="margin-left: 15px;"><strong>联系电话：</strong>' + (eventData.contact || '-') + '</span>';
            html += '</div>';
            
            // 右边删除按钮
            html += '<div>';
            html += '<a href="javascript:updateRemoveFirstEventAssociation();" class="easyui-linkbutton" data-options="iconCls:\'icon-remove\'" style="font-size: 12px;">取消</a>';
            html += '</div>';
            
            html += '</div>';
            
            // 显示面板并设置内容
            $('#updateRelevanceFirstEventPanel').show().html(html);
            
            // 重新解析linkbutton
            $.parser.parse('#updateRelevanceFirstEventPanel');

        }

        /**
         * 删除首次事件关联 - eventInfo页面专用
         */
        function updateRemoveFirstEventAssociation() {
            var eventId = $('#update-event-id').val();

            $.messager.confirm('确认', '确定要删除首次事件关联吗？', function(r) {
                if (r) {
                    unbindFirstEvent({
                        id: eventId
                    }, function(data) {  
                        // 清空首次事件ID
                        $('#update-first-event-id').val('');
                        // 清空显示区域并隐藏面板
                        $('#updateRelevanceFirstEventPanel').html('').hide();
                        $.messager.alert('提示', '已成功删除首次事件关联', 'info');
                    }, function(e, url, errMsg) {
                        $.messager.alert('错误', '删除首次事件关联失败：' + errMsg, 'error');
                    });
                }
            });
        }
    </script>
</body>

</html>
