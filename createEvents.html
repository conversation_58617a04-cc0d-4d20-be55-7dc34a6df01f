<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/5/27
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台-创建事件</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/eventReport.js"></script>
    <script type="text/javascript" src="script/symptomData.js"></script>
    <style>
        body {
            overflow-y: hidden; /* 禁用Y轴滚动条 */
            margin: 0; /* 防止有默认的外边距 */
            padding: 0; /* 防止有默认的内边距 */
        }
        a {
            color: #099DFC;
            font-weight: bold;
        }

        #confirm-receive-editor table td {
            font-size: 15px !important;
            height: 40px !important;
        }

        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }

        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }

        .group-box {
            min-width: 100px;
            min-height: 100px;
            border: solid #E2E7EA 1px;
            border-radius: 5px;
            margin: 3px 5px 5px 5px;
            padding: 0 16px;
        }

        /* 文字闪烁 定义keyframe动画，命名为blink */
        @keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        /* 添加兼容性前缀 */
        @-webkit-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-moz-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-ms-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-o-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        /* 定义红色闪动blink类*/
        .redblink {
            font-size: 2.0em;
            color: #f10404;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        .blueblink {
            font-size: 2.0em;
            color: #099DFC;
            margin-left: 10px;
        }

        .greenblink {
            font-size: 2.0em;
            color: #1e8804;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        .tabs-header {
            border-width: 0px;
            background: url(./style/img/grouptitle.png) repeat-x;
        }

        .panel-body {
            overflow: hidden;
        }

        .tabs-wrap {
            overflow-x: auto;
        }

        .tabs-wrap::-webkit-scrollbar {
            display: none;
        }

        .tabs {
            width: auto !important;
        }

        /*
            tabel左边按钮
        */
        .tabs-button-left {
            display: inline-block;
            width: 0;
            height: 0;
            border: 7px solid;
            border-color: transparent #0000ff75 transparent transparent;
            position: absolute;
            right: 30px;
            top: 7px;
            cursor: pointer;
        }

        /*
            tabel右边按钮
        */
        .tabs-button-right {
            display: inline-block;
            width: 0;
            height: 0;
            border: 7px solid;
            border-color: transparent transparent transparent #0000ff75;
            position: absolute;
            right: 14px;
            top: 7px;
            cursor: pointer;
        }

        /*
            下拉框样式修改

        select {
            border-width: 0px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        }
 */
        /*
            联级输入框样式修改

        textarea {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        } */
        /*
            树型下拉框样式修改
        */
        input[readonly], textarea[readonly] {
            background: transparent !important;
        }
        /* .textbox{
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
            font-size: 1em !important;
        }
        .textbox .textbox-text{
            height: 1.3em !important;
            line-height: 1.3 !important;
            padding: 5px !important;
        }
        .textbox-icon{
            height: 1.4em !important;
        } */
        .tree-title {
            font-size: 1.3em !important;
        }

        .combo-panel {
            overflow: auto !important;
        }

        #createEventDetail-dialog {
            width: 100% !important;
            height: 98% !important;
        }

        .createEventDetail-content {
            width: 100%;
            height: calc(100% - 35px);
            /*overflow: hidden;*/ /* 强制不显示滚动条 */
            box-sizing: border-box; /* 确保 padding 和 border 不影响元素的尺寸 */
        }

        .textbox-label {
            display: inline-block;
            width: 120px;
        }

        #updateMobileConditionDialog .textbox-label {
            display: inline-block;
            width: 50px;
        }

        .chckbox-status {
            width: 50px;
            height: 50px;
        }

        /*开关css配置*/
        .btn_fath {
            margin-top: 10px;
            position: relative;
            border-radius: 20px;
        }

        .btn1 {
            /*"开"字的位置*/
            float: left;
        }

        .btn2 {
            /*"关"字的位置*/
            float: right;
        }

        .btnSwitch {
            height: 25px;
            width: 25px;
            border: none;
            color: #fff;
            line-height: 25px;
            font-size: 14px;
            text-align: center;
            z-index: 1;
        }

        .move {
            /*按钮的设置*/
            z-index: 100;
            width: 25px;
            border-radius: 20px;
            height: 25px;
            position: absolute;
            cursor: pointer;
            border: 1px solid #828282;
            background-color: #f1eff0;
            box-shadow: 1px 1px 1px 1px #fff inset, 0 0 1px 1px #999;
        }

        .on .move {
            left: 40px;
        }

        .on.btn_fath {
            /*开启效果的背景*/
            background-color: #44b549;
            height: 25px;
            width: 50px;
        }

        .off.btn_fath {
            /*关闭效果的背景 */
            background-color: #828282;
            height: 25px;
            width: 50px;
        }

        #createEventDetail .detail-title {
            background: #099DFC;
            height: 35px;
            line-height: 35px;
            vertical-align: middle;
            width: 100%;
            color: white;
            font-size: 14px;
        }

        #dis-region .region-letters,
        #station-region .station-letters {
          padding: 3px 6px;
          cursor: pointer;
        }
            /* 调度选中样式 station_region */
        #dis-region .region-letters.selected,
        #station-region .station-letters.selected {
          background: rgba(179,210,225,0.8);
        }
        #station_region .station-letters {
            padding: 3px 6px;
            cursor: pointer;
        }
            #station_region .station-letters.selected {
                background: rgba(179,210,225,0.8);
            }
        .panel-tool-close {
            background-size: 360% 240%;
            background-position: -21px -1px;
        }

        input[type=radio], input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            line-height: 20px;
            position: relative;
        }

        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
            /* 未选中时的边框颜色 */
        }

        input[type=radio]:checked:after {
            content: "L";
            transform: matrix(-0.766044, -0.642788, -0.642788, 0.766044, 0, 0);
            -webkit-transform: matrix(-0.766044, -0.642788, -0.642788, 0.766044, 0, 0);
            border-color: #37AF6E;
            background-color: #37AF6E;
        }

        input[type="checkbox"]:checked::before {
            content: "\2713";
            /* 对号的转义符 √ */
            background-color: #fff;
            /* 选中时的背景颜色 */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            /* 选中后的边框颜色 */
            color: #000;
            /* 选中后的文字颜色   */
            font-size: 20px;
            font-weight: bold;
        }

        input[type=radio]:after {
            content: "";
            display: block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            text-align: center;
            line-height: 14px;
            font-size: 16px;
            color: #fff;
            border: 2px solid #ddd;
            background-color: #fff;
            box-sizing: border-box;
        }

        #dis-region > .textbox, #station-region>.textbox  {
            border: none !important;
            width: 100% !important;
            background-color: transparent !important;
        }

        .checkSpan {
            cursor: pointer;
        }

        .panel-title {
            font-size: 15px;
        }

        #blacklist .select-common {
            margin-left: 0;
        }

        #blacklist .textbox .textbox-text {
            height: 28px !important;
            line-height: 28px !important;
            font-size: 1em;
        }

        #blacklist .blacklist-label {
            display: inline-block;
            width: 70px;
            font-size: 16px;
            text-align: right;
            vertical-align: middle;
        }

        #eventInfoSelectWindow .textbox .textbox-text {
            height: 24px !important;
            line-height: 24px !important;
        }

        .window {
            overflow: initial !important;
        }

        #wavPlayWindow {
            overflow: initial;
        }

        #detail-left {
            font-size: 14px;
            overflow: auto;
        }

        .larger-event-message {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50px;
            background-color: rgb(242, 242, 242);
            line-height: 50px;
        }

        .larger-event-btns {
            height: 100%;
            display: flex;
            align-items: center;
        }

        .larger-event-title {
            width: calc(100% - 210px);
            white-space: nowrap;
            color: rgb(245, 154, 35);
            font-size: 16px;
            padding-left: 10px;
            text-align: left;
            overflow: hidden;
        }

        .larger-form-line1 {
            display: flex;
            width: 100%;
            height: 40px;
            align-items: center;
        }

        .larger-form-item {
            width: 33.3%;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-code {
            width: 100%;
            font-size: 1.1em;
            height: 2.1em;
            padding: 5px;
            border-radius: 3px;
        }

        .larger-form-label {
            margin-left: 5px;
            width: 140px;
            font-size: 15px;
            text-align: right;
        }

        .datebox {
            height: 1.7rem; /* 设置 DateTimeBox 输入框的高度 */
        }

        .clear-fix .datebox {
            height: 1.9rem; /* 设置 DateTimeBox 输入框的高度 */
        }

        .larger-form-line2 {
            height: 40px;
            display: flex;
            align-items: center;
            width: 100%;
            padding-left: 20px;
        }

        .larger-form-line2-item {
            width: 16.6%;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-line3 {
            width: 100%;
            margin-bottom: 6px;
        }

        .larger-form-line3-item {
            width: 100%;
            display: flex;
        }

        .larger-event-flowOfCasualties {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .larger-event-uploadList, .larger-event-flowOfCasualties {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 35px;
            background-color: rgb(242, 242, 242);
            line-height: 35px;
        }

        .larger-event-flowOfCasualties {
            margin-top: 6px;
        }

        .larger-event-uploadList-title, .larger-event-flowOfCasualties-title {
            text-align: left;
            color: rgb(245, 154, 35);
            font-size: 15px;
            padding-left: 10px;
        }

        .larger-form-line4 {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
        }

        .larger-form-line4-item {
            height: 40px;
            display: flex;
            align-items: center;
        }

        .larger-form-line5 {
            width: 100%;
        }

        .larger-form-line5-item {
            display: flex;
            width: 100%;
        }
        #chooseCarNoId .layui-tree-entry, #chooseStationId .layui-tree-entry{
            text-align: left;
        }

    </style>
</head>

<body>
    <!--创建事件窗口-->
    <div id="createEventDetail" style="width:100%; height: 99%;top:20px;margin: auto;left: 0;">
        <!--播放录音回放窗口-->
        <div id="wavPlayWindow" class="easyui-window" title="录音回放" style="background-color:#FAFAFA; width: 600px; height: 100px; text-align: center; padding-top:5px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="position: relative;">
                <audio id="playWavAudio" src="" style="width: 100%;height: 42px;" autostart="false" controls></audio>
                <!--<img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="http://preview.qiantucdn.com/58pic/20220321/00S58PIC5Xkc6mdzM5xU3_PIC2018_PIC2018.jpg!w290_290" />-->
                <img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="style/img/download.jpg" />
            </div>
        </div>
        <!--本次通话关联事件窗口-->
        <div id="eventInfoSelectWindow" class="easyui-window" title="选择本次通话关联事件" style="width:1240px;height:700px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="margin-bottom:15px;height:40px;">
                <div id="eventSearch" style="display: inline-block; width: Calc(100% - 50px); float: left; padding-left: 10px;padding-top: 10px; font-size: 14px;">
                    创建时间： <input id="startCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endCreateTime" class="easyui-datetimebox" style="width:160px;" />
                    <!--&nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>-->
                    <span>&nbsp;呼叫原因：</span>
                    <input id="majorCall" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;地址：</span>
                    <input id="address" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;任务状态：</span>
                    <select id="eventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                        <option value="0">全部</option>
                        <option value="7">未调度-预约</option>
                        <option value="1">未调度-落单</option>
                        <option value="6">未调度-待派</option>
                        <option value="2">已调度</option>
                        <option value="3">已完成</option>
                        <option value="4">已撤销</option>
                    </select>
                    <a id="getDgEventListDatasBtn" class="easyui-linkbutton" href="javascript:getDgEventListDatas();" style="width: 80px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;vertical-align: middle;">搜 索</a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 10px); position: relative;font-size: 14px; margin-top: 15px; margin-top:20px">
                <table class="easyui-datagrid" id="dg-event-pages" style="width: 1230px; height: 600px;" pagination="true" singleSelect="true" rownumbers="true" fit="false">
                    <thead>
                        <tr>
                            <th field="eventId" width="180" checkbox="false" align="center">编号</th>
                            <th field="createTime" width="150" checkbox="false" align="center">创建时间</th>
                            <th field="majorCall" width="180" checkbox="false" align="center">呼叫原因</th>
                            <th field="address" width="320" align="center">地址</th>
                            <th field="callIn" width="100" align="center">呼救电话</th>
                            <th field="callInTimes" width="150" align="center">来电时间</th>
                            <th field="eventStatusStr" width="80" align="center">状态</th>
                            <th field="eventSrcName" width="80" align="center">来源</th>
                            <th field="callTypeName" width="80" align="center">类型</th>
                            <th field="contact" width="100" align="center">联系电话</th>
                            <th field="contacter" width="100" align="center">联系人</th>
                            <th field="processCount" width="80" align="center">任务总数</th>
                            <th field="runProcessCount" width="80" align="center">运行的任务数</th>
                            <th field="finishedProcessCount" width="80" align="center">完成任务数</th>
                            <th field="cancelProcessCount" width="80" align="center">撤销任务数</th>
                            <th field="eventCreateSeatUser" width="80" align="center">创建人</th>
                            <th field="eventCreateSeatCode" width="80" align="center">创建座席</th>
                            <th field="centerRemark" width="200" align="center">备注</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <!--关联首次事件窗口-->
        <div id="firstEventSelectWindow" class="easyui-window" title="选择关联首次事件" style="width:1240px;height:700px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="margin-bottom:15px;height:40px;">
                <div id="firstEventSearch" style="display: inline-block; width: Calc(100% - 50px); float: left; padding-left: 10px;padding-top: 10px; font-size: 14px;">
                    创建时间： <input id="firstStartCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="firstEndCreateTime" class="easyui-datetimebox" style="width:160px;" />
                    <span>&nbsp;呼叫原因：</span>
                    <input id="firstMajorCall" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;地址：</span>
                    <input id="firstAddress" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;任务状态：</span>
                    <select id="firstEventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                        <option value="0">全部</option>
                        <option value="7">未调度-预约</option>
                        <option value="1">未调度-落单</option>
                        <option value="6">未调度-待派</option>
                        <option value="2">已调度</option>
                        <option value="3">已完成</option>
                        <option value="4">已撤销</option>
                    </select>
                    <a id="getFirstEventListDatasBtn" class="easyui-linkbutton" href="javascript:getFirstEventListDatas();" style="width: 80px;height:28px;line-height: 28px; font-size: 12px; margin: 5px;vertical-align: middle;">搜 索</a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 10px); position: relative;font-size: 14px; margin-top: 15px; margin-top:20px">
                <table class="easyui-datagrid" id="dg-first-event-pages" style="width: 1230px; height: 600px;" pagination="true" singleSelect="true" rownumbers="true" fit="false"
                       data-options="onDblClickRow:selectFirstEvent">
                    <thead>
                        <tr>
                            <th field="eventId" width="180" checkbox="false" align="center">编号</th>
                            <th field="createTime" width="150" checkbox="false" align="center">创建时间</th>
                            <th field="majorCall" width="180" checkbox="false" align="center">呼叫原因</th>
                            <th field="address" width="320" align="center">地址</th>
                            <th field="callIn" width="100" align="center">呼救电话</th>
                            <th field="callInTimes" width="150" align="center">来电时间</th>
                            <th field="eventStatusStr" width="80" align="center">状态</th>
                            <th field="eventSrcName" width="80" align="center">来源</th>
                            <th field="callTypeName" width="80" align="center">类型</th>
                            <th field="contact" width="100" align="center">联系电话</th>
                            <th field="contacter" width="100" align="center">联系人</th>
                            <th field="processCount" width="80" align="center">任务总数</th>
                            <th field="runProcessCount" width="80" align="center">运行的任务数</th>
                            <th field="finishedProcessCount" width="80" align="center">完成任务数</th>
                            <th field="cancelProcessCount" width="80" align="center">撤销任务数</th>
                            <th field="eventCreateSeatUser" width="80" align="center">创建人</th>
                            <th field="eventCreateSeatCode" width="80" align="center">创建座席</th>
                            <th field="centerRemark" width="200" align="center">备注</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="easyui-layout" id="createEventDetail-dialog" style="width:100%;height:100%;"
             data-options="fit:true">
            <div style="" class="detail-title">
                <div style="width: 7em; display:inline-block; color: #fff;">&nbsp;&nbsp;创建事件&nbsp;</div>
                <!-- <div style="width: Calc(100% - 55.5em); display: inline-block; text-align: center;"></div> -->
                <div style="width: 14em;display: inline-block;float: right;" id="windowHandlePanel">
                    <div id="sendMessageP">
                        <a href="javascript:sendMessage();" class="easyui-linkbutton" id="sendMessageBtn"
                        style="font-size: 14px; width:auto;margin: 0px;"><img src="style/img/sendMessage.png" style="width: 16px;height: 16px;" alt="">保存并发送指导短信</a>&nbsp;
                    </div>
                </div>
                <div style="width: 7.6em;display: inline-block;float: right;display: none;" id="windowHandlePanel" class="getAdlsUrl">
                    <a href="javascript:redirectAdlsUrl();" class="easyui-linkbutton" style="font-size: 14px; width:80px;margin-right: 0px;">ADLS</a>&nbsp;
                </div>
                <div style="width: 7.6em;display: inline-block;float: right;" id="windowHandlePanel">
                    <a href="javascript:syncEventBtn();" class="easyui-linkbutton" data-options="iconCls:'icon-save'" style="font-size: 14px; width:100px;margin-right: 0px;">保存事件</a>&nbsp;
                </div>
                <div style="width: 9em;display: inline-block;float: right;" id="windowHandlePanel">
                    <a href="javascript:relevanceEventInfoBtn();" class="easyui-linkbutton" data-options="iconCls:'icon-filter'" style="font-size: 14px; width:120px;margin-right: 0px;">通话关联事件</a>&nbsp;
                </div>
                <div style="width: 9em;display: inline-block;float: right;" id="windowHandlePanel">
                    <div id="relevanceFirstEventP">
                        <a href="javascript:relevanceFirstEventBtn();" class="easyui-linkbutton" data-options="iconCls:'icon-filter'" style="font-size: 14px; width:120px;margin-right: 0px;">关联首次事件</a>&nbsp;
                    </div>
                </div>

                <div style="width: 7.6em;display: inline-block;float: right;" id="windowHandlePanel">
                    <a href="javascript:getCallPageListShow();" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" style="font-size: 14px; width:100px;margin-right: 0px;">刷新通话</a>&nbsp;
                </div>
                <div style="display: inline-block;float: right;" id="windowHandlePanel">
                    <a href="javascript:addBlacklist();" id="blacklistButton" class="easyui-linkbutton" data-options="iconCls:'icon-add'" style="font-size: 14px; width:140px;margin-right: 6px;display: inline-block;">加入黑名单</a>&nbsp;
                </div>
                <div style="width: 10em; display: inline-block;float: right;" id="windowHandlePanel">
                    <div id="largerEventUploadP">
                        <a href="javascript:uploadMajorEvents();" id="largerEventUpload" class="easyui-linkbutton" data-options="iconCls:'icon-redo'" 
                           style="font-size: 14px; width:130px;background-color: #EC0808;color: #ffffff;" hidden>上传重大事件</a>
                    </div>
                </div>
                <div id="dispatchTypeDiv" style="display: inline-block;float: right;margin-right: 10px;">
                    <span>预约：</span>
                    <input type="radio" name="dispatchType" value="7" style="margin-right: 5px;">
                    <span>落单：</span>
                    <input type="radio" name="dispatchType" value="1" checked style="margin-right: 5px;">
                    <span>待派：</span>
                    <input type="radio" name="dispatchType" value="6" style="margin-right: 5px;">
                </div>
            </div>
            <div class="createEventDetail-content">
                <div id="detail-left" style="height: calc(67% - 6px);width:75%;overflow: auto;">
                    <form id="evd-form" action="" style="height: 100%;" onsubmit="return false">
                        <div class="clear-fix">
                            <input type="submit" id="evd-btn-submit" style="display: none;" />
                            <input type="hidden" value="" id="event-id" />
                            <!--事件ID-->
                            <input type="hidden" value="" id="call-r-id" />
                            <input type="hidden" value="" id="det-region-linkage-id" /><!-- 区域联动ID -->
                            <!--通话记录ID-->
                            <input type="hidden" value="" id="event-lat" />
                            <input type="hidden" value="" id="event-lng" />
                            <input type="hidden" value="" id="pickup-lat" />
                            <input type="hidden" value="" id="pickup-lng" />
                            <input type="hidden" value="" id="destination-lat" />
                            <input type="hidden" value="" id="destination-lng" />
                            <input type="hidden" value="" id="firstEventId" />
                            <span style="float: left;height: 2em;line-height: 2em;">事件来源：</span>
                            <select id="evd-source" class="select-common" style="width: 7em; float: left;"></select>
                            <span style="margin-left: 1em; float: left;height: 2em;line-height: 2em;">呼叫类型：</span>
                            <select id="evd-call-type" class="select-common" style="width: 7em; float: left;"></select>

                            <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;">所属区域：</span>
                            <select id="det-area" class="select-common" style="width: 7em; float: left;">
                                <option value="">请选择</option>
                            </select>

                            <span style="margin-left: 1em;">重大事件：</span>
                            <input type="checkbox" id="eventType" name="eventType" style="margin-left: 0;vertical-align: middle;" />
                      
                            <span style="margin-left: 1em;float: left;height: 2em;line-height: 2em;">事件类型：</span>
                            <select id="mainEventType" class="select-common" style="width: 7em; float: left;" disabled>
                                <option value="" selected>请选择</option>
                                <option value="01">自然灾害</option>
                                <option value="02">事故灾难</option>
                                <option value="03">公共卫生事件</option>
                                <option value="04">社会安全事件</option>
                            </select>
                              
                            <span style="margin-left: 1em;; float: left;height: 2em;line-height: 2em;">事故等级：</span>
                            <select id="mainEventLevel" class="select-common" style="width:7em; float: left;" disabled>
                                <option value="">请选择</option>
                                <option value="01">特大（Ⅰ级）</option>
                                <option value="02">重大（Ⅱ级）</option>
                                <option value="03">较大（Ⅲ级）</option>
                                <option value="04">一般（Ⅳ级）</option>
                            </select>
  <!--<a href="javascript:reportBigEvent();" class="easyui-linkbutton" style="background: red; color: white; float: right;">上报重大事故</a>-->
                        </div>

                        <div class="clear-fix">
                            <span style="color: red;" id="required-address">现场地址：</span><span id="not-required-address">现场地址：</span>
                            <input id="det-address" required type="text" style="width: Calc(100% - 180px);" maxlength="200" />
                            <a href="javascript:queryLocationFntsss($('#det-callin').val());" class="easyui-linkbutton" style="position: relative;top: -0.2em;" id="isPhone" data-options="iconCls:'icon-show'" title="电话号码查询地址">
                                <i class="fa fa-phone"></i>
                            </a>
                            <a href="javascript:confirmAddress('发送到地图');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                            <a href="javascript:getMapLocation('scene');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                            <div id="the-map" style="display: none;"></div>
                            <div id="searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                        </div>
                        
                        <div class="clear-fix">
                            <span>等车地址：</span>
                            <input id="pickup-address" type="text" style="width: Calc(100% - 180px);" maxlength="200" />
                            <a href="javascript:syncSceneToPickupAddress();" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="同步现场地址"><i class="fa fa-refresh"></i></a>
                            <a href="javascript:confirmPickupAddress('发送到地图');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                            <a href="javascript:getMapLocation('pickup');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                            <div id="pickup-searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                        </div>

                        <div class="clear-fix">
                            <span style="float: left; margin-left: 0em;height: 2em;line-height: 2em;">主叫号码：</span>
                            <input id="det-callin" type="text" style="width: 8em;" maxlength="20"
                                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                   onblur="fillPersonInfoByPhoneNumber(this.value)" />
                            <a href="javascript:callingPhone($('#det-callin').val());" class="easyui-linkbutton" title="拨打电话">
                                <i class="fa fa-volume-control-phone"></i>
                            </a>
                            <span style="margin-left: 1em; color: red;" id="required-contact">联系电话：</span>
                            <span style="margin-left: 1em; " id="not-required-contact">联系电话：</span>
                            <input id="det-contact" type="text" style="width: 8em;" required maxlength="20"
                                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" 
                                   onblur="fillPersonInfoByPhoneNumber(this.value)" />
                            <a href="javascript:callingPhone($('#det-contact').val());" class="easyui-linkbutton" title="拨打电话">
                                <i class="fa fa-volume-control-phone"></i>
                            </a>
                            <span style="float: left; margin-left: 1em;color: red;height: 2em;line-height: 2em;">来电时间：</span>
                            <input id="det-call-in-times" required class="easyui-datetimebox" style="width:170px;text-indent: 0.5em;" />

                            <span style="margin-left: 1em;">联系人：</span>
                            <input id="det-contacter" type="text" style="width: 8em;" maxlength="20" />

                            <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;">患者人数：</span>
                            <input id="det-amount" type="number" style="width: 4em; float: left;" value="1" min="0" max="100" />
                        </div>

                        <div class="clear-fix">
                            <span>送往地址：</span>
                            <input id="destination-address" type="text" style="width: Calc(100% - 150px);" maxlength="200" />
                            
                            <a href="javascript:confirmDestinationAddress('发送到地图');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="发送到地图"><i class="fa fa-map-pin"></i></a>
                            <a href="javascript:getMapLocation('destination');" class="easyui-linkbutton" style="position: relative;top: -0.2em;" title="获取地图位置"><i class="fa fa-map-marker"></i></a>
                            <div id="destination-searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                        </div>
                        
                        <div class="clear-fix">
                            <span style="color: red;" id="required-major">呼叫原因：</span><span id="not-required-major">呼叫原因：</span>
                            <input id="det-major" type="text" style="width: Calc(60% - 111px);" maxlength="200" />

                            <span style="margin-left: 1em;">病状判断：</span>
                            <!--<div style="flex:1">
                                <div class="layui-form" id="patientDiagnosislevel1" style="width: calc(100% - 67px);min-height: 63px;padding-top: 2px;display: flex;flex-wrap: wrap;align-items: flex-start;"></div>
                                <div class="layui-form" id="patientDiagnosislevel2" style="width: calc(100% - 67px);border-top: solid 1px #cccccc;padding-top: 6px;min-height: 63px;display: flex;flex-wrap: wrap;align-items: flex-start;"></div>
                                <div class="layui-form" id="patientDiagnosislevel3" style="width: calc(100% - 67px);"></div>
                            </div>-->
                            <!--更改为下拉选择框-->
                            <select name="symptom" class="select-common" id="select1" style="width: 10em;margin-left: 0;">
                            </select>
                            <select name="symptom" class="select-common" id="select2" style="width: 15em;margin-left: 5px">
                            </select>

                            <span style="display:inline-block;width:85px;"></span>
                            <span id="mainSuit" style="display:inline-block;width: Calc(100% - 111px);"></span>
                        </div>

                        <div class="clear-fix">
                            <span>患者姓名：</span>
                            <input id="det-name" type="text" maxlength="20" style="width: 10em;margin-left: 0;" />
                            <!-- <span style="margin-left: 1em;">病情：</span>
                            <select id="det-condition" class="select-common" style="width: 5em;">
                                <option value="0">轻微</option>
                                <option value="1">中度</option>
                                <option value="2">严重</option>
                                <option value="3">死亡</option>
                            </select> -->
                            <span style="margin-left: 10px;">性别：</span>
                            <select id="det-gender" class="select-common" style="width: 5em;">
                            </select>
                            <span style="margin-left: 10px;">年龄：</span>
                            <input id="det-age" type="number" style="width: 5em; text-indent: 0.5em;" min="0" max="999" />
                            <span style="margin-left: 10px;">国籍：</span>
                            <select id="det-country" class="select-common" style="width: 10em;margin-left: 0;">
                            </select>

                            <span style="margin-left: 1em;" class="field-create-foreign">外籍人员：</span>
                            <input type="checkbox" id="createForeign" name="createForeign" class="field-create-foreign" style="margin-left: 0;vertical-align: middle;" />

                            <span style="margin-left: 1em;">民族：</span>
                            <select id="det-race" class="select-common" style="width: 8em;">
                                <!--<option value="0">汉族</option>-->
                                <!--<option value="1">少数民族</option>-->
                            </select>
                            <span style="margin-left: 1em;">需要担架：</span>
                            <input type="checkbox" id="need-stretcher" style="margin-left: 0;vertical-align: middle;" />
                        </div>
                        <div class="clear-fix">
                            <span>特殊要求：</span>
                            <input id="det-special-req" type="text" style="width: calc(50% - 6em);margin-left: 0;" maxlength="500" />
                            <span style="margin-left: 10px;">120备注：</span>
                            <input id="det-120remark" type="text" style="width: calc(50% - 6em);" maxlength="500" />
                        </div>
                        <div class="clear-fix">
                            <span style="float: left;" class="field-det-alds-summary">ALDS汇总：</span>
                             <input id="det-alds-summary" type="text" class="field-det-alds-summary" style="width: Calc(70% - 6px);" maxlength="500" />

                            <span style="float: left; margin-left: 1em;height: 2em;line-height: 2em;" class="field-emergency-level">紧急程度：</span>
                             <select id="emergency-level" class="select-common field-emergency-level" style="width: 10em; float: left;">
                                 <option value="O">O级-非紧急</option>
                                 <option value="E">E级-急诊</option>
                                 <option value="D">D级-较紧急</option>
                                 <option value="C">C级-紧急</option>
                                 <option value="B">B级-高危</option>
                                 <option value="A">A级-最高紧急</option>
                             </select>
                        </div>
                        <div class="clear-fix">
                            <span id="patient-info-lable" style="float: left;">上次来电：</span>
                            <!-- <input id="patient-info" type="text" style="width: Calc(100% - 111px);" maxlength="512" /> -->
                            <textarea id="patient-info" maxlength="512" style="width: Calc(100% - 100px); resize: none; height: 4em; float: left; font-size: 1em;"></textarea>
                        </div>
                        <div class="clear-fix" id="other-info-panel">
                             <span style="margin-left:0em;" class="field-push-outer-type">接收中心：</span>
                             <select id="push-outer-type" class="select-common field-push-outer-type" style="width: 10em;" title="选择要接收联动事件的其他急救中心"></select>
                             <!-- <a href="#" class="easyui-tooltip field-push-outer-type" title="选择要接收联动事件的其他急救中心">
                                <i class="fa fa-info-circle" style="margin-left: 5px;"></i>
                             </a> -->

                             <span style="margin-left: 1em;" class="field-is-test">测试：</span>
                             <input type="checkbox" id="is-test" class="field-is-test" style="margin-left: 0;vertical-align: middle;" />
                        </div>
                        <!-- 关联首次事件 -->
                        <div class="clear-fix" id="relevanceFirstEventPanel">

                        </div>
                    </form>
                </div>
                <!--通话记录-->
                <div id="region-call-voices" style="height: calc(67% - 6px);width:25%;">
                    <!-- 120电话或视频呼入  -->
                    <div style=" width: 100%;height: 75%; position: relative;padding: 3px;">
                        <table class="easyui-datagrid" id="dg-call-list" style="width:100%" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                            <thead>
                                <tr>
                                    <!-- 视频呼救 -->
                                    <th field="callTime" width="150" align="center">呼入时间</th>
                                    <th field="number" width="100" checkbox="false" align="center" formatter="callNumberFormatter">来电号码</th>
                                    <th field="recordAddr" width="40" align="center" formatter="wavPlayFormatter">录音</th>
                                    <th field="callbackTel" width="40" align="center" formatter="callbackPhoneFormatter">回拨</th>
                                    <th field="duration" width="40" align="center">时长</th>
                                    <th field="isAnswer" width="100" align="center" formatter="isAnswerFormatter">是否接听</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div style="width: 100%;overflow-y: auto; padding: 3px;">
                        <div style="width: 347px;height: 120px;">
                            <table class="easyui-datagrid" style="width: 100%" id="dg-uploadAdress" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                <thead>
                                    <tr>
                                        <th field="operation" width="40px" align="center" formatter="uploadOperationFormatter">同步</th>
                                        <th field="address" width="190px" align="left" formatter="uploadAddressFormatter">上报地址</th>
                                        <th field="createTime" width="112px" align="left" formatter="uploadTimeFormatter">上报时间</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
                <!--调度-->
                <div class="group-box" style="width: Calc(100% - 8px);height: 33%; clear: both;padding: 0;padding-left: 0px;">
                    <div class="group-box-title" style="width: calc(100% + 16px);">待派车辆列表
                        <div id="vehicle-type-container" style="float: right; margin-right: 10px;">
                            <span>车辆类型：</span>
                            <input type="radio" name="vehicleType" value="all" checked style="margin-right: 5px;"><span>全部</span>
                            <input type="radio" name="vehicleType" value="emergency" style="margin-right: 5px;margin-left: 10px;"><span>急救</span>
                            <input type="radio" name="vehicleType" value="home" style="margin-right: 5px;margin-left: 10px;"><span>回家</span>
                            <input type="radio" name="vehicleType" value="transfer" style="margin-right: 5px;margin-left: 10px;"><span>转院</span>
                        </div>
                    </div>
                    <!-- 车辆列表 -->
                    <div id="vehicleList" style="width: 100%;height: calc( 100% - 50px ); margin-top: 10px;">
                        <ul id="station-region" style="width: 180px; float: left; height: Calc(100% - 0.3em); display: inline-block;margin-right: 5px;" class="edc-listbox">
                            <input id="search-regionName" prompt="输入区县名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;">
                        </ul>
                        <ul id="dis-region" style="width: 250px; float: left; height: Calc(100% - 0.3em); display: inline-block;" class="edc-listbox">
                            <input id="search-title" prompt="输入分站名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;">
                        </ul>
                        <table id="dis-cars" class="edc-table" cellpadding="0" cellspacing="0" style="width: Calc(100% - 430px - 20px); height: Calc(100% - 0.3em); float: left; margin-left: 10px;">
                            <thead>
                                <tr>
                                    <th style="width: 22%;">分站</th>
                                    <th style="width: 9%;">车牌号</th>
                                    <th style="width: 7%;">车辆名称</th>
                                    <th style="width: 8%;">类型</th>
                                    <th style="width: 9%;">距离</th>
                                    <th style="width: 8%;">状态</th>
                                    <th style="width: 6%;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="dis-cars-body" style="overflow-y: scroll;"></tbody>
                        </table>
                    </div>

                    <!-- 分站列表 -->
                    <div id="substationsList" style="width:100%;height: 88%; position: relative;font-size: 14px; margin-top: 5px;display:flex;">
                        <div style="width:12%;margin-right:10px;">
                            <ul id="station_region" style="width: 100%; float: left; height: Calc(100% - 0.1em); display: inline-block;" class="edc-listbox">
                                <input id="search_regionName" prompt="输入区县名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;">
                            </ul>
                        </div>
                        <table class="easyui-datagrid" id="station-list" style="width:88%;font-weight: bold;" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true" toolbar="#tb-station" nowrap="false">
                            <thead data-options="frozen:true">
                                <tr>
                                    <th field="stationIsLoginIcon" width="30" checkbox="false" align="center" formatter="stationIsLoginIconFormatter"></th>
                                </tr>
                            </thead>
                            <thead>
                                <tr>
                                    <th field="stationName" width="200" align="center">分站名称</th>
                                    <th field="stationRegionName" width="70" align="center">所属区域</th>
                                    <th field="isLogin" width="50" align="center" formatter="stationIsLoginFormatter">状态</th>
                                    <th field="stationToEventDistance" width="100" align="center">事件距离(Km)</th>
                                    <th field="standByNum" width="60" checkbox="false" align="center">空闲车辆</th>
                                    <th field="statusCount" width="240" checkbox="false" align="center" formatter="vehicleStatusFormatter">车辆状态数量</th>
                                    <th field="stationContact" width="120" checkbox="false" align="center" formatter="stationContactFormatter">分站电话</th>
                                    <th field="remark" width="240" checkbox="false" align="center" formatter="stationRemarkFormatter">备注</th>
                                    <th field="operation" width="100" checkbox="false" align="center" formatter="stationOperationFormatter">操作</th>
                                </tr>
                            </thead>
                        </table>
                        <!--tb-station-->
                        <div id="tb-station" style="height: 40px;line-height:40px;font-weight: bold;">
                            <div style="height:30px;line-height:0px;float:left; margin-right:10px;font-size:14px;position: relative;top: 4px;">
                                <a class="easyui-linkbutton" id="refresh-button" style="width: 110px;height:26px;line-height: 26px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-reload'">刷新</a>
                                <input type="checkbox" id="chkStationIsLogin" name="chkStationIsLogin" value="0" style="zoom:100%;position: relative;top: -1px;width:20px;height:18px;font-size:14px;" />分站在线
                                <input type="checkbox" id="chkCarStandBy" name="chkCarStandBy" value="0" checked="checked" style="zoom:100%;position: relative;top: -1px;width:20px;height:18px;font-size:14px;" />有空闲车辆分站
                            </div>
                        </div>
                    </div>
                </div>
                <!--加入黑名单弹窗-->
                <div id="blacklist" class="easyui-dialog" title="加入黑名单" style="width:400px;" data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#blacklist-buttons">
                    <p style="text-align:center">
                        <span class="blacklist-label">电话:</span>
                        <input id="blacklist-number" class="easyui-numberbox" value="" style="height:28px;width: 173px;">
                    </p>
                    <p style="text-align:center">
                        <span class="blacklist-label">时间间隔:</span>
                        <select id="blacklist-period" class="select-common" style="height:28px;width: 173px;">
                            <option value="30">30分钟</option>
                            <option value="60">1小时</option>
                            <option value="300">5小时</option>
                            <option value="1440">24小时</option>
                        </select>
                    </p>
                    <p style="text-align:center">
                        <span class="blacklist-label">备注:</span>
                        <input id="blacklist-remark" class="easyui-textbox" value="" style="height:28px;width: 173px;" />
                    </p>
                </div>
                <div id="blacklist-buttons">
                    <a href="#" class="easyui-linkbutton" onclick="javascript: $('#blacklist').window('close');" style="width:80px;">取 消</a>
                    <a href="#" class="easyui-linkbutton" onclick="javascript:affirmBlacklist()" style="width:80px;">确 定</a>
                </div>
            </div>
        </div>

        <!-- 通信录弹窗 -->
        <div id="phone-list" class="easyui-window" title="选择通讯录" style="width: 600px;"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div id="phone-book" style="height: 300px;"></div>
            <div style="text-align: center;margin-bottom: 20px;">
                <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton"
                   style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">保存并发送短信</a>
                <a href="javascript:cancelPhoneList();" class="easyui-linkbutton"
                   style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">取消</a>
            </div>
        </div>
    </div>
    <!--车辆确认接收派车界面-->
    <div id="confirm-receive-editor" class="easyui-window" title="任务出车人员设置" style="width: 400px; height: 320px; text-align: center; padding-top:20px"
         data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
        <table tableframe=void style="padding-top:30px">
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><label>选择班次:</label></td>
                <td style="border-style:none;"><select id="dispatchClassesDri" name="dispatchClassesDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车医生:</label></td>
                <td style="border-style:none;"><select id="dispatchDoctorDri" name="dispatchDoctorDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车护士:</label></td>
                <td style="border-style:none;"><select id="dispatchNurseDri" name="dispatchNurseDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></tdstyle="border-style:none;">
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车司机:</label></td>
                <td style="border-style:none;"><select id="dispatchDriverDri" name="dispatchDriverDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><label>出车护工:</label></td>
                <td style="border-style:none;"><select id="dispatchWorkerDri" name="dispatchWorkerDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></td>
            </tr>
        </table>
        <input type="hidden" value="" id="dispatchCarId" />
        <input type="hidden" value="" id="dispatchEventId" />
        <input type="hidden" value="" id="mobileProcessId" />
        <a href="javascript:confirmReceiveSubmit();" id="confirmReceiveSubmitBtn" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 20px 10px 0 0;">确认发车</a>
    </div>
    <!-- 上传重大事件的弹窗 -->
    <div id="large-event-upload" class="easyui-window" title="上传重大事件" style="width: 1200px; height: 850px; text-align: center;" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
        <div style="width: 100%;height:  calc(100% - 50px);padding: 10px 10px 0;box-sizing: border-box;">
            <div class="larger-event-message">
                <div class="larger-event-title" id="largerEventTitle">
                    <div style="height: 25px;line-height: 25px;">地址：<span id="largerEventAddress"></span></div>
                    <div style="height: 25px;line-height: 25px;">主诉：<span id="largerEventZS"></span></div>
                    <input type="hidden" id="largerEvent-lat">
                    <input type="hidden" id="largerEvent-lng">
                </div>
            </div>
            <div class="larger-event-form">
                <div class="larger-form-line1">
                    <div class="larger-form-item">
                        <span class="larger-form-label">事件编号：</span>
                        <input class="larger-form-code" disabled id="largerFormEventId" type="text" value="" />
                    </div>
                    <div class="larger-form-item">
                        <span class="larger-form-label">事件类型<span style="color:red;">*</span>：</span>
                        <select id="largeEventType" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                            <option value=""> </option>
                            <option value="01">自然灾害</option>
                            <option value="02">事故灾难</option>
                            <option value="03">公共卫生事件</option>
                            <option value="04">社会安全事件</option>
                        </select>
                    </div>
                    <div class="larger-form-item">
                        <span class="larger-form-label">事件等级<span style="color:red;">*</span>：</span>
                        <select id="largeEventLevel" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                            <option value=""> </option>
                            <option value="01">特大（Ⅰ级）</option>
                            <option value="02">重大（Ⅱ级）</option>
                            <option value="03">较大（Ⅲ级）</option>
                            <option value="04">一般（Ⅳ级）</option>
                        </select>
                    </div>
                </div>
            </div>
            <!-- 上报列表 -->
            <div class="larger-event-uploadList">
                <div class="larger-event-uploadList-title">上报列表</div>
                <div class="larger-event-btns">
                    <input id="largeEventId" type="hidden" value="" />
                    <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
<!--                    <a href="javascript:largeEventAddUpload(true);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>-->
                    <a href="javascript:phoneList(false);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                    <a href="javascript:phoneList(true);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                </div>
            </div>
            <div class="larger-event-form">
                <div class="larger-form-line1">
                    <div class="larger-form-item">
                        <span class="larger-form-label">上报类别<span style="color:red;">*</span>：</span>
                        <select id="reportTypeCode" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                            <option value=""> </option>
                            <option value="01">首报</option>
                            <option value="02">追报</option>
                            <option value="03">终报</option>
                        </select>
                    </div>
                    <div class="larger-form-item">
                        <span class="larger-form-label">上报时间<span style="color:red;">*</span>：</span>
                        <input id="largeEventReportTime" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                    </div>
                    <div class="larger-form-item">
                    </div>
                </div>
                <div class="larger-form-line2">
                    <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label" style="min-width: 81px;">出动车辆<span style="color:red;">*</span>：</span>
                        <input id="vehicleDispatchedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">辆</span>
                    </div>
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label">伤员<span style="color:red;">*</span>：</span>
                        <input id="woundedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label">死亡<span style="color:red;">*</span>：</span>
                        <input id="deadNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label">重伤<span style="color:red;">*</span>：</span>
                        <input id="woundedSevereNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label">轻伤<span style="color:red;">*</span>：</span>
                        <input id="woundedSlightNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line2-item">
                        <span class="larger-form-label">转送<span style="color:red;">*</span>：</span>
                        <input id="transferNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                </div>
                <div class="larger-form-line3">
                    <div class="larger-form-line3-item">
                        <span class="larger-form-label" style="width: 101px;">汇报内容<span style="color:red;">*</span>：</span>
                        <textarea rows="3" id="reportContent" style="width:calc(100% - 106px);resize: none;font-size: 1.4em;"></textarea>
                    </div>
                </div>
            </div>
            <div style="height: 140px;">
                <table class="easyui-datagrid" id="larger-event-upload-list" style="width: 100%" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true">
                    <thead>
                        <tr>
                            <th field="reportTime" width="150" align="center">上报时间</th>
                            <th field="reportTypeName" width="70" align="center">类别</th>
                            <th field="reportUserName" width="80" checkbox="false" align="center">上报人</th>
                            <!--<th field="eventTypeName" width="100" align="center">事件类型</th>-->
                            <th field="eventLevelName" width="100" align="center">事件等级</th>
                            <th field="vehicleDispatchedNum" width="80" checkbox="false" align="center">出动车辆</th>
                            <th field="woundedNum" width="60" checkbox="false" align="center">伤员</th>
                            <th field="deadNum" width="60" checkbox="false" align="center">死亡</th>
                            <th field="woundedSevereNum" width="60" checkbox="false" align="center">重伤</th>
                            <th field="woundedSlightNum" width="60" checkbox="false" align="center">轻伤</th>
                            <th field="transferNum" width="60" checkbox="false" align="center">转送</th>
                            <th field="reportContent" width="280" align="center" formatter="reportContentFormatter">汇报内容</th>
                            <th field="operation" width="100" align="center" formatter="operation">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
            <!-- 伤员流向 -->
            <div class="larger-event-flowOfCasualties">
                <div class="larger-event-flowOfCasualties-title">伤员流向</div>
                <div class="larger-event-btns">
                    <a href="javascript:flowOfCasualtiesAddUpload();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                    <a href="javascript:flowOfCasualtiesAddUpload(true);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                </div>
            </div>
            <div class="larger-form-line4">
                <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                <div class="larger-form-line4-item" style="width: 21%;">
                    <span class="larger-form-label" style="min-width: 82px;">发车时间<span style="color:red;">*</span>：</span>
                    <input id="eventWoundedTransferId" type="hidden" value="" />
                    <input id="vehicleDispatchedTime_liudong" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                </div>
                <div class="larger-form-line4-item">
                    <span class="larger-form-label" style="width: 84px;">车牌号<span style="color:red;">*</span>：</span>
                    <input id="vehiclePlateNo_liudong" maxlength="8" type="text" value="" style="width: 6rem;height: 1.7rem;margin-right: 5px;" />
                    <a href="javascript:chooseCar();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                </div>
                <div class="larger-form-line4-item">
                    <span class="larger-form-label" style="width: 100px;">转送人数<span style="color:red;">*</span>：</span>
                    <input id="transferNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                </div>
                <div class="larger-form-line4-item">
                    <span class="larger-form-label" style="width: 74px;">死亡<span style="color:red;">*</span>：</span>
                    <input id="deadNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                </div>
                <div class="larger-form-line4-item">
                    <span class="larger-form-label" style="width: 74px;">重伤<span style="color:red;">*</span>：</span>
                    <input id="woundedSevereNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                </div>
                <div class="larger-form-line4-item">
                    <span class="larger-form-label" style="width: 74px;">轻伤<span style="color:red;">*</span>：</span>
                    <input id="woundedSlightNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                    <span style="font-size: 14px;margin-left: 5px;">人</span>
                </div>
                <div class="larger-form-line4-item" style="width: 100%">
                    <span class="larger-form-label" style="width: 82px;">送往地点<span style="color:red;">*</span>：</span>
                    <input id="transferAddress_liudong" type="text" value="" style="width: calc(100% - 154px);height: 1.7rem;margin-right: 5px;" />
                    <a href="javascript:chooseStation();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                </div>
            </div>
            <!-- 点击伤员流动选择车牌号 -->
            <div id="chooseCarNoId" class="easyui-window" title="选择车辆" style="width: 350px; height: 400px; text-align: center;;padding:10px" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                <input id="chooseCarInput" oninput="chooseCarInput()" type="text" value="" placeholder="请输入车牌号筛选" style="height: 1.7rem;width: 100%;" />
                <div style="width: 100%;">
                    <div id="car-number-tree"></div>
                </div>
            </div>
            <!-- 点击伤员流动选择分站 -->
            <div id="chooseStationId" class="easyui-window" title="选择分站" style="width: 280px; height: 400px; text-align: center;" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                <input id="chooseStationInput" oninput="chooseStationInput()" type="text" value="" placeholder="请输入分站名称筛选" style="height: 1.7rem;width: 100%;" />
                <div style="width: 100%;">
                    <div id="station-tree"></div>
                </div>
            </div>
            <div class="larger-form-line5">
                <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                <div class="larger-form-line5-item">
                    <span class="larger-form-label" style="width: 81px;">伤情备注：</span>
                    <textarea rows="3" id="injuryRemark" style="width:calc(100% - 81px); resize: none; font-size: 1.4em;"></textarea>
                </div>
            </div>
            <div style="height: 140px;margin-top: 6px;">
                <table class="easyui-datagrid" id="larger-event-flowOfCasualties-list" style="width: 100%" title=""
                       pagination="false" singleSelect="true" rownumbers="false" fit="true">
                    <thead>
                        <tr>
                            <th field="vehicleDispatchedTime" width="160" align="center">发车时间</th>
                            <th field="vehiclePlateNo" width="80" align="center">车牌号</th>
                            <th field="transferNum" width="80" align="center">转送人数</th>
                            <th field="transferAddress" width="274" checkbox="false" align="center">送往地点</th>
                            <th field="deadNum" width="80" checkbox="false" align="center">死亡</th>
                            <th field="woundedSevereNum" width="80" checkbox="false" align="center">重伤</th>
                            <th field="woundedSlightNum" width="80" checkbox="false" align="center">轻伤</th>
                            <th field="injuryRemark" width="330" checkbox="false" align="center">伤情备注</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <!-- <a href="javascript:largeEventSubmit();" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;">上传</a> -->
        <a href="javascript:largeEventCancel();" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: #000; background: #fff; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;border: 1px solid lightgray;">关闭</a>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        // 页面加载完成后执行
        $(function () {
           
            // 应用受理页面配置
            function applyAcceptPageConfig() {
                const config = getAcceptPageConfig();
                
                // 紧急程度
                if (!config.emergency_level) {
                    $('.field-emergency-level').hide();
                    // 调整同行的alds汇总的宽度
                    $('#det-alds-summary').css('width', 'calc(100% - 100px)');
                }
                
                // 外籍人员
                if (!config.create_foreign) {
                    $('.field-create-foreign').hide();
                }
                
                // 分中心（原联动类型修改）
                if (!config.push_outer_type) {
                    $('.field-push-outer-type').hide();
                    // 调整同行的测试的左边距
                    $('.field-is-test').css('margin-left', '0');
                }
                
                // 测试
                if (!config.is_test) {
                    $('.field-is-test').hide();
                }
                
                // ALDS汇总
                if (!config.det_alds_summary) {
                    $('.field-det-alds-summary').hide();
                    // 调整紧急程度的样式
                    $('.field-emergency-level').first().css('margin-left', '0');
                    $('#emergency-level').css('width', 'calc(100% - 100px)');
                }

                // 如果分中心（原联动类型修改）和外籍成员都隐藏,调整padding
                if (!config.push_outer_type && !config.is_test) {
                    $('#other-info-panel').hide();
                    $('#detail-left > form > div').css('padding', '4px');
                }
            }

            // 页面加载时应用配置
            applyAcceptPageConfig();
            
            // 默认隐藏接收中心
            $('.field-push-outer-type').hide();
            
            // 监听呼叫类型的变更
            $('#evd-call-type').change(function() {
                if($(this).val() === '11') {
                    // 呼叫类型为联动派车，显示接收中心
                    $('.field-push-outer-type').show();
                } else {
                    // 呼叫类型不是联动派车，隐藏接收中心
                    $('.field-push-outer-type').hide();
                }
            });
        });

        /**
         * 获取当前地图选择的位置信息
         * @returns {Object|null} 位置信息对象或null
         */
        function getCurrentMapEventLocation() {
            const locationStr = localStorage.getItem('currentMapEventLocation');
            if (!locationStr) {
                $.messager.alert('提示', '没有找到地图位置信息，请先在地图上选择位置');
                return null;
            }
            try {
                return JSON.parse(locationStr);
            } catch (e) {
                console.error('解析地图位置信息失败:', e);
                $.messager.alert('错误', '解析地图位置信息失败');
                return null;
            }
        }

        /**
         * 获取地图位置并回填到对应的地址输入框
         * @param {string} type - 地址类型：'scene'表示现场地址，'pickup'表示等车地址
         */
        function getMapLocation(type) {
            const location = getCurrentMapEventLocation();
            if (!location) return;
            
            if (type === 'scene') {
                // 回填现场地址
                $('#det-address').val(location.address);
                // 如果有经纬度相关的隐藏字段，也进行回填
                if ($('#det-longitude').length) $('#det-longitude').val(location.longitude);
                if ($('#det-latitude').length) $('#det-latitude').val(location.latitude);
            } else if (type === 'pickup') {
                // 回填等车地址
                $('#pickup-address').val(location.address);
                // 如果有经纬度相关的隐藏字段，也进行回填
                if ($('#pickup-longitude').length) $('#pickup-longitude').val(location.longitude);
                if ($('#pickup-latitude').length) $('#pickup-latitude').val(location.latitude);
            } else if (type === 'destination') {
                // 回填送往地址
                $('#destination-address').val(location.address);
                // 如果有经纬度相关的隐藏字段，也进行回填
                if ($('#destination-longitude').length) $('#destination-longitude').val(location.longitude);
                if ($('#destination-latitude').length) $('#destination-latitude').val(location.latitude);
            }
        }

        /**
         * 同步现场地址到送往地址
         */
        function syncSceneToDestinationAddress() {
            const sceneAddress = $('#det-address').val();
            $('#destination-address').val(sceneAddress);
            // 如果有经纬度字段，也同步
            if ($('#det-longitude').length && $('#destination-longitude').length) {
                $('#destination-longitude').val($('#det-longitude').val());
            }
            if ($('#det-latitude').length && $('#destination-latitude').length) {
                $('#destination-latitude').val($('#det-latitude').val());
            }
        }

        /**
         * 送往地址确认发送到地图
         * @param addr
         * @param lng
         * @param lat
         */
        function confirmDestinationAddress(addr, lng, lat) {
            if (addr == '发送到地图') {
                addr = $('#destination-address').val()
                if ($('#destination-lng').val() && $('#destination-lat').val()) {
                    lng = $('#destination-lng').val()
                    lat = $('#destination-lat').val()
                }
            }
            setDestinationPlace(addr, lng, lat);
        }

        /**
         * 设置送往地址位置
         * @param value
         * @param lng
         * @param lat
         */
        function setDestinationPlace(value, lng, lat) {
            handleDestinationSearch(value, lng, lat)
        }

        /**
         * 送往地址中文搜索定位
         * @param value
         * @param lng
         * @param lat
         */
        function handleDestinationSearch(value, lng, lat) {
            let currentLat;
            let currentLng;
            if (lng && lat) {
                currentLat = lat;
                currentLng = lng;
                try {
                    window.parent.gProxy.sendToMap(value, String(lng), String(lat));
                } catch (err) {
                    window.parent.bsProxy.sendToMap(value, String(lng), String(lat));
                }
            } else {
                if (value) {
                    AMap.plugin('AMap.PlaceSearch', function () {
                        placeSearch = new AMap.PlaceSearch(map);
                        placeSearch.search(value, function (status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                // 获取搜索结果的范围
                                var bounds = result.bounds;
                                // 将地图视图调整为搜索结果的范围
                                map.setBounds(bounds);
                                var firstPOI = result.poiList.pois[0];
                                //自动选中
                                if (firstPOI) {
                                    //将经纬度写入到送往地址字段
                                    $('#destination-lat').val(firstPOI.location.lat);
                                    $('#destination-lng').val(firstPOI.location.lng);
                                    currentLat = firstPOI.location.lat;
                                    currentLng = firstPOI.location.lng;
                                    try {
                                        window.parent.gProxy.sendToMap(value, String(firstPOI.location.lng), String(firstPOI.location.lat));
                                    } catch (err) {
                                        window.parent.bsProxy.sendToMap(value, String(firstPOI.location.lng), String(firstPOI.location.lat));
                                    }
                                }
                            } else {
                                // 查询失败或没有结果的处理逻辑
                                console.log('送往地址查询失败或没有结果');
                            }
                        });
                    })
                }
            }
        }

        /**
         * 地图选中后回显送往地址
         * @param address
         * @param lng
         * @param lat
         */
        function echoDestinationMapSelected(address, lng, lat) {
            // 获取输入框元素
            var inputElement = document.getElementById('destination-address');
            // 设置输入框的值
            inputElement.value = address;
            $('#destination-lat').val(lat);
            $('#destination-lng').val(lng);
        }

        var _currentInCallDetails = queryURLParams(window.location.search);
        var _currentLocCreateEvent = null; //当前事件选中的地图经纬度
        var _currentEventDetails = null; //当前事件实体
        var _currentAwaitMobile = null; //当前等待车辆列表，用于车辆类型筛选
        var _currentSelectedVehicleType = 'all'; //当前选择的车辆类型，默认为全部
        var _carTypeDictionary = []; //车辆类型字典数据
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
		console.log("创建事件页，获取到的座席信息：", _pSeat)
        var _currentCallList = [];//当前正在接听的电话
        var _currentVedioCallList = [];//当前正在接听的视频呼救
        
        var uploadAddress = []
        var dispatchClassesList = [] // 出车班次
        var callTypeShouldCreateEventList = [] // 呼叫类型创建事件判断列表
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }

        var _stationRegionList = []//分站列表
        var _substationList = []//分站医院列表
        var _stationRegionId = '' // 分站区域点击的id
        var _formIdList = [ //form表单id列表
            '#evd-source',
            '#evd-call-type',
            '#det-amount',
            '#det-call-in-times',
            '#det-address',
            '#det-major',
            '#det-callin',
            '#det-contact',
            '#det-contacter',
            '#det-120remark',
            '#det-name',
            // '#det-condition',
            '#det-gender',
            '#det-country',
            '#det-age',
            '#det-race',
            '#push-outer-type',
        ]
        //多选框选中参数
        let checkedList = []
        let morbidity = []
        var largerEventList = [] //重大事件的列表
        var flowOfCasualtiesList = [] //伤员流向的列表
        var hasLargerEventEdit = false // 判断是否有正在编辑的重大事件
        var hasFlowOfCasualtiesEdit = false // 判断是否有正在编辑的伤员流向表单
        var placeSearch = null;//中文查询对象
        var curAssignTaskMode = '';//当前系统配置
        
        //病状数据
        var symptomData = getSymptomData();

        //默认隐藏上传重大事件按钮
        $("#largerEventUpload").hide()
        //默认隐藏非必填地址
        $("#not-required-address").hide()
        //默认隐藏非必填呼叫原因
        $("#not-required-major").hide()
        //默认隐藏非必填联系电话
        $("#not-required-contact").hide()
        //默认隐藏加入黑名单按钮 电话自动创建才需要
        $("#blacklistButton").hide()
        if (_currentInCallDetails.phone && _currentInCallDetails.callUid) {
            $("#blacklistButton").show()
        }
        var playWavAudio = null //语音播放插件
        var downloadUrl = ''
        //是否拨打电话打开的创建事件  不是隐藏查询地址按钮
        var pollTime = null
        var num = 0
        var latLngArr = ['111', 'lbs', 'net', 'gps']
        var queryLocationStatus = true
        
        if (_currentInCallDetails.isPhone !== '1') {
            $('#det-address').width('calc(100% - 150px)')
            $("#isPhone").hide()
        } else {
            //通过电话号码查询是否已被标记骚扰电话
            getHarassInfo(_currentInCallDetails.phone);

            //todo 通过电话号码获取上一次呼入出任务的患者信息
            getLastCallInfo(_currentInCallDetails.phone);

            if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                //视频呼入
                console.log("视频呼入，不需要通过手机查询位置网的经纬度。");
                $("#isPhone").hide();
                $('#det-address').width('calc(100% - 150px)')
            }else{
                pollQueryLocation()
                if (!_currentInCallDetails.callUid) {
                    $.messager.alert('提示', '无法获取关联通话记录，请自行手动关联', 'info');
                }
            }
        }
        // setInterval(()=>{
        //     simulation()
        // },2000)
        function validateInput(input) {
            const value = input.value;
            const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
            if (!regex.test(value)) {
                input.value = ""; // 清空输入框
            }
        }
        var invalidNumnber = []//无效电话列表
        //关联事件页码
        var page = 1;
        var size = 20;
        // 判断重大事件按钮是否展示
        querySysConf('button_show', function (data) {
            let obj = JSON.parse(data)
            if (obj.event.btn_event_report == '1') {
                $("#largerEvent").show()
                $("#largerEventUploadP").show()
            } else {
                $("#largerEvent").hide()
                $("#largerEventUploadP").hide()
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '异常信息：' + errMsg);
            $("#largerEvent").hide()
            $("#largerEventUploadP").hide()
        });
        var ohead = document.getElementsByTagName("head")[0];
        var ele = document.createElement('script');
        ele.type = 'text/javascript';
        ele.onload = ele.onreadystatechange = function () {
            //动态加载完百度地图后操作
            if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
                // Handle memory leak in IE
                ele.onload = ele.onreadystatechange = null;

                //设置自动完成
                setAutoComplete();
                queryAllDic(["event_source", "call_type", "patient_status", "country", "race", "push_outer_type", "car_type","patient_gender"],
                    function (data) {
                        console.log("字典数据加载成功：", data);
                        //事件来源
                        insertIntoSelect('evd-source', 'event_source', data);
                        //呼叫类型
                        insertIntoSelect('evd-call-type', 'call_type', data);
                        //病情
                        // insertIntoSelect('det-condition', 'patient_status', data);
                        //性别
                        insertIntoSelect('det-gender', 'patient_gender', data);
                        //国家
                        insertIntoSelect('det-country', 'country', data);
                        //民族
                        insertIntoSelect('det-race', 'race', data);
                        //接收中心下拉框
                        insertIntoSelect2('push-outer-type', 'push_outer_type', data);
                        // 添加区域联动配置提示
                        $("#push-outer-type").parent().append('<div class="config-tips" style="display:none; position:absolute; background:#f9f9f9; border:1px solid #ccc; padding:10px; z-index:1000; width:300px;">'+
                            '<p><strong>运行模式说明：</strong>本地模式表示是当前系统所在的急救中心，云模式表示非本中心的其他中心；本地模式只能有一个。配置的时候要注意</p>'+
                            '<p><strong>内部通讯地址说明：</strong>其他中心的接口地址，例如：http://127.0.0.1:9999/</p>'+
                        '</div>');
                        
                        // 显示/隐藏配置提示
                        $(".fa-info-circle").hover(
                            function() {
                                $(this).closest("#other-info-panel").find(".config-tips").show();
                            }, 
                            function() {
                                $(this).closest("#other-info-panel").find(".config-tips").hide();
                            }
                        );
                        //车辆类型
                        insertIntoRadio('vehicleType', 'car_type', data);
                        
                        // 初始化国籍和外籍人员的联动功能
                        initCountryForeignLinkage();
                        
                        // 添加监听器，当呼叫类型改变时检查是否需要显示接收中心
                        $('#evd-call-type').change(function() {
                            if($(this).val() === '11') { // 呼叫类型为联动派车
                                $('.field-push-outer-type').show();
                            } else {
                                $('.field-push-outer-type').hide();
                            }
                        });
                        
                        // 初始执行一次，确保状态正确
                        if($('#evd-call-type').val() === '11') {
                            $('.field-push-outer-type').show();
                        } else {
                            $('.field-push-outer-type').hide();
                        }

                    }); //查询所有字典
                
                // 初始化获取呼叫类型创建事件判断列表
                initCallTypeShouldCreateEventList();
                // 设置性别默认选中"未知"（值为2）
                $('#det-gender').val('2')
                $('#det-address').keydown(function (e) {
                    if (e.keyCode == 13) {
                        confirmAddress($('#det-address').val(), $('#event-lng').val(), $('#event-lat').val());
                        autoMatchRegionByAddress($('#det-address').val());
                    }
                });
                
                // 为等车地址添加回车键支持
                $('#pickup-address').keydown(function (e) {
                    if (e.keyCode == 13) {
                        confirmPickupAddress($('#pickup-address').val(), $('#pickup-lng').val(), $('#pickup-lat').val());
                        autoMatchRegionByAddress($('#pickup-address').val());
                    }
                });
                //查询诊断字典

                /**
                 * 获取当前地图选择的位置信息
                 * @returns {Object|null} 位置信息对象或null
                 */
                function getCurrentMapEventLocation() {
                    const locationStr = localStorage.getItem('currentMapEventLocation');
                    if (!locationStr) {
                        $.messager.alert('提示', '没有找到地图位置信息，请先在地图上选择位置');
                        return null;
                    }
                    try {
                        return JSON.parse(locationStr);
                    } catch (e) {
                        console.error('解析地图位置信息失败:', e);
                        $.messager.alert('错误', '解析地图位置信息失败');
                        return null;
                    }
                }

                /**
                 * 获取地图位置并回填到对应的地址输入框
                 * @param {string} type - 地址类型：'scene'表示现场地址，'pickup'表示等车地址
                 */
                function getMapLocation(type) {
                    const location = getCurrentMapEventLocation();
                    if (!location) return;
                    
                    if (type === 'scene') {
                        // 回填现场地址
                        $('#det-address').val(location.address);
                        // 如果有经纬度相关的隐藏字段，也进行回填
                        if ($('#det-longitude').length) $('#det-longitude').val(location.longitude);
                        if ($('#det-latitude').length) $('#det-latitude').val(location.latitude);
                    } else if (type === 'pickup') {
                        // 回填等车地址
                        $('#pickup-address').val(location.address);
                        // 如果有经纬度相关的隐藏字段，也进行回填
                        if ($('#pickup-longitude').length) $('#pickup-longitude').val(location.longitude);
                        if ($('#pickup-latitude').length) $('#pickup-latitude').val(location.latitude);
                    }
                }

                /*getPatientDiagnosis(function (data) {
                    let list = data[0].val
                    //加载诊断树字典
                    loadRegionTree(list, "#patientDiagnosislevel1")
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '查询诊断字典失败，异常信息：' + errMsg);
                })*/

                //加载完百度地图后的逻辑
                if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                    //视频呼入 
                    createNewEventByVideo(_currentInCallDetails.phone, _currentInCallDetails.callUid,  decodeURI(_currentInCallDetails.callInTimes), decodeURI(_currentInCallDetails.address), _currentInCallDetails.lng, _currentInCallDetails.lat,decodeURI(_currentInCallDetails.nickName), decodeURI(_currentInCallDetails.avatarUrl), _currentInCallDetails.gender);
                }else if(_currentInCallDetails.regionLinkageId && _currentInCallDetails.regionLinkageId != ''){
                    //区域联动呼入
                    createNewEventByRegionLinkage(_currentInCallDetails.regionLinkageId,_currentInCallDetails.externalBizId);
                }else{
                    //电话呼入
                    createNewEventByPhone(_currentInCallDetails.phone, _currentInCallDetails.callUid, unescape(_currentInCallDetails.callInTimes));
                }
                $('#dg-uploadAdress').datagrid({
                    loadMsg: '数据加载，请稍等.....',
                    data: getUploadAdress() //这一步是加载ajax查询的数据，非常重要
                });
                //加载事件监控列表
                $('#dg-event-pages').datagrid({
                    loadMsg: '数据加载，请稍等.....',
                    onLoadSuccess: function () {
                    },
                    //单击事件
                    onClickRow: function (rowIndex, rowData) {

                    },
                    onDblClickRow: function (rowIndex) { //鼠标双击事件
                        //打开地图上事件所在位置
                        var row = $('#dg-event-pages').datagrid('getSelected');
                        //查询事件信息
                        openEventDetail(row.eventId);
                    }
                });
                //关闭播放wav录音的窗口时
                $('#wavPlayWindow').window({
                    onBeforeClose: function () {
                        var playWavAudio = document.getElementById('playWavAudio');
                        playWavAudio.pause(); //暂停播放
                    }
                });

                //响铃列表
                $('#dg-call-list').datagrid({
                    loadMsg: '数据加载，请稍等.....',
                    rowStyler: function (index, row) {
                        if (row.isCurrentCall) {
                            return 'background-color:#4cfd13;color:#000;';
                        } else {
                            return 'background-color:#fff;color:#000;'; // return inline style
                        }
                    },
                    //单击事件
                    onClickRow: function (rowIndex, rowData) {
                        $(this).datagrid('unselectRow', rowIndex);
                    }
                });

                //获得当前响铃的来电号码
                if (_currentInCallDetails.callUid) {
                    //获取当前响铃的电话号码
                    if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                        //获取当前响铃的视频呼救
                        getAnswerVideoCalls(_currentInCallDetails.callUid);
                    }else{
                        getAnswerCalls(_currentInCallDetails.callUid);
                    }
                    
                }
            }
        };
        
        // 暂时写死高德，没有配置
        ele.src = "https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652";
        ohead.appendChild(ele);
        /** 重大事件弹窗关闭按钮 */
        function largeEventCancel() {
            $('#large-event-upload').window('close')
        }
        /** 上报地址 */
        function getUploadAdress() {
            let eventId = $("#event-id").val()
            clearTimeout(window.getUploadAddressTimeout);
            let _uploadAddIntervals
            try {
                _uploadAddIntervals = window.parent._uploadAddInterval
            } catch (e) {
                _uploadAddIntervals = window.parent.parent._uploadAddInterval;
            }
            // $('#dg-uploadAdress').datagrid('loading');//打开进度条：打开等待div
            //获取事件任务信息
            if (eventId) {
                var getuploadAddressParams = {
                    eventId: eventId,
                    maxSize: 2,
                };
                getUploadAddress(getuploadAddressParams,
                    function (data) {
                        uploadAddress = data
                        $('#dg-uploadAdress').datagrid('loadData', { total: 0, rows: [] });//清理数据
                        //获取正在进行的事件的列表
                        for (var i = 0; i < data.length; i++) {
                            // 如果上报的地址其一和 地址栏上的地址时相同的，默认就是启用了当前上报地址给一个背景颜色
                            let useLng = $('#event-lng').val()
                            let useLat = $('#event-lat').val()
                            $('#dg-uploadAdress').datagrid({
                                rowStyler: function (index, row) {
                                    if (data[index].lng == useLng && data[index].lat == useLat) {
                                        return 'background-color: #48c400; color: #fff;';
                                    }
                                }
                            });
                            $('#dg-uploadAdress').datagrid('insertRow', {
                                index: i,  // 索引从0开始
                                row: {
                                    address: data[i].address,
                                    createTime: data[i].createTime,
                                }
                            });
                        }
                    },
                    function (e, url, errMsg) {
                        // $('#dg-mobiel-process-list').datagrid('loaded');//关闭loding进度条
                        $.messager.alert('提示', '获取事件任务信息异常：' + errMsg, 'error');
                    });
            }
            if (_uploadAddIntervals) {
                window.getUploadAddressTimeout = setTimeout(getUploadAdress, 3000);
            }
        }
        function uploadTimeFormatter(value, row, index) {
            let y = new Date(row.createTime).getFullYear()
            let m = new Date(row.createTime).getMonth() + 1
            let d = new Date(row.createTime).getDate()
            let h = new Date(row.createTime).getHours()
            let mm = new Date(row.createTime).getMinutes()
            let node = `
              <div>${y}-${m}-${d} ${h}:${mm}</div>
              `
            return node
        }
        /**
         * 同步地址走接口
         * @param index
         */
        function syncAddress(index) {
            let uploadAddressParams = {
                address: uploadAddress[index].address,
                eventId: uploadAddress[index].eventId,
                lat: uploadAddress[index].lat,
                lng: uploadAddress[index].lng
            }
            updateEventAddress(uploadAddressParams,
                function (data) {
                    $('#det-address').val(uploadAddress[index].address);
                    // 地址设置后自动匹配区域
                    autoMatchRegionByAddress(uploadAddress[index].address);
                    $('#event-lng').val(uploadAddress[index].lng);
                    $('#event-lat').val(uploadAddress[index].lat);
                    try {
                        window.parent.gProxy.sendToMap(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
                    } catch (e) {
                        window.parent.bsProxy.sendToMap(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
                    }

                    $.messager.alert('提示', '同步地址成功', 'success');
                },
                function (e, url, errMsg) {
                    $.messager.alert('提示', '同步地址信息异常：' + errMsg, 'error');
                });
        }
        /**
         * 发送到地图不走接口
         * @param index
         */
        function sendToMap(index) {
            if (uploadAddress[index] && uploadAddress[index].lat && uploadAddress[index].lng) {
                try {
                    window.parent.gProxy.sendToMap(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
                } catch (e) {
                    window.parent.bsProxy.sendToMap(uploadAddress[index].address, String(uploadAddress[index].lng), String(uploadAddress[index].lat));
                }

            }
            uploadAddress[index]
        }
        function uploadOperationFormatter(value, row, index) {
            let node = `
                <img style="width:22px;height:22px;cursor: pointer;" src="style/img/tongbuSelect.png" onclick="syncAddress(${index})">
              `
            return node
        }
        function uploadAddressFormatter(value, row, index) {
            let node = `
                <div id="uploadAddress${index}" onmouseout="closeAllTips()" onmouseover="uploadAddressHover(${index})">${row.address}</div>
              `
            return node
        }
        function uploadAddressHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + uploadAddress[index].address + "</span>", `#uploadAddress${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }

        //出车医生输入自动搜索
        $('#dispatchDoctorDri').combobox({
            prompt: '输入医生工号和姓名',
            editable: true,
            hasDownArrow: true,
            valueField: 'id',
            textField: 'name',
            filter: function (q, row) {
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            }
        });
        //出车护士输入自动搜索
        $('#dispatchNurseDri').combobox({
            prompt: '输入护士工号和姓名',
            editable: true,
            hasDownArrow: true,
            valueField: 'id',
            textField: 'name',
            filter: function (q, row) {
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            }
        });
        //出车司机输入自动搜索
        $('#dispatchDriverDri').combobox({
            prompt: '输入司机工号和姓名',
            editable: true,
            hasDownArrow: true,
            valueField: 'id',
            textField: 'name',
            filter: function (q, row) {
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            }
        });
        //出车护工输入自动搜索
        $('#dispatchWorkerDri').combobox({
            prompt: '输入护工工号和姓名',
            editable: true,
            hasDownArrow: true,
            valueField: 'id',
            textField: 'name',
            filter: function (q, row) {
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            }
        });

        //获得出车班次
        $('#dispatchClassesDri').combobox({
            prompt: '请选择出车班次',
            editable: true,
            hasDownArrow: true,
            valueField: 'id',
            textField: 'name',
            filter: function (q, row) {
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            },
            onSelect: function (record) {

                //找到当前的班次的出车医生护士等自动填写进去
                dispatchClassesSelect = dispatchClassesList.find((item) => item.id === record.id);
                var dispatchDoctorDatas = $('#dispatchDoctorDri').combobox('getData');
                for (var i = 0; i < dispatchDoctorDatas.length; i++) {
                    if (dispatchDoctorDatas[i].id == dispatchClassesSelect.dispatchDoctorId) {
                        $('#dispatchDoctorDri').combobox('select', dispatchClassesSelect.dispatchDoctorId);
                        break;
                    }
                }

                var dispatchNurseDatas = $('#dispatchNurseDri').combobox('getData');
                for (var i = 0; i < dispatchNurseDatas.length; i++) {
                    if (dispatchNurseDatas[i].id == dispatchClassesSelect.dispatchNurseId) {
                        $('#dispatchNurseDri').combobox('select', dispatchClassesSelect.dispatchNurseId);
                        break;
                    }
                }

                var dispatchWorkerDatas = $('#dispatchWorkerDri').combobox('getData');
                for (var i = 0; i < dispatchWorkerDatas.length; i++) {
                    if (dispatchWorkerDatas[i].id == dispatchClassesSelect.dispatchWorkerId) {
                        $('#dispatchWorkerDri').combobox('select', dispatchClassesSelect.dispatchWorkerId);
                        break;
                    }
                }

                var dispatchDriverDatas = $('#dispatchDriverDri').combobox('getData');
                for (var i = 0; i < dispatchDriverDatas.length; i++) {
                    if (dispatchDriverDatas[i].id == dispatchClassesSelect.dispatchDriverId) {
                        $('#dispatchDriverDri').combobox('select', dispatchClassesSelect.dispatchDriverId);
                        break;
                    }
                }

            },
            onChange: function (newValue, oldValue) {
            }

        });
        /** 发送指导短信 */
        function sendMessage() {
            if (hasDisabled) return // 十秒倒计时中，不执行任何操作
            let eventId = $("#event-id").val()
            if (!eventId) {
                // 没有事件id则 表示没有保存事件，必须先保存事件，有事件 id 才能发送指导短信
                syncEventBtn(function (id) {
                    doSendH5Message(id);
                }, true)
            } else {
                doSendH5Message(eventId);
            }
        }
        // 10秒倒计时方法
        var hasDisabled = false;// 点击按钮发送短信成功后进入10秒倒计时的标记
        var disTime = 10; // 10秒倒计时
        function useCountdown() {
            hasDisabled = true // 启动10秒倒计时
            let countdownId = setInterval(() => {
                $("#sendMessageBtn").css({ "background-color": "#f4f4f5", "color": "#bcbec2", "line-height": "28px", "padding": "0 12px" });
                $("#sendMessageBtn").html("<img src='style/img/sendMessageDisabled.png' style='width:16px;height: 16px;'' alt=''> 保存并发送指导短信" + ' ' + disTime);
                disTime--
                if (disTime < 0) {
                    hasDisabled = false
                    $("#sendMessageBtn").css({ "background-color": "white", "color": "#099DFC" });
                    $('#sendMessageBtn').removeProp('border');
                    $("#sendMessageBtn").html("<img src='style/img/sendMessage.png' style='width:16px;height: 16px;'' alt=''> 保存并发送指导短信");
                    clearInterval(countdownId)
                    disTime = 10
                }
            }, 1000)
        }
        /**
         * 真正发指导短信
         * @param eventId
         */
        function doSendH5Message(eventId) {
            countSendH5Message({ eventId: eventId }, function (res) {
                if (res > 0) {
                    $.messager.confirm("提示", "已发送过指导视频短信，是否重新发送", function (r) {
                        if (r) {
                            sendH5Message({ eventId: eventId }, function (resp) {
                                useCountdown() // 启用10秒倒计时
                                $.messager.alert('提示', '短信发送成功', 'info');
                            }, function (e2, url2, errMsg2) {
                                //失败才做处理
                                $.messager.alert('提示', '请求错误：' + errMsg2);
                            })
                        }
                    });
                } else {
                    sendH5Message({ eventId: eventId }, function (resp) {
                        useCountdown() // 启用10秒倒计时
                        $.messager.alert('提示', '短信发送成功', 'info');
                    }, function (e2, url, errMsg2) {
                        //失败才做处理
                        $.messager.alert('提示', '请求错误：' + errMsg2);
                    })
                }
            }, function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('提示', '请求错误：' + errMsg);
            })
        }

        /** 获取打开ADLS系统的链接 */
        function redirectAdlsUrl() {
            let eventId = $('#event-id').val();//事件id
            if (eventId != null && eventId != "") {
                getEventDetailById(eventId,
                    function (data) {
                        if (data.status == '1' || data.status == '2') {
                            var getAdlsUrlDTO = {};
                            getAdlsUrlDTO.eventId = eventId;//事件id
                            getAdlsUrlDTO.contactPhone = data.contact;//联系人电话
                            getAdlsUrlDTO.exactAddress = data.address;
                            getAdlsUrlDTO.reason = data.majorCall;//呼叫原因
                            getAdlsUrlDTO.seatCode = _pSeat.seatId; //座席code
                            getAdlsUrlDTO.seatId = _pSeat.id; //座席id
                            getAdlsUrlDTO.username = _pSeat.userName;//座席用户工号
                            getAdlsUrlDTO.displayName = _pSeat.user;//座席用户姓名
                            getAdlsUrlDTO.gpsType = "02";//经纬度类型 01:gps 02:高德 03:百度
                            getAdlsUrlDTO.latitude = data.lat;//纬度
                            getAdlsUrlDTO.longitude = data.lng;//经度
                            getAdlsUrlDTO.patientGender = data.patientGender == null || data.patientGender == '' ? '2' : data.patientGender;//患者性别
                            getAdlsUrlDTO.alarmPhone = data.contact;//报警人电话
                            getAdlsUrlDTO.patientAge = data.patientAge;//患者年龄，单位：年
                            getAdlsUrlDTO.patientAmount = data.patientNumber;//患者人数
                            getAdlsUrl(getAdlsUrlDTO,
                                function (url) {
                                    try {
                                        window.parent.gProxy.openBrowser(url);
                                    } catch (e) {
                                        try {
                                            if (window.parent.opener && typeof window.parent.opener.windowOpenUrl === 'function') {
                                                window.parent.opener.windowOpenUrl(url);
                                            } else {
                                                // 如果opener不存在，尝试直接打开链接
                                                window.open(url, '_blank');
                                            }
                                        } catch (e2) {
                                            // 最后的备选方案：直接打开链接
                                            window.open(url, '_blank');
                                        }
                                    }
                                },
                                function (e, url, errMsg) {
                                    $.messager.alert('提示', "获取ADLS系统链接失败" + errMsg, 'error');
                                }
                            );
                        }else{
                            $.messager.alert('提示', '事件已结束，无法获取ADLS系统链接', 'info');
                        }
                    },
                    function (e, url, errMsg) {
                        $.messager.alert('提示', "事件查询失败：" + errMsg, 'error');
                    }
                );
            } else {
                $.messager.alert('提示', '事件未保存，无法获取ADLS系统链接', 'info');
            }
        }
        /**
         * 返回参数对象
         * @param urlParams
         */
        function queryURLParams(urlParams) {
            // const url = location.search; // 项目中可直接通过search方法获取url中"?"符后的字串
            let url = urlParams.split("?")[1];
            let obj = {}; // 声明参数对象
            let arr = url.split("&"); // 以&符号分割为数组
            for (let i = 0; i < arr.length; i++) {
                let arrNew = arr[i].split("="); // 以"="分割为数组
                obj[arrNew[0]] = arrNew[1];
            }
            return obj;
        }
        //重大事件勾选按钮事件
        $('#eventType').on("click", function (ev) {
            if ($('#eventType').is(':checked')) {
                $("#eventType").prop("checked", true);
                $("#largerEventUpload").show()
                // 启用事故类型和事故等级下拉框
                $('#mainEventType').prop('disabled', false);
                $('#mainEventLevel').prop('disabled', false);
            } else {
                let hasEventId = $("#event-id").val()
                if (!hasEventId) { // 如果没有id说明事件还没有生成，不存在上报重大事件，可以隐藏
                    $("#largerEventUpload").hide()
                    // 禁用并清空事故类型和事故等级下拉框
                    $('#mainEventType').prop('disabled', true).val('');
                    $('#mainEventLevel').prop('disabled', true).val('');
                } else {
                    isReported({ eventCode: hasEventId },
                        function (res) {
                            //是否上传过
                            if (res.isReported == '0') {
                                $("#largerEventUpload").hide()
                                // 禁用并清空事故类型和事故等级下拉框
                                $('#mainEventType').prop('disabled', true).val('');
                                $('#mainEventLevel').prop('disabled', true).val('');
                            } else {
                                // 上传过 不准取消
                                $.messager.alert('注意', '已上报过重大事件，不可取消勾选', false);
                                $("#largerEventUpload").show()
                                $("#eventType").prop("checked", true);
                                // 保持启用状态
                                $('#mainEventType').prop('disabled', false);
                                $('#mainEventLevel').prop('disabled', false);
                            }
                        },
                        function (e, url, errMsg) {
                            //失败不做处理
                        });
                }
            }
        });

        /** 上传重大事件方法 */
        function uploadMajorEvents() {
            //先判断有没有事件
            if ($("#event-id").val() == "") {
                //没有事件先创建一个事件
                addEvent(
                    function (res) {
                        $("#event-id").val(res.id)
                        // 呼叫类型不容许修改，只要创建了。
                        // isUpdateEventCallType($("#event-id").val());
                        //重大事件才发送短信
                        if ($('#eventType').is(':checked')) {
                            uploadEvent($("#event-id").val());
                        }
                    },
                    function (errMsg) {
                        //失败才做处理
                        showProcess(false);
                        $.messager.alert('创建事件错误', errMsg);
                    });
            } else {
                //重大事件才发送短信
                if ($('#eventType').is(':checked')) {
                    uploadEvent($("#event-id").val());
                }
            }
        }
        /** 查询重大事件模块的信息 */
        function getLargerEventInfo() {
            let idEvent = $('#event-id').val()
            eventReportItem({ eventCode: idEvent, page: 1, size: 9999 },
                function (data) {
                    largerEventList = data
                    //设置默认上报类型
                    let defaultReportTypeCode = getDefaultReportTypeCode(data);
                    $("#reportTypeCode").val(defaultReportTypeCode);
                    selectLargerEvent() // 回显汇报内容

                    resetReportContentIfAbsent(idEvent)

                    $('#larger-event-upload-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].reportTime) {
                            data[i].reportTime = convertTimeToRead(data[i].reportTime)
                        }
                        $('#larger-event-upload-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                reportTime: data[i].reportTime,
                                reportTypeName: data[i].reportTypeName,
                                reportTypeCode: data[i].reportTypeCode,
                                reportUserName: data[i].reportUserName,
                                eventTypeName: data[i].eventTypeName,
                                eventLevelName: data[i].eventLevelName,
                                vehicleDispatchedNum: data[i].vehicleDispatchedNum,
                                woundedNum: data[i].woundedNum,
                                deadNum: data[i].deadNum,
                                woundedSevereNum: data[i].woundedSevereNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                transferNum: data[i].transferNum,
                                reportContent: data[i].reportContent,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-upload-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#largeEventId').val(rowData.id)
                            $('#reportTypeCode').val(rowData.reportTypeCode)
                            $('#largeEventReportTime').datetimebox('setValue', rowData.reportTime)
                            $('#vehicleDispatchedNum').val(rowData.vehicleDispatchedNum)
                            $('#woundedNum').val(rowData.woundedNum)
                            $('#deadNum').val(rowData.deadNum)
                            $('#woundedSevereNum').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum').val(rowData.woundedSlightNum)
                            $('#transferNum').val(rowData.transferNum)
                            setTimeout(() => { // 为了防止select 事件覆盖此条记录 不能去掉延时器
                                $('#reportContent').val(rowData.reportContent)
                            }, 0);
                            // $('#larger-event-upload-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasLargerEventEdit = true // 表示有正在编辑的数据不让再双击其它数据
                            selectLargerEvent()
                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                });
        }
        /** 查询流动伤员的列表详情 */
        function getEventWoundedTransfer() {
            let idEvent = $('#event-id').val()
            eventWoundedTransfer({ eventCode: idEvent },
                function (data) {
                    flowOfCasualtiesList = data
                    $('#larger-event-flowOfCasualties-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].vehicleDispatchedTime) {
                            data[i].vehicleDispatchedTime = convertTimeToRead(data[i].vehicleDispatchedTime);
                        }
                        $('#larger-event-flowOfCasualties-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                vehiclePlateNo: data[i].vehiclePlateNo,
                                vehicleDispatchedTime: data[i].vehicleDispatchedTime,
                                transferNum: data[i].transferNum,
                                transferAddress: data[i].transferAddress,
                                woundedSevereNum: data[i].woundedSevereNum,
                                deadNum: data[i].deadNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                injuryRemark: data[i].injuryRemark,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-flowOfCasualties-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#eventWoundedTransferId').val(rowData.id)
                            $('#vehiclePlateNo_liudong').val(rowData.vehiclePlateNo)
                            $('#transferNum_liudong').val(rowData.transferNum)
                            $('#transferAddress_liudong').val(rowData.transferAddress)
                            $('#woundedSevereNum_liudong').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum_liudong').val(rowData.woundedSlightNum)
                            $('#deadNum_liudong').val(rowData.deadNum)
                            $('#injuryRemark').val(rowData.injuryRemark)
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', rowData.vehicleDispatchedTime)
                            // $('#larger-event-flowOfCasualties-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasFlowOfCasualtiesEdit = true // 表示有正在编辑的数据不让再双击其它数据
                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                })

        }
        function resetuploadEventPage() {
            $('#largerFormEventId').val('')
            $('#largeEventType').val('')
            $('#largeEventLevel').val('')
            $('#reportTypeCode').val('')
            $('#vehicleDispatchedNum').val('')
            $('#woundedNum').val('')
            $('#deadNum').val('')
            $('#woundedSevereNum').val('')
            $('#woundedSlightNum').val('')
            $('#transferNum').val('')
            $('#reportContent').val('')
            $('#eventWoundedTransferId').val('')
            $('#vehiclePlateNo_liudong').val('')
            $('#transferNum_liudong').val('')
            $('#transferAddress_liudong').val('')
            $('#woundedSevereNum_liudong').val('')
            $('#woundedSlightNum_liudong').val('')
            $('#deadNum_liudong').val('')
            $('#injuryRemark').val('')
        }

        // 保存座席人员电话，保存时需要传参
        let seatUserPhone = ''

        /**
         * 开始上传重大事件 打开弹窗
         * @param id
         */
        function uploadEvent(id) {
            // 先获取主界面的事故类型和事故等级值
            var mainEventType = $('#mainEventType').val();
            var mainEventLevel = $('#mainEventLevel').val();
            
            // 不清空值，让主界面的选择保持到弹窗中
            // $('#largeEventLevel').val('')
            // $('#largeEventType').val('')
            // 查询重大事件的信息
            // 获取事件基本信息的接口
            getImportantEventInfo({ id },
                function (res) {
                    seatUserPhone = res.seatUserPhone
                    $('#large-event-upload').window({
                        title: `<div style="display:flex;justify-content: space-between;align-items: center;">
                                <div>
                                    <span style="margin-right:20px">重大事件上报</span>
                                    <span>来电时间：<span id='largeEventCallInTimes'>${res.callInTimes ? res.callInTimes : ''}</span></span>
                                </div>
                                <div style="margin-right: 60px">
                                    调度员：${res.seatUser}
                                </div>
                            </div>`, // 替换 esay ui的title内容
                        onClose: function () {
                            resetuploadEventPage()
                        }
                    });
                    $('#largerEvent-lat').val(res.lat) // 重大事件的标题设置回显
                    $('#largerEvent-lng').val(res.lng) // 重大事件的标题设置回显
                    $('#largerEventAddress').text(res.address) // 重大事件的标题设置回显
                    $('#largerEventZS').text(res.majorCall) // 重大事件的标题设置回显
                    $('#largerFormEventId').val(res.eventId) // 重大事件的编号回显
                    // 事件类型和事件等级回显
                    // $('#largeEventType').val(res.largeEventType)
                    // $('#largeEventLevel').val(res.largeEventLevel)

                    $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                    $('#woundedNum').val(res.woundedNum)// 伤员人数
                    $('#deadNum').val(res.deadNum)// 死亡人数
                    $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                    $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                    $('#transferNum').val(res.transferNum)// 转送人数

                    $('#large-event-upload').window('open'); //打开上报重大事件页面
                    //查询重大事件的上报列表
                    getLargerEventInfo()
                    //查询重大事件的伤员流向列表
                    getEventWoundedTransfer()
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                }
            );
            // 查询重大事件类型和等级的两个字段单独用了一个接口
            getByEventCode({ eventCode: id },
                function (res) {
                    // 如果数据库有值，则使用数据库的值；如果数据库没有值但主界面有值，则保持主界面的值
                    if (res.eventTypeCode) {
                        $('#largeEventType').val(res.eventTypeCode);
                    } else if (mainEventType) {
                        $('#largeEventType').val(mainEventType);
                    }
                    
                    if (res.eventLevelCode) {
                        $('#largeEventLevel').val(res.eventLevelCode);
                    } else if (mainEventLevel) {
                        $('#largeEventLevel').val(mainEventLevel);
                    }
                    
                    selectLargerEvent() // 第一次打开页面时也要回显汇报内容的默认话术
                },
                function (e, url, errMsg) {
                    // 如果API调用失败，但主界面有值，则使用主界面的值
                    if (mainEventType) {
                        $('#largeEventType').val(mainEventType);
                    }
                    if (mainEventLevel) {
                        $('#largeEventLevel').val(mainEventLevel);
                    }
                    selectLargerEvent() // 确保在有值的情况下也回显汇报内容
                }
            );
            let nowTimeStr = getNowTimeStr();
            //上报时间默认取当前时间
            $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
            //出动车辆时间默认取当前时间
            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)

            // 之前上传重大事件是给领导发短信先注释掉，现在改为弹窗 填写信息
            // $.messager.confirm("提示", "是否确认上传重大事件", function (r) {
            //     if (r) {
            //         importantEvent(id, function (res) {
            //             let data = res.sendStatusSet || []
            //             let status = true;
            //             let errorMsg = "";
            //             data.forEach(r => {
            //                 if (r.message !== 'send success') {
            //                     if (status) {
            //                         status = false;
            //                     }
            //                     errorMsg += r.message;

            //                 }
            //             })
            //             if (status) {
            //                 $.messager.alert('提示', '上传重大事件成功', 'info');
            //             } else {
            //                 $.messager.alert('提示', '上传重大事件失败！！！' + errorMsg, 'error');
            //             }
            //             // parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails
            //             //                 .callUid + ')' : '创建新事件');
            //         },
            //             function (e, url, errMsg) {
            //                 $.messager.alert('提示', '获取事件信息异常：' + errMsg, 'error');
            //             }
            //         )
            //     }
            // });
        }
        /** 重大事件表单保存接口 */
        function largeEventAddUpload() {
          let selectPhone = phoneTree.getChecked('phone-list'); // 获取选中节点的数据
          let sendMsgList =[]
          for(let s=0;s<selectPhone.length;s++){
            let item = selectPhone[s]
            for(let c=0;c<item.children.length;c++){
              let child = item.children[c]
              let obj = {
                "name": child.title,
                "phone": child.number
              }
              sendMsgList.push(obj)
            }
          }
          var id = $('#largeEventId').val();
          var reportTypeCodeSelected = document.getElementById("reportTypeCode");
          var reportTypeCode = reportTypeCodeSelected.value; //上报类别编码
          var reportTypeName = reportTypeCodeSelected.options[reportTypeCodeSelected.selectedIndex].text;//上报类别名称
          var reportTimeStr = $('#largeEventReportTime').val(); //上报时间
          var reportTime = reportTimeStr.replace(/[\s:-]/g, "");
          var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()// 出动车辆
          var woundedNum = $('#woundedNum').val()// 伤员人数
          var deadNum = $('#deadNum').val()// 死亡人数
          var woundedSevereNum = $('#woundedSevereNum').val()// 重伤人数
          var woundedSlightNum = $('#woundedSlightNum').val()// 轻伤人数
          var transferNum = $('#transferNum').val()// 转送人数
          var reportContent = $('#reportContent').val()// 汇报内容
          var objLE = {
              reportTypeCode,
              reportTypeName,
              reportTime,
              vehicleDispatchedNum,
              woundedNum,
              deadNum,
              woundedSevereNum,
              woundedSlightNum,
              transferNum,
              reportContent,
              reportUserName: _pSeat.user,
              reportUserPhone: seatUserPhone
          }
          if (isEdit) {
              objLE.id = id
              var largeEventTypeSelected = document.getElementById("largeEventType");
              var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
              var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
              var largeEventLevelSelected = document.getElementById("largeEventLevel");
              var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
              var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
              let objValue = {
                  eventTypeCode,
                  eventTypeName,
                  eventLevelCode,
                  eventLevelName,
              }
              const values = Object.values(objLE).map(item => {
                  return item.trim();
              })
              if (objLE.id == '') {
                  return $.messager.alert('提示', '如需修改事件请先双击表格内已上报的事件，否则请先添加并上报新事件。', 'error');
              }
              if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                  return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
              }
              $.messager.confirm("提示", '是否新增并立即上报重大事件？', function (r) {
                  if (r) {
                      largeUpdateById({ ...objLE, ...objValue, users: sendMsgList },
                          function (res) {
                              $.messager.alert('提示', '修改成功', 'success');
                              // 表单编辑完全部清空
                              $('#largeEventId').val('')
                              $('#reportTypeCode').val('')
                              $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                              $('#vehicleDispatchedNum').val('')
                              $('#woundedNum').val('')
                              $('#deadNum').val('')
                              $('#woundedSevereNum').val('')
                              $('#woundedSlightNum').val('')
                              $('#transferNum').val('')
                              $('#reportContent').val('')
                              // 表格重新渲染一下
                              getLargerEventInfo()
                              cancelPhoneList()
                          },
                          function (e, url, errMsg) {
                              //失败做处理
                              $.messager.alert('提示', errMsg);
                          }
                      );
                  }
              });
          } else {
              // 修改重大事件，调用专用的接口
              largeEventSubmit('1', objLE, sendMsgList)
          }
        }
        /**
         * 伤员流向表单新增
         * @param isEdit
         */
        function flowOfCasualtiesAddUpload(isEdit) {
            // 伤员的流动数据
            var id = $('#eventWoundedTransferId').val()// id
            var vehicleDispatchedTime = $('#vehicleDispatchedTime_liudong').val().replace(/[\s:-]/g, "")// 出动车辆时间
            var vehiclePlateNo = $('#vehiclePlateNo_liudong').val()// 出动车辆数量
            var transferNum = $('#transferNum_liudong').val()// 转送人数
            var transferAddress = $('#transferAddress_liudong').val()// 送往地点
            var woundedSevereNum = $('#woundedSevereNum_liudong').val()// 重伤
            var woundedSlightNum = $('#woundedSlightNum_liudong').val()// 轻伤
            var deadNum = $('#deadNum_liudong').val()// 死亡
            var injuryRemark = $('#injuryRemark').val()// 伤情备注
            let objFOC = {
                vehicleDispatchedTime,
                vehiclePlateNo,
                transferNum,
                transferAddress,
                woundedSevereNum,
                woundedSlightNum,
                deadNum,
                injuryRemark,
            }
            if (isEdit) {
                objFOC.id = id
                flowOfCasualtiesUpdate(objFOC)
            } else {
                largeEventSubmit('2', objFOC)
            }
        }
        /**
         * 伤员流向更新
         * @param obj
         */
        function flowOfCasualtiesUpdate(obj) {
            const values = Object.keys(obj).filter(val => {
                if (val != 'injuryRemark' && obj[val] == '') {
                    return val
                }
            })
            if (obj.id == '') {
                return $.messager.alert('提示', '如需修改伤员流向请先双击表格内已添加的伤员流向，否则请先添加一条新的伤员流向。', 'error');
            }
            if (values.length > 0) {
                return $.messager.alert('提示', '流动伤员必要信息未填写，无法上报。', 'error');
            }
            $.messager.confirm("提示", '是否修改并立即上报伤员流向', function (r) {
                if (r) {
                    let params = {
                        ...obj
                    }
                    eventWoundedTransferUpdateById(params,
                        function (res) {
                            $.messager.alert('提示', '修改成功', 'success');
                            // 表单编辑完全部清空
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                            $('#vehiclePlateNo_liudong').val('')
                            $('#transferNum_liudong').val('')
                            $('#transferAddress_liudong').val('')
                            $('#woundedSevereNum_liudong').val('')
                            $('#woundedSlightNum_liudong').val('')
                            $('#deadNum_liudong').val('')
                            $('#injuryRemark').val('')
                            // 表格重新渲染一下
                            getEventWoundedTransfer()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        /**
         * 重大事件保存按钮提交数据
         * @param type 1-上报列表，2-伤员流向
         * @param obj
         * @param sendMsgList
         */
        function largeEventSubmit(type, obj, sendMsgList) {
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            let objValue = {}
            if (type == '2') {
                objValue.injuryRemark = obj.injuryRemark
                delete obj.injuryRemark
            }
            const values = Object.values(obj).map(item => {
                return item.trim();
            })
            if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
            }
            $.messager.confirm("提示", `${type == '1' ? '是否新增并立即上报重大事件？' : '是否新增并立即上报伤员流向'}`, function (r) {
                if (r) {
                    let params = {
                        eventInfo: {
                            eventTypeCode,
                            eventTypeName,
                            eventLevelCode,
                            eventLevelName,
                            eventCode: $('#event-id').val(),
                            address: $('#largerEventAddress').text(),
                            latitude: $('#largerEvent-lat').val(),
                            longitude: $('#largerEvent-lng').val(),
                            occurrenceTime: $('#det-call-in-times').val().replace(/[\s:-]/g, ""),
                            seatUserName: _pSeat.user
                        },
                        reportItems: type == '1' ? [obj] : [],
                        woundedTransfers: type == '2' ? [{ ...obj, ...objValue }] : [],
                        users: sendMsgList
                    }
                    eventInfoReport(params,
                        function (res) {
                            $.messager.alert('提示', '上报成功', 'success');
                            // 上报成功表单全清空
                            if (type == '1') {
                                // 表单编辑完全部清空
                                $('#reportTypeCode').val('')
                                $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                                $('#vehicleDispatchedNum').val('')
                                $('#woundedNum').val('')
                                $('#deadNum').val('')
                                $('#woundedSevereNum').val('')
                                $('#woundedSlightNum').val('')
                                $('#transferNum').val('')
                                $('#reportContent').val('')
                                // 表格重新渲染一下
                                getLargerEventInfo()
                                //查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasLargerEventEdit = false //事件保存后 允许再双击其它事件 目前不存在编辑，先注释 后续可能会加此功能
                            } else {
                                // 表单编辑完全部清空
                                $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                                $('#vehiclePlateNo_liudong').val('')
                                $('#transferNum_liudong').val('')
                                $('#transferAddress_liudong').val('')
                                $('#woundedSevereNum_liudong').val('')
                                $('#woundedSlightNum_liudong').val('')
                                $('#deadNum_liudong').val('')
                                $('#injuryRemark').val('')
                                //查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasFlowOfCasualtiesEdit = false //事件保存后 允许再双击其它事件/目前不存在编辑，先注释 后续可能会加此功能
                            }
                            // $('#large-event-upload').window('close')
                            cancelPhoneList()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }

        /**
         * 重置汇报内容
         * @param idEvent 120事件id
         */
        function resetReportContentIfAbsent(idEvent) {
            if (!$('#reportContent').val()) {
                // 获取事件基本信息的接口
                getImportantEventInfo({ id: idEvent },
                    function (res) {
                        let nowTimeStr = getNowTimeStr();
                        //上报时间默认取当前时间
                        $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
                        //出动车辆时间默认取当前时间
                        $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)

                        $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                        $('#woundedNum').val(res.woundedNum)// 伤员人数
                        $('#deadNum').val(res.deadNum)// 死亡人数
                        $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                        $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                        $('#transferNum').val(res.transferNum)// 转送人数

                        selectLargerEvent()
                    },
                    function (e, url, errMsg) {
                        //失败做处理
                        $.messager.alert('提示', errMsg);
                    });
            }
        }

        /** 重大事件类型等级选择时触发，第一次打开页面或事件等级和事件类型选择时，回显汇报内容 */
        function selectLargerEvent() {
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            var reportTypeCode = $('#reportTypeCode').val()
            var reportTime = $('#largeEventReportTime').val()
            var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()
            var woundedNum = $('#woundedNum').val()
            var deadNum = $('#deadNum').val()
            var woundedSevereNum = $('#woundedSevereNum').val()
            var woundedSlightNum = $('#woundedSlightNum').val()
            var transferNum = $('#transferNum').val()
            if (eventTypeName && eventLevelName) {
                //默认汇报内容模板
                var largeEventCallInTimes = $('#largeEventCallInTimes').text().replace(/[\s:-]/g, "");// 来电时间
                let timeArr = largeEventCallInTimes.split('')
                let y = timeArr.slice(0, 4).join('')
                let m = timeArr.slice(4, 6).join('')
                let d = timeArr.slice(6, 8).join('')
                let hh = timeArr.slice(8, 10).join('')
                let mm = timeArr.slice(10, 12).join('')
                let ss = timeArr.slice(12, 14).join('')
                var callInTimes = y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
                var largerEventAddress = $('#largerEventAddress').text() // 地点
                var largerEventZS = $('#largerEventZS').text() // 呼叫原因
                if (reportTypeCode == '01') {
                    //var str = `${callInTimes}，在【${largerEventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
                    var str = createEventFirstReportContent(callInTimes, largerEventAddress, eventLevelName, eventTypeName);
                    $('#reportContent').val(str)
                } else if ((reportTypeCode == '02' || reportTypeCode == '03') && vehicleDispatchedNum && woundedNum && deadNum && woundedSevereNum && woundedSlightNum && transferNum && reportTime) {
                    //追报默认汇报内容模板
                    var str = createEventReportContent(convertTime(reportTime.replace(/[\s:-]/g, "")), vehicleDispatchedNum, woundedNum, deadNum, woundedSevereNum, woundedSlightNum, transferNum);
                    $('#reportContent').val(str)
                }

            }

        }
        function operation(value, row, index) {
            let node = '';
            //首报和终报不可以删除
            if (row.reportTypeCode === '02') {
                node += `
                    <a href="javascript:largeEventDel(${index});" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;padding: 3px 8px;border-radius: 5px;">删 除</a>
                    `
            }
            return node
        }
        function largeEventDel(index) {
            $.messager.confirm("提示", '是否删除此条重大事件？', function (r) {
                if (r) {
                    var id = largerEventList[index].id
                    let params = {
                        id
                    }
                    largeDeleteById(params,
                        function (res) {
                            $.messager.alert('提示', '删除成功', 'success');
                            getLargerEventInfo() // 表格数据重新渲染下
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        /**
         * 重大事件的汇报内容气泡弹窗
         * @param value
         * @param row
         * @param index
         */
        function reportContentFormatter(value, row, index) {
            let node = `
                <div id="reportContentFormatter${index}" onmouseout="closeAllTips()" onmouseover="reportContentHover(${index})">${row.reportContent}</div>
              `
            return node
        }
        function reportContentHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + largerEventList[index].reportContent + "</span>", `#reportContentFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /**
         * 重大事件的伤情备注气泡
         * @param value
         * @param row
         * @param index
         */
        function injuryRemarkFormatter(value, row, index) {
            let node = `
                <div id="injuryRemarkFormatter${index}" onmouseout="closeAllTips()" onmouseover="injuryRemarkHover(${index})">${row.injuryRemark}</div>
              `
            return node
        }
        function injuryRemarkHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].injuryRemark + "</span>", `#injuryRemarkFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /**
         * 重大事件的送往地点气泡
         * @param value
         * @param row
         * @param index
         */
        function transferAddressFormatter(value, row, index) {
            let node = `
                <div id="transferAddressFormatter${index}" onmouseout="closeAllTips()" onmouseover="transferAddressHover(${index})">${row.transferAddress}</div>
              `
            return node
        }
        function transferAddressHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].transferAddress + "</span>", `#transferAddressFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        /** 点击伤员流动的车牌号选择弹窗打开 */
        function chooseCar() {
            $('#chooseCarNoId').window('open');
            chooseCarInput()
        }
        /** 查询车辆 */
        function chooseCarInput() {
            let params = {
                plateNum: $('#chooseCarInput').val(),// 车牌号
                stationName: '' // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item,
                        spread: $('#chooseCarInput').val() ? true : false,
                        type: '1',
                        children: []
                    }
                    if (data[item].length > 0) {
                        data[item].forEach(val => {
                            obj.children.push({
                                spread: $('#chooseCarInput').val() ? true : false,
                                type: '2',
                                title: val.plateNum
                            })
                        })

                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#car-number-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            if (obj.data.type == '2') {
                                $('#chooseCarNoId').window('close');
                                // 输入框赋值
                                $('#vehiclePlateNo_liudong').val(obj.data.title)
                            }
                        }
                    });
                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        /** 点击伤员流动的车牌号选择弹窗打开 */
        function chooseStation() {
            $('#chooseStationId').window('open');
            chooseStationInput()
        }
        /** 查询车辆 */
        function chooseStationInput() {
            let params = {
                plateNum: '',// 车牌号
                stationName: $('#chooseStationInput').val() // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item
                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#station-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            // 输入框赋值
                            $('#chooseStationId').window('close');
                            $('#transferAddress_liudong').val(obj.data.title)
                        }
                    });

                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        function closeAllTips() {
            layui.use('layer', function () {
                var layer = layui.layer;
                layer.closeAll('tips');
            });
        }
        function showCheck(ev) {
            ev = ev || ''
            $('#mainSuit').html(ev)
        }
        /**
         * 呼叫类型变化处理
         * - 当选择咨询、投诉、骚扰等直接完成类型时：
         *   1. 隐藏必填项标识
         *   2. 禁用除呼叫类型、事件来源、来电时间外的所有输入框
         * - 当选择其他类型时：
         *   1. 显示必填项标识
         *   2. 启用所有输入框
         */
        $('#evd-call-type').on('click', function (ev) {
            let callType = $('#evd-call-type').val()
            // 如果是直接完成类型（咨询、投诉、骚扰等）
            if (isDirectCompleteCallType(callType)) {
                $('#sendMessageP').hide();//保存并发送指导短信
                $('#relevanceFirstEventP').hide();//关联首次事件

                // 隐藏必填项标识
                $('#not-required-address').show();
                $('#not-required-major').show();
                $('#not-required-contact').show();
                $('#required-address').hide();
                $('#required-major').hide();
                $('#required-contact').hide();
                $('#dispatchTypeDiv').hide();

                //接收中心隐藏掉
                $('.field-push-outer-type').hide();
                // 调整同行的测试的左边距
                $('.field-is-test').css('margin-left', '0');
                    

                // === 禁用所有非必要输入框（保留呼叫类型、事件来源、来电时间） ===
                // 基本信息
                $('#det-area').attr('disabled', true);           // 所属区域
                $('#eventType').attr('disabled', true);          // 重大事件复选框
                $('#mainEventType').attr('disabled', true);      // 事件类型
                $('#mainEventLevel').attr('disabled', true);     // 事故等级
                $('#emergency-level').attr('disabled', true);    // 紧急程度

                // 地址信息
                $('#det-address').attr('disabled', true);        // 现场地址
                $('#pickup-address').attr('disabled', true);     // 等车地址
                $('#destination-address').attr('disabled', true); // 送往地址

                // 联系信息
                $('#det-callin').attr('disabled', true);         // 主叫号码
                $('#det-contact').attr('disabled', true);        // 联系电话
                $('#det-contacter').attr('disabled', true);      // 联系人
                $('#det-amount').attr('disabled', true);         // 患者人数

                // 呼叫原因和症状
                $('#det-major').attr('disabled', true);          // 呼叫原因
                $('#select1').attr('disabled', true);            // 症状选择1
                $('#select2').attr('disabled', true);            // 症状选择2

                // 患者信息
                $('#det-name').attr('disabled', true);           // 患者姓名
                $('#det-gender').attr('disabled', true);         // 性别
                $('#det-age').attr('disabled', true);            // 年龄
                $('#det-country').attr('disabled', true);        // 国籍
                $('#createForeign').attr('disabled', true);      // 外籍人员
                $('#det-race').attr('disabled', true);           // 民族
                $('#need-stretcher').attr('disabled', true);     // 需要担架

                // 备注信息
                $('#det-special-req').attr('disabled', true);    // 特殊要求
                $('#det-120remark').attr('disabled', true);      // 120备注
                $('#det-alds-summary').attr('disabled', true);   // ALDS汇总
                $('#patient-info').attr('disabled', true);       // 上次来电

                // 其他信息
                $('#push-outer-type').attr('disabled', true);    // 分中心（原联动类型修改）
                $('#is-test').attr('disabled', true);            // 测试

                // 注意：呼叫类型(#evd-call-type)、事件来源(#evd-source)、来电时间(#det-call-in-times)保持启用状态

            } else {
                $('#sendMessageP').show();//保存并发送指导短信
                $('#relevanceFirstEventP').show();//关联首次事件
                // 显示必填项标识
                $('#not-required-address').hide();
                $('#not-required-major').hide();
                $('#not-required-contact').hide();
                $('#required-address').show();
                $('#required-major').show();
                $('#required-contact').show();
                $('#dispatchTypeDiv').show();  // 显示调度类型选择

                // === 启用所有输入框 ===
                // 基本信息
                $('#det-area').attr('disabled', false);          // 所属区域
                $('#eventType').attr('disabled', false);         // 重大事件复选框
                $('#emergency-level').attr('disabled', false);   // 紧急程度

                // 地址信息
                $('#det-address').attr('disabled', false);       // 现场地址
                $('#pickup-address').attr('disabled', false);    // 等车地址
                $('#destination-address').attr('disabled', false); // 送往地址

                // 联系信息
                $('#det-callin').attr('disabled', false);        // 主叫号码
                $('#det-contact').attr('disabled', false);       // 联系电话
                $('#det-contacter').attr('disabled', false);     // 联系人
                $('#det-amount').attr('disabled', false);        // 患者人数

                // 呼叫原因和症状
                $('#det-major').attr('disabled', false);         // 呼叫原因
                $('#select1').attr('disabled', false);           // 症状选择1
                $('#select2').attr('disabled', false);           // 症状选择2

                // 患者信息
                $('#det-name').attr('disabled', false);          // 患者姓名
                $('#det-gender').attr('disabled', false);        // 性别
                $('#det-age').attr('disabled', false);           // 年龄
                $('#det-country').attr('disabled', false);       // 国籍
                $('#createForeign').attr('disabled', false);     // 外籍人员
                $('#det-race').attr('disabled', false);          // 民族
                $('#need-stretcher').attr('disabled', false);    // 需要担架

                // 备注信息
                $('#det-special-req').attr('disabled', false);   // 特殊要求
                $('#det-120remark').attr('disabled', false);     // 120备注
                $('#det-alds-summary').attr('disabled', false);  // ALDS汇总
                $('#patient-info').attr('disabled', false);      // 上次来电

                // 其他信息
                $('#push-outer-type').attr('disabled', false);   // 分中心（原联动类型修改）
                $('#is-test').attr('disabled', false);           // 测试

                if($('#evd-call-type').val() === '11') {
                    if($('#det-region-linkage-id').val() === ''){
                        //接收中显示出来
                        $('.field-push-outer-type').show();
                        // 调整同行的测试的左边距
                        $('.field-is-test').css('margin-left', '0em');
                    }else{
                        //接收中隐藏掉,因为是区域联动的受理，所以不需要选择接收中心
                        $('.field-push-outer-type').hide();
                        // 调整同行的测试的左边距
                        $('.field-is-test').css('margin-left', '0');
                    }
                } else {
                    //接收中隐藏掉
                    $('.field-push-outer-type').hide();
                    // 调整同行的测试的左边距
                    $('.field-is-test').css('margin-left', '0');
                }
            }
        });
        /**
         * 从区域联动界面打开
         * @param {string} regionLinkageId 区域联动ID
         */
        function createNewEventByRegionLinkage(regionLinkageId,externalBizId) {
            createNewEvent();
            
            // 通过ID获取区域联动详细信息
            getEventRegionLinkageDetailById(regionLinkageId, function(data) {
                if (data) {
                    // 填充联动信息
                    if (data.callIn) {
                        $('#det-callin').val(data.callIn); //主叫号码
                    }
                    if (data.contact) {
                        $('#det-contact').val(data.contact); //联系电话
                    }
                    if (data.contacter) {
                        $('#det-contacter').val(data.contacter); //联系人
                    }
                    if (data.callUser) {
                        $('#det-contacter').val(data.callUser); //报警人
                    }
                    if (data.callReason) {
                        $('#det-major').val(data.callReason); //呼叫原因
                    }
                    
                    // 填充地址信息
                    if (data.address) {
                        $('#det-address').val(data.address); //现场地址
                    }
                    if (data.lng && data.lat) {
                        $('#event-lng').val(data.lng); //经度
                        $('#event-lat').val(data.lat); //纬度
                        // 如果有经纬度，设置地图定位
                        try {
                            window.parent.echoMapSelected(data.address, data.lng, data.lat);
                        } catch (e) {
                            console.warn('地图定位失败:', e);
                        }
                    }
                    
                    // 填充患者信息
                    if (data.patientName) {
                        $('#det-name').val(data.patientName); //患者姓名
                    }
                    if (data.patientAge) {
                        $('#det-age').val(data.patientAge); //患者年龄
                    }
                    if (data.patientGender) {
                        // 患者性别:0-男 1-女 2-未知
                        $('#det-gender').val(data.patientGender);
                    }
                    if (data.patientCountry) {
                        // 患者国籍
                        $('#det-country').val(data.patientCountry);
                    }
                    if (data.patientRace) {
                        // 患者民族
                        $('#det-race').val(data.patientRace);
                    }
                    
                    // 填充备注信息
                    if (data.remark) {
                        $('#det-120remark').val(data.remark); //备注
                    }
                    
                    if(data.callInTimes){
                        // 填充来电时间
                        $('#det-call-in-times').datetimebox('setValue', data.callInTimes);
                    }
                    
                    // 标记为区域联动事件，添加自定义字段存储区域联动ID
                    $('#det-region-linkage-id').val(regionLinkageId);
                    $('#evd-call-type').val('11');//呼叫类型设置成联动派车
                    $('#evd-call-type').attr('disabled', true);//呼叫类型不容许修改
                    
                    // 如果有联动类型，设置相应的联动类型到事件来源下拉框
                    if (data.ldType) {
                        switch(data.ldType) {
                            case '1': // 120联动
                                $('#evd-source').val('4'); // 映射到"联网联动"
                                break;
                            case '2': // 110联动
                                $('#evd-source').val('6'); // 映射到"110联动"
                                break;
                            case '3': // 112联动
                                $('#evd-source').val('7'); // 映射到"122联动"
                                break;
                            case '4': // 119联动
                                $('#evd-source').val('5'); // 映射到"119联动"
                                break;
                        }
                        $('#evd-source').attr('disabled', true);//事件来源不容许修改
                    }
                }
            }, function(e, url, errMsg) {
                $.messager.alert('提示', '获取区域联动信息失败：' + errMsg, 'error');
            });
        }
        
        /**
         * 从电话列表中点开
         * @param phone
         * @param callUid
         * @param callInTimes
         */
        function createNewEventByPhone(phone, callUid, callInTimes) {
            createNewEvent();
            $('#det-callin').val(phone); //主叫号码
            $('#det-contact').val(phone); //联系电话
            $('#call-r-id').val(callUid); //此次电话记录的ID，call-r-id
            setTimeout(function() {
                if (callInTimes && callInTimes !== "") {
                    $('#det-call-in-times').val(); //此次电话记录的ID，call-r-id
                    try {
                        $('#det-call-in-times').datetimebox('setValue', callInTimes);
                    } catch (e) {
                        console.warn('Datetimebox 设置通话时间失败:', e);
                        $('#det-call-in-times').datetimebox({});
                        $('#det-call-in-times').datetimebox('setValue', callInTimes);
                    }
                } else {
                    try {
                        $('#det-call-in-times').datetimebox('setValue', new Date().formatString("yyyy-MM-dd hh:mm:ss"));
                    } catch (e) {
                        console.warn('Datetimebox 设置当前时间失败:', e);
                        $('#det-call-in-times').datetimebox({});
                        $('#det-call-in-times').datetimebox('setValue', new Date().formatString("yyyy-MM-dd hh:mm:ss"));
                    }
                }
            }, 100);
            //检测当前号码和系统配置是否是同一个，是则提示未检测到来电号码，请询问来电人 并且清空主叫号码和联系电话
            if (phone) {
                querySysConf("site_out_lines_nums", res => {
                    res = res || {}
                    if (res.val) {
                        let val = JSON.parse(res.val) || []
                        if (Array.isArray(val) && val.indexOf(phone) !== -1) {
                            $('#det-callin').val('');
                            $('#det-contact').val('');
                            $.messager.alert('提示', '未检测到来电号码，请询问来电人', 'error');
                        }
                    }
                },
                    (e, url, errMsg) => {
                        $.messager.alert('提示', '获取配置异常：' + errMsg, 'error');
                    })
            }
            //从电话列表中点开，那么电话号码不为空
        }

        /**
         * 从视频列表中点开或者打开页面时候加载
         * @param phone
         * @param callUid
         * @param callInTimes
         */
         function createNewEventByVideo(phone, callUid, callInTimes, address, lng, lat, nickName, avatarUrl, gender) {
    
            createNewEvent();
            $('#det-callin').val(phone); //主叫号码
            $('#det-contact').val(phone); //联系电话
            $('#det-name').val(nickName);
            $('#det-contacter').val(nickName);

            //回填地址信息
            if(lng && lat && address) {
                $('#event-lng').val(lng);
                $('#event-lat').val(lat);
                $('#det-address').val(address) & confirmAddress(address, lng, lat) & autoMatchRegionByAddress(address)
            }

            //回填性别信息
            if(gender == 0) {
                $('#det-gender').val('2');
            } else if(gender == 1) {
                $('#det-gender').val('0');  
            } else if(gender == 2) {
                $('#det-gender').val('1');
            }
           
            //$('#call-r-id').val(callUid); //此次电话记录的ID，call-r-id
            setTimeout(function() {
                if (callInTimes && callInTimes !== "") {
                    $('#det-call-in-times').val(); //此次电话记录的ID，call-r-id
                    try {
                        $('#det-call-in-times').datetimebox('setValue', callInTimes);
                    } catch (e) {
                        console.warn('Datetimebox 设置视频通话时间失败:', e);
                        $('#det-call-in-times').datetimebox({});
                        $('#det-call-in-times').datetimebox('setValue', callInTimes);
                    }
                } else {
                    try {
                        $('#det-call-in-times').datetimebox('setValue', new Date().formatString("yyyy-MM-dd hh:mm:ss"));
                    } catch (e) {
                        console.warn('Datetimebox 设置当前时间失败:', e);
                        $('#det-call-in-times').datetimebox({});
                        $('#det-call-in-times').datetimebox('setValue', new Date().formatString("yyyy-MM-dd hh:mm:ss"));
                    }
                }
            }, 100);

        }
        /**
         * 设置分站显示方法
         * @param data
         */
        function renderSubstation(data) {
            //设置分站显示区域
            $(".region-letters").remove();
            if (data.length > 0) {
                $('#dis-region').append('<li id="region_all" class="selected region-letters">全部</li>');
            }
            for (var i = 0; i < data.length; i++) {
                var d = data[i]; //区域数据
                $('#dis-region').append('<li id="{0}" class="region-letters">{1}</li>'.formatStr(d.id, d.stationName));
            }
            //单击分站显示
            $('#dis-region li').click(function (e) {
                edcui_selectLi(e); // 只是 做样式选中效果而已
                var id = $(e.target).attr('id');
                var carList;
                if (id == 'region_all') {
                    carList = window.parent.getMobilesWithDistance().where(function (d) {
                        return ((d.status == "0" || d.status == "3") && d.hCondition == "0");
                    });
                } else {
                    carList = window.parent.getMobilesWithDistance().where(function (d) {
                        return (d.stationId == id && (d.status == "0" || d.status == "3") && d.hCondition == "0");
                    });
                }
                // 使用新的筛选函数，保持车辆类型筛选状态
                applyVehicleTypeFilter(carList);
            });
        }
        /**
         * 设置分站区域显示方法
         * @param data
         */
        function renderStationRegion(data) {
            //设置分站显示区域
            $(".station-letters").remove();
            if (data.length > 0) {
                $('#station-region').append('<li id="" class="selected station-letters">全部</li>');
            }
            for (var i = 0; i < data.length; i++) {
                var d = data[i]; //区域数据
                $('#station-region').append('<li id="{0}" class="station-letters">{1}</li>'.formatStr(d.id, d.regionName));
            }
            //单击分站显示
            $('#station-region li').click(function (e) {
                edcui_selectLi(e); // 只是 做样式选中效果而已
                // 点击时，筛选医院再筛选医院下的车辆
                var idSta = $(e.target).attr('id'); // 点击分站的id
                // 如果之前医院查询输入框有数据要清除掉
                $('#search-title').textbox('setValue', '');
                if (!idSta) {
                    _stationRegionId = '' //点击全部分站区域id清空
                    renderSubstation(_substationList) // 调用渲染事件渲染全部分站
                    // 车辆正常全部渲染
                    var carList = window.parent.getMobilesWithDistance().where(function (d) {
                        return ((d.status == "0" || d.status == "3") && d.hCondition == "0");
                    });
                    // 使用新的筛选函数，保持车辆类型筛选状态
                    applyVehicleTypeFilter(carList);
                } else {
                    // 当点击分站区域时 点击的id设置为全局
                    _stationRegionId = idSta
                    // 过滤所属的分站 再渲染
                    let filterStation = _substationList.filter(item => {
                        return item.stationRegionId == idSta
                    })
                    renderSubstation(filterStation) // 调用渲染事件
                    // 车辆也需要根据筛选的分站再重新渲染
                    let totalCar = []
                    filterStation.forEach(item => {
                        var carList = window.parent.getMobilesWithDistance().where(function (d) {
                            return (d.stationId == item.id && (d.status == "0" || d.status == "3") && d.hCondition == "0");
                        });
                        totalCar.push(...carList)
                    })
                    // 使用新的筛选函数，保持车辆类型筛选状态
                    applyVehicleTypeFilter(totalCar);
                }
            });
        }
        /** 创建新事件，打开创建事件的页面 */
        function createNewEvent() {
           
            //呼叫时间为打开的时间
            var callInTimes = new Date().formatString("yyyy-MM-dd hh:mm:ss")
            
            // 确保datetimebox组件已初始化后再设置值
            setTimeout(function() {
                try {
                    $('#det-call-in-times').datetimebox('setValue', callInTimes);
                } catch (e) {
                    console.warn('Datetimebox 初始化设置失败，尝试重新初始化:', e);
                    $('#det-call-in-times').datetimebox({});
                    $('#det-call-in-times').datetimebox('setValue', callInTimes);
                }
            }, 100);
            //$('#evd-call-type').removeAttr('disabled'); //按钮变成可用
            if (_currentInCallDetails != null) {
                $('#det-callin').val(_currentInCallDetails.phone); //主叫号码
                $('#det-contact').val(_currentInCallDetails.phone); //联系电话
                if (_currentInCallDetails.callUid && _currentInCallDetails.callUid !== "") {
                    $('#call-r-id').val(_currentInCallDetails.callUid); //此次电话记录的ID，call-r-id
                }
                if (_currentInCallDetails.callInTimes && _currentInCallDetails.callInTimes !== "") {
                    $('#det-call-in-times').val(); //此次电话记录的ID，call-r-id
                    setTimeout(function() {
                        try {
                            $('#det-call-in-times').datetimebox('setValue', _currentInCallDetails.callInTimes);
                        } catch (e) {
                            console.warn('Datetimebox 设置通话时间失败:', e);
                            $('#det-call-in-times').datetimebox({});
                            $('#det-call-in-times').datetimebox('setValue', _currentInCallDetails.callInTimes);
                        }
                    }, 100);
                }
            }

            if (_currentInCallDetails.phone) {
                //填充人员信息
                fillPersonInfoByPhoneNumber(_currentInCallDetails.phone)
            }


            //$('#eventDetailRegion').show();
            //副屏地图状态变成派车中的页面
            try {
                window.parent.gProxy.sendToNewEventStatus("1"); //派车状态
            } catch (e) {
                window.parent.bsProxy.sendToNewEventStatus("1"); //派车状态
            }
            
            //获取系统配置参数    
            curAssignTaskMode = window.parent.assignTaskMode;
            if (curAssignTaskMode == "2") {
                $('#vehicleList').hide();
                $('#substationsList').show();
                // 查询出所有分站区域
                getAllStationRegionList(
                    function (data) {
                        _stationregionlist = data || []
                        // 遍历节点
                        //设置分站显示区域
                        renderStations(_stationregionlist)
                        $('#search_regionName').textbox('textbox').bind('input', function (e) {
                            let filterlist = json.parse(json.stringify(_stationregionlist)).filter(r => r.regionname.indexof(e.target.value) !== -1)
                            if (!filterlist || !filterlist[0]) {
                                //字母过滤
                                filterlist = json.parse(json.stringify(_stationregionlist)).filter(r => upperfirst(r.regionname, { upperfirst: true }).indexof(e.target.value) !== -1)
                            }
                            renderStations(filterlist)
                        })
                    },
                    function (e, url, errmsg) {
                        //失败才做处理
                        $.messager.alert('异常', '查询分站区域信息错误，异常信息：' + errmsg);
                    }
                );
                loadStationList();
            } else {
                $('#substationsList').hide();
                $('#vehicleList').show();
                // 查询出所有分站区域
                getAllStationRegionList(
                    function (data) {
                        _stationRegionList = data || []
                        // 遍历节点
                        //设置分站显示区域
                        renderStationRegion(_stationRegionList)
                        $('#search-regionName').textbox('textbox').bind('input', function (e) {
                            let filterlist = JSON.parse(JSON.stringify(_stationRegionList)).filter(r => r.regionName.indexOf(e.target.value) !== -1)
                            if (!filterlist || !filterlist[0]) {
                                //字母过滤
                                filterlist = JSON.parse(JSON.stringify(_stationRegionList)).filter(r => upperfirst(r.regionName, { upperfirst: true }).indexOf(e.target.value) !== -1)
                            }
                            renderStationRegion(filterlist)
                        })
                    },
                    function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '查询分站区域信息错误，异常信息：' + errMsg);
                    }
                );
                //查询分站显示分站车辆
                getAllStationList(
                    {},
                    function (data) {
                        _substationList = data || []
                        //设置分站显示区域
                        renderSubstation(_substationList)
                        // $('#dis-region').empty();
                        // $('#dis-region').append('<li id="region_all" class="selected">全部<input id="search-title" class="easyui-textbox" style="width:110px"></li>');
                        // for (var i = 0; i < data.length; i++) {
                        //     var d = data[i]; //区域数据
                        //     $('#dis-region').append('<li id="{0}">{1}</li>'.formatStr(d.id, d.stationName));
                        // }
                        $('#search-title').textbox('textbox').bind('input', function (e) {
                            let filterlist = JSON.parse(JSON.stringify(_substationList)).filter(r => r.stationName.indexOf(e.target.value) !== -1)
                            if (!filterlist || !filterlist[0]) {
                                //字母过滤
                                filterlist = JSON.parse(JSON.stringify(_substationList)).filter(r => upperfirst(r.stationName, { upperfirst: true }).indexOf(e.target.value) !== -1)
                            }
                            // 再过滤一次，判断分站是否在点击分站区域内
                            if (_stationRegionId != '') {
                                let filterStations = filterlist.filter(item => {
                                    return item.stationRegion == _stationRegionId
                                })
                                renderSubstation(filterStations)
                            } else {
                                renderSubstation(filterlist)
                            }

                        })
                    },
                    function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '查询分站信息错误，异常信息：' + errMsg);
                    });

                //显示所有待命的车辆信息车辆信息
                //获取本站车辆信息
                var params = {
                    statusList: ['1']
                }; //查询所有待命的车辆
                showProcess(true, '温馨提示', '正在加载数据，请稍后...');
                getMobilesStatusListWithDistance(params, function (data) {
                    //需要把所有待命(取消接诊)的且没有维修和驻场中的车辆给显示出来
                    // var carList = window.parent.getMobilesWithDistance().where(function (d) {
                    //     return ((d.status == "0" || d.status == "3") && d.hCondition == "0");
                    // });
                    var carList = data.where(function (d) {
                        return ((d.status == "0" || d.status == "3") && d.hCondition == "0");
                    });
                    //使用新的筛选函数，保持车辆类型筛选状态
                    applyVehicleTypeFilter(carList);
                    showProcess(false);
                }, function (e, url, errMsg) {
                    //失败才做处理
                    showProcess(false);
                    $.messager.alert('异常', '异常信息：' + errMsg);
                });
            }
        }
        // 刷新分站列表
        $('#refresh-button').on('click', function () {
            loadStationList(_selectedId); // 调用接口更新表格
        });
        //勾选是否有空闲车辆分站
        $('#chkCarStandBy').on('change', function () {
            loadStationList()
        });
        //勾选分站在线
        $('#chkStationIsLogin').on('change', function () {
            loadStationList()
        });
        //需求变更-->座席端下方列表（分站）
        function loadStationList(stationRegionId, lat, lng) {
            var params = {
                eventLat: lat,
                eventLng: lng,
                id: '',
                stationName: '',
                stationRegionId: stationRegionId ? stationRegionId : '',
            }
            
            if (!stationRegionId) {
                params.stationRegionId = "";
            }

            //是否勾选有空闲车辆分站
            var isChecked = $('#chkCarStandBy').prop('checked');
            if (isChecked) {
                params.standBy = '1'
            } else {
                params.standBy = ''
            }

            //勾选分站在线
            var isCheckedStationIsLogin = $('#chkStationIsLogin').prop('checked');
            if (isCheckedStationIsLogin) {
                params.isLogin = '1'
            } else {
                params.isLogin = ''
            }

            getStationInfo(params,
                function (res) {
                    if (res) {
                        $('#station-list').datagrid('loadData', res); // 使用 loadData 方法加载数据
                    } else {
                        $.messager.alert("分站列表获取数据失败");
                    }

                }, function (e, url, errMsg) {
                    // showProcess(false);
                    // //失败才做处理
                    $.messager.alert('提示', '分站列表获取数据失败，异常信息：' + errMsg);
                })
        }
        function stationIsLoginIconFormatter(value, row, index) {
            switch (row.isLogin) {
                case "0":
                    return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
                case "1":
                    return '<img src="./style/img/yiyuan_zaixian_ic.png"/>';
                default:
                    return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
            }
        }
        function stationIsLoginFormatter(value, row, index) {
            var str = "未知";
            switch (value) {
                case "0":
                    str = "离线";
                    break;
                case "1":
                    str = "在线";
                    break;
                default:
                    str = "未知";
                    break;
            }
            return str;
        }
        function vehicleStatusFormatter(value, row, index) {
            // 拼接多个字段的值，并格式化显示
            var statusCount = `总数: ${row.countByNum}, `
                + `未值班: ${row.notOnDutyByNum}, `
                + `暂停: ${row.pauseByNum}, `
                + `任务: ${row.taskByNum}`;
            return statusCount;
        }
        /**
         * 分站电话
         * @param value
         * @param row
         * @param index
         */
        function stationContactFormatter(value, row, index) {
            if (!value) {
                return '';
            }
            if (/\d/.test(value)) {
                return formatPhoneNumber(value);
            }
            return value;
        }
        /**
         * 分站备注
         * @param value
         * @param row
         * @param index
         */
         function stationRemarkFormatter(value, row, index) {
             if (!value) {
                 return '';
             }
             if (/\d/.test(value)) {
                 return formatPhoneNumber(value);
             }
             return value;
        }
        // 格式化文本中的电话
        function formatPhoneNumber(text) {
            // 定义正则表达式来匹配固定电话号码的三种格式
            // (xxxx)xxxxxxxx 或 xxxx-xxxxxxxx 或 xxxxxxxxxxx
            const phoneRegex = /\((\d{4})\)(\d{7,8})|(\d{4})-(\d{7,8})|(\d{11})/g;
        
            // 使用回调函数替换匹配到的电话号码，并返回处理后的HTML字符串
            const formatText = text.replace(phoneRegex, (match) => {
                const phoneNumberOrigin = match;
                const phoneNumber = match.trim().replace(/[\(\)-]/g, ''); // 去除括号和连字符
                return `<span style="color: #0AA0FB;cursor: pointer;" onclick="callingPhone2('${phoneNumberOrigin}','${phoneNumber}')" title="拨打">${match}</span>`;
            });
            return formatText; // 返回处理后的HTML字符串，供后续插入到DOM中
        }
        
        //分站列表操作
        function stationOperationFormatter(value, row, index) {
            let node = '';
            if (row) {
                let stationId = row.id.toString()
                node += `
                    <a href="javascript:void(0);"
                       onclick="sendTaskBtn('${ stationId}');" 
                       style="color: #39c; width: 90px; font-size: 12px; margin-right: 10px; top: 0; padding: 3px 8px;">
                       派发调遣
                    </a>
                `;
            }
            return node;
        }
        /** 派车/增派救护车操作（派任务给分站，不指定车和人） */
        function sendTaskBtn(stationId) {
            $.messager.confirm("提示", "是否进行派车操作？", function (r) {
                if (r) {
                    //如果没有事件id得先创建事件
                    if ($("#event-id").val() == "") {
                        //调用创建事件方法
                        addEvent(
                            //返回的res.id即为事件id
                            function (res) {
                                $("#event-id").val(res.id)
                                eventId = res.id
                                showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');
                                //派车
                                var params = {
                                    "id": '',
                                    "eventId": eventId,
                                    "toStatus": '5',
                                    "dispatchDoctorId": '',
                                    "dispatchDoctor": '',
                                    "dispatchDoctorPhone": '',
                                    "dispatchNurseId": '',
                                    "dispatchNurse": '',
                                    "dispatchNursePhone": '',
                                    "dispatchDriverId": '',
                                    "dispatchDriver": '',
                                    "dispatchDriverPhone": '',
                                    "dispatchWorkerId": '',
                                    "dispatchWorker": '',
                                    "dispatchWorkerPhone": '',
                                    "stationId": stationId,
                                    "seatId": _pSeat.id, //座席id
                                    "seatCode": _pSeat.seatId, //座席code
                                    "seatUserId": _pSeat.userId, //当前座席用户id
                                    "seatUserName": _pSeat.userName, //当前座席用户工号
                                    "seatUser": _pSeat.user //当前座席用户姓名
                                };
                                addMobileProcess(params,
                                    function (res) {
                                        //成功
                                        showProcess(false);
                                        window.parent.refreshEventlist();
                                        window.parent.refreshMobilesOrStations();
                                        $.messager.alert('提示', '派车(增派)车辆成功！', 'info');
                                    },
                                    function (e, url, errMsg) {
                                        showProcess(false);
                                        $.messager.alert('提示', errMsg, 'error');
                                    });
                            }, function (errMsg) {
                                //失败才做处理
                                showProcess(false);
                                $.messager.alert('创建事件错误', errMsg);
                            });
                    } else {
                        eventId = $("#event-id").val()
                        showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');
                        //派车
                        var params = {
                            "id": '',
                            "eventId": eventId,
                            "toStatus": '5',
                            "dispatchDoctorId": '',
                            "dispatchDoctor": '',
                            "dispatchDoctorPhone": '',
                            "dispatchNurseId": '',
                            "dispatchNurse": '',
                            "dispatchNursePhone": '',
                            "dispatchDriverId": '',
                            "dispatchDriver": '',
                            "dispatchDriverPhone": '',
                            "dispatchWorkerId": '',
                            "dispatchWorker": '',
                            "dispatchWorkerPhone": '',
                            "stationId": stationId,
                            "seatId": _pSeat.id, //座席id
                            "seatCode": _pSeat.seatId, //座席code
                            "seatUserId": _pSeat.userId, //当前座席用户id
                            "seatUserName": _pSeat.userName, //当前座席用户工号
                            "seatUser": _pSeat.user //当前座席用户姓名
                        };
                        addMobileProcess(params,
                            function (res) {
                                //成功
                                showProcess(false);
                                window.parent.refreshEventlist();
                                window.parent.refreshMobilesOrStations();
                                $.messager.alert('提示', '派车(增派)车辆成功！', 'info');
                            },
                            function (e, url, errMsg) {
                                showProcess(false);
                                $.messager.alert('提示', errMsg, 'error');
                            });

                    }
                   
                }
            });
        }
        /**
         * 设置分站区域显示方法
         * @param data
         */
        function renderStations(data) {
            //设置分站显示区域
            $(".station-letters").remove();
            if (data.length > 0) {
                $('#station_region').append('<li id="" class="selected station-letters">全部</li>');
            }
            for (var i = 0; i < data.length; i++) {
                var d = data[i]; //区域数据
                $('#station_region').append('<li id="{0}" class="station-letters">{1}</li>'.formatStr(d.id, d.regionName));
            }
            // 给每个li添加点击事件
            $('.station-letters').on('click', function (e) {
                edcui_selectLi(e);
                var regionId = $(this).attr('id'); // 获取当前li的id
                _selectedId = $(this).attr('id');
                loadStationList(regionId,$('#event-lat').val(),$('#event-lng').val()); // 传递id到接口并更新表格
            });
        }

        /** 查询电话簿里的信息 */
        function fillPersonInfoByPhoneNumber(phoneNumber) {
            if (!phoneNumber) {
                return;
            }
            personInfoByNumber({ number: phoneNumber }, function (res) {
                if (res) {
                    let oldContactor = $('#det-contacter').val();
                    if (!oldContactor) {
                        $('#det-contacter').val(res.contactor)
                    }

                    let oldRemark = $('#det-120remark').val();
                    if (!oldRemark) {
                        //institution--单位   job--职务
                        let remark120 = ''
                        if (res.institution && res.job) {
                            remark120 = '姓名：' + res.contactor + '，单位：' + res.institution + '，职务：' + res.job
                        }
                        if (res.institution && !res.job) {
                            remark120 = '姓名：' + res.contactor + '，单位：' + res.institution
                        }
                        if (!res.institution && res.job) {
                            remark120 = '姓名：' + res.contactor + '，职务：' + res.job
                        }
                        $('#det-120remark').val(remark120)
                    }
                }
            }, function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('提示', '查询数据失败，异常信息：' + errMsg);
            })
        }

        function sortAndShowCars(carList) {
            if (carList == null) {
                return;
            }
            /*var temp = carList.slice();
            for (var i = 0; i < temp.length; i++) {
                var data = temp[i];
                data.distance = MAX_DISTANCE;
                if (_currentLocCreateEvent != null) {
                    //算距离
                    caculateRouteDistance(data,
                        function (dist, staData) {
                            staData.distance = dist;
                        });
                }
            }*/
            reRangeAndShowCars(carList);
        }

        function reRangeAndShowCars(carList) {

            var disCar = $('#dis-cars-body');
            disCar.empty();
            /*carList.sort(function (left, right) {
                //按照距离排序算法
                return left.distance - right.distance;
            });*/
            if (carList.length <= 0) {
                disCar.append('<tr><td colspan="8">当前无可用车辆</td></tr>');
            } else {
                for (var j = 0; j < carList.length; j++) {
                    var data = carList[j];
                    var offline = data.lng == 0 && data.lat == 0;
                    disCar.append('<tr data="{0}" stationContact="{8}" carContact="{9}">\
                            <td style="width: 22%;">{1}</td>\
                            <td style="width: 9%;">{2}</td>\
                            <td style="width: 7%;">{3}</td>\
                            <td style="width: 8%;">{4}</td>\
                            <td style="width: 9%;">{5}</td>\
                            <td style="width: 8%;">{6}</td>\
                            <td style="width: 6%;">{7}</td>\
                            </tr>'.formatStr(
                        data.id,
                        data.stationName,
                        data.plateNum,
                        data.carName,
                        getCarTypeDisplayName(data),
                        //data.distance < MAX_DISTANCE ? (offline ? '<span style="color:#888888; font-size:0.8em;">离线</span>' : (data.distance / 1000).toFixed(1) + "km") : "",
                        data.mobileToEventMapDistance ? data.mobileToEventMapDistance.toFixed(1) + "km" : "",
                        data.hCondition == '0' ? getTextByMobileStatus(data.status) : parent.getConditionFromCode(data.hCondition),
                        data.hCondition != '0' && data.status == '0' ? '' : getHandleButtonByMobileStatus(data.status, "", data.id, data.isDispatch, data.stationCode),
                        data.stationContact,
                        data.contact)
                    );
                }

                //绑定事件
                $('#dis-cars-body tr').mouseup(function (e) {
                    edcui_selectLi(e);// 只是 做样式选中效果而已
                    if (3 == e.which) {
                        _menuPhone.sta = $(e.target).parent().attr('stationContact');
                        _menuPhone.car = $(e.target).parent().attr('carContact');
                        $('#menu-station-fast').menu('show', {
                            left: e.clientX,
                            top: e.clientY
                        });
                    }
                });
            }
        }

        function getTextByMobileStatus(mobileStatus) {
            if (mobileStatus == "1") {
                return '<span style="color:red;">发车</span>';
            } else if (mobileStatus == "2") {
                return '<span style="color:red;">抵达</span>';
            } else if (mobileStatus == "3") {
                return '<span style="color:green;">途中待命</span>';
            } else if (mobileStatus == "8") {
                return '<span style="color:red;">离开现场</span>';
            } else if (mobileStatus == "4") {
                return '<span style="color:red;">回院</span>';
            } else if (mobileStatus == "5") {
                return '<span style="color:red;">调度中</span>';
            } else if (mobileStatus == "6") {
                return '<span style="color:red;">已派车</span>';
            }

            return '<span style="color:green;">站内待命</span>';
        }

        /**
         * 
         * @param mobileStatus 状态
         * @param mobileProcessId 出车车辆任务ID
         * @param carId 出车车辆ID
         * @param isDispatch 是否分站派车 0否 1是
         * @param stationCode 站点编码
         */
        function getHandleButtonByMobileStatus(mobileStatus, mobileProcessId, carId, isDispatch, stationCode) {
            if (mobileStatus == "0" || mobileStatus == "3") {
                return '<a href="javascript:modifyMobileStatus(\'{0}\',\'{1}\',\'{2}\',\'{3}\',\'5\');">派出此车</a> '.formatStr(mobileProcessId, carId, isDispatch, stationCode);
            } else {
                return '';
            }
        }

        var map;
        var ac;
        var pickupAc;
        var destinationAc;
        /** 高德地图自动完成 */
        function setAutoComplete() {
            //高德地图
            map = new AMap.Map("the-map"); // 创建Map实例
            //map.centerAndZoom(new AMap.Point(114.42586, 23.093224), 13);  // 初始化地图,设置中心点坐标和地图级别
            querySysConf('map_city',
                function (data) {
                    map.setCity(data); // 设置地图显示的城市 此项是必须设置的
                    map.setZoom(data, 11); // 初始化,设置中心点坐标和地图级别
                    AMap.plugin('AMap.AutoComplete', function () {
                        ac = new AMap.AutoComplete( //建立一个自动完成的对象
                            {
                                "input": "det-address",
                                "location": map,
                                "city": data, // 限制城市
                            });
                        ac.on("complete", function (e) { //鼠标点击下拉列表后的事件
                            console.log(e)
                        });
                        var myValue;//地址名称
                        var location;//位置坐标
                        // ac.on("mouseover", function (e) { //鼠标放在下拉列表上的事件
                        //     var str = "";
                        //     var _value = e.fromitem.value;
                        //     var value = "";
                        //     if (e.fromitem.index > -1) {
                        //         value = _value.province + _value.city + _value.district + _value.street + _value
                        //             .business;
                        //     }
                        //     str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

                        //     value = "";
                        //     if (e.toitem.index > -1) {
                        //         _value = e.toitem.value;
                        //         value = _value.province + _value.city + _value.district + _value.street + _value
                        //             .business;
                        //     }
                        //     str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
                        //     G("searchResultPanel").innerHTML = str;
                        // });
                        ac.on("select", function (e) { //鼠标点击下拉列表后的事件
                            var _value = e.poi;
                            myValue = _value.name;
                            location = _value.location;
                            // G("searchResultPanel").innerHTML = "onconfirm<br />index = " + e.item.index +
                            //     "<br />myValue = " + myValue;
                            $('#det-address').val(myValue);
                            // 地址设置后自动匹配区域
                            autoMatchRegionByAddress(myValue);
                            confirmAddress(myValue, location.lng, location.lat);
                        });
                    });

                    // 为等车地址添加自动完成功能
                    AMap.plugin('AMap.AutoComplete', function () {
                        pickupAc = new AMap.AutoComplete( //建立一个自动完成的对象
                            {
                                "input": "pickup-address",
                                "location": map,
                                "city": data, // 限制城市
                            });
                        pickupAc.on("complete", function (e) { //鼠标点击下拉列表后的事件
                            console.log(e)
                        });
                        var pickupValue;//地址名称
                        var pickupLocation;//位置坐标
                        
                        pickupAc.on("select", function (e) { //鼠标点击下拉列表后的事件
                            var _value = e.poi;
                            pickupValue = _value.name;
                            pickupLocation = _value.location;
                            $('#pickup-address').val(pickupValue);
                            // 地址设置后自动匹配区域
                            autoMatchRegionByAddress(pickupValue);
                            confirmPickupAddress(pickupValue, pickupLocation.lng, pickupLocation.lat);
                        });
                    });

                    // 为送往地址添加自动完成功能
                    AMap.plugin('AMap.AutoComplete', function () {
                        destinationAc = new AMap.AutoComplete({
                            "input": "destination-address",
                            "location": map,
                            "city": data, // 限制城市
                        });
                        destinationAc.on("complete", function (e) {
                            console.log(e)
                        });
                        var destinationValue;//地址名称
                        var destinationLocation;//位置坐标
                        
                        destinationAc.on("select", function (e) { //鼠标点击下拉列表后的事件
                            var _value = e.poi;
                            destinationValue = _value.name;
                            destinationLocation = _value.location;
                            $('#destination-address').val(destinationValue);
                            // 地址设置后自动匹配区域
                            autoMatchRegionByAddress(destinationValue);
                            confirmDestinationAddress(destinationValue, destinationLocation.lng, destinationLocation.lat);
                        });
                    });
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
                });
        }

        /**
         * 地图选中后回显派车界面的地址一栏,地图屏幕回显
         * @param address
         * @param lng
         * @param lat
         */
        function echoMapSelected(address, lng, lat) {
            // 获取输入框元素
            var inputElement = document.getElementById('det-address');
            // 设置输入框的值
            inputElement.value = address;
            $('#event-lat').val(lat);
            $('#event-lng').val(lng);

            _currentLocCreateEvent = new AMap.LngLat(lng, lat);
            //车辆距离重新计算,看用户是否点击了区域
            if ($("#dis-region").find(".selected")) {
                var id = $("#dis-region").find(".selected").attr('id');
                var carList;
                if (id == 'region_all') {
                    carList = window.parent.getMobilesWithDistance().where(function (d) { return d.status == "0" || d.status == "3"; });
                } else {
                    carList = window.parent.getMobilesWithDistance().where(function (d) { return d.stationId == id && (d.status == "0" || d.status == "3"); });
                }

                sortAndShowCars(carList);
            } else {
                sortAndShowCars(window.parent.getMobilesWithDistance());
            }

        }

        /**
         * 副屏地图选中后回显等车地址（扩展功能）
         * 可以通过副屏地图选择等车地址
         * @param address
         * @param lng
         * @param lat
         */
        function echoPickupMapSelected(address, lng, lat) {
            // 获取输入框元素
            var inputElement = document.getElementById('pickup-address');
            // 设置输入框的值
            inputElement.value = address;
            $('#pickup-lat').val(lat);
            $('#pickup-lng').val(lng);
            
            // 自动匹配区域
            autoMatchRegionByAddress(address);
            
            console.log('副屏地图选中等车地址:', address, lng, lat);
        }

        // /**
        //  * 地图选中后回显等车地址
        //  * @param address
        //  * @param lng
        //  * @param lat
        //  */
        // function echoPickupMapSelected(address, lng, lat) {
        //     // 获取输入框元素
        //     var inputElement = document.getElementById('pickup-address');
        //     // 设置输入框的值
        //     inputElement.value = address;
        //     $('#pickup-lat').val(lat);
        //     $('#pickup-lng').val(lng);
        // }

        function confirmAddress(addr, lng, lat) {
            if (addr == '发送到地图') {
                addr = $('#det-address').val()
                if ($('#event-lng').val() && $('#event-lat').val()) {
                    lng = $('#event-lng').val()
                    lat = $('#event-lat').val()
                }
            }
            setPlace(addr, lng, lat);
        }

        /**
         * 同步现场地址到等车地址
         */
        function syncSceneToPickupAddress() {
            var sceneAddress = $('#det-address').val();
            var sceneLat = $('#event-lat').val();
            var sceneLng = $('#event-lng').val();
            
            if (!sceneAddress) {
                $.messager.alert('提示', '现场地址为空，无法同步！', 'warning');
                return;
            }
            
            $('#pickup-address').val(sceneAddress);
            $('#pickup-lat').val(sceneLat);
            $('#pickup-lng').val(sceneLng);
            
            // 自动匹配区域
            autoMatchRegionByAddress(sceneAddress);
            
            //$.messager.alert('提示', '现场地址已同步到等车地址！', 'info');
        }

        /**
         * 等车地址确认发送到地图
         * @param addr
         * @param lng
         * @param lat
         */
        function confirmPickupAddress(addr, lng, lat) {
            if (addr == '发送到地图') {
                addr = $('#pickup-address').val()
                if ($('#pickup-lng').val() && $('#pickup-lat').val()) {
                    lng = $('#pickup-lng').val()
                    lat = $('#pickup-lat').val()
                }
            }
            setPickupPlace(addr, lng, lat);
        }

        /**
         * 设置等车地址位置
         * @param value
         * @param lng
         * @param lat
         */
        function setPickupPlace(value, lng, lat) {
            handlePickupSearch(value, lng, lat)
        }

        /**
         * 等车地址中文搜索定位
         * @param value
         * @param lng
         * @param lat
         */
        function handlePickupSearch(value, lng, lat) {
            let currentLat;
            let currentLng;
            if (lng && lat) {
                currentLat = lat;
                currentLng = lng;
                try {
                    window.parent.gProxy.sendToMap(value, String(lng), String(lat));
                } catch (err) {
                    window.parent.bsProxy.sendToMap(value, String(lng), String(lat));
                }
            } else {
                if (value) {
                    AMap.plugin('AMap.PlaceSearch', function () {
                        placeSearch = new AMap.PlaceSearch(map);
                        placeSearch.search(value, function (status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                // 获取搜索结果的范围
                                var bounds = result.bounds;
                                // 将地图视图调整为搜索结果的范围
                                map.setBounds(bounds);
                                var firstPOI = result.poiList.pois[0];
                                //自动选中
                                if (firstPOI) {
                                    //将经纬度写入到等车地址字段
                                    $('#pickup-lat').val(firstPOI.location.lat);
                                    $('#pickup-lng').val(firstPOI.location.lng);
                                    currentLat = firstPOI.location.lat;
                                    currentLng = firstPOI.location.lng;
                                    try {
                                        window.parent.gProxy.sendToMap(value, String(firstPOI.location.lng), String(firstPOI.location.lat));
                                    } catch (err) {
                                        window.parent.bsProxy.sendToMap(value, String(firstPOI.location.lng), String(firstPOI.location.lat));
                                    }
                                }
                            } else {
                                // 查询失败或没有结果的处理逻辑
                                console.log('等车地址查询失败或没有结果');
                            }
                        });
                    })
                }
            }
        }

        function setPlace(value, lng, lat) {
            handleSearch(value, lng, lat)
        }

        /*function caculateRouteDistance(carData, callback) {
            var distance = MAX_DISTANCE;
            if (_currentLocCreateEvent != null) {
                distance = getDistance(carData.lat, carData.lng, _currentLocCreateEvent.lat, _currentLocCreateEvent.lng);
            }
            callback(distance, carData);
        }*/

        /**
         * 插入下拉框选项
         * @param id
         * @param typeCode
         */
        function insertIntoSelect(id, typeCode, dicts) {
            var ui = $('#' + id);
            var list = getDicList(typeCode, dicts);
            ui.empty();
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
            }
            //默认选中第一个
            if (list != null && list.length > 0) {
                ui.val(list[0].codeName);
            }
        }

        /**
         * 插入下拉框选项，首项为空
         * @param id
         * @param typeCode
         */
         function insertIntoSelect2(id, typeCode, dicts) {
            console.log("执行insertIntoSelect2函数", id, typeCode);
            var ui = $('#' + id);
            if (!ui.length) {
                console.error("找不到元素:", id);
                return;
            }
            
            console.log("元素可见性:", ui.is(":visible"), "父元素可见性:", ui.parent().is(":visible"));
            
            var list = getDicList(typeCode, dicts);
            console.log("获取到的字典数据:", list, "字典长度:", list ? list.length : 0);
            
            if (!list || list.length === 0) {
                console.warn("字典数据为空:", typeCode);
                return;
            }
            
            ui.empty();
            ui.append('<option value="{0}">{1}</option>'.formatStr("", ""));
            
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                console.log("添加选项:", d.codeName, d.codeVale);
                ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
            }
            
            console.log("添加完成, 当前选项数:", ui.find("option").length);
            
            // 检查特定的字段初始化情况
            if (id === "push-outer-type") {
                console.log("接收中心下拉框初始化完成, 选项数:", ui.find("option").length);
                console.log("接收中心下拉框HTML:", ui.parent().html());
                
                // 立即检查下拉框的显示状态
                setTimeout(function() {
                    console.log("接收中心下拉框延迟检查 - 可见性:", ui.is(":visible"), 
                                "CSS display:", ui.css("display"), 
                                "父元素可见性:", ui.parent().is(":visible"),
                                "父元素CSS display:", ui.parent().css("display"));
                }, 100);
            }
            
            //默认选中首项为空
            ui.val("");   
        }

        /**
         * 动态生成radio按钮组
         * @param name radio按钮的name属性
         * @param typeCode 字典类型编码
         * @param dicts 字典数据
         */
        function insertIntoRadio(name, typeCode, dicts) {
            var list = getDicList(typeCode, dicts);
            var container = $('#vehicle-type-container');
            
            // 如果是车辆类型字典，保存到全局变量
            if (typeCode === 'car_type') {
                _carTypeDictionary = list;
                console.log('保存车辆类型字典数据:', _carTypeDictionary);
            }
            
            // 清空现有的radio按钮
            container.empty();
            
            // 生成radio按钮HTML
            var radioHtml = '<span>车辆类型：</span>';
            
            // 添加"全部"选项并设为默认选中
            radioHtml += '<input type="radio" name="' + name + '" value="all" checked style="margin-right: 5px;"><span>全部</span>';
            
            // 根据字典数据生成radio按钮
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                radioHtml += '<input type="radio" name="' + name + '" value="' + d.codeName + '" style="margin-right: 5px;margin-left: 10px;"><span>' + d.codeVale + '</span>';
            }
            
            container.html(radioHtml);
            
            // 绑定车辆类型筛选事件
            $('input[name="' + name + '"]').on('change', function() {
                var selectedType = $(this).val();
                console.log('选择的车辆类型:', selectedType);
                console.log('当前_currentAwaitMobile:', _currentAwaitMobile);
                
                // 保存当前选择的车辆类型
                _currentSelectedVehicleType = selectedType;
                
                // 重新筛选和显示车辆列表
                if (_currentAwaitMobile && _currentAwaitMobile.length > 0) {
                    console.log('使用_currentAwaitMobile进行筛选，车辆数量:', _currentAwaitMobile.length);
                    filterAndShowCarsByType(_currentAwaitMobile, selectedType);
                } else {
                    console.log('_currentAwaitMobile为空，尝试获取所有可用车辆');
                    // 如果_currentAwaitMobile为空，尝试重新获取车辆列表
                    var allCars = window.parent.getMobilesWithDistance().where(function (d) {
                        return ((d.status == "0" || d.status == "3") && d.hCondition == "0");
                    });
                    console.log('获取到的所有可用车辆数量:', allCars.length);
                    if (allCars && allCars.length > 0) {
                        _currentAwaitMobile = allCars;
                        filterAndShowCarsByType(allCars, selectedType);
                    } else {
                        console.log('没有可用的车辆');
                    }
                }
            });
        }

        /**
         * 根据车辆类型筛选并显示车辆
         * @param carList 车辆列表
         * @param vehicleType 车辆类型编码
         */
        function filterAndShowCarsByType(carList, vehicleType) {
            console.log('开始筛选车辆，传入参数:');
            console.log('carList:', carList);
            console.log('vehicleType:', vehicleType);
            console.log('_currentAwaitMobile:', _currentAwaitMobile);
            
            var filteredCars = carList;
            
            if (vehicleType !== 'all') {
                // 根据车辆的carType字段进行筛选
                filteredCars = carList.filter(function(car) {
                    var carType = getCarVehicleType(car);
                    console.log('车辆:', car.plateNum, '车辆类型:', carType, '目标类型:', vehicleType, '是否匹配:', carType === vehicleType);
                    return carType === vehicleType;
                });
            }
            
            console.log('筛选前车辆数量:', carList.length);
            console.log('筛选后车辆数量:', filteredCars.length);
            console.log('筛选类型:', vehicleType);
            console.log('筛选后的车辆:', filteredCars);
            
            // 显示筛选后的车辆
            sortAndShowCars(filteredCars);
        }

        /**
         * 获取车辆类型
         * @param car 车辆对象
         * @returns 车辆类型编码
         */
        function getCarVehicleType(car) {
            // 尝试多个可能的字段
            return car.carType || car.vehicleType || car.mobileType || car.type || car.typeCode || '';
        }

        /**
         * 应用车辆类型筛选的统一函数
         * @param carList 原始车辆列表
         */
        function applyVehicleTypeFilter(carList) {
            console.log('应用车辆类型筛选，当前选择类型:', _currentSelectedVehicleType);
            console.log('原始车辆列表:', carList);
            
            _currentAwaitMobile = carList;
            
            // 应用车辆类型筛选
            if (_currentSelectedVehicleType !== 'all') {
                filterAndShowCarsByType(carList, _currentSelectedVehicleType);
            } else {
                sortAndShowCars(carList);
            }
        }

        /**
         * 重置车辆类型选择为全部
         */
        function resetVehicleTypeSelection() {
            _currentSelectedVehicleType = 'all';
            // 重新设置radio按钮为全部选中状态
            $('input[name="vehicle-type"][value="all"]').prop('checked', true);
        }

        /**
         * 获取车辆类型的显示名称
         * @param car 车辆对象
         * @returns 车辆类型显示名称
         */
        function getCarTypeDisplayName(car) {
            var carType = getCarVehicleType(car);
            
            if (!carType || carType === '') {
                return '未知';
            }
            
            // 使用车辆类型字典数据进行映射
            if (_carTypeDictionary && _carTypeDictionary.length > 0) {
                var matchedType = _carTypeDictionary.find(function(dict) {
                    return dict.codeName === carType;
                });
                
                if (matchedType) {
                    return matchedType.codeVale;
                }
            }
            
            // 如果字典中没找到，返回原始值
            return carType;
        }

        function G(id) {
            return document.getElementById(id);
        }
        function addBlacklist() {
            $('#blacklist-number').textbox('setValue', _currentInCallDetails.phone);
            $('#blacklist-period').val('30');
            $('#blacklist').window('open'); //打开交接班界面
        }
        /** 黑名单加入确定后调用接口 */
        function affirmBlacklist() {
            let params = {
                number: $('#blacklist-number').val(),
                period: $('#blacklist-period').val(),
                remark: $('#blacklist-remark').val(),
            }
            let submitState = true
            for (let key in params) {
                if (submitState && !params[key]) {
                    submitState = false
                }
            }
            if (submitState) {
                if (params.number.length > 11) {
                    return $.messager.alert('提示', '号码最多只能11位并且是数字！', 'error');
                }
                showProcess(true, '温馨提示', '正在加入中，请稍后...');
                insertBlacklist(params, function (res) {
                    if (res.success) {
                        $.messager.alert('提示', '加入成功', 'info');
                        var params = {
                            id: _currentInCallDetails.callUid,
                            handleStatus: '0'
                        }
                        //保存到通话记录
                        updateHandleStatusAndType(params, function (res) {
                            //延时一秒后关闭 防止在自动创建
                            showProcess(false);
                            setTimeout(() => {
                                parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails.callUid + ')' : '创建新事件');
                            }, 1000)
                        },
                            function (e, url, errMsg) {
                                showProcess(false);
                                $.messager.alert('提示', '保存通话记录失败' + errMsg, 'error');
                            })

                    } else {
                        $.messager.alert('提示', '加入失败，异常信息：' + res.msg, 'error');
                    }
                },
                    function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('提示', '事件保存失败，异常信息：' + errMsg);
                    });
            } else {
                $.messager.alert('提示', '请选择加入时间间隔和备注!', 'error');
            }
        }
        
        /**
         * 派出此车
         * @param id 本次任务ID
         * @param carId 车辆id
         * @param isDispatch 是否分站派车 0否 1是
         * @param stationCode 分站编码
         * @param toStatus 车辆状态 车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-接诊完成、5-调度接收
         */
        function modifyMobileStatus(id, carId, isDispatch, stationCode, toStatus) {
            $.messager.confirm('',
                '你确定要进行派车操作吗？',
                function (res) {
                    if (res) {
                        doModifyMobileStatus(id, carId, toStatus, isDispatch, stationCode, null, null, null, null);
                    }
                });
        }
        /**
         * 派车的弹窗处理   打开确认接收弹窗
         * @param eventId
         * @param carId
         * @param processId
         * @param stationCode
         */
        function confirmReceiveShow(eventId, carId, processId, stationCode) {
            reloadDispatch(stationCode);
            //清理下拉选择框
            $('#dispatchClassesDri').combobox('clear');
            $('#dispatchDoctorDri').combobox('clear');
            $('#dispatchNurseDri').combobox('clear');
            $('#dispatchWorkerDri').combobox('clear');
            $('#dispatchDriverDri').combobox('clear');
            $('#dispatchEventId').val(eventId);//事件ID
            $('#dispatchCarId').val(carId);//车辆ID
            $('#mobileProcessId').val(processId);//车辆事件ID
            $('#confirm-receive-editor').window('open')
        }
        /**
         * 更新出车人员班次选择框列表
         * @param stationCode
         */
        function reloadDispatch(stationCode) {
            // 获得分站班次
            var params = {
                "substationcode": stationCode,
            }
            getDispatchClassesByStation(params,
                function (docs) {
                    dispatchClassesList = docs;
                    var jsonstr = [];
                    for (var i = 0; i < docs.length; i++) {
                        jsonstr.push({ 'id': docs[i].id, 'name': docs[i].scheduleName });
                    }
                    if (jsonstr.length == 0) {
                        //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                        $('#dispatchClassesDri').combobox('loadData', []);//重新本地加载一个空数据
                    } else {
                        $('#dispatchClassesDri').combobox('loadData', jsonstr);
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获得排版信息，异常信息：' + errMsg);
                });

            //获取医生1001
            params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1001", "rows": "999" }
            getPeopleByStation(params,
                function (docs) {
                    var jsonstr = [];
                    for (var i = 0; i < docs.length; i++) {
                        jsonstr.push(
                            {
                                'id': docs[i].id,
                                'name': docs[i].userName + "-" + docs[i].name,
                                'phone': docs[i].legalContactPhone,
                                'username': docs[i].userName,
                                'xm': docs[i].name,
                            });
                    }
                    if (jsonstr.length == 0) {
                        $('#dispatchDoctorDri').combobox('loadData', []);//重新本地加载一个空数据
                    } else {
                        $('#dispatchDoctorDri').combobox('loadData', jsonstr);
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获得医生信息，异常信息：' + errMsg);
                });

            //获取护士1002
            params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1002", "rows": "999" }
            getPeopleByStation(params,
                function (docs) {
                    var jsonstr = [];
                    for (var i = 0; i < docs.length; i++) {
                        jsonstr.push(
                            {
                                'id': docs[i].id,
                                'name': docs[i].userName + "-" + docs[i].name,
                                'phone': docs[i].legalContactPhone,
                                'username': docs[i].userName,
                                'xm': docs[i].name,
                            });
                    }
                    if (jsonstr.length == 0) {
                        //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                        $('#dispatchNurseDri').combobox('loadData', []);//重新本地加载一个空数据
                    } else {
                        $('#dispatchNurseDri').combobox('loadData', jsonstr);
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获得护士信息，异常信息：' + errMsg);
                });

            //获取司机1003
            params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1003", "rows": "999" }
            getPeopleByStation(params,
                function (docs) {
                    var jsonstr = [];
                    for (var i = 0; i < docs.length; i++) {
                        jsonstr.push(
                            {
                                'id': docs[i].id,
                                'name': docs[i].userName + "-" + docs[i].name,
                                'phone': docs[i].legalContactPhone,
                                'username': docs[i].userName,
                                'xm': docs[i].name,
                            });
                    }
                    if (jsonstr.length == 0) {
                        $('#dispatchDriverDri').combobox('loadData', []);//重新本地加载一个空数据
                    } else {
                        $('#dispatchDriverDri').combobox('loadData', jsonstr);
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获得司机信息，异常信息：' + errMsg);
                });

            //获取工人1004
            params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1004", "rows": "999" }
            getPeopleByStation(params,
                function (docs) {
                    var jsonstr = [];
                    for (var i = 0; i < docs.length; i++) {
                        jsonstr.push(
                            {
                                'id': docs[i].id,
                                'name': docs[i].userName + "-" + docs[i].name,
                                'phone': docs[i].legalContactPhone,
                                'username': docs[i].userName,
                                'xm': docs[i].name,
                            });
                    }
                    if (jsonstr.length == 0) {
                        $('#dispatchWorkerDri').combobox('loadData', []);//重新本地加载一个空数据
                    } else {
                        $('#dispatchWorkerDri').combobox('loadData', jsonstr);
                    }
                }, function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('提示', '获得工人信息，异常信息：' + errMsg);
                });
            
        }
        function doModifyMobileStatus(id, carId, toStatus, isDispatch, stationCode, doctorId, nurseId, doctorName, nurseName) {
            if ($("#event-id").val() == "") {
                showProcess(true, '温馨提示', '正在进行调度，请稍后...');
                //先创建事件
                addEvent(
                    function (res) {
                        //隐藏预约、落单、待派选项
                        $('#dispatchTypeDiv').hide();
                        if (isDispatch === '0') { //座席端可以直接派任务给车辆，不需要经过分站确认
                            // 打开派车弹窗选人
                            confirmReceiveShow(res.id, carId, '', stationCode)
                        } else {
                            $("#event-id").val(res.id);
                            //创建调度，由分站选人
                            var params = {
                                "id": id,
                                "eventId": $("#event-id").val(),
                                "carId": carId,
                                "toStatus": toStatus,
                                "doctorId": doctorId,
                                "nurseId": nurseId,
                                "doctorName": doctorName,
                                "nurseName": nurseName,
                                "workerId": '',
                                "workerName": '',
                                "driverId": '',
                                "driverName": '',
                                "seatId": _pSeat.id, //座席id
                                "seatCode": _pSeat.seatId, //座席code
                                "seatUserId": _pSeat.userId, //当前座席用户id
                                "seatUserName": _pSeat.userName, //当前座席用户工号
                                "seatUser": _pSeat.user //当前座席用户姓名
                            };
                            addMobileProcess(params,
                                function (res2) {
                                    showProcess(false);
                                    openEventDetail($("#event-id").val());
                                    window.parent.refreshEventlist();
                                    window.parent.refreshMobilesOrStations();
                                    $.messager.alert('提示', '车辆调度成功。', 'info');
                                    //先不关闭窗口。现在关闭窗口会导致，如果电话没有挂，但是派车了，就会再次弹窗
                                    // parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails
                                    //    .callUid + ')' : '创建新事件');
                                    //重大事件才发送短信
                                    // if($('#eventType').is(':checked')) {
                                    //     uploadEvent(eventId)
                                    // } else{
                                    //     parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails
                                    //         .callUid + ')' : '创建新事件');
                                    // }
                                },
                                function (e, url, errMsg) {
                                    showProcess(false);
                                    $.messager.alert('提示', '车辆调度成功错误：' + errMsg, 'error');
                                    openEventDetail($("#event-id").val());
                                });
                        }
                    },
                    function (errMsg) {
                        //失败才做处理
                        showProcess(false);
                        $.messager.alert('创建事件错误', errMsg);
                    });
            } else {
                if (isDispatch === '0') { //座席端可以直接派任务给车辆，不需要经过分站确认
                    // 打开派车弹窗选人
                    confirmReceiveShow($("#event-id").val(), carId, '', stationCode)
                } else {
                    showProcess(true, '温馨提示', '正在进行调度，请稍后...');
                    //创建调度，由分站选人
                    //
                    var params = {
                        "id": id,
                        "eventId": $("#event-id").val(),
                        "carId": carId,
                        "toStatus": toStatus,
                        "doctorId": doctorId,
                        "nurseId": nurseId,
                        "doctorName": doctorName,
                        "nurseName": nurseName,
                        "workerId": '',
                        "workerName": '',
                        "driverId": '',
                        "driverName": '',
                        "seatId": _pSeat.id, //座席id
                        "seatCode": _pSeat.seatId, //座席code
                        "seatUserId": _pSeat.userId, //当前座席用户id
                        "seatUserName": _pSeat.userName, //当前座席用户工号
                        "seatUser": _pSeat.user //当前座席用户姓名
                    };
                    addMobileProcess(params,
                        function (res) {
                            openEventDetail($("#event-id").val());
                            window.parent.refreshEventlist();
                            window.parent.refreshMobilesOrStations();
                            $.messager.alert('提示', '车辆调度成功。', 'info');
                            showProcess(false);
                            //先不关闭窗口。现在关闭窗口会导致，如果电话没有挂，但是派车了，就会再次弹窗
                            //parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails.callUid + ')' : '创建新事件');
                            //重大事件才发送短信
                            // if($('#eventType').is(':checked')) {
                            //     uploadEvent($("#event-id").val())
                            // } else{
                            //     parent.closeTab(_currentInCallDetails.callUid ? '创建新事件(' + _currentInCallDetails.callUid + ')' : '创建新事件');
                            // }
                        },
                        function (e, url, errMsg) {
                            showProcess(false);
                            $.messager.alert('提示', '车辆调度错误：' + errMsg, 'error');
                        });
                }
            }
        }
        function confirmReceiveSubmit() {
            var mobileProcessId = $('#mobileProcessId').val();
            var carId = $('#dispatchCarId').val();
            var eventId = $('#dispatchEventId').val();
            var dispatchDoctor = "";
            var dispatchDoctorNum = "";
            var dispatchDoctorPhone = "";
            var dispatchDoctorId = $('#dispatchDoctorDri').combobox('getValue');

            var dispatchDoctorDatas = $('#dispatchDoctorDri').combobox('getData');
            for (var i = 0; i < dispatchDoctorDatas.length; i++) {
                if (dispatchDoctorId == dispatchDoctorDatas[i].id) {
                    dispatchDoctor = dispatchDoctorDatas[i].xm;
                    dispatchDoctorNum = dispatchDoctorDatas[i].username;
                    dispatchDoctorPhone = dispatchDoctorDatas[i].phone == null ? "" : dispatchDoctorDatas[i].phone;
                    break;
                }
            }

            var dispatchNurse = "";
            var dispatchNursePhone = "";
            var dispatchNurseNum = "";
            var dispatchNurseId = $('#dispatchNurseDri').combobox('getValue');
            var dispatchNurseDatas = $('#dispatchNurseDri').combobox('getData');
            for (var i = 0; i < dispatchNurseDatas.length; i++) {
                if (dispatchNurseId == dispatchNurseDatas[i].id) {
                    dispatchNurse = dispatchNurseDatas[i].xm;
                    dispatchNurseNum = dispatchNurseDatas[i].username;
                    dispatchNursePhone = dispatchNurseDatas[i].phone == null ? "" : dispatchNurseDatas[i].phone;
                    break;
                }
            }

            var dispatchWorker = "";
            var dispatchWorkerNum = "";
            var dispatchWorkerPhone = "";
            var dispatchWorkerId = $('#dispatchWorkerDri').combobox('getValue');
            var dispatchWorkerDatas = $('#dispatchWorkerDri').combobox('getData');
            for (var i = 0; i < dispatchWorkerDatas.length; i++) {
                if (dispatchWorkerId == dispatchWorkerDatas[i].id) {
                    dispatchWorker = dispatchWorkerDatas[i].xm;
                    dispatchWorkerNum = dispatchWorkerDatas[i].username;
                    dispatchWorkerPhone = dispatchWorkerDatas[i].phone == null ? "" : dispatchWorkerDatas[i].phone;
                    break;
                }
            }

            var dispatchDriver = $('#dispatchDriverDri').combobox('getText');
            var dispatchDriverId = $('#dispatchDriverDri').combobox('getValue');
            var dispatchDriver = "";
            var dispatchDriverNum = "";
            var dispatchDriverPhone = "";
            var dispatchDriverId = $('#dispatchDriverDri').combobox('getValue');
            var dispatchDriverDatas = $('#dispatchDriverDri').combobox('getData');
            for (var i = 0; i < dispatchDriverDatas.length; i++) {
                if (dispatchDriverId == dispatchDriverDatas[i].id) {
                    dispatchDriver = dispatchDriverDatas[i].xm;
                    dispatchDriverNum = dispatchDriverDatas[i].username;
                    dispatchDriverPhone = dispatchDriverDatas[i].phone == null ? "" : dispatchDriverDatas[i].phone;
                    break;
                }
            }
            let verify = [
                {
                    name: dispatchWorker,
                    id: dispatchWorkerId,
                    // phone:dispatchWorkerPhone,
                    num: dispatchWorkerNum,
                    msg: '出车护工'
                },
                {
                    name: dispatchDoctor,
                    id: dispatchDoctorId,
                    // phone:dispatchDoctorPhone,
                    num: dispatchDoctorNum,
                    msg: '出车医生'
                },
                {
                    name: dispatchNurse,
                    id: dispatchNurseId,
                    // phone:dispatchNursePhone,
                    num: dispatchNurseNum,
                    msg: '出车护士'
                },
                {
                    name: dispatchDriver,
                    id: dispatchDriverId,
                    // phone:dispatchDriverPhone,
                    num: dispatchDriverNum,
                    msg: '出车司机'
                }
            ]
            let msgArr = []
            verify.forEach(r => {
                let { name, id, num, msg } = r
                if (!(name && id && num) && (name || id || num)) {
                    msgArr.push(msg)
                }
            })
            if (msgArr[0]) {
                return $.messager.alert('温馨提示', msgArr.join(',') + "数据不完整，请在下拉列表选择，不要手动输入数据", 'error');
            }
            //派车
            var params = {
                "id": '',
                "eventId": eventId,
                "carId": carId,
                "toStatus": '6',
                "dispatchDoctorId": dispatchDoctorId,
                "dispatchDoctorNum": dispatchDoctorNum,
                "dispatchDoctor": dispatchDoctor,
                "dispatchDoctorPhone": dispatchDoctorPhone,
                "dispatchNurseId": dispatchNurseId,
                "dispatchNurseNum": dispatchNurseNum,
                "dispatchNurse": dispatchNurse,
                "dispatchNursePhone": dispatchNursePhone,
                "dispatchDriverId": dispatchDriverId,
                "dispatchDriverNum": dispatchDriverNum,
                "dispatchDriver": dispatchDriver,
                "dispatchDriverPhone": dispatchDriverPhone,
                "dispatchWorkerId": dispatchWorkerId,
                "dispatchWorkerNum": dispatchWorkerNum,
                "dispatchWorker": dispatchWorker,
                "dispatchWorkerPhone": dispatchWorkerPhone,
                "seatId": _pSeat.id, //座席id
                "seatCode": _pSeat.seatId, //座席code
                "seatUserId": _pSeat.userId, //当前座席用户id
                "seatUserName": _pSeat.userName, //当前座席用户工号
                "seatUser": _pSeat.user //当前座席用户姓名
            };
            addMobileProcess(params,
                function (res) {
                    //成功
                    $('#confirm-receive-editor').window('close', true)
                    $.messager.alert('提示', '派车(增派)车辆成功！', 'info');
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', errMsg, 'error');
                });
        }
        
        /**
         * 创建新事件
         * @param callback
         * @param errorCallback
         * @param sendMessage
         */
        function addEvent(callback, errorCallback, sendMessage) {
            //检查必填项
            // 检查联动派车类型必填
            if($('#evd-call-type').val() === '11' && $('#det-region-linkage-id').val() === '') { // 11代表联动派车，且不是区域联动创建的事件
                if(!$('#push-outer-type').val()) {
                    $.messager.alert('提示', '联动派车类型必须选择接收中心', 'warning');
                    pass = false;
                }
            }
            var pass = true;
            $('#evd-form input[required]').each(function (e) {
                var v = $(this).val();
                if (v == undefined || v == null || v == "") {
                    pass = false;
                    return false;
                }
                return true;
            });
            if (pass) {
                //二次检测
                $('#evd-form input[type=number]').each(function (e) {
                    var v = $(this).val();
                    var min = 0;
                    var max = parseInt($(this).attr('max'));
                    if (v > max || v < min) {
                        pass = false;
                        return false;
                    }
                    return true;
                });
            }
            // 呼叫原因和病状二选一 必填一项检测
            checkedList = []
            var select1Value = $('#select1').val();
            var select2Value = $('#select2').val();
            if(select1Value){
                checkedList.push(symptomData[select1Value].t)
                if (symptomData[select1Value].c && select2Value){
                    checkedList.push(symptomData[select1Value].c[select2Value].t)
                }
            }
            let major = $('#det-major').val()
            if (!major && checkedList.length == 0) {
                pass = false
            }

            // 呼叫类型为咨询电话、投诉电话、骚扰电话，不用填地址，呼叫原因，患者姓名等信息
            if (isDirectCompleteCallType($('#evd-call-type').val())) {
                
                pass = true
            }
            if (pass) {

                //检测通过，创建事件
                var newEve = {};
                let patientDiagnosis = checkedList.join(' ')
                // 检查联动派车类型并传入联动类型的方向，是发送给对方，还是接收
                if($('#evd-call-type').val() === '11') { // 11代表联动派车，且不是区域联动创建的事件
                    if($('#det-region-linkage-id').val() === ''){
                        newEve.regionLinkageDirection = "1";//如果等于1的话，那就是发送，发送直接创建事件并变成完成状态。
                    }else{
                        newEve.regionLinkageDirection = "2";//如果等于2的话，那么就是接收，接收的话需要创建事件。
                    }
                }else{
                    newEve.regionLinkageDirection = "";
                }
                newEve.eventType = "0";
                newEve.eventSrc = $('#evd-source').val();
                newEve.eventSrcCodeName = $('#evd-source option:selected').text();
                newEve.callType = $('#evd-call-type').val();
                newEve.callTypeCodeName = $('#evd-call-type option:selected').text();
                newEve.address = $('#det-address').val();
                newEve.majorCall = major + filterPatientDiagnosis(patientDiagnosis);
                newEve.callIn = $('#det-callin').val();
                newEve.contact = $('#det-contact').val();
                newEve.contacter = $('#det-contacter').val();
                newEve.patientName = $('#det-name').val() || '未知';
                newEve.patientGender = $('#det-gender').val() || '';
                newEve.patientAge = $('#det-age').val();
                newEve.patientAmount = $('#det-amount').val();
                newEve.patientCountry = $('#det-country').val();
                newEve.patientRace = $('#det-race').val();
                // newEve.patientCondition = $('#det-condition').val();
                newEve.centerRemark = $('#det-120remark').val();
                newEve.lat = $('#event-lat').val();
                newEve.lng = $('#event-lng').val();
                newEve.pickupAddress = $('#pickup-address').val();
                newEve.pickupLat = $('#pickup-lat').val();
                newEve.pickupLng = $('#pickup-lng').val();
                // 新增字段映射
                newEve.addressWait = $('#pickup-address').val();
                newEve.addressWaitLat = $('#pickup-lat').val() ? parseFloat($('#pickup-lat').val()).toFixed(6) : null;
                newEve.addressWaitLng = $('#pickup-lng').val() ? parseFloat($('#pickup-lng').val()).toFixed(6) : null;
                newEve.addressDelivery = $('#destination-address').val();
                newEve.addressDeliveryLat = $('#destination-lat').val() ? parseFloat($('#destination-lat').val()).toFixed(6) : null;
                newEve.addressDeliveryLng = $('#destination-lng').val() ? parseFloat($('#destination-lng').val()).toFixed(6) : null;
                newEve.callRId = $('#call-r-id').val();
                newEve.callRegionId = $('#det-area').val(); // 所属区域ID
                newEve.callRegionName = $('#det-area option:selected').text(); // 所属区域名称

                newEve.isNeedStretcher = $('#need-stretcher').is(':checked') ? 1 : 0; // 是否需要担架
                newEve.isTest = $('#is-test').is(':checked') ? 1 : 0; // 是否测试
                newEve.mpdsSummary = $('#det-alds-summary').val(); // MPDS汇总
                newEve.specialRequirement = $('#det-special-req').val(); // 特殊要求

                if ($('#createForeign').is(':checked')){//外籍人员
                    newEve.patientIsForeign = 1
                }else{
                    newEve.patientIsForeign = 0
                }
                newEve.pushOuterType = $('#push-outer-type').val();
                newEve.emergencyDegree = $('#emergency-level').val(); // 紧急程度
                newEve.firstEventId = $('#firstEventId').val(); // 关联首次事件ID

                if ($('#eventType').is(':checked')) {
                    newEve.eventType = '1'
                    // 重大事件类型和等级信息
                    var largeEventTypeSelected = document.getElementById("mainEventType");
                    var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
                    var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
                    var largeEventLevelSelected = document.getElementById("mainEventLevel");
                    var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
                    var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
                    
                    newEve.eventTypeCode = eventTypeCode;
                    newEve.eventTypeName = eventTypeName;
                    newEve.eventLevelCode = eventLevelCode;
                    newEve.eventLevelName = eventLevelName;

                } else {
                    newEve.eventType = '0'
                }
                var callInTimesValue = $('#det-call-in-times').val();
                var callInTimes = callInTimesValue == undefined || callInTimesValue == "" ? null : new Date(callInTimesValue).formatString("yyyy-MM-dd hh:mm:ss");

                newEve.callInTimes = callInTimes; //来电时间
                newEve.regionLinkageId = $('#det-region-linkage-id').val(); //区域联动id
                newEve.seatId = _pSeat.id; //座席id
                newEve.seatCode = _pSeat.seatId; //座席code
                newEve.seatUserId = _pSeat.userId; //当前座席用户id
                newEve.seatUserName = _pSeat.userName; //当前座席用户工号
                newEve.seatUser = _pSeat.user; //当前座席用户姓名
                newEve.patientDiagnosis = patientDiagnosis,
                newEve.status = $('input[name="dispatchType"]:checked').val();//事件状态  7-预约 1-落单 6-待派
                createNewEventData(newEve,
                    function (res) {
                        if(res && res.id){//事件创建成功,有些呼叫类型是不用保存成事件的。只对通话记录进行修改
                            _currentEventDetails = res;
                        

                            //关联本事件与当前呼入的电话id
                            if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                                //关联本事件与当前视频呼救id
                                if (_currentCallList.length > 0) {
                                    bindVideoCallAndEvent({id: _currentCallList[0].id, eventId: res.id},
                                        function (data) {
                                        }, function (e, url, errMsg) {
                                            //失败才做处理
                                            $.messager.alert('异常', '关联视频呼救记录，异常信息：' + errMsg, 'error');
                                        });
                                }
                            }else{
                                if (_currentCallList.length > 0) {
                                    if (res && res.id) {
                                    bindCallAndEvent(_currentCallList[0].id, res.id,
                                        function (data) {
                                        }, function (e, url, errMsg) {
                                            //失败才做处理
                                            $.messager.alert('异常', '关联通话记录，异常信息：' + errMsg, 'error');
                                        });
                                    }
                                }
                            }
                            
                            // 事件创建成功后,禁用事件来源和呼叫类型输入框
                            $('#evd-source').attr('disabled', true);
                            $('#evd-call-type').attr('disabled', true);
                            $('#push-outer-type').attr('disabled', true);
                            

                            //处理区域联动的派车事件，发送请求给对应的急救分中心
                            if(newEve.callType == "11"){
                                if($('#det-region-linkage-id').val() === ''){
                                    //急救中心联动事件-发送给其他急救中心
                                    var pushEventToRegionLinkageParams = {
                                        "address": newEve.address,//现场地址
                                        "callIn": newEve.callIn,//来电号码
                                        "callInTimes": newEve.callInTimes,//来电时间
                                        "callReason": newEve.majorCall,//呼救原因
                                        "callUser": newEve.contacter,//报警人
                                        "contact": newEve.contact,//联系电话
                                        "contacter": newEve.contacter,//联系人
                                        "direction": "1",//方向 1-发送 2-接收，
                                        "externalBizId": res.id,//外部业务id
                                        "lat": newEve.lat || 0,//现场地址纬度（GCJ02坐标系），保留6位小数，>0北纬  <0 南纬   
                                        "ldType": "1",//目前只支持120联动
                                        "lng": newEve.lng || 0,//现场地址经度（GCJ02坐标系），保留6位小数，>0东经  <0 西经
                                        "optUser": newEve.seatUser,
                                        "patientAge": newEve.patientAge || '',
                                        "patientCountry": newEve.patientCountry,
                                        "patientGender": newEve.patientGender,
                                        "patientName": newEve.patientName,
                                        "patientRace": newEve.patientRace,
                                        "receiveRegionCode": newEve.pushOuterType,
                                        "recordTime": newEve.recordTime,
                                        "remark": newEve.centerRemark //备注
                                    }
                                    pushEventToRegionLinkage(pushEventToRegionLinkageParams,
                                            function (data) {
                                                // 推送区域联动事件成功
                                                openEventDetail(_currentEventDetails.id);
                                                callback(res);
                                            }, function (e, url, errMsg) {
                                                errorCallback(errMsg);
                                            });
                                }else{
                                    openEventDetail(_currentEventDetails.id);
                                    callback(res);
                                }
                            }else{
                                openEventDetail(_currentEventDetails.id);
                                callback(res)
                            }
                        }else{
                            //待开发-预约事件处理
                            if(newEve.status == 7){
                                //预约事件
                                console.log("待开发-预约事件处理")
                            }

                            // 判断呼叫类型是否为报停车辆(70)
                            if (newEve.callType == '70') {
                                // 获取当前通话记录ID
                                var callId = _currentCallList.length > 0 ? _currentCallList[0].id : '';
                                // 弹出报停新增界面
                                openVehicleStopEdit(callId);
                            }else{
                                $.messager.alert('提示', '本次通话受理无需创建事件只对通话记录标记为已处理', 'info');
                            }
                            //隐藏预约、落单、待派选项
                            $('#dispatchTypeDiv').hide();
                            showProcess(false);
                        }

                    },
                    function (e, url, errMsg) {
                        errorCallback(errMsg);
                    });

            } else {
                showProcess(false);
                if (sendMessage) {
                    $.messager.alert('', '事件必要信息未填写或填写有误，无法保存并发送指导短信，请检查后再进行操作。', 'error');
                } else {
                    $.messager.alert('', '事件必要信息未填写或填写有误，无法保存事件。', 'error');
                }
            }
            return pass;
        }

        /** 更新事件信息 */
        function syncEvent() {
            //检查必填项
            var pass = true;
            $('#evd-form input[required]').each(function (e) {
                var v = $(this).val();
                if (v == undefined || v == null || v == "") {
                    pass = false;
                    return false;
                }
                return true;
            });
            if (pass) {
                //二次检测
                $('#evd-form input[type=number]').each(function (e) {
                    var v = $(this).val();
                    var min = 0;
                    var max = parseInt($(this).attr('max'));
                    if (v > max || v < min) {
                        pass = false;
                        return false;
                    }
                    return true;
                });
            }
            // 呼叫原因和病状二选一 必填一项检测
            let major = $('#det-major').val()
            if (!major && !$('#evd-form #mainSuit').text()) {
                pass = false
            }
            // 呼叫类型为咨询电话、投诉电话、骚扰电话，不用填地址，病状，患者姓名等信息
            if (isDirectCompleteCallType($('#evd-call-type').val())) {
                pass = true
            }
            if (pass) {
                //检测通过，更新事件
                var newEve = {};
                let patientDiagnosis = checkedList.join(' ')
                newEve.id = _currentEventDetails.id;
                newEve.eventType = "0";
                newEve.eventSrc = $('#evd-source').val();
                newEve.eventSrcCodeName = $('#evd-source option:selected').text();
                newEve.callType = $('#evd-call-type').val();
                newEve.callTypeCodeName = $('#evd-call-type option:selected').text();
                newEve.address = $('#det-address').val();
                newEve.majorCall = major + filterPatientDiagnosis(patientDiagnosis);
                newEve.callIn = $('#det-callin').val();
                newEve.contact = $('#det-contact').val();
                newEve.contacter = $('#det-contacter').val();

                newEve.patientName = $('#det-name').val() == _currentEventDetails.patientName ? null : ($('#det-name').val() || '未知');
                newEve.patientGender = $('#det-gender').val() == _currentEventDetails.patientGender ? null : ($('#det-gender').val() || '');
                newEve.patientAge = $('#det-age').val() == _currentEventDetails.patientAge ? null : $('#det-age').val();
                newEve.patientAmount = $('#det-amount').val() == _currentEventDetails.patientAmount ? null : $('#det-amount').val();
                newEve.patientCountry = $('#det-country').val() == _currentEventDetails.patientCountry ? null : $('#det-country').val();
                newEve.patientRace = $('#det-race').val() == _currentEventDetails.patientRace ? null : $('#det-race').val();
                // newEve.patientCondition = $('#det-condition').val() == _currentEventDetails.patientCondition ? null : $('#det-cond').val();

                newEve.centerRemark = $('#det-120remark').val();

                newEve.lat = $('#event-lat').val();
                newEve.lng = $('#event-lng').val();
                newEve.pickupAddress = $('#pickup-address').val();
                newEve.pickupLat = $('#pickup-lat').val();
                newEve.pickupLng = $('#pickup-lng').val();
                // 新增字段映射
                newEve.addressWait = $('#pickup-address').val();
                newEve.addressWaitLat = $('#pickup-lat').val() ? parseFloat($('#pickup-lat').val()).toFixed(6) : null;
                newEve.addressWaitLng = $('#pickup-lng').val() ? parseFloat($('#pickup-lng').val()).toFixed(6) : null;
                newEve.addressDelivery = $('#destination-address').val();
                newEve.addressDeliveryLat = $('#destination-lat').val() ? parseFloat($('#destination-lat').val()).toFixed(6) : null;
                newEve.addressDeliveryLng = $('#destination-lng').val() ? parseFloat($('#destination-lng').val()).toFixed(6) : null;

                newEve.firstEventId = $('#firstEventId').val(); // 关联首次事件ID

                newEve.callRegionId = $('#det-area').val(); // 所属区域ID
                newEve.callRegionName = $('#det-area option:selected').text(); // 所属区域名称
                newEve.isNeedStretcher = $('#need-stretcher').is(':checked') ? 1 : 0; // 是否需要担架
                newEve.isTest = $('#is-test').is(':checked') ? 1 : 0; // 是否测试
                newEve.mpdsSummary = $('#det-alds-summary').val(); // MPDS汇总
                newEve.specialRequirement = $('#det-special-req').val(); // 特殊要求

                if ($('#eventType').is(':checked')) {
                    newEve.eventType = '1'
                    // 重大事件类型和等级信息
                    var largeEventTypeSelected = document.getElementById("mainEventType");
                    var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
                    var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
                    var largeEventLevelSelected = document.getElementById("mainEventLevel");
                    var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
                    var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
                    
                    newEve.eventTypeCode = eventTypeCode;
                    newEve.eventTypeName = eventTypeName;
                    newEve.eventLevelCode = eventLevelCode;
                    newEve.eventLevelName = eventLevelName;
                } else {
                    newEve.eventType = '0'
                }
                newEve.pushOuterType = $('#push-outer-type').val();
                var callInTimesValue = $('#det-call-in-times').val();
                var callInTimes = callInTimesValue == undefined || callInTimesValue == "" ? null : new Date(callInTimesValue).formatString("yyyy-MM-dd hh:mm:ss");

                newEve.callInTimes = callInTimes; //来电时间
                // newEve.seatId = _pSeat.id; //座席id
                // newEve.seatCode = _pSeat.seatId; //座席code
                // newEve.seatUserId = _pSeat.userId; //当前座席用户id
                // newEve.seatUserName = _pSeat.userName; //当前座席用户工号
                // newEve.seatUser = _pSeat.user; //当前座席用户姓名
                newEve.patientDiagnosis = patientDiagnosis
                updateEventData(newEve,
                    function (res) {
                        //成功不做任何处理
                        showProcess(false);
                        window.parent.refreshEventlist();
			
			            //关联本事件与当前呼入的电话id
                        if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                            //关联本事件与当前视频呼救id
                            if (_currentCallList.length > 0) {
                                bindVideoCallAndEvent({id: _currentCallList[0].id, eventId: $("#event-id").val()},
                                    function (data) {
                                    }, function (e, url, errMsg) {
                                        //失败才做处理
                                        $.messager.alert('异常', '关联视频呼救记录，异常信息：' + errMsg, 'error');
                                    });
                            }
                        }else{
                            if (_currentCallList.length > 0) {
                                bindCallAndEvent(_currentCallList[0].id, $("#event-id").val(),
                                    function (data) {
                                    }, function (e, url, errMsg) {
                                        //失败才做处理
                                        $.messager.alert('异常', '关联通话记录，异常信息：' + errMsg, 'error');
                                    });
                            }
                        }

                        openEventDetail(_currentEventDetails.id);

                        $.messager.alert('提示', '事件保存成功', 'info');
                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('提示', '事件保存失败，异常信息：' + errMsg);
                    });

            } else {
                $('#evd-btn-submit').click();
                showProcess(false);
                $.messager.alert('提示', '事件必要信息未填写或填写有误，无法保存事件。', 'error');
            }
            return pass;
        }
        function filterPatientDiagnosis(str) {
            if (str) {
                return '_' + str
            }
            return ''
        }
        /**
         * 更新事件
         * @param callBack
         * @param sendMessage
         */
        function syncEventBtn(callBack, sendMessage) {
            showProcess(true, '温馨提示', '正在保存事件信息，请稍后...');
            if (_currentEventDetails == null) {
                addEvent(function (res) {
                    if (callBack) {
                        callBack(res.id)
                    }
                    // 呼叫类型不容许修改，只要创建了。
                    // isUpdateEventCallType($("#event-id").val());
                },
                    function (errMsg) {
                        //失败才做处理
                        showProcess(false);
                        $.messager.alert('创建事件错误', errMsg);

                    }, sendMessage);
            } else {
                syncEvent(function (res) {
                    // 呼叫类型不容许修改，只要创建了。
                    // isUpdateEventCallType($("#event-id").val());
                },
                    function (errMsg) {
                        //失败才做处理
                        showProcess(false);
                        $.messager.alert('创建事件错误', errMsg);

                    });
            }
        }


        function openEventDetail(eventId) {
            if (eventId == null || eventId == "") {
                return;
            }
            //调用接口获取数据
            $('#eventListWaitBox').show();
            getEventDetailById(eventId,
                function (data) {
                    _currentEventDetails = data;
                    //选择了事件的话那么就隐藏掉预约、落单、待派选项
                    $('#dispatchTypeDiv').hide();
                    if (data.status == '3' || data.status == '4') {
                        // ADLS系统按钮隐藏掉
                        $('.getAdlsUrl').css('display', 'none')
                    } else {
                        // ADLS系统按钮隐显示
                        $('.getAdlsUrl').css('display', 'inline-block')
                    }
                    $('#event-id').val(data.id);
                    $('#evd-source').val(data.eventSrc);
                    $('#evd-call-type').val(data.callType);
                    // $('#det-condition').val(data.patientCondition);
                    $('#det-gender').val(data.patientGender);
                    $('#det-country').val(data.patientCountry);
                    setTimeout(function() {
                        try {
                            $('#det-call-in-times').datetimebox('setValue', data.callInTimes);
                        } catch (e) {
                            console.warn('Datetimebox 设置事件通话时间失败:', e);
                            $('#det-call-in-times').datetimebox({});
                            $('#det-call-in-times').datetimebox('setValue', data.callInTimes);
                        }
                    }, 100);
                    $('#det-amount').val(data.patientAmount);
                    _hideAcTemp = true; //临时禁止自动提示
                    $('#det-address').val(data.address);
                    // 地址设置后自动匹配区域
                    autoMatchRegionByAddress(data.address);
                    confirmAddress($('#det-address').val(), data.lng, data.lat);
                    // 设置接收中心
                    $('#push-outer-type').val(data.pushOuterType);
                    let index = data.majorCall.indexOf('_')
                    if (index !== -1) {
                        let majorCall = data.majorCall.split('_')
                        $('#det-major').val(majorCall[0] || '');
                        $('#mainSuit').html(majorCall[1] || '')
                    } else {
                        $('#det-major').val(data.majorCall);
                    }
                    $('#det-callin').val(data.callIn);
                    $('#det-contact').val(data.contact);
                    $('#det-contacter').val(data.contacter);
                    $('#det-120remark').val(data.centerRemark);
                    $('#det-name').val(data.patientName);
                    $('#det-race').val(data.patientRace);
                    $('#det-age').val(data.patientAge);
                    $('#event-lat').val(data.lat);
                    $('#event-lng').val(data.lng);
                    
                    // 处理区域联动事件接收中心回显
                    if (data.callType === "11") {
                        console.log('检测到区域联动事件，事件ID:', eventId);
                        console.log('事件数据:', data);
                        console.log('原始接收中心:', data.pushOuterType);
                        console.log('当前接收中心下拉框值:', $('#push-outer-type').val());
                        console.log('接收中心下拉框是否可见:', $('.field-push-outer-type').is(':visible'));
                        console.log('接收中心下拉框选项数:', $('#push-outer-type option').length);
                        
                        // 确保先设置原始值
                        if (data.pushOuterType) {
                            $('#push-outer-type').val(data.pushOuterType);
                            console.log('先设置原始接收中心:', data.pushOuterType);
                        }
                        
                        // 如果是区域联动事件，调用接口获取区域联动信息
                        getEventRegionLinkageByExternalBizId(eventId, function(regionLinkageData) {
                            console.log('区域联动信息查询结果:', regionLinkageData);
                            if (regionLinkageData && regionLinkageData.receiveRegionCode) {
                                // 确保下拉框可见
                                $('.field-push-outer-type').show();
                                
                                // 设置接收中心下拉框的值为区域联动信息中的接收区域编码
                                $('#push-outer-type').val(regionLinkageData.receiveRegionCode);
                                console.log('区域联动事件接收中心回显成功:', regionLinkageData.receiveRegionCode);
                                console.log('设置后的接收中心下拉框值:', $('#push-outer-type').val());
                            } else {
                                // 如果没有找到区域联动信息，则使用事件本身的pushOuterType
                                console.log('未找到区域联动信息，使用事件原始接收中心:', data.pushOuterType);
                                if (data.pushOuterType) {
                                    $('.field-push-outer-type').show();
                                    $('#push-outer-type').val(data.pushOuterType);
                                }
                            }
                        }, function(e, url, errMsg) {
                            // 获取区域联动信息失败，使用事件本身的pushOuterType
                            console.error('获取区域联动信息失败:', errMsg);
                            if (data.pushOuterType) {
                                $('.field-push-outer-type').show();
                                $('#push-outer-type').val(data.pushOuterType);
                                console.log('使用事件原始接收中心:', data.pushOuterType);
                            }
                        });
                    } 
                    
                    // 设置所属区域 - 优先使用事件的区域ID，如果没有则通过地址自动匹配
                    if (data.callRegionId) {
                        $('#det-area').val(data.callRegionId);
                        // 联动station-region选择，延迟执行确保DOM已更新
                        setTimeout(function() {
                            linkageStationRegion(data.callRegionId);
                        }, 200);
                    } else if (data.address) {
                        // 如果事件没有区域信息，通过地址自动匹配区域
                        setTimeout(function() {
                            autoMatchRegionByAddress(data.address);
                        }, 300);
                    }
                    //调度信息填入
                    $('#dis-selectedStation').empty();
                    $('#dis-stationStatus').empty();
                    $('#dis-mobileStatus').empty();
                    $('#event-calls-body').empty();
                    $('#eventListWaitBox').hide();
                    //$('#eventDetailRegion').show();
                    checkedList = data.patientDiagnosis.split(' ')
                    //查询诊断字典
                    /*getPatientDiagnosis(function (data) {
                        let list = data[0].val
                        loadRegionTree(list, "#patientDiagnosislevel1")
                    }, function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('提示', '查询诊断字典失败，异常信息：' + errMsg);
                    })*/
                    //更新车辆状态
                    window.parent.refreshMobileStatusCallBack(function (data) {
                        //刷新所有车辆坐标
                        //显示车辆列表
                        var carList = window.parent.getMobilesWithDistance().where(function (d) { return (d.status == "0" || d.status == "3"); });//显示全部，状态是0和3的。
                        //插入已被调配的车辆
                        sortAndShowCars(carList, $('#det-address').val());
                    });
                    // 呼叫类型不容许修改，只要创建了。
                    // isUpdateEventCallType(eventId);

                    $('#eventInfoSelectWindow').window('close', true);
                    getCallPageListShow(data.id);//回显通话录音
                    //检测当前号码和系统配置是否是同一个，是则提示未检测到来电号码，请询问来电人 并且清空主叫号码和联系电话
                    if (data.callIn) {
                        querySysConf("site_out_lines_nums", res => {
                            res = res || {}
                            if (res.val) {
                                let val = JSON.parse(res.val) || []
                                if (Array.isArray(val) && val.indexOf(data.callIn) !== -1) {
                                    $('#det-callin').val('');
                                    $('#det-contact').val('');
                                    $.messager.alert('提示', '未检测到来电号码，请询问来电人', 'error');
                                }
                            }
                        },
                            (e, url, errMsg) => {
                                $.messager.alert('提示', '获取配置异常：' + errMsg, 'error');
                            })
                    }
                    
                    showProcess(false);
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', "事件查询失败：" + errMsg, 'error');
                });

        }

        /**
         * 是否容许修改事件呼叫类型
         * @param eventId
         */
        function isUpdateEventCallType(eventId) {
            if (eventId != null && eventId != "") {
                getIsUpdateEventCallType(eventId,
                    function (res) {
                        if (res) {
                            $('#evd-call-type').removeAttr("disabled");;
                        } else {
                            $('#evd-call-type').attr("disabled", true);
                        }
                    },
                    function (e, url, errMsg) {
                        $.messager.alert('提示', '获取是否容许修改事件呼叫类型错误，异常信息：' + errMsg);

                    });
            }
        }

        /** 关联事件窗口 */
        function relevanceEventInfoBtn() {
            //获得当前选中行
            $('#eventInfoSelectWindow').window('open');
            getDgEventListDatas(1, 20);
        }

        /** 关联首次事件 */
        function relevanceFirstEventBtn() {
            //打开首次事件选择窗口
            $('#firstEventSelectWindow').window('open');
            getFirstEventListDatas(1, 20);
        }

        /**
         * 获得关联事件列表数据
         * @param pageNum
         * @param pageSize
         */
        function getDgEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = size;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = size;
            }

            var startCreateTimeVal = $('#startCreateTime').datetimebox('getValue');
            var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endCreateTimeVal = $('#endCreateTime').datetimebox('getValue');
            var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            //打开进度条：
            $('#dg-event-pages').datagrid('loading');//打开等待div

            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "majorCall": $("#majorCall").val(),
                "address": $('#address').val(),
                "eventStatusList": $('#eventStatus').combobox('getValues'),
            };
            getEventPages(params, function (data) {
                $('#dg-event-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-event-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            eventId: data.content[i].eventId,
                            eventStatus: data.content[i].eventStatus,
                            eventStatusStr: data.content[i].eventStatusStr,
                            callIn: data.content[i].callIn,
                            callInTimes: data.content[i].callInTimes,
                            createTime: data.content[i].createTime,
                            majorCall: data.content[i].majorCall.replace("_", " "),
                            eventSrcCode: data.content[i].eventSrcCode,
                            eventSrcName: data.content[i].eventSrcName,
                            callTypeCode: data.content[i].callTypeCode,
                            lng: data.content[i].lng,
                            lat: data.content[i].lat,
                            callTypeName: data.content[i].callTypeName,
                            contact: data.content[i].contact,
                            contacter: data.content[i].contacter,
                            addressWait: data.content[i].addressWait,
                            address: data.content[i].address,
                            createUser: data.content[i].createUser,
                            eventCreateSeatCode: data.content[i].eventCreateSeatCode,
                            eventCreateSeatUserId: data.content[i].eventCreateSeatUserId,
                            eventCreateSeatUserName: data.content[i].eventCreateSeatUserName,
                            eventCreateSeatUser: data.content[i].eventCreateSeatUser,
                            processCount: data.content[i].processCount,
                            runProcessCount: data.content[i].runProcessCount,
                            finishedProcessCount: data.content[i].finishedProcessCount,
                            cancelProcessCount: data.content[i].cancelProcessCount,
                            centerRemark: data.content[i].centerRemark
                        }
                    });
                }
                $("#dg-event-pages").datagrid({
                    rowStyler: function (index,row) {
                            switch (row.eventStatus) {
                                case '1':
                                    return 'background-color:#dad6d557;';//灰色
                                case "2":
                                    return 'background-color:#f9a296ad;';//红色
                                case "3":
                                    return 'background-color:#dad6d557;';//灰色
                                case "4":
                                    return 'background-color:#dad6d557;';//灰色
                            }
                        },
                });
                setPage(data.total || 0, pageNum)
                $('#dg-event-pages').datagrid('loaded');//关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-event-pages').datagrid('loaded');//关闭loding进度条；
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        function setPage(total, pageNum) {
            var pg = $("#dg-event-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total,
                    pageSize: size,
                    pageNumber: pageNum,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function (pageNumber, pageSize) {
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function (pageSize) {
                        page = 1
                        size = pageSize
                        getDgEventListDatas(page, pageSize);
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getDgEventListDatas(pageNumber, pageSize);
                    }

                });
            }

        }

        /**
         * 获取当前响铃的通话记录，未关联的记录
         * @param callId
         */
        function getAnswerCalls(callId) {
            $('#dg-call-list').datagrid('loading');//打开进度条：打开等待div
            //获取通话记录
            var getCallPageListParams = {
                "id": callId,
                "size": 100,
                "current": 1
            };

            getCallPageList(getCallPageListParams,
                function (data) {
                    //$('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //回显通话记录
                    console.log("getAnswerCalls初始化_currentCallList:", _currentCallList);
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.content.length; i++) {
                        var callTimes = "";
                        if (data.content[i].callTime == null || data.content[i].callTime == "") {
                            callTimes = new Date().formatString("yyyy-MM-dd hh:mm:ss");//默认是打开的时间
                        } else {
                            callTimes = data.content[i].callTime;
                        }
                        data.content[i].isCurrentCall = true;//当前通话电话，赋值true

                        $('#dg-call-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data.content[i].id,
                                type: data.content[i].type,
                                number: data.content[i].number,
                                toNumber: data.content[i].toNumber,
                                callType: data.content[i].callType,
                                callTime: data.content[i].callTime,
                                handleTime: data.content[i].handleTime,
                                endTime: data.content[i].endTime,
                                handleStatus: data.content[i].handleStatus,
                                eventId: data.content[i].eventId,
                                recordAddr: data.content[i].recordAddr,
                                callId: data.content[i].callId,
                                visitorId: data.content[i].visitorId,
                                outerId: data.content[i].outerId,
                                duration: data.content[i].duration,
                                isAnswer: data.content[i].isAnswer,
                                seatId: data.content[i].seatId,
                                seatUserId: data.content[i].seatUserId,
                                seatUserName: data.content[i].seatUserName,
                                seatUser: data.content[i].seatUser,
                                isNewDevice: data.content[i].isNewDevice,
                                returnVisitStatus: data.content[i].returnVisitStatus,
                                callOpenId: data.content[i].callOpenId,
                                address: data.content[i].address,
                                lng: data.content[i].lng,
                                lat: data.content[i].lat,
                                nickName: data.content[i].nickName,
                                avatarUrl: data.content[i].avatarUrl,
                                gender: data.content[i].gender,
                                callCurentStatus: data.content[i].callCurentStatus,
                                createTime: data.content[i].createTime,
                                createUserId: data.content[i].createUserId,
                                createUser: data.content[i].createUser,
                                lastUpdateTime: data.content[i].lastUpdateTime,
                                lastUpdateUserId: data.content[i].lastUpdateUserId,
                                lastUpdateUser: data.content[i].lastUpdateUser,
                                disabled: data.content[i].disabled,
                                callInType: "2", //呼入类型 1:视频呼救 2:120电话呼入
                                recordName: data.content[i].recordName, //录音文件的名称，实际没有用途
                                isCurrentCall: data.content[i].isCurrentCall,//是否当前电话，是的话那么true
                            }
                        });
                        //将当前电话存储起来
                        _currentCallList.push(data.content[i]);
                        console.log("getAnswerCalls添加电话记录后的_currentCallList:", _currentCallList);
                    }

                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                },
                function (e, url, errMsg) {
                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                    $.messager.alert('提示', '获取当前通话记录异常：' + errMsg, 'error');

                });
        }

        /**
         * 获取当前视频呼救的通话记录，未关联的记录
         * @param callId
         */
         function getAnswerVideoCalls(callId) {
            $('#dg-call-list').datagrid('loading');//打开进度条：打开等待div
            //获取视频呼救通话记录
            var getVideoCallPageListParams = {
                "id": callId,
                "size": 100,
                "page": 1
            };
            getVideoCallPageList(getVideoCallPageListParams,
                function (data) {
                    //$('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //回显通话记录
                    console.log("getAnswerVideoCalls初始化_currentCallList:", _currentCallList);
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.records.length; i++) {
                        var callTimes = "";
                        if (data.records[i].callTime == null || data.records[i].callTime == "") {
                            callTimes = new Date().formatString("yyyy-MM-dd hh:mm:ss");//默认是打开的时间
                        } else {
                            callTimes = data.records[i].callTime;
                        }
                        data.records[i].isCurrentCall = true;//当前通话电话，赋值true
                        if(data.records[i].callTime){
                            data.records[i].callTime = dateTimeFormatter(data.records[i].callTime);
                        }
                        if(data.records[i].handleTime){
                            data.records[i].handleTime = dateTimeFormatter(data.records[i].handleTime);
                        }
                        if(data.records[i].endTime){
                            data.records[i].endTime = dateTimeFormatter(data.records[i].endTime);
                        }
                        $('#dg-call-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data.records[i].id,
                                type: data.records[i].type,
                                number: data.records[i].number,
                                toNumber: data.records[i].toNumber,
                                callType: data.records[i].callType,
                                callTime: data.records[i].callTime,
                                handleTime: data.records[i].handleTime,
                                endTime: data.records[i].endTime,
                                handleStatus: data.records[i].handleStatus,
                                eventId: data.records[i].eventId,
                                recordAddr: data.records[i].recordAddr,
                                callId: data.records[i].callId,
                                visitorId: data.records[i].visitorId,
                                outerId: data.records[i].outerId,
                                duration: data.records[i].duration,
                                isAnswer: data.records[i].isAnswer,
                                seatId: data.records[i].seatId,
                                seatUserId: data.records[i].seatUserId,
                                seatUserName: data.records[i].seatUserName,
                                seatUser: data.records[i].seatUser,
                                isNewDevice: data.records[i].isNewDevice,
                                returnVisitStatus: data.records[i].returnVisitStatus,
                                callOpenId: data.records[i].callOpenId,
                                address: data.records[i].address,
                                lng: data.records[i].lng,
                                lat: data.records[i].lat,
                                nickName: data.records[i].nickName,
                                avatarUrl: data.records[i].avatarUrl,
                                gender: data.records[i].gender,
                                callCurentStatus: data.records[i].callCurentStatus,
                                createTime: data.records[i].createTime,
                                createUserId: data.records[i].createUserId,
                                createUser: data.records[i].createUser,
                                lastUpdateTime: data.records[i].lastUpdateTime,
                                lastUpdateUserId: data.records[i].lastUpdateUserId,
                                lastUpdateUser: data.records[i].lastUpdateUser,
                                disabled: data.records[i].disabled,
                                callInType: "1", //呼入类型 1:视频呼救 2:120电话呼入
                                recordName: data.records[i].recordName, //录音文件的名称，实际没有用途
                                isCurrentCall: data.records[i].isCurrentCall,//是否当前电话，是的话那么true

                            }
                        });
                        //将当前电话存储起来
                        _currentCallList.push(data.records[i]);
                        console.log("getAnswerVideoCalls添加视频呼救记录后的_currentCallList:", _currentCallList);
                    }

                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                },
                function (e, url, errMsg) {
                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                    $.messager.alert('提示', '获取当前视频呼救记录异常：' + errMsg, 'error');

                });
        }
        
        /** 获取已经关联的通话记录 */
        function getCallPageListShow() {
      
            if ($("#event-id").val() == "") {
                //判断是否包含当前通话
                if (_currentCallList.length > 0) {
                    //如果没有包含当前通话，那么把当前通话显示出来;
                    $('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                        getAnswerVideoCalls(_currentCallList[0].id);
                    }else{
                        getAnswerCalls(_currentCallList[0].id);
                    }
                }
                return null;
            }
            $('#dg-call-list').datagrid('loading');//打开进度条：打开等待div
            //获取通话记录
            var getCallPageListParams = {
                "eventId": $("#event-id").val()
            };
            getPhoneAndVideoCallListByEventId(getCallPageListParams,
                function (data) {
                    $('#dg-call-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    //回显通话记录
                    //判断记录是否当前创建事件的这个通话记录
                    var isIncludeCurrentCall = false;
                    //获取正在进行的事件的列表
                    for (var i = 0; i < data.length; i++) {
                        //判断是否包含当前通话，如果是，那么显示
                        if (_currentCallList.length > 0) {
                            if (_currentCallList[0].id == data[i].id) {
                                data[i].isCurrentCall = true;
                                isIncludeCurrentCall = true;
                            } else {
                                data[i].isCurrentCall = false;
                            }
                        }

                        $('#dg-call-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                type: data[i].type,
                                number: data[i].number,
                                toNumber: data[i].toNumber,
                                callType: data[i].callType,
                                callTime: data[i].callTime,
                                handleTime: data[i].handleTime,
                                endTime: data[i].endTime,
                                handleStatus: data[i].handleStatus,
                                eventId: data[i].eventId,
                                recordAddr: data[i].recordAddr,
                                callId: data[i].callId,
                                visitorId: data[i].visitorId,
                                outerId: data[i].outerId,
                                duration: data[i].duration,
                                isAnswer: data[i].isAnswer,
                                seatId: data[i].seatId,
                                seatUserId: data[i].seatUserId,
                                seatUserName: data[i].seatUserName,
                                seatUser: data[i].seatUser,
                                isNewDevice: data[i].isNewDevice,
                                returnVisitStatus: data[i].returnVisitStatus,
                                callOpenId: data[i].callOpenId,
                                address: data[i].address,
                                lng: data[i].lng,
                                lat: data[i].lat,
                                nickName: data[i].nickName,
                                avatarUrl: data[i].avatarUrl,
                                gender: data[i].gender,
                                callCurentStatus: data[i].callCurentStatus,
                                createTime: data[i].createTime,
                                createUserId: data[i].createUserId,
                                createUser: data[i].createUser,
                                lastUpdateTime: data[i].lastUpdateTime,
                                lastUpdateUserId: data[i].lastUpdateUserId,
                                lastUpdateUser: data[i].lastUpdateUser,
                                disabled: data[i].disabled,
                                callInType: data[i].callInType, //呼入类型 1:视频呼救 2:120电话呼入
                                recordName: data[i].recordName //录音文件的名称，实际没有用途
                            }
                        });
                    }

                    //判断是否包含当前通话
                    if (!isIncludeCurrentCall && _currentCallList.length > 0) {
                        //如果没有包含当前通话，那么把当前通话显示出来
                        if(_currentInCallDetails.isVideo && _currentInCallDetails.isVideo == 1) {
                            getAnswerVideoCalls(_currentCallList[0].id);
                        }else{
                            getAnswerCalls(_currentCallList[0].id);
                        }
                    }

                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                },
                function (e, url, errMsg) {
                    //回显任务信息的结果
                    $('#dg-call-list').datagrid('loaded');//关闭loding进度条；
                    $.messager.alert('提示', '获取通话记录异常：' + errMsg, 'error');

                });
        }

        /**
         * 处理结果
         * @param value
         * @param row
         * @param index
         */
        function handleStatusFormatter(value, row, index) {
            switch (row.handleStatus) {
                case "0":
                    return "无效";
                case "1":
                    return "已处理";
                case "2":
                    return "已合并";
                case "3":
                    return "未处理";
            }
        }

        function isAnswerFormatter(value, row, index) {
            switch (row.isAnswer) {
                case "0":
                    return "未接听";
                case "1":
                    return "已接听";
            }
        }

        /**
         * 拨打电话
         * @param value
         * @param row
         * @param index
         */
        function callbackPhoneFormatter(value, row, index) {
            if (row.number) {
                return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + row.number + '\')">拨打</a>';
            }
        }

        /**
         * 视频混录播放
         * @param value
         * @param row
         * @param index
         */
        function videoPlayFormatter(value, row, index) {
            console.log("视频混录播放待开发"+row);
        }

        /**
         * 播放录音
         * @param value
         * @param row
         * @param index
         */
        function wavPlayFormatter(value, row, index) {
            var address = "";
            if (row.recordAddr != null) {
                address = row.recordAddr;
                if (row.number) {
                    return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#"  style="color: #099DFC;" onclick="playWav(\'' + address + '\',\'' + row.recordName + '\')">播放</a>';
                }
            } else {
                return '';
            }

        }

        /**
         * 来电号码
         * @param value
         * @param row
         * @param index
         */
        function callNumberFormatter(value, row, index) {
            if (row.callInType == 1) {
                return "小程序视频呼救";
            } else{
                return row.number;
            }
        }

        let throttle = null;
        /**
         * 录音窗口打开
         * @param address
         * @param name
         */
        function playWav(address, name) {
            $('#playWavAudio').attr('src', address);
            $('#wavPlayWindow').window('open');
            downloadUrl = address
            $('#download').off('click').on('click', () => {
                if (!throttle) {
                    throttle = true
                    downloadURL(downloadUrl, downloadUrl.substring(downloadUrl.lastIndexOf("/") + 1))
                    setTimeout(() => {
                        throttle = null;
                    }, 1000);
                }
            })
            // $('#playWavAudio').attr('src', address);
        }
        function downloadURL(url, name) {
            window.parent.gProxy.download(url, name)
        }
        
        /**
         * 回填地址
         * @param location
         */
        function addressBackfilling(location) {
            $('#det-address').val(location);
            // 地址设置后自动匹配区域
            autoMatchRegionByAddress(location);
            handleSearch(location)
        }

        /**
         * 中文搜索定位
         * @param value
         * @param lng
         * @param lat
         */
        function handleSearch(value, lng, lat) {
            let currentLat;
            let currentLng;
            if (lng && lat) {
                currentLat = lat;
                currentLng = lng;
                try {
                    window.parent.gProxy.sendToMap(value, String(lng), String(lat));
                } catch (err) {
                    window.parent.bsProxy.sendToMap(value, String(lng), String(lat));
                }

            } else {
                if (value) {
                    AMap.plugin('AMap.PlaceSearch', function () {
                        placeSearch = new AMap.PlaceSearch(map);
                        placeSearch.search(value, function (status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                // 获取搜索结果的范围
                                var bounds = result.bounds;
                                // 将地图视图调整为搜索结果的范围
                                map.setBounds(bounds);
                                var firstPOI = result.poiList.pois[0];
                                //自动选中
                                if (firstPOI) {
                                    _currentLocCreateEvent = firstPOI;
                                    //将经纬度写入到当前弹出框
                                    $('#event-lat').val(_currentLocCreateEvent.location.lat);
                                    $('#event-lng').val(_currentLocCreateEvent.location.lng);
                                    currentLat = _currentLocCreateEvent.location.lat;
                                    currentLng = _currentLocCreateEvent.location.lng;
                                    try {
                                        window.parent.gProxy.sendToMap(value, String(_currentLocCreateEvent.location.lng), String(_currentLocCreateEvent.location.lat));
                                    } catch (err) {
                                        window.parent.bsProxy.sendToMap(value, String(_currentLocCreateEvent.location.lng), String(_currentLocCreateEvent.location.lat));
                                    }

                                } else {
                                    _currentLocCreateEvent = null;
                                }
                            } else {
                                // 查询失败或没有结果的处理逻辑
                                console.log('查询失败或没有结果');
                            }
                        });
                    })
                }
            }
            // 刷新分站列表
            if (curAssignTaskMode == "2") {
                loadStationList("",currentLat,currentLng);
            }
        }

        // 通过电话号码查询是否已被标记骚扰电话
        function getHarassInfo(phone){
            var params = {
                "number": phone
            };
            getHarassInfoByNumber(params,
                function (data) {
                    let harassInfo = data;
                    if (harassInfo.isHarass && harassInfo.count > 0) {
                        let remark120 = $('#det-120remark').val();
                        remark120 = remark120 + phone + "已标记为骚扰电话,共拨打" + harassInfo.count + "次";
                        $('#det-120remark').val(remark120);
                    }
                }
            )
        }

        //通过电话号码获取上一次呼入出任务的信息
        function getLastCallInfo(phone){
            var params = {
                "number": phone
            };

            getLastCallInfoByNumber(params,
                function (data) {
                    if (data && (data.lastCallInTime || data.lastAddress || data.lastCallType)) {
                        $("#patient-info-lable").show()
                        $("#patient-info").show()
                        let patientInfoStr =  $("#patient-info").val();
                        var lastCallInTime = data.lastCallInTime ? '来电时间：' + data.lastCallInTime + "，" : "";
                        var lastCallType = data.lastCallType ? '呼入类型：' + data.lastCallType + "，" : "";
                        var lastAddress = data.lastAddress ? '地址：' + data.lastAddress : "";
                        patientInfoStr = patientInfoStr + lastCallInTime + lastCallType + lastAddress;
                        if (data.lastPatientInfoList && data.lastPatientInfoList.length > 0) {
                            for (let index = 0; index < data.lastPatientInfoList.length; index++) {
                                let lastPatientInfo = data.lastPatientInfoList[index];
                                var name = lastPatientInfo.name;//姓名
                                var gender = lastPatientInfo.gender ? lastPatientInfo.gender + "，" : "";//性别
                                var age = lastPatientInfo.age ? lastPatientInfo.age + "岁，" : "";//年龄
                                var majorCall = lastPatientInfo.majorCall ? lastPatientInfo.majorCall + "，" : "";//呼叫原因
                                var medicalHistory = lastPatientInfo.medicalHistory ? lastPatientInfo.medicalHistory + "，" : "";//患者病史
                                var infoWoundedDirection = lastPatientInfo.infoWoundedDirection ? lastPatientInfo.infoWoundedDirection + "，" : "";//回院后患者去向
                                var diagnose = lastPatientInfo.diagnose ? lastPatientInfo.diagnose + "，" : "";//患者损伤诊断
                                let push = name + gender + age + majorCall + medicalHistory + diagnose + infoWoundedDirection;
                                patientInfoStr = patientInfoStr ? patientInfoStr + "\n" + "患者信息：" + push : "患者信息：" + push;
                            }
                        }
                        $("#patient-info").val(patientInfoStr);
                    }
                }
            )
        }


        //电话创建事件后根据电话号码查询地址经纬度--------------------------------------------

        function pollQueryLocation() {
            setTimeout(() => {
                let number = $('#det-callin').val()
                if (!_currentInCallDetails.callUid) {
                    $.messager.alert('提示', '无法获取关联通话记录，请自行手动关联', 'info');
                }
                queryLocationFnt(number)
                pollTime = setInterval(() => {
                    let parentPhoneStats = window.parent.$('#currentCallNum').html()
                    if (parentPhoneStats !== "接听:" + _currentInCallDetails.phone) {
                        //停止定时器
                        if (pollTime) {
                            clearInterval(pollTime)
                            pollTime = null
                        }
                    }
                    queryLocationFnt(number)
                }, 3000)
            }, 2000)
        }
        function queryLocationFntsss(number) {
            num--
            this.queryLocationFnt(number)
        }
        function queryLocationFnt(number) {
            if (number) {
                queryLocation({ number }, res => {
                    let { lng, lat, address, latLngType } = res || {}
                    let index = latLngArr.indexOf(latLngType)
                    if (num >= index || (num + 1) == latLngArr.length) {
                        return;
                    }
                    num = index
                    if ($('#det-address').val()) {
                        if (latLngType == 'gps') {
                            //停止定时器
                            if (pollTime) {
                                clearInterval(pollTime)
                                pollTime = null
                            }
                        }
                        $.messager.confirm('提示', '查询到地址' + address + '，是否需要将地址填入?', function (r) {
                            if (r) {
                                //回填地址信息
                                lng && $('#event-lng').val(lng);
                                lat && $('#event-lat').val(lat);
                                address && ($('#det-address').val(address) & confirmAddress(address, lng, lat)) & autoMatchRegionByAddress(address)
                            }
                        });
                    } else {
                        //回填地址信息
                        lng && $('#event-lng').val(lng);
                        lat && $('#event-lat').val(lat);
                        address && ($('#det-address').val(address) & confirmAddress(address, lng, lat)) & autoMatchRegionByAddress(address)
                        
                    }
                },
                    (e, url, errMsg) => {
                        // messagerAlert('提示', '获取地址信息异常：' + errMsg)
                    })
            } else {
                // messagerAlert('提示', '获取地址信息失败：主叫号码为空')
            }
        }
        function messagerAlert(title, errMsg) {
            if (queryLocationStatus) {
                queryLocationStatus = false
                $.messager.alert('提示', errMsg, 'error', function () {
                    queryLocationStatus = true
                });
            }
        }
        /** 模拟gps位置信息 */
        function simulation() {
            let res = {
                "caller": "17607672337",
                "called": "12030",
                "callTime": 1678874452018,
                "latLngType": 'lbs',
                "lng": 110.62917,
                "lat": 21.645652,
                "address": "广东省茂名市化州市下郭街道下郭大道-中国农业银行(化州东山支行)",
                "shortAddress": "化州市下郭街道下郭大道-中国农业银行(化州东山支行)",
                "province": "广东省",
                "city": "茂名市",
                "district": "化州市",
                "addressCode": "440982",
                "organ": "CU",
                "status": 0,
                "time": 1678874452467,
                "locationId": 400882655,
                "locationFlag": "1",
                "callId": "1802677716382760961"
            }
            let { lng, lat, address, latLngType } = res || {}
            let index = latLngArr.indexOf(latLngType)
            if (num >= index || (num + 1) == latLngArr.length) {
                return;
            }
            num = index
            if ($('#det-address').val()) {
                if (latLngType == 'gps') {
                    //停止定时器
                    if (pollTime) {
                        clearInterval(pollTime)
                        pollTime = null
                    }
                }
                $.messager.confirm('提示', '查询到地址' + address + '，是否需要将地址填入?', function (r) {
                    if (r) {
                        //回填地址信息
                        lng && $('#event-lng').val(lng);
                        lat && $('#event-lat').val(lat);
                        address && ($('#det-address').val(address) & confirmAddress(address, lng, lat))
                    }
                });
            } else {
                //回填地址信息
                lng && $('#event-lng').val(lng);
                lat && $('#event-lat').val(lat);
                address && ($('#det-address').val(address) & confirmAddress(address, lng, lat))
            }
        }
        /**
         * 将yyyyMMddHHmmss转换为yyyy年MM月dd日 HH时mm分
         * @param time
         */
        function convertTime(time) {
            let timeArr = time.split('')
            let y = timeArr.slice(0, 4).join('')
            let m = timeArr.slice(4, 6).join('')
            let d = timeArr.slice(6, 8).join('')
            let hh = timeArr.slice(8, 10).join('')
            let mm = timeArr.slice(10, 12).join('')
            let ss = timeArr.slice(12, 14).join('')
            return y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
        }
        initSelect()
        initAreaSelect()
        
        // 初始化地址自动匹配功能，需要在区域数据加载完成后调用
        setTimeout(function() {
            initAddressAutoMatch();
        }, 1000);
        
        /**
         * 根据症状判断的选择更新呼叫原因
         */
        function updateMajorCallFromSymptom() {
            var select1Value = $('#select1').val();
            var select2Value = $('#select2').val();
            var majorCallText = '';
            
            if (select1Value && select1Value !== '') {
                // 获取第一个下拉框的文本
                var select1Text = symptomData[select1Value].t;
                majorCallText = select1Text;
                
                // 如果第二个下拉框也有选择
                if (select2Value && select2Value !== '' && symptomData[select1Value].c && symptomData[select1Value].c[select2Value]) {
                    var select2Text = symptomData[select1Value].c[select2Value].t;
                    majorCallText += ',' + select2Text;
                }
            }
            
            // 更新呼叫原因输入框
            if (majorCallText) {
                $('#det-major').val(majorCallText);
            }
        }
        /** 初始化病状数据，渲染下拉框 */
        function initSelect(){
            // 根据返回的数据生成第一个下拉框的选项
            var select1 = document.getElementById('select1');
            var select2 = document.getElementById('select2');
            select1.innerHTML = '<option value="">请选择分类</option>';
            select2.innerHTML = '<option value="">请选择分类</option>';
            for (var i = 0; i < symptomData.length; i++) {
                var option = document.createElement('option');
                option.value = i;
                option.innerHTML = symptomData[i].t;
                select1.appendChild(option);
            }

            // 监听第一个下拉框的选择事件
            $('#select1').on('change', function() {
                var selectedValue = $(this).val();
                // 判断选择的值，决定是否显示第二个下拉框
                if (selectedValue !== '' && symptomData[selectedValue].c) {
                    // 清空第二个下拉框的选项
                    var select2 = document.getElementById('select2');
                    select2.innerHTML = '<option value="">请选择分类</option>';

                    // 根据返回的数据生成第二个下拉框的选项
                    for (var i = 0; i < symptomData[selectedValue].c.length; i++) {
                        var option = document.createElement('option');
                        option.value = i;
                        option.innerHTML = symptomData[selectedValue].c[i].t;
                        select2.appendChild(option);
                    }

                    // 显示第二个下拉框
                    document.getElementById('select2').style.display = '';
                } else {
                    // 隐藏第二个下拉框
                    document.getElementById('select2').style.display = 'none';

                    // 清空第二个下拉框的选项
                    document.getElementById('select2').innerHTML = '';
                }
                
                // 更新呼叫原因
                updateMajorCallFromSymptom();
            })

            // 监听第二个下拉框的选择事件
            $('#select2').on('change', function() {
                // 更新呼叫原因
                updateMajorCallFromSymptom();
            });

        }
        
        /**
         * 初始化国籍和外籍人员的联动功能
         */
        function initCountryForeignLinkage() {
            // 国籍选择变化时的处理
            $('#det-country').on('change', function() {
                var selectedCountry = $(this).val();
                var selectedText = $(this).find('option:selected').text();
                
                // 如果选择了海外相关的国籍，自动勾选外籍人员
                if (selectedCountry === '3') {
                    $('#createForeign').prop('checked', true);
                } else {
                    // 如果选择了中国，取消勾选外籍人员
                    $('#createForeign').prop('checked', false);
                }
            });
            
            // 外籍人员复选框变化时的处理
            $('#createForeign').on('change', function() {
                var isChecked = $(this).is(':checked');
                
                if (isChecked) {
                    // 如果勾选了外籍人员，自动选择海外
                    var $countrySelect = $('#det-country');
                    var found = false;
                    
                    // 查找海外选项
                    $countrySelect.find('option').each(function() {
                        var optionValue = $(this).val();
                        var optionText = $(this).text();
                        if (optionValue === '3') {
                            $countrySelect.val(optionValue);
                            found = true;
                            return false; // 跳出循环
                        }
                    });
                } else {
                    // 如果取消勾选外籍人员，自动选择中国
                    var $countrySelect = $('#det-country');
                    $countrySelect.find('option').each(function() {
                        var optionValue = $(this).val();
                        var optionText = $(this).text();
                        if (optionValue === '0') {
                            $countrySelect.val(optionValue);
                            return false; // 跳出循环
                        }
                    });
                }
            });
        }

        var phoneTree =null
        var isEdit = false
        /**
         * 通信录弹窗
         * @param flag
         */
        function phoneList(flag) {
          isEdit = flag
          $('#phone-list').window({
            title: `<div>选择通讯录</div>`
          });
          let phoneArr = []
          getPhoneList({}, function (res) {
            for(let i=0;i<res.length;i++){
              let parent = {
                title:res[i].groupName,
                id:res[i].id,
                children:[]
              }
              for(let c=0;c<res[i].phoneBooks.length;c++){
                let itemChild = res[i].phoneBooks[c]
                let child = {
                  title:itemChild.contactor,
                  id:itemChild.id,
                  number:itemChild.number
                }

                parent.children.push(child)
              }
              phoneArr.push(parent)
            }

            layui.use(function () {
              phoneTree = layui.tree;
              var layer = layui.layer;
              var util = layui.util;
              // 渲染
              phoneTree.render({
                elem: '#phone-book',
                data: phoneArr,
                id: 'phone-list',
                showCheckbox: true,
                edit: [], // 开启节点的右侧操作图标

              });
            });
            $('#phone-list').window('open')
          }, function (e, url, errMsg) {
            //失败做处理
            $.messager.alert('提示', errMsg);
          })
        }

        /** 关闭通讯录弹窗 */
        function cancelPhoneList(){
          $('#phone-list').window('close')
        }

        function dateTimeFormatter(value, row, index) {
            if (!value) return '';
            let str = value.toString();
            if (str.length != 14) return value;
            
            let year = str.substring(0,4);
            let month = str.substring(4,6); 
            let day = str.substring(6,8);
            let hour = str.substring(8,10);
            let minute = str.substring(10,12);
            let second = str.substring(12,14);
            
            return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
        }
    /**
     * 判断呼叫类型是否为直接完成且不保存事件，只保存通话记录，不需要派车的事件类型
     * 使用动态获取的呼叫类型配置数据进行判断
     * @param {string} callType 呼叫类型代码
     * @returns {boolean} 是否为直接完成类型（不需要创建事件的类型）
     */
    function isDirectCompleteCallType(callType) {
        
        // 在呼叫类型配置列表中查找对应的配置
        var callTypeConfig = callTypeShouldCreateEventList.find(function(item) {
            return item.eventCallType === callType;
        });
        
        if (callTypeConfig) {
            // 如果找到配置，返回相反的值：shouldCreateEvent为false表示直接完成
            var isDirectComplete = !callTypeConfig.shouldCreateEvent;
            console.log('呼叫类型判断结果:', {
                callType: callType,
                callTypeName: callTypeConfig.eventCallTypeName,
                shouldCreateEvent: callTypeConfig.shouldCreateEvent,
                isDirectComplete: isDirectComplete
            });
            return isDirectComplete;
        } else {
            // 如果未找到配置，默认认为不需要创建事件
            console.warn('未找到呼叫类型配置:', callType, '默认认为不需要创建事件');
            return false;
        }
    }


    /**
     * 安全设置datetimebox的值，避免组件未初始化的错误
     * @param {string} selector - jQuery选择器
     * @param {string} value - 要设置的值
     * @param {string} errorMsg - 错误提示信息
     */
    function safeSetDatetimeboxValue(selector, value, errorMsg) {
        setTimeout(function() {
            try {
                $(selector).datetimebox('setValue', value);
            } catch (e) {
                console.warn(errorMsg || 'Datetimebox 设置值失败:', e);
                $(selector).datetimebox({});
                $(selector).datetimebox('setValue', value);
            }
        }, 100);
    }

    /**
     * 初始化所属区域下拉框
     * 通过getAllStationRegionList接口获取区域数据
     */
    function initAreaSelect() {
        getAllStationRegionList(
            function (data) {
                var $detArea = $('#det-area');
                // 清空现有选项，保留默认的"请选择"
                $detArea.html('<option value="">请选择</option>');
                
                // 遍历返回的区域数据，添加选项
                if (data && Array.isArray(data)) {
                    data.forEach(function(region) {
                        var option = '<option value="' + region.id + '">' + region.regionName + '</option>';
                        $detArea.append(option);
                    });
                }
                
                // 添加change事件监听器，联动station-region
                $detArea.off('change.areaLinkage').on('change.areaLinkage', function() {
                    linkageStationRegion($(this).val());
                });
                
                // 检查是否已有选中的区域值，如果有则触发联动
                setTimeout(function() {
                    var currentValue = $detArea.val();
                    if (currentValue) {
                        linkageStationRegion(currentValue);
                    }
                }, 300);
            },
            function (e, url, errMsg) {
                console.error('加载区域数据失败:', errMsg);
                $.messager.alert('提示', '加载区域数据失败：' + errMsg, 'error');
            }
        );
    }

    /**
     * 联动station-region选择
     * @param {string} selectedRegionId - 选中的区域ID
     */
    function linkageStationRegion(selectedRegionId) {
        // 如果没有选择区域，则不处理
        if (!selectedRegionId) {
            return;
        }
        
        // 判断当前显示的是哪种调度模式
        var $stationRegion = null;
        var targetSelector = '';
        
        if ($('#station-region').is(':visible') && $('#station-region').length > 0) {
            // vehicleList模式 (station-region)
            $stationRegion = $('#station-region');
            targetSelector = '#station-region';
        } else if ($('#station_region').is(':visible') && $('#station_region').length > 0) {
            // substationsList模式 (station_region)
            $stationRegion = $('#station_region');
            targetSelector = '#station_region';
        }
        
        if (!$stationRegion || $stationRegion.length === 0) {
            console.log('未找到可用的区域选择组件');
            return;
        }
        
        // 找到对应的li元素并触发点击
        var $targetLi = $stationRegion.find('li[id="' + selectedRegionId + '"]');
        if ($targetLi.length > 0) {
            // 先移除所有选中状态
            $stationRegion.find('li').removeClass('selected');
            // 添加选中状态并触发点击事件
            $targetLi.addClass('selected').trigger('click');
            
            console.log('已联动选择区域：' + selectedRegionId + ' (使用' + targetSelector + ')');
        } else {
            // 如果找不到对应的区域，选择"全部"
            var $allLi = $stationRegion.find('li[id=""]').first();
            if ($allLi.length > 0) {
                $stationRegion.find('li').removeClass('selected');
                $allLi.addClass('selected').trigger('click');
                console.log('未找到对应区域，已选择"全部" (使用' + targetSelector + ')');
            }
        }
    }

    /**
     * 根据地址自动匹配所属区域
     * @param {string} address - 输入的地址
     * @param {boolean} autoSelect - 是否自动选择匹配到的区域，默认为true
     * @returns {object|null} 匹配到的区域信息，包含id和regionName，如果没有匹配到则返回null
     */
    function autoMatchRegionByAddress(address, autoSelect = true) {
        if (!address || typeof address !== 'string') {
            return null;
        }
        
        // 清理地址字符串，去除多余的空格和特殊字符
        var cleanAddress = address.trim().replace(/\s+/g, '');
        
        // 获取当前区域选项数据
        var $detArea = $('#det-area');
        var regionOptions = [];
        
        $detArea.find('option').each(function() {
            var $option = $(this);
            var regionId = $option.val();
            var regionName = $option.text();
            
            if (regionId && regionName && regionName !== '请选择') {
                regionOptions.push({
                    id: regionId,
                    regionName: regionName,
                    // 提取区域关键词，去除"区"、"县"、"市"等后缀
                    keywords: extractRegionKeywords(regionName)
                });
            }
        });
        
        if (regionOptions.length === 0) {
            console.warn('未找到可用的区域选项数据');
            return null;
        }
        
        // 执行地址匹配算法
        var matchedRegion = null;
        var maxMatchScore = 0;
        
        for (var i = 0; i < regionOptions.length; i++) {
            var region = regionOptions[i];
            var matchScore = calculateAddressMatchScore(cleanAddress, region);
            
            if (matchScore > maxMatchScore && matchScore > 0.3) { // 设置最低匹配阈值
                maxMatchScore = matchScore;
                matchedRegion = region;
            }
        }
        
        if (matchedRegion) {
            console.log('地址匹配结果:', {
                address: address,
                matchedRegion: matchedRegion.regionName,
                matchScore: maxMatchScore
            });
            
            // 如果需要自动选择，则设置区域下拉框的值
            if (autoSelect) {
                $detArea.val(matchedRegion.id);
                // 触发change事件以便联动其他组件
                $detArea.trigger('change');
            }
            
            return {
                id: matchedRegion.id,
                regionName: matchedRegion.regionName,
                matchScore: maxMatchScore
            };
        }
        
        console.log('未找到匹配的区域，地址:', address);
        return null;
    }
    
    /**
     * 提取区域名称的关键词
     * @param {string} regionName - 区域名称
     * @returns {array} 关键词数组
     */
    function extractRegionKeywords(regionName) {
        if (!regionName) return [];
        
        var keywords = [];
        var name = regionName.trim();
        
        // 去除常见的行政区划后缀
        var suffixes = ['市', '区', '县', '镇', '街道', '乡', '村'];
        for (var i = 0; i < suffixes.length; i++) {
            if (name.endsWith(suffixes[i])) {
                name = name.substring(0, name.length - suffixes[i].length);
                break;
            }
        }
        
        // 添加完整名称作为主要关键词
        keywords.push(name);
        keywords.push(regionName); // 也保留原始名称
        
        // 如果名称较长，尝试提取子关键词
        if (name.length > 2) {
            // 添加2-3字的子串作为关键词
            for (var j = 0; j <= name.length - 2; j++) {
                keywords.push(name.substring(j, j + 2));
                if (j <= name.length - 3) {
                    keywords.push(name.substring(j, j + 3));
                }
            }
        }
        
        return keywords;
    }
    
    /**
     * 计算地址与区域的匹配度
     * @param {string} address - 清理后的地址
     * @param {object} region - 区域信息对象
     * @returns {number} 匹配分数 (0-1)
     */
    function calculateAddressMatchScore(address, region) {
        var score = 0;
        var keywords = region.keywords;
        
        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            if (!keyword) continue;
            
            // 完全匹配得分最高
            if (address.indexOf(keyword) !== -1) {
                var keywordLength = keyword.length;
                var addressLength = address.length;
                
                // 根据关键词长度和在地址中的位置计算得分
                var baseScore = keywordLength / addressLength;
                
                // 如果关键词出现在地址开头，额外加分
                if (address.indexOf(keyword) === 0) {
                    baseScore *= 1.5;
                }
                
                // 如果是完整的区域名称匹配，额外加分
                if (keyword === region.regionName || keyword === region.regionName.replace(/[市区县镇]$/, '')) {
                    baseScore *= 2;
                }
                
                score = Math.max(score, baseScore);
            }
        }
        
        return Math.min(score, 1); // 确保分数不超过1
    }
    


    /**
     * 初始化获取呼叫类型创建事件判断列表
     * 页面加载时调用接口获取所有呼叫类型的创建事件判断配置
     */
    function initCallTypeShouldCreateEventList() {
        console.log('开始初始化呼叫类型创建事件判断列表...');
        
        // 调用接口获取呼叫类型创建事件判断列表
        getCallTypeShouldCreateEventList(
            function(response) {
                // 成功回调
                if (response) {
                    callTypeShouldCreateEventList = response;
                    // 打印每个呼叫类型的详细信息
                    callTypeShouldCreateEventList.forEach(function(item, index) {
                        console.log('呼叫类型 ' + (index + 1) + ':', {
                            eventCallType: item.eventCallType,
                            eventCallTypeName: item.eventCallTypeName,
                            shouldCreateEvent: item.shouldCreateEvent
                        });
                    });
                } else {
                    console.warn('接口返回数据格式异常:', response);
                    callTypeShouldCreateEventList = [];
                }
            },
            function(error, url, errorMessage) {
                // 失败回调
                console.error('获取呼叫类型创建事件判断列表失败:', {
                    error: error,
                    url: url,
                    errorMessage: errorMessage
                });
                
                // 失败时设置为空数组，确保不影响其他功能
                callTypeShouldCreateEventList = [];
                
                // 可选：显示用户友好的错误提示
                $.messager.alert('提示', '获取呼叫类型配置失败：' + errorMessage, 'warning');
            }
        );
    }

    /**
     * 为地址输入框添加自动匹配功能
     * 在地址输入完成后（失焦或按回车）自动匹配区域
     */
    function initAddressAutoMatch() {
        var $addressInput = $('#det-address');
        var $pickupAddressInput = $('#pickup-address');
        
        // 防抖处理，避免频繁触发
        var matchTimer;
        var pickupMatchTimer;
        
        function triggerAutoMatch() {
            var address = $addressInput.val();
            if (address && address.trim().length >= 2) {
                clearTimeout(matchTimer);
                matchTimer = setTimeout(function() {
                    autoMatchRegionByAddress(address);
                }, 500); // 500ms延迟
            }
        }
        
        function triggerPickupAutoMatch() {
            var address = $pickupAddressInput.val();
            if (address && address.trim().length >= 2) {
                clearTimeout(pickupMatchTimer);
                pickupMatchTimer = setTimeout(function() {
                    autoMatchRegionByAddress(address);
                }, 500); // 500ms延迟
            }
        }
        
        // 现场地址绑定事件
        $addressInput.off('blur.autoMatch').on('blur.autoMatch', triggerAutoMatch);
        $addressInput.off('keypress.autoMatch').on('keypress.autoMatch', function(e) {
            if (e.which === 13) { // 回车键
                triggerAutoMatch();
            }
        });
        
        // 等车地址绑定事件
        $pickupAddressInput.off('blur.autoMatch').on('blur.autoMatch', triggerPickupAutoMatch);
        $pickupAddressInput.off('keypress.autoMatch').on('keypress.autoMatch', function(e) {
            if (e.which === 13) { // 回车键
                triggerPickupAutoMatch();
            }
        });
        
        console.log('现场地址和等车地址自动匹配功能已初始化');
    }

    /**
     * 获得首次事件列表数据
     * @param pageNum
     * @param pageSize
     */
    function getFirstEventListDatas(pageNum, pageSize) {
        try {
            if (pageNum == null) {
                pageNum = 1;
            }
            if (pageSize == null) {
                pageSize = size;
            }
        } catch (e) {
            pageNum = 1;
            pageSize = size;
        }

        var startCreateTimeVal = $('#firstStartCreateTime').datetimebox('getValue');
        var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

        var endCreateTimeVal = $('#firstEndCreateTime').datetimebox('getValue');
        var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

        //打开进度条：
        $('#dg-first-event-pages').datagrid('loading');//打开等待div

        var params = {
            "pageNum": pageNum,
            "pageSize": pageSize,
            "startCreateTime": startCreateTime,
            "endCreateTime": endCreateTime,
            "majorCall": $("#firstMajorCall").val(),
            "address": $('#firstAddress').val(),
            "eventStatusList": $('#firstEventStatus').combobox('getValues'),
        };
        getEventPages(params, function (data) {
            $('#dg-first-event-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
            for (var i = 0; i < data.content.length; i++) {
                $('#dg-first-event-pages').datagrid('insertRow', {
                    index: i,  // 索引从0开始
                    row: {
                        id: data.content[i].id,
                        eventId: data.content[i].eventId,
                        eventStatus: data.content[i].eventStatus,
                        eventStatusStr: data.content[i].eventStatusStr,
                        callIn: data.content[i].callIn,
                        callInTimes: data.content[i].callInTimes,
                        createTime: data.content[i].createTime,
                        majorCall: data.content[i].majorCall.replace("_", " "),
                        eventSrcCode: data.content[i].eventSrcCode,
                        eventSrcName: data.content[i].eventSrcName,
                        callTypeCode: data.content[i].callTypeCode,
                        lng: data.content[i].lng,
                        lat: data.content[i].lat,
                        callTypeName: data.content[i].callTypeName,
                        contact: data.content[i].contact,
                        contacter: data.content[i].contacter,
                        addressWait: data.content[i].addressWait,
                        address: data.content[i].address,
                        createUser: data.content[i].createUser,
                        eventCreateSeatCode: data.content[i].eventCreateSeatCode,
                        eventCreateSeatUserId: data.content[i].eventCreateSeatUserId,
                        eventCreateSeatUserName: data.content[i].eventCreateSeatUserName,
                        eventCreateSeatUser: data.content[i].eventCreateSeatUser,
                        processCount: data.content[i].processCount,
                        runProcessCount: data.content[i].runProcessCount,
                        finishedProcessCount: data.content[i].finishedProcessCount,
                        cancelProcessCount: data.content[i].cancelProcessCount,
                        centerRemark: data.content[i].centerRemark,
                        callRegionId: data.content[i].callRegionId,
                        callRegionName: data.content[i].callRegionName
                    }
                });
            }
            $("#dg-first-event-pages").datagrid({
                rowStyler: function (index,row) {
                        switch (row.eventStatus) {
                            case '1':
                                return 'background-color:#dad6d557;';//灰色
                            case "2":
                                return 'background-color:#f9a296ad;';//红色
                            case "3":
                                return 'background-color:#dad6d557;';//灰色
                            case "4":
                                return 'background-color:#dad6d557;';//灰色
                        }
                    },
            });
            setFirstEventPage(data.total || 0, pageNum)
            $('#dg-first-event-pages').datagrid('loaded');//关闭loding进度条；
        }, function (e, url, errMsg) {
            $('#dg-first-event-pages').datagrid('loaded');//关闭loding进度条；
            //失败才做处理
            //$.messager.alert('异常', '异常信息：' + errMsg);
        });
    }

    /**
     * 设置首次事件分页
     * @param total
     * @param pageNum
     */
    function setFirstEventPage(total, pageNum) {
        var pg = $("#dg-first-event-pages").datagrid("getPager");
        if (pg) {
            $(pg).pagination({
                total,
                pageSize: size,
                pageNumber: pageNum,
                pageList: [10, 20, 30, 40, 50],
                onBeforeRefresh: function (pageNumber, pageSize) {
                },
                onRefresh: function (pageNumber, pageSize) {
                },
                onChangePageSize: function (pageSize) {
                    page = 1
                    size = pageSize
                    getFirstEventListDatas(page, pageSize);
                },
                onSelectPage: function (pageNumber, pageSize) {
                    page = pageNumber
                    size = pageSize
                    getFirstEventListDatas(pageNumber, pageSize);
                }
            });
        }
    }

    /**
     * 选择首次事件（双击事件）
     * @param index 行索引
     * @param row 行数据
     */
    function selectFirstEvent(index, row) {
        console.log('选择首次事件:', row);
        
        // 关闭窗口
        $('#firstEventSelectWindow').window('close');
        
        // 获取首次事件的详细信息用于回填
        getEventById(row.eventId, function(eventData) {
            if (eventData) {
                // 回填数据
                fillFirstEventData(eventData);
                
                // 显示关联信息
                showFirstEventInfo(eventData);
                
                // 存储首次事件ID
                $('#firstEventId').val(eventData.id);
                
            } else {
                $.messager.alert('错误', '获取首次事件详情失败', 'error');
            }
        }, function(e, url, errMsg) {
            $.messager.alert('错误', '获取首次事件详情失败：' + errMsg, 'error');
        });
    }

    /**
     * 回填首次事件数据
     * @param eventData 首次事件数据
     */
    function fillFirstEventData(eventData) {
        // 回填呼救类型
        if (eventData.callType) {
            $('#evd-call-type').val(eventData.callType);
        }
        
        // 回填所属区域
        if (eventData.callRegionId) {
            $('#det-area').val(eventData.callRegionId);
            // 触发区域联动
            setTimeout(function() {
                linkageStationRegion(eventData.callRegionId);
            }, 300);
        }
        
        // 回填现场地址
        if (eventData.address) {
            $('#det-address').val(eventData.address);
            // 如果有经纬度也回填
            if (eventData.lat && eventData.lng) {
                $('#event-lat').val(eventData.lat);
                $('#event-lng').val(eventData.lng);
            }
        }
        
        console.log('已回填首次事件数据:', {
            callType: eventData.callType,
            callRegionId: eventData.callRegionId,
            address: eventData.address
        });
    }

    /**
     * 显示首次事件关联信息
     * @param eventData 首次事件数据
     */
    function showFirstEventInfo(eventData) {
        var html = '<div style="border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin: 5px 0; background-color: #f9f9f9; display: flex; justify-content: space-between; align-items: center; width: calc(100% - 22px);">';
        
        // 左边信息内容
        html += '<div style="font-size: 13px;">';
        html += '<i class="fa fa-link" style="margin-right: 5px; color: #2c5aa0;"></i>';
        html += '<strong>已关联首次事件：</strong>';
        html += '<span style="margin-left: 10px;"><strong>现场地址：</strong>' + (eventData.address || '-') + '</span>';
        html += '<span style="margin-left: 15px;"><strong>呼叫原因：</strong>' + (eventData.majorCall ? eventData.majorCall.replace("_", " ") : '-') + '</span>';
        html += '<span style="margin-left: 15px;"><strong>联系电话：</strong>' + (eventData.contact || '-') + '</span>';
        html += '</div>';
        
        // 右边删除按钮
        html += '<div>';
        html += '<a href="javascript:removeFirstEventAssociation();" class="easyui-linkbutton" data-options="iconCls:\'icon-remove\'" style="font-size: 12px;">取消</a>';
        html += '</div>';
        
        html += '</div>';
        
        $('#relevanceFirstEventPanel').html(html);
        
        // 重新解析linkbutton
        $.parser.parse('#relevanceFirstEventPanel');

    }

    /**
     * 删除首次事件关联
     */
    function removeFirstEventAssociation() {
        $.messager.confirm('确认', '确定要删除首次事件关联吗？', function(r) {
            if (r) {
                // 清空首次事件ID
                $('#firstEventId').val('');
                // 清空显示区域
                $('#relevanceFirstEventPanel').html('');
                
                $.messager.alert('提示', '已删除首次事件关联', 'info');
            }
        });
    }

    /**
     * 打开报停车辆编辑界面
     * @param callId 通话记录ID
     */
    function openVehicleStopEdit(callId) {
        // 创建报停编辑窗口（如果不存在）
        if ($('#vehicleStopEditWindow').length === 0) {
            $('body').append(
                '<div id="vehicleStopEditWindow" class="easyui-window" title="报停记录" ' +
                'style="width:900px;height:620px" data-options="iconCls:\'icon-save\',modal:true,closed:true">' +
                '<iframe id="vehicleStopEditIframe" name="vehicleStopEditIframe" scrolling="auto" frameborder="0" ' +
                'style="width:100%;height:100%;"></iframe>' +
                '</div>'
            );
            $('#vehicleStopEditWindow').window();
        }
        
        // 设置iframe的src，传入callId参数
        var href = "vehicleStopEdit.html";
        if (callId) {
            href += "?callId=" + callId;
        }
        
        $('#vehicleStopEditIframe').attr('src', href);
        $('#vehicleStopEditWindow').window('open');
    }

    /**
     * 刷新报停记录列表（供子页面调用）
     */
    function refreshStopRecordList() {
        // 报停记录保存成功，关闭当前tab
        console.log('报停记录保存成功，关闭当前tab');
        $.messager.alert('提示', '报停车辆保存成功，请进入车辆报停页面查看。', 'info', function() {
            // 关闭当前tab
            try {
                // 先关闭编辑窗口
                $('#vehicleStopEditWindow').window('close');
                // 获取当前tab的标题
                var currentTab = window.parent.$('#main-tabs').tabs('getSelected');
                var title = currentTab.panel('options').title;
                // 关闭当前选中的tab
                window.parent.closeTab(title);
            } catch (e) {
                console.error('关闭tab失败:', e);
            }
        });
    }
</script>
</body>

</html>
