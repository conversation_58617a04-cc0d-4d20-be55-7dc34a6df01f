# 120急救指挥系统 - 关联首次事件功能说明

## 功能概述

关联首次事件功能允许调度员在创建新事件时，关联到之前已存在的首次事件，实现事件信息的快速回填和关联管理。这对于处理重复呼救、后续呼救等场景非常有用。

## 功能入口

在事件受理界面的右上角，点击"关联首次事件"按钮即可打开功能窗口。

## 操作流程

### 1. 打开关联首次事件窗口
- 在事件受理界面点击"关联首次事件"按钮
- 系统自动打开"选择关联首次事件"对话框
- 自动加载最近的事件列表供选择

### 2. 搜索和筛选事件
可通过以下条件进行事件搜索：
- **创建时间**：选择开始和结束时间范围
- **呼叫原因**：输入关键词进行模糊搜索
- **地址**：输入地址关键词进行搜索
- **任务状态**：选择特定的事件状态
  - 全部
  - 未调度-预约
  - 未调度-落单
  - 未调度-待派
  - 已调度
  - 已完成
  - 已撤销

### 3. 选择首次事件
- 在事件列表中找到要关联的首次事件
- **双击事件行**即可选择该事件作为首次事件
- 系统自动关闭选择窗口

### 4. 数据自动回填
选择首次事件后，系统会自动回填以下信息：
- **呼叫类型**：从首次事件复制呼叫类型
- **所属区域**：从首次事件复制所属区域，并触发分站联动
- **现场地址**：从首次事件复制现场地址及经纬度坐标

### 5. 关联信息显示
选择首次事件后，在事件受理界面会显示关联信息卡片，包含：
- 事件编号
- 呼叫类型
- 所属区域
- 现场地址
- 创建时间
- 删除关联按钮

### 6. 删除关联
- 点击关联信息卡片右下角的"删除关联"按钮
- 确认删除后，清除关联关系和显示信息

### 7. 保存事件
- 点击"保存事件"按钮时，关联的首次事件ID会一并保存到数据库
- 后端可通过`firstEventId`字段获取关联的首次事件信息

## 技术实现

### 前端组件
- **窗口ID**：`firstEventSelectWindow`
- **数据表格**：`dg-first-event-pages`
- **关联显示区域**：`relevanceFirstEventPanel`
- **隐藏字段**：`firstEventId`

### 核心函数
- `relevanceFirstEventBtn()`：打开关联首次事件窗口
- `getFirstEventListDatas()`：获取首次事件列表数据
- `selectFirstEvent()`：选择首次事件（双击触发）
- `fillFirstEventData()`：回填首次事件数据
- `showFirstEventInfo()`：显示关联信息
- `removeFirstEventAssociation()`：删除关联关系

### 数据传输
```javascript
// 保存事件时传递的首次事件ID字段
newEve.firstEventId = $('#firstEventId').val();
```

## 界面截图说明

### 1. 关联首次事件按钮
位于事件受理界面右上角，与"通话关联事件"按钮并列显示。

### 2. 选择首次事件窗口
- 尺寸：1240px × 700px
- 包含搜索条件区域和数据表格
- 支持分页显示
- 双击行选择事件

### 3. 关联信息显示
- 样式：带边框的卡片形式
- 背景色：浅灰色 (#f9f9f9)
- 包含关联图标和删除按钮
- 信息以标签形式横向排列

## 注意事项

1. **数据回填**：选择首次事件后会自动回填部分字段，用户可以根据需要进行调整
2. **关联关系**：关联关系仅在当前事件中生效，不影响首次事件本身
3. **重复关联**：可以重复选择不同的首次事件，新选择会覆盖之前的关联
4. **数据完整性**：确保首次事件的数据完整，避免回填空值
5. **权限控制**：建议后端根据用户权限控制可关联的事件范围

## 扩展功能建议

1. **智能推荐**：根据呼叫号码、地址等信息自动推荐可能的首次事件
2. **关联历史**：显示该号码或地址的历史关联记录
3. **批量关联**：支持一次关联多个首次事件
4. **关联统计**：统计分析关联事件的数据报表

## 故障排除

### 常见问题
1. **窗口无法打开**：检查窗口ID是否正确，确保easyUI已正确加载
2. **数据不显示**：检查`getEventPages`接口是否正常返回数据
3. **回填失败**：检查选择的事件数据是否完整
4. **保存失败**：检查`firstEventId`字段是否正确传递到后端

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console中的日志输出
3. 检查Network中的接口调用情况
4. 验证DOM元素是否正确生成

## 更新日志

- **v1.0.0** (2024-12-19)
  - 初始版本发布
  - 实现基础的关联首次事件功能
  - 支持数据回填和关联信息显示 