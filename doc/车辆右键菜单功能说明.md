# 车辆右键菜单功能说明

## 概述
副屏幕地图调度页面的车辆列表支持右键菜单操作，提供多种车辆管理功能。

## 菜单项说明

### 通话功能
- **与分站通话**：拨打车辆所属分站的联系电话
- **与车辆通话**：拨打车头平板或车辆联系电话
- **与车组人员通话**：显示车组人员（医生、护士、司机、护工）通话界面，支持分别拨打各岗位人员电话

### 任务管理
- **补发**：对非待派状态的车辆进行任务补发操作

### 车辆状态管理
- **修改车况**：仅限待派车辆（状态为0）可以修改车况，包括：
  - ✅ 正常
  - 🔧 维护中
  - 🏥 驻场中
  - ⏰ 未值班

### 其他功能
- **轨迹回放**：查看车辆历史轨迹

## 使用方法

### 基本操作
1. 在车辆列表中右键点击任意车辆
2. 从弹出的右键菜单中选择所需功能
3. 根据提示完成相应操作

### 修改车况操作流程
1. 右键点击**待派状态**的车辆
2. 选择"修改车况"菜单项
3. 在弹出的对话框中选择新的车况状态
4. 点击"确定"保存修改
5. 系统将自动刷新车辆状态显示

## 权限说明

### 状态限制
- **待派车辆**（状态0）：可以修改车况
- **运行中车辆**（状态非0）：可以通话、补发、查看轨迹，但不能修改车况

### 功能可用性
- **与分站通话**：待派状态时可用
- **与车辆通话**：运行状态时可用  
- **与车组人员通话**：运行状态时可用
- **补发**：运行状态时可用
- **修改车况**：仅待派状态时可用
- **轨迹回放**：始终可用

### 车辆派遣限制
- **可派遣条件**：车况正常（hCondition=0）且待命状态（status=0）
- **不可派遣情况**：
  - 车况为维护中、驻场中、未值班
  - 车辆状态非待命（已派出、执行任务等）
- **显示规则**：
  - 正常待命车辆：显示"任务派遣"按钮
  - 非正常车况：显示车况文字（红色标识）
  - 其他状态：不显示按钮或文字

## 车组人员通话详情

### 显示信息
- 医生、护士、司机、护工四个岗位
- 显示姓名、电话号码、工号等信息
- 红色边框标识无电话号码的人员

### 操作说明
- 点击有电话的人员可直接拨打电话
- 点击无电话的人员显示配置提示
- 支持待命车辆显示默认车组信息

## 技术实现

### 接口调用
- `updateMobileCondition`：修改车况
- `getMobileProcessById`：获取车组人员信息
- `createRoom`：创建视频通话房间

### 状态管理
- 车况状态：0-正常，1-维护中，2-驻场中，3-未值班
- 车辆状态：0-待派，其他-运行中
- 自动同步主屏幕车辆状态

## 注意事项

1. **修改车况限制**：只有待派状态的车辆才能修改车况，确保运行中车辆状态稳定
2. **权限检查**：系统会根据车辆当前状态动态启用/禁用相应菜单项
3. **数据同步**：修改车况后会自动通知主屏幕刷新车辆列表
4. **错误处理**：操作失败时会显示详细错误信息
5. **用户体验**：提供确认对话框避免误操作

## 更新日志

### 2024年最新版本
- 将"车辆上班/下班"功能改为"修改车况"
- 参考主屏幕修改车况业务逻辑实现
- 增强了车辆状态管理的精确性
- 优化了用户界面和操作流程 