# 事件受理界面配置方案

## 1. 配置目的
实现事件受理界面的字段显示/隐藏配置，使界面更加简洁清晰。

## 2. 必需字段
以下字段为系统必需字段，不可隐藏：
- 事件来源
- 呼叫类型
- 重大事件
- 事件类型
- 事故等级
- 来电时间
- 现场地址
- 呼叫原因
- 联系电话

## 3. 可配置字段列表

以下字段可通过配置进行显示/隐藏：

### 基本信息区域
- `det-amount`: 患者人数
- `det-call-in-times`: 来电时间
- `emergency-level`: 紧急程度
- `det-area`: 所属区域

### 联系信息区域
- `det-callin`: 主叫号码（包含拨打按钮）
- `det-contact`: 联系电话（包含拨打按钮）
- `det-contacter`: 联系人  
- `createForeign`: 外籍人员

### 患者信息区域
- `det-name`: 患者姓名
- `det-gender`: 性别
- `det-age`: 年龄
- `det-country`: 国籍
- `det-race`: 民族

### 诊断和要求区域
- `symptom`: 病状判断（包含两个下拉选择框）
- `need-stretcher`: 需要担架
- `push-outer-type`: 联动类型
- `is-test`: 测试

### 备注信息区域
- `det-special-req`: 特殊要求
- `det-120remark`: 120备注
- `det-alds-summary`: ALDS汇总
- `patient-info`: 上次来电

## 4. 配置方案

```json
{
  "version": "1.0",
  "fields": {
    // 基本信息字段
    "det-amount": true,         // 患者人数 - 显示
    "det-call-in-times": true,  // 来电时间 - 显示
    "emergency-level": true,    // 紧急程度 - 显示
    "det-area": true,          // 所属区域 - 显示
    
    // 联系信息区域
    "det-callin": false,       // 主叫号码 - 隐藏
    "det-contact": true,       // 联系电话 - 显示
    "det-contacter": true,     // 联系人 - 显示
    "createForeign": true,     // 外籍人员 - 显示
    
    // 患者信息区域
    "det-name": true,          // 患者姓名 - 显示
    "det-gender": true,        // 性别 - 显示
    "det-age": true,           // 年龄 - 显示
    "det-country": false,      // 国籍 - 隐藏
    "det-race": false,         // 民族 - 隐藏
    
    // 诊断和要求字段
    "symptom": true,           // 病状判断 - 显示
    "need-stretcher": true,    // 需要担架 - 显示
    "push-outer-type": true,   // 联动类型 - 显示
    "is-test": false,          // 测试 - 隐藏
    
    // 备注字段
    "det-special-req": true,   // 特殊要求 - 显示
    "det-120remark": true,     // 120备注 - 显示
    "det-alds-summary": false, // ALDS汇总 - 隐藏
    "patient-info": true       // 上次来电 - 显示
  }
}
```

## 5. 配置说明

1. **字段包裹**：每个可配置字段都使用 `<div id="field-group-字段ID">` 包裹，包含标签、输入框、相关按钮等所有元素
2. **配置格式**：采用简单的键值对形式
   - 键名：字段ID（如 det-callin、det-country 等）
   - 值：布尔类型（true显示，false隐藏）
3. **必需字段**：不在配置中出现的字段默认永远显示（如事件来源、呼叫类型、联系电话等）
4. **整体隐藏**：隐藏字段时，标签、输入框、按钮等所有相关元素都会一起隐藏
5. **HTML结构示例**：
```html
<div id="field-group-det-callin" style="display: inline-block;">
    <span>主叫号码：</span>
    <input id="det-callin" type="text" />
    <a href="javascript:callingPhone(...)">拨打</a>
</div>
```

## 6. 实现步骤

1. 前端实现：
   - 页面加载时读取配置
   - 根据配置显示/隐藏对应字段
   - 自动调整布局（隐藏字段的空间由其他字段自动填充）

2. 配置界面：
   - 提供简单的开关控制界面
   - 实时预览效果
   - 保存配置功能

## 7. 注意事项

1. 字段隐藏规则：
   - 被隐藏的字段不会被删除，只是不显示
   - 字段的值会被保留
   - 必需字段不受配置影响

2. 布局调整：
   - 当字段被隐藏时，同行的其他字段会自动扩展
   - 保持界面整洁美观

3. 配置保存：
   - 配置修改后即时生效
   - 保存到本地配置文件 