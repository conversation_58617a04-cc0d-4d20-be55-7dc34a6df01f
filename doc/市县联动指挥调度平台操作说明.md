# 市县联动指挥调度平台操作说明

## 系统概述

市县联动指挥调度平台是一个基于高德地图的急救调度系统，主要用于实现市级和县级急救中心之间的协调联动，提供实时的车辆监控、事故任务管理、医疗资源调度等功能。

## 主要业务功能

### 1. 地图显示与导航
- **地图引擎**: 基于高德地图API 2.0
- **地图控制**: 支持缩放、平移、鹰眼视图
- **地图工具**: 测距、标注、范围绘制、路况显示
- **比例尺**: 可开启/关闭地图比例尺显示

### 2. 车辆管理与监控
- **实时定位**: 显示所有急救车辆的实时位置
- **车辆状态**: 
  - 待命状态（绿色图标）
  - 任务状态（红色图标）
  - 报停状态（蓝色图标）
  - 未值班状态（灰色图标）
- **车辆信息**: 车牌号、所属分站、速度、方向、ACC状态
- **状态过滤**: 支持按车辆状态筛选显示
- **中心过滤**: 支持按急救中心筛选车辆

### 3. 事故任务管理
- **任务显示**: 在地图上显示正在进行的事故任务
- **任务信息**: 事件ID、类型、紧急程度、现场地址、伤病员数量
- **状态管理**: 
  - 未调度-预约
  - 未调度-落单
  - 未调度-待派
  - 已调度
  - 已完成
  - 已撤销
- **联动派车**: 支持跨中心车辆调度

### 4. 医疗资源管理
- **医院信息**: 显示各医院位置、等级、床位数、急诊科状态
- **分站管理**: 显示急救分站位置和车辆配置
- **血站信息**: 血站位置、血型储备、库存状态
- **疾控中心**: 疾控中心位置、专业领域、服务状态

### 5. 通信状态监控
- **中心状态**: 实时显示各急救中心的通信状态
- **在线状态**: 绿色表示在线，红色表示离线
- **状态面板**: 可折叠的连接状态显示面板

## 界面布局

### 顶部工具栏
- **系统标题**: 120调度指挥市县联动子系统
- **功能按钮**:
  - 任务信息: 打开任务管理面板
  - 数据查询: 打开数据模块面板
  - 数据统计: 打开查询统计面板
  - 页面刷新: 刷新整个页面

### 状态指示器（顶部中央）
- **中心数量**: 显示当前中心总数，支持按中心过滤
- **车辆数量**: 显示当前车辆总数，支持按状态过滤
- **事故数量**: 显示当前事故任务数量
- **医院数量**: 显示医院总数
- **分站数量**: 显示分站总数

### 右侧地图工具栏
- **地图资源组**:
  - 车辆显示/隐藏
  - 事故任务显示/隐藏
  - 分站显示/隐藏
  - 医院显示/隐藏
  - 血站显示/隐藏
  - 疾控中心显示/隐藏

- **地图工具组**:
  - 放大/缩小
  - 比例尺开关
  - 路况信息
  - 测距工具
  - 标注工具
  - 范围工具
  - 鹰眼地图
  - 清除标注

### 左侧连接状态面板
- **中心状态**: 显示各急救中心的在线状态
- **状态指示**: 绿色圆点表示在线，红色圆点表示离线
- **面板控制**: 支持展开/折叠显示

## 操作流程

### 1. 车辆监控操作
1. 点击右侧工具栏"车辆"按钮激活车辆显示
2. 使用顶部"车辆过滤"按钮筛选特定状态的车辆
3. 点击车辆图标查看详细信息
4. 使用"中心过滤"按钮筛选特定中心的车辆

### 2. 事故任务处理
1. 点击"事故"按钮显示当前事故任务
2. 点击事故图标查看任务详情
3. 点击顶部"任务信息"按钮打开任务管理面板
4. 在任务面板中可以进行联动派车操作

### 3. 地图工具使用
1. **测距工具**: 点击测距按钮，在地图上点击起点和终点
2. **标注工具**: 点击标注按钮，在地图上点击添加标注点
3. **范围工具**: 点击范围按钮，选择范围半径绘制圆形区域
4. **清除工具**: 点击清除按钮删除所有测量标注

### 4. 数据查询操作
1. 点击顶部"数据查询"按钮打开数据模块
2. 切换不同标签页查看医院、血站、疾控中心、电话本信息
3. 使用搜索功能筛选特定数据
4. 点击操作按钮进行编辑或定位

## 系统架构与数据流

### 核心组件
1. **地图引擎**: 高德地图API 2.0，提供地图显示和交互功能
2. **数据接口**: RESTful API，实现前后端数据交互
3. **实时通信**: WebSocket连接，确保数据实时更新
4. **状态管理**: 前端状态管理，维护系统运行状态

### 数据源
1. **车辆数据**: 通过`getVehicleLocations`接口获取实时车辆位置和状态
   - 支持centerCode参数进行中心过滤
2. **事故数据**: 通过`getCityLinkageEventList`接口获取事故任务信息
   - 支持centerCode参数进行中心过滤
3. **分站数据**: 通过`getCityLinkageStationInfo`接口获取分站信息和状态
   - 支持centerCode参数进行中心过滤
4. **医院数据**: 通过`getResourceHospitalInfo`接口获取医院信息
   - 支持centerCode参数进行中心过滤
   - 类型编码: ZYLX_01
5. **血站数据**: 通过`getResourceInfoByTypeCode`接口获取血站信息
   - 支持centerCode参数进行中心过滤
   - 类型编码: ZYLX_10
6. **疾控中心数据**: 通过`getResourceInfoByTypeCode`接口获取疾控中心信息
   - 支持centerCode参数进行中心过滤
   - 类型编码: ZYLX_09
7. **中心状态**: 通过`getCommunicationStatus`接口获取各中心通信状态
8. **字典数据**: 通过`queryAllDic`接口获取系统配置和枚举值

### 接口过滤机制
- **全部中心**: centerCode传递空字符串("")
- **特定中心**: centerCode传递中心编码，多个中心用逗号分隔
- **实时更新**: 中心过滤变化时，所有相关接口重新调用
- **数据同步**: 确保地图显示与过滤条件完全一致
- **支持接口**: 车辆、事故、分站、医院、血站、疾控中心数据接口
- **模拟数据**: 通讯录数据仍使用模拟数据（无需接口）

### 业务逻辑核心

#### 车辆状态判断逻辑
- **hCondition字段**:
  - 0: 正常状态
  - 1: 报停状态
  - 3: 未值班状态
- **statusCode字段**:
  - 0: 待命状态
  - 1: 发车状态
  - 2: 抵达状态
- **图标选择逻辑**:
  - 待命且距离分站超过1公里: 黄色图标
  - 待命且在分站附近: 绿色图标
  - 执行任务中: 红色图标
  - 非正常状态: 蓝色图标

#### 事故任务状态管理
- **状态码定义**:
  - 1: 未调度-落单
  - 2: 已调度
  - 3: 已完成
  - 4: 已撤销
  - 6: 未调度-待派
  - 7: 未调度-预约
- **紧急程度分级**:
  - A级: 最高优先级（红色）
  - B级: 高优先级（橙色）
  - C级: 中等优先级（黄色）
  - D级: 低优先级（绿色）
  - E级: 最低优先级（蓝色）

#### 分站管理逻辑
- **分站等级分类**:
  - 1级分站: 一级分站（红色图标）
  - 2级分站: 二级分站（橙色图标）
  - 3级分站: 三级分站（绿色图标）
- **分站信息**:
  - 分站名称、所属中心、等级
  - 急救半径、救治能力
  - 在线状态、联系电话
- **中心过滤**: 分站显示跟随中心过滤条件变化

#### 医院管理逻辑
- **医院等级分类**:
  - 三级医院: 三甲、三乙、三丙（绿色图标）
  - 二级医院: 二甲、二乙、二丙（橙色图标）
  - 一级医院: 一甲、一乙、一丙（红色图标）
  - 其他: 乡镇卫生院、社区医疗服务中心、诊所等
- **医院信息**:
  - 医院名称、所属中心、等级
  - 床位数量、ICU床位、隔离床位
  - 急诊科状态、急诊科等级
  - 联系电话、联系人、地址
- **专业中心**: 胸痛中心、卒中中心、创伤中心等级
- **中心过滤**: 医院显示跟随中心过滤条件变化

#### 血站管理逻辑
- **血站信息**:
  - 血站名称、所属中心
  - 联系电话、联系人、地址
  - 描述信息
- **图标标识**: 红色十字图标，标记"B"
- **中心过滤**: 血站显示跟随中心过滤条件变化

#### 疾控中心管理逻辑
- **疾控中心信息**:
  - 疾控中心名称、所属中心
  - 联系电话、联系人、地址
  - 描述信息
- **图标标识**: 绿色勾选图标，标记"C"
- **中心过滤**: 疾控中心显示跟随中心过滤条件变化

#### 联动调度机制
1. **跨中心协调**: 支持不同急救中心之间的车辆调度
2. **资源优化**: 根据距离和车辆状态智能推荐最优车辆
3. **实时同步**: 调度决策实时同步到所有相关中心
4. **状态追踪**: 全程跟踪任务执行状态

## 技术特性

### 数据更新机制
- **实时更新**: 车辆位置每30秒自动更新
- **状态同步**: 中心通信状态每30秒检查一次
- **事故刷新**: 事故任务数据实时同步
- **分站更新**: 分站信息根据中心过滤条件动态加载
- **医疗资源更新**: 医院、血站、疾控中心数据根据中心过滤条件动态加载
- **数据模块同步**: 打开数据模块时自动加载最新数据
- **缓存策略**: 合理使用缓存减少网络请求

### 数据模块详细操作

#### 医院管理表格
- **数据来源**: `getResourceHospitalInfo` 接口（类型编码：ZYLX_01）
- **表格列说明**:
  - 医院名称: 医院的完整名称
  - 所属中心: 医院所属的急救分中心
  - 医院等级: 三甲、三乙、二甲等医院等级
  - 床位数: 医院总床位数量
  - ICU床位: 重症监护室床位数量
  - 急诊科: 是否设有急诊科（有/无）
  - 急诊等级: 急诊科的等级评定
  - 联系电话: 医院联系电话
  - 联系人: 医院联系人姓名
  - 地址: 医院详细地址
  - 状态: 医院启用/禁用状态
- **操作功能**: 编辑、定位、查询、重置

#### 血站管理表格
- **数据来源**: `getResourceInfoByTypeCode` 接口（类型编码：ZYLX_10）
- **表格列说明**:
  - 血站名称: 血站的完整名称
  - 所属中心: 血站所属的急救分中心
  - 联系电话: 血站联系电话
  - 联系人: 血站联系人姓名
  - 地址: 血站详细地址
  - 描述: 血站功能描述
  - 备注: 其他备注信息
- **操作功能**: 编辑、定位、查询、重置

#### 疾控中心管理表格
- **数据来源**: `getResourceInfoByTypeCode` 接口（类型编码：ZYLX_09）
- **表格列说明**:
  - 疾控中心名称: 疾控中心的完整名称
  - 所属中心: 疾控中心所属的急救分中心
  - 联系电话: 疾控中心联系电话
  - 联系人: 疾控中心联系人姓名
  - 地址: 疾控中心详细地址
  - 描述: 疾控中心功能描述
  - 备注: 其他备注信息
- **操作功能**: 编辑、定位、查询、重置

### 过滤与搜索
- **车辆状态过滤**: 支持按待命、任务、报停、下班状态筛选
- **中心过滤**: 支持按急救中心筛选所有资源
- **组合过滤**: 支持多条件组合筛选
- **模糊搜索**: 支持车牌号、医院名称等模糊搜索

### 界面交互
- **拖拽面板**: 所有功能面板支持拖拽移动
- **响应式设计**: 适配不同屏幕尺寸
- **快捷操作**: 支持键盘快捷键和右键菜单
- **无障碍访问**: 支持屏幕阅读器等辅助功能

### 安全与权限
- **权限控制**: 基于角色的访问控制
- **操作日志**: 记录所有关键操作
- **数据加密**: 敏感数据传输加密
- **会话管理**: 安全的用户会话管理

## 注意事项

### 系统要求
1. **网络要求**: 需要稳定的网络连接以确保实时数据更新
2. **浏览器兼容**: 建议使用Chrome、Firefox等现代浏览器
3. **屏幕分辨率**: 建议1920x1080或更高分辨率
4. **硬件配置**: 建议8GB内存以上，确保流畅运行

### 操作规范
1. **权限管理**: 部分功能需要相应的操作权限
2. **数据安全**: 所有操作都有日志记录，请规范使用
3. **并发操作**: 避免多人同时操作同一任务
4. **数据备份**: 重要操作前建议备份相关数据

## 详细操作指南

### 任务信息面板操作
1. **打开任务面板**: 点击顶部"任务信息"按钮
2. **任务筛选**:
   - 状态筛选: 选择任务状态（全部/未调度/已调度/已完成等）
   - 中心筛选: 选择急救分中心
   - 时间筛选: 设置呼叫开始时间和结束时间
3. **联动派车**: 点击"联动派车"按钮创建跨中心调度任务
4. **数据导出**: 点击"导出"按钮导出任务数据

### 数据模块面板操作
1. **医院管理**（使用真实接口数据）:
   - 显示字段：医院名称、所属中心、医院等级、床位数、ICU床位、急诊科、急诊等级、联系电话、联系人、地址、状态
   - 按分中心筛选医院
   - 按医院名称搜索
   - 查看医院详细信息（等级、床位、急诊科状态）
   - 定位医院位置
   - 编辑医院信息

2. **血站管理**（使用真实接口数据）:
   - 显示字段：血站名称、所属中心、联系电话、联系人、地址、描述、备注
   - 按分中心筛选血站
   - 按血站名称搜索
   - 查看血站详细信息
   - 定位血站位置
   - 编辑血站信息

3. **疾控中心管理**（使用真实接口数据）:
   - 显示字段：疾控中心名称、所属中心、联系电话、联系人、地址、描述、备注
   - 按分中心筛选疾控中心
   - 按中心名称搜索
   - 查看疾控中心详细信息
   - 定位疾控中心位置
   - 编辑疾控中心信息

4. **电话本功能**（使用模拟数据）:
   - 按姓名或科室搜索联系人
   - 查看职务和部门信息
   - 获取办公电话和手机号码

### 地图工具详细说明

#### 测距工具使用
1. 点击右侧工具栏"测距"按钮
2. 在地图上点击起始位置
3. 继续点击设置途径点（可多个）
4. 双击结束绘制，系统自动显示总距离
5. 点击距离标签可删除测距线

#### 标注工具使用
1. 点击右侧工具栏"标注"按钮
2. 在地图上点击要标注的位置
3. 在弹出窗口中输入标注描述
4. 系统自动获取坐标和地址信息
5. 可复制坐标或地址信息
6. 点击标注点可重新编辑或删除

#### 范围工具使用
1. 点击右侧工具栏"范围"按钮
2. 选择范围半径（1-50公里）
3. 系统以地图中心为圆心绘制范围圆
4. 点击范围标签可删除范围圆

### 过滤功能详细操作

#### 车辆状态过滤
1. 点击顶部状态栏"车辆过滤"按钮
2. 在弹出对话框中选择要显示的车辆状态：
   - 全部车辆: 显示所有车辆
   - 待命状态: 在分站待命的车辆
   - 任务状态: 正在执行任务的车辆
   - 暂停状态: 报停、维修中的车辆
   - 下班状态: 未值班的车辆
3. 点击"应用过滤"确认设置

#### 中心过滤
1. 点击顶部状态栏"中心过滤"按钮
2. 选择要显示的急救中心：
   - 选择"全部中心"：显示所有中心的资源
   - 选择特定中心：只显示选中中心的资源
3. 过滤将应用到所有地图资源：
   - **车辆数据**：重新调用接口，传递centerCode参数
   - **事故数据**：重新调用接口，传递centerCode参数
   - **分站数据**：重新调用接口，传递centerCode参数
   - **医院数据**：重新调用接口，传递centerCode参数
   - **血站数据**：重新调用接口，传递centerCode参数
   - **疾控中心**：重新调用接口，传递centerCode参数
4. 选择全部中心时，接口参数传递空字符串
5. 选择特定中心时，接口参数传递中心编码（多个用逗号分隔）

### 连接状态监控
1. **状态面板**: 左上角显示各中心连接状态
2. **状态指示**:
   - 绿色圆点: 中心在线
   - 红色圆点: 中心离线
3. **面板控制**: 点击标题栏可展开/折叠面板
4. **自动更新**: 每30秒自动检查连接状态

## 快捷键和技巧

### 键盘快捷键
- **Ctrl + R**: 刷新页面
- **Esc**: 关闭当前打开的面板或工具
- **空格**: 暂停/继续地图动画

### 操作技巧
1. **批量操作**: 在任务面板中可以批量选择和操作任务
2. **快速定位**: 双击地图资源图标可快速定位
3. **信息复制**: 在标注工具中可一键复制坐标和地址
4. **面板管理**: 所有面板支持拖拽移动和大小调整

## 故障排除

### 常见问题
1. **地图无法加载**: 检查网络连接和高德地图API密钥
2. **车辆不显示**: 检查车辆数据接口和过滤条件
3. **面板无法打开**: 刷新页面或检查浏览器控制台错误
4. **数据不更新**: 检查后端服务状态和网络连接
5. **标注工具失效**: 清除浏览器缓存重新加载
6. **过滤功能异常**: 重置过滤条件或刷新页面

### 性能优化建议
1. **定期清理**: 使用"清除"工具清理地图标注
2. **合理过滤**: 使用过滤功能减少显示的数据量
3. **关闭不需要的图层**: 隐藏不需要的地图资源
4. **浏览器优化**: 定期清理浏览器缓存

### 联系支持
如遇到技术问题，请联系系统管理员或技术支持团队。

---

**文档版本**: v1.0
**最后更新**: 2025年1月
**适用系统**: 120调度指挥市县联动子系统
