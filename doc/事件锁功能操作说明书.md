# 事件锁功能操作说明书

## 1. 功能概述

事件锁功能是120急救指挥系统中的重要安全机制，用于防止多个调度员同时操作同一事件，确保事件处理的准确性和一致性。

### 1.1 功能目的
- **防止冲突**：避免多个调度员同时修改同一事件
- **保证数据一致性**：确保事件信息不会因并发操作而出现错误
- **明确责任**：明确当前事件的负责调度员

### 1.2 适用场景
- 多座席并发工作环境
- 重要事件需要专人负责处理
- 防止误操作或重复操作

## 2. 锁定状态说明

### 2.1 锁定图标含义

| 图标 | 颜色 | 含义 | 操作权限 |
|------|------|------|----------|
| 🔒 | 绿色 | 本人锁定 | 可以操作和解锁 |
| 🔒 | 蓝色 | 同座席其他用户锁定 | 可以操作和解锁 |
| 🔒 | 红色 | 其他座席锁定 | 无法操作 |
| 无图标 | - | 未锁定 | 可以操作和锁定 |

### 2.2 权限规则
1. **本人锁定**：锁定座席ID和用户ID都匹配 → 完全权限
2. **同座席锁定**：锁定座席ID匹配但用户ID不匹配 → 可以操作
3. **其他座席锁定**：锁定座席ID不匹配 → 无操作权限
4. **未锁定**：任何人都可以锁定和操作

## 3. 操作指南

### 3.1 锁定事件

#### 操作步骤：
1. **选择事件**：在事件列表中找到要锁定的事件
2. **右键点击**：在事件行上点击鼠标右键
3. **选择菜单**：在弹出的右键菜单中选择"锁定事件"
4. **确认操作**：在确认对话框中点击"确定"
5. **查看结果**：事件锁定成功后，事件行会显示绿色锁图标

#### 注意事项：
- 只能锁定未被锁定的事件
- 锁定后其他座席无法对该事件进行关键操作
- 锁定状态会在事件列表中实时显示

### 3.2 解锁事件

#### 操作步骤：
1. **选择事件**：在事件列表中找到已锁定的事件
2. **检查权限**：确认您有解锁权限（绿色或蓝色锁图标）
3. **右键点击**：在事件行上点击鼠标右键
4. **选择菜单**：在弹出的右键菜单中选择"解锁事件"
5. **确认操作**：在确认对话框中点击"确定"
6. **查看结果**：解锁成功后，锁图标消失

#### 解锁权限：
- **绿色锁**：本人锁定，可以解锁
- **蓝色锁**：同座席锁定，可以解锁
- **红色锁**：其他座席锁定，无法解锁

## 4. 受限操作

当事件被其他座席锁定时，以下操作将被禁用：

### 4.1 事件管理操作
- 撤销事件
- 恢复任务
- 修改事件信息

### 4.2 调度操作
- 撤销调度
- 换车操作
- 派车操作
- 调度车辆

### 4.3 其他操作
- 事件状态变更
- 重要信息修改

## 5. 锁定信息显示

### 5.1 悬停提示
将鼠标悬停在锁定图标上，会显示详细的锁定信息：
- 锁定座席代码
- 锁定用户名称
- 锁定时间（如果有）

示例：`由座席 [001] (张三) 锁定`

### 5.2 状态实时更新
- 锁定状态会在所有客户端实时同步
- 事件列表会自动刷新显示最新的锁定状态
- 权限检查在每次操作时动态执行

## 6. 故障排除

### 6.1 常见问题

#### 问题1：无法锁定事件
**可能原因**：
- 事件已被其他人锁定
- 网络连接问题
- 权限不足

**解决方案**：
- 检查事件锁定状态
- 确认网络连接正常
- 联系系统管理员检查权限

#### 问题2：无法解锁事件
**可能原因**：
- 没有解锁权限（红色锁）
- 系统异常

**解决方案**：
- 确认锁定状态和权限
- 刷新页面重试
- 联系锁定该事件的座席

#### 问题3：锁定状态显示异常
**可能原因**：
- 页面缓存问题
- 数据同步延迟

**解决方案**：
- 手动刷新事件列表
- 重新加载页面
- 检查网络连接

### 6.2 技术支持
如遇到无法解决的问题，请联系系统管理员，并提供：
- 操作时间
- 事件编号
- 错误信息截图
- 座席信息

## 7. 技术实现

### 7.1 相关函数
- `canOperateEvent()`：权限检查函数
- `updateEventLock()`：锁定/解锁操作函数
- `onEventRowContextMenu()`：右键菜单处理函数

### 7.2 支持页面
- `index.html`：主页面事件列表
- `eventList.html`：事件列表页面

### 7.3 数据字段
- `isLock`：锁定状态标识
- `lockSeatId`：锁定座席ID
- `lockSeatCode`：锁定座席代码
- `lockSeatUserId`：锁定用户ID
- `lockSeatUserName`：锁定用户名

---

**文档版本**：v1.0  
**创建日期**：2024年  
**更新日期**：2024年  
**维护人员**：系统开发团队 