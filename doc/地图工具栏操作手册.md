# 120急救指挥系统副屏幕地图工具栏操作手册

## 📖 概述

本手册详细介绍120急救指挥系统副屏幕页面（second.html）地图工具栏的使用方法。地图工具栏提供了丰富的地图操作功能，包括车辆管理、地图控制、测距标注等实用工具。

## 🎯 工具栏布局

地图工具栏位于地图左上角，采用现代化设计，分为两个主要区域：
- **车辆操作组**：管理车辆跟随、轨迹显示和配置
- **地图工具组**：提供地图控制、测距、标注等功能

## 🚗 车辆操作功能

### 1. 跟随功能 🎯
**位置**：车辆操作组第一个按钮  
**图标**：🎯 (激活时显示 ✅)  
**功能**：自动跟随选中的车辆，地图视角会随车辆移动而移动

**使用方法**：
1. 在车辆列表中选择一辆车辆
2. 点击"跟随"按钮激活跟随模式
3. 地图会自动居中显示选中车辆并跟随其移动
4. 再次点击可取消跟随模式

**跟随特点**：
- **自动跟随**：地图视角将自动跟随车辆移动
- **保持中央**：地图会始终保持车辆在屏幕中央
- **手动中断**：手动拖拽地图会暂时中断跟随，车辆移动时会重新跟随
- **实时更新**：跟随状态会随车辆位置实时更新

**状态显示**：
- 未激活：显示"跟随"文字，灰色状态
- 已激活：显示"取消"文字，绿色高亮状态

**使用提示**：
- 必须先选中车辆才能开启跟随功能
- 开启后会显示详细的使用说明和当前跟随的车辆信息

### 2. 路径功能 🛣️
**位置**：车辆操作组第二个按钮  
**图标**：🛣️ (激活时显示 📍)  
**功能**：显示或隐藏选中车辆的规划路径

**使用方法**：
1. 选择要查看路径的车辆
2. 点击"路径"按钮显示规划路径
3. 路径以绿色线条显示在地图上，显示预计到达时间和距离
4. 再次点击可隐藏路径

**路径类型**：
- **待派状态车辆**：显示到当前任务地点的路径
- **执行任务车辆**：显示到任务目的地的路径  
- **返回状态车辆**：显示到分站的路径

**状态显示**：
- 未显示：显示"显示路径"文字
- 已显示：显示"隐藏路径"文字，蓝色高亮状态

**使用提示**：
- 必须先选中车辆才能显示路径
- 待派车辆需要先在主屏幕选择任务才能显示路径
- 路径会显示绿色线条和目的地标记
- 目的地标记显示距离和预计到达时间信息

### 3. 配置功能 ⚙️
**位置**：车辆操作组第三个按钮  
**图标**：⚙️ (激活时显示 🔧)  
**功能**：打开车辆信息显示配置对话框

**配置选项**：
- ☑️ 所属分站：显示/隐藏车辆所属分站信息
- ☑️ 车牌号：显示/隐藏车辆车牌号
- ☑️ 速度：显示/隐藏车辆当前速度
- ☑️ 状态：显示/隐藏车辆当前状态

**使用方法**：
1. 点击"配置"按钮打开配置对话框
2. 勾选或取消勾选相应选项
3. 配置会实时应用到地图上的车辆显示

## 🗺️ 地图控制功能

### 1. 放大功能 🔍
**图标**：🔍  
**功能**：放大地图显示，增加地图缩放级别

### 2. 缩小功能 🔎
**图标**：🔎  
**功能**：缩小地图显示，减少地图缩放级别

### 3. 比例尺功能 📏
**图标**：📏 (激活时显示 📐)  
**功能**：显示或隐藏地图比例尺控件

**特点**：
- 默认开启状态
- 显示在地图左下角
- 帮助用户了解地图上的实际距离

### 4. 路况功能 🚦
**图标**：🚦 (激活时显示 🟢)  
**功能**：显示或隐藏实时路况信息

**使用场景**：
- 查看道路拥堵情况
- 选择最佳行驶路线
- 评估到达时间

## 📐 测距工具

### 功能介绍
**图标**：📐 (激活时显示 ✏️)  
**功能**：在地图上测量两点或多点之间的直线距离

### 使用方法
1. **激活工具**：点击"测距"按钮进入测距模式
2. **查看提示**：系统会显示详细的使用说明和功能介绍
3. **设置起点**：在地图上点击设置测距起点
4. **添加路径点**：继续点击可添加多个测距点
5. **结束测距**：双击结束测距绘制
6. **查看结果**：系统会在终点显示总距离标签

### 功能亮点
- **一键开启**：点击测距按钮即可开启，附带详细使用说明
- **精确计算**：提供精确的直线距离计算
- **多点测距**：支持多个途径点的连续测距
- **智能转换**：自动进行单位转换（米/公里）

### 距离显示
- **小于1000米**：显示为"XXX米"
- **大于等于1000米**：显示为"X.XX公里"

### 删除功能
- 点击测距线段可删除整条测距线
- 点击距离标签（带✖符号）也可删除
- 删除前会弹出确认对话框

### 视觉效果
- **测距线条**：橙色虚线，线宽3像素
- **距离标签**：橙色背景，白色文字，圆角设计

## 📌 标注工具

### 功能介绍
**图标**：📌 (激活时显示 ✅)  
**功能**：在地图上添加自定义标注点，显示坐标和地址信息

### 使用方法
1. **激活工具**：点击"标注"按钮进入标注模式
2. **查看提示**：系统会显示详细的使用说明和功能介绍
3. **添加标注**：在地图上点击任意位置添加标注点
4. **自动编辑**：标注点创建后立即弹出编辑窗口
5. **输入信息**：在编辑窗口中输入标注描述信息
6. **保存标注**：点击"保存"按钮完成标注创建

### 功能亮点
- **一键开启**：点击标注按钮即可开启，附带详细使用说明
- **即点即标**：在地图任意位置点击即可创建标注
- **自动弹窗**：创建标注后自动弹出编辑窗口，无需额外操作
- **智能获焦**：新建标注时输入框自动获得焦点，提升操作效率

### 操作流程示例
1. **开始标注**：点击工具栏中的"📌 标注"按钮
2. **阅读提示**：系统弹出详细使用说明，包含：
   - 基本使用方法（点击地图添加标注）
   - 功能特色（坐标复制、地址解析等）
   - 编辑方式（点击标注重新编辑）
3. **添加标注**：在地图上点击目标位置
4. **编辑信息**：在弹出的编辑窗口中：
   - 输入标注描述
   - 查看自动获取的坐标和地址
   - 使用一键复制功能（坐标/地址）
5. **完成标注**：点击"保存"按钮，标注以红色醒目标签显示

### 标注编辑窗口
**编辑窗口包含**：
- **标注描述**：输入框，可修改标注信息
- **坐标信息区域**：
  - 经度（精确到6位小数）
  - 纬度（精确到6位小数）
  - 详细地址（自动获取）
  - 📋 复制坐标按钮（红色）
  - 📍 复制地址按钮（绿色）
- **操作按钮**：
  - 💾 保存按钮（蓝色）
  - 🗑️ 删除按钮（红色）
  - ❌ 取消按钮（灰色）

### 坐标功能特色
- **自动获取**：点击标注时自动获取百度坐标系的经纬度
- **地址解析**：自动解析并显示标注点的详细地址
- **智能复制填写**：
  - 📋 **复制坐标**：快速复制经纬度信息到剪贴板，并自动填写到标注描述
  - 📍 **复制地址**：快速复制详细地址信息到剪贴板，并自动填写到标注描述
  - **智能合并**：如果描述框已有内容，新复制的内容会用" | "分隔符追加
  - **自动焦点**：复制后输入框自动获得焦点，便于进一步编辑
- **高精度**：坐标精确到小数点后6位，满足精确定位需求

### 标注样式特点
- **标注图标**：红色渐变地图标记（20x25像素）
- **信息标签**：**纯红色背景、纯白色字体、白色边框**，使用HTML内容确保样式正确应用
- **超醒目标签特性**：
  - 背景色：#FF0000（纯红色，使用!important强制应用）
  - 字体色：#FFFFFF（纯白色，使用!important强制应用）
  - 字体大小：12px（精致字体）
  - 字体粗细：bold（粗体）
  - 边框：3px白色边框（强烈对比）
  - 内边距：12px 18px（宽松间距）
  - 圆角：15px（圆润设计）
  - 双重阴影：深色阴影 + 红色光晕效果
  - 文字阴影：2px黑色阴影（超强对比）
  - 字间距：1px（清晰可读）
  - 最大宽度：300px，最小宽度：80px
  - 技术实现：使用HTML div元素 + 内联样式确保兼容性

### 视觉设计亮点
- **极致醒目显示**：纯红色背景(#FF0000) + 纯白色字体 + 白色边框 + 双重阴影，确保在任何地图背景下都极其清晰可见
- **强制样式应用**：使用HTML内联样式 + !important 确保样式不被覆盖
- **现代美观**：大圆角设计(15px)、立体阴影效果、16px大字体，符合现代UI设计规范
- **信息丰富**：不仅显示标注文字，还提供完整的坐标和地址信息
- **交互友好**：创建时直接编辑，操作流畅直观
- **智能处理**：
  - 新建标注时自动获得输入焦点
  - 空内容保存时提示删除标注
  - 取消新建标注时自动清理
  - 地址复制采用安全的data属性方式
- **按钮区分**：
  - 📋 复制坐标按钮：红色背景（#FF4444）
  - 📍 复制地址按钮：绿色背景（#52C41A）
  - 不同颜色便于快速识别功能
- **可见性优化**：
  - HTML div容器确保样式正确渲染
  - !important 强制应用关键样式
  - 16px大字体提升可读性
  - 强烈文字阴影增强对比度
  - 红色光晕效果增强视觉冲击力
  - z-index: 9999 确保显示在最顶层

## ⭕ 范围工具

### 功能介绍
**图标**：⭕ (激活时显示 🎯)  
**功能**：以指定位置为圆心绘制范围圆圈

### 范围选项
提供9个预设范围选项：
- 1公里（1000米）
- 2公里（2000米）
- 3公里（3000米）
- 5公里（5000米）
- 10公里（10000米）
- 20公里（20000米）
- 30公里（30000米）
- 40公里（40000米）
- 50公里（50000米）

### 圆心确定优先级（智能追踪）
1. **最后选中的位置**（最高优先级 - 车辆/标注点/事件地址）
2. **当前选中的车辆位置**（备选方案）
3. **事件地址标记位置**（备选方案）
4. **最新标注点位置**（备选方案）
5. **当前设置的位置**（最后备选）

### 使用方法
1. **选择圆心**：点击任意目标（车辆/标注点/事件地址）
2. **打开菜单**：点击"范围"按钮
3. **选择半径**：从下拉菜单选择所需公里数
4. **自动创建**：范围圆圈以选中目标为圆心自动创建
5. **重新选择**：点击新目标，再次使用范围工具

### 智能特性
- **精准追踪**：智能追踪最后选中的车辆、标注点或事件地址
- **实时响应**：每次点击都会更新选中状态，范围工具实时响应
- **自动工具关闭**：创建范围后工具自动关闭，允许选择新圆心
- **详细信息显示**：标签显示具体的圆心位置信息和类型
- **多类型支持**：支持车辆、标注点、事件地址三种类型的圆心

### 删除功能
- 点击范围圆圈可删除
- 点击范围标签（带✖符号）也可删除
- 删除前会弹出确认对话框

### 视觉效果
- **圆圈边框**：橙色实线，透明度80%
- **圆圈填充**：橙色填充，透明度20%
- **范围标签**：橙色背景，白色边框，阴影效果
  - 显示范围距离（如"3公里范围"）
  - 显示圆心位置信息（如"圆心: 车辆 京A12345"）
  - 右上角显示删除按钮（✖）

## 🗺️ 鹰眼地图

### 功能介绍
**图标**：🗺️ (激活时显示 👁️)  
**功能**：显示缩略地图，便于总览和导航

### 特点
- **默认开启**：页面加载时自动显示
- **位置**：地图左上角
- **尺寸**：200x150像素
- **边框**：灰色边框，圆角设计
- **地图类型**：普通矢量地图

### 功能特性
- **自动跟随**：随主地图视角变化而更新
- **矩形框显示**：显示当前主地图的视野范围
- **无控制按钮**：隐藏默认的控制按钮

## 🧹 清除功能

### 功能介绍
**图标**：🧹  
**功能**：清除所有地图标注和控件

### 清除内容
- 所有测距线段和标签
- 所有标注点和信息
- 所有范围圆圈和标签
- 比例尺控件
- 路况图层
- 鹰眼地图

### 注意事项
- 清除操作不可撤销
- 会弹出确认提示
- 不影响车辆显示

## 🎨 界面设计特点

### 现代化设计
- **圆角按钮**：12px圆角，现代美观
- **渐变背景**：白色到浅灰色渐变
- **阴影效果**：柔和的投影增强立体感
- **动画过渡**：平滑的悬停和点击动画

### 交互反馈
- **悬停效果**：按钮上移2px，阴影加深
- **激活状态**：蓝色高亮，图标变化
- **工具提示**：鼠标悬停显示功能说明

### 颜色系统
- **默认状态**：灰色文字（#64748b）
- **悬停状态**：对应功能色彩
- **激活状态**：蓝色主题（#1890ff）

## 🔧 技术实现

### 地图引擎
- **高德地图API 2.0**：提供地图基础功能
- **插件支持**：测距、鹰眼、比例尺等插件
- **自定义控件**：工具栏和交互功能

### 数据管理
- **状态管理**：mapToolsState对象管理工具状态
- **标注管理**：mapAnnotations对象管理地图标注
- **事件处理**：统一的事件监听和处理机制

### 兼容性
- **C#客户端**：支持gProxy代理调用
- **B/S浏览器**：支持bsProxy代理调用
- **响应式设计**：适配不同屏幕尺寸

## 🚨 注意事项

### 使用建议
1. **合理使用工具**：避免同时激活多个绘制工具
2. **及时清理**：定期清理不需要的标注
3. **保存重要信息**：标注信息会在页面刷新后丢失
4. **网络依赖**：部分功能需要网络连接

### 常见问题
1. **工具无响应**：检查是否有其他工具正在激活
2. **标注丢失**：页面刷新会清除所有手动标注
3. **距离不准确**：测距为直线距离，非实际道路距离
4. **鹰眼不显示**：检查网络连接和地图API

## 📹 车辆轨迹回放功能

### 功能介绍
**触发方式**：在车辆右键菜单中选择"轨迹回放"  
**功能**：查看车辆的历史行驶轨迹回放

### 功能特点
- **历史轨迹**：可以查看车辆在指定时间段内的历史行驶轨迹
- **动画回放**：以动画形式回放车辆的移动过程
- **详细信息**：显示车辆的历史位置、速度和时间信息
- **播放控制**：支持暂停、快进、倒退等播放控制功能

### 与路径显示的区别
- **轨迹回放**：显示车辆的历史实际行驶路线（已走过的路）
- **路径显示**：显示车辆当前的规划导航路径（要走的路）

## 📞 技术支持

如果在使用过程中遇到问题，请联系技术支持团队。本手册会根据系统更新持续维护和完善。

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**适用系统**：120急救指挥系统副屏幕 