# 120急救指挥系统副屏三种派车模式操作说明

## 概述

120急救指挥系统副屏完全兼容主屏的三种派车模式，为调度员提供了灵活多样的车辆派遣方式。系统会根据全局配置和分站设置自动判断采用哪种派车模式。

## 三种派车模式详解

### 模式1：派车给分站
**适用场景：** 系统配置为分站派车模式时  
**配置条件：** `assignTaskMode = '2'`

**操作流程：**
1. 在副屏地图上右键点击车辆
2. 选择"派车"操作
3. 系统直接将任务派发给对应分站
4. 分站接收任务后安排具体车辆和人员

**特点：**
- 调度中心只需要选择分站，不需要选择具体车辆
- 分站负责安排具体的出车车辆和人员
- 适用于分站有较强自主调度能力的场景

### 模式2：直接派车（不经过分站确认）
**适用场景：** 车辆派车模式且车辆不需要分站确认时  
**配置条件：** `assignTaskMode = '1'` 且车辆 `isDispatch = '0'`

**操作流程：**
1. 在副屏地图上右键点击车辆
2. 选择"派车"操作
3. 系统弹出"任务出车人员设置"界面
4. 选择出车班次、医生、护士、司机、护工
5. 点击"确认发车"完成派车

**人员选择界面功能：**
- **班次选择：** 提示"请选择出车班次"，选择后自动填充对应人员
- **医生选择：** 提示"请输入医生工号和姓名"，支持输入匹配
- **护士选择：** 提示"请输入护士工号和姓名"，支持输入匹配
- **司机选择：** 提示"请输入司机工号和姓名"，支持输入匹配
- **护工选择：** 提示"请输入护工工号和姓名"，支持输入匹配

**特点：**
- 调度中心直接控制车辆派遣
- 需要指定具体的出车人员
- 派车后车辆直接接收任务，无需分站确认

### 模式3：派车到车辆（需分站确认）
**适用场景：** 车辆派车模式但车辆需要分站确认时  
**配置条件：** `assignTaskMode = '1'` 且车辆 `isDispatch = '1'`

**操作流程：**
1. 在副屏地图上右键点击车辆
2. 选择"派车"操作
3. 系统将任务派发给车辆所属分站
4. 分站确认后车辆接收任务

**特点：**
- 调度中心指定具体车辆
- 但需要分站确认出车
- 结合了车辆指定和分站确认的双重控制

## 系统自动判断逻辑

系统会按照以下优先级自动选择派车模式：

```javascript
// 判断逻辑伪代码
if (assignTaskMode === '2') {
    // 模式1：派车给分站
    执行分站派车逻辑
} else if (车辆.isDispatch === '0') {
    // 模式2：直接派车，不经过分站确认
    弹出人员选择界面
} else if (车辆.isDispatch === '1') {
    // 模式3：派车到车辆，由分站确认
    执行分站确认逻辑
}
```

## 配置说明

### 全局配置
- **assignTaskMode**：派车模式配置
  - `'1'`：车辆派车模式（显示具体车辆）
  - `'2'`：分站派车模式（显示分站列表）

### 分站/车辆配置
- **isDispatch**：是否需要分站确认
  - `'0'`：不需要分站确认，直接派车
  - `'1'`：需要分站确认出车

## 人员选择界面详细说明

### 界面特点
- 与主屏样式完全一致，使用表格布局
- 窗口大小：400x360px
- 支持输入自动匹配和下拉选择

### 操作技巧

**班次选择：**
- 可直接从下拉列表选择
- 可输入班次名称进行过滤查找
- 选择班次后自动填充对应的医生、护士、司机、护工

**人员选择：**
- 支持输入工号或姓名进行搜索
- 显示格式：工号-姓名
- 可通过键盘输入快速定位

**数据验证：**
- 系统会检查人员信息的完整性
- 必须从下拉列表中选择，不能手动输入
- 确保数据的准确性和一致性

### 自动填充功能
当选择班次后，系统会自动填充该班次预设的人员：
- 自动选择预设医生
- 自动选择预设护士  
- 自动选择预设司机
- 自动选择预设护工

用户可以根据实际情况修改自动填充的人员选择。

## 状态同步机制

### 数据同步
- 派车成功后立即刷新车辆列表
- 延迟500ms自动更新车辆状态
- 确保主副屏数据一致性

### 错误处理
- 车辆信息不存在时提示错误
- 人员数据不完整时阻止提交
- 网络异常时显示友好提示

## 调试功能

系统提供详细的调试日志，便于问题排查：
- 派车模式判断日志
- 车辆查找过程日志
- 人员数据加载日志
- 接口调用状态日志

## 注意事项

1. **权限控制**：确保操作人员有相应的派车权限
2. **数据完整性**：人员信息必须完整，不能缺失关键字段
3. **网络状况**：派车操作需要良好的网络连接
4. **界面响应**：派车过程中会显示进度提示，请耐心等待
5. **错误处理**：遇到错误时请查看提示信息，必要时联系技术支持

## 技术实现

### 核心函数
- `executeDispatchByMode()`: 根据模式执行派车逻辑
- `confirmReceiveShowSecondScreen()`: 显示人员选择界面
- `initPersonnelSelectionSecondScreen()`: 初始化人员选择组件
- `autoFillPersonnelByScheduleSecondScreen()`: 班次联动填充

### 兼容性
- 完全兼容主屏幕的派车逻辑
- 支持所有现有的分站和车辆配置
- 保持与原有系统的接口一致性

## 更新历史

- **v1.0**：实现三种派车模式的完整功能
- **v1.1**：优化人员选择界面，增加自动匹配功能
- **v1.2**：完善错误处理和状态同步机制

---

*本说明文档适用于120急救指挥系统副屏模块，如有疑问请联系技术支持团队。* 