# 120急救指挥系统记事本操作功能手册

## 📋 概述

记事本模块是120急救指挥系统的重要组成部分，为调度员提供便捷的信息记录和管理功能。支持富文本编辑、快速查询、分页浏览等特性，帮助调度员高效管理工作记录。

## 🖥️ 界面布局

### 主界面结构
- **左侧区域**：记事本列表，显示所有记事本条目
- **右侧区域**：编辑区域，用于新增或修改记事本内容
- **顶部区域**：查询条件和操作按钮

### 列表字段说明
| 字段名称 | 宽度 | 说明 |
|---------|------|------|
| 标题 | 25% | 记事本标题，超过15个字符自动截断显示 |
| 内容 | 35% | 记事本内容预览，去除HTML标签，超过25个字符截断 |
| 所属人 | 10% | 记事本创建者姓名 |
| 创建时间 | 15% | 记事本创建的日期时间 |
| 最后修改时间 | 15% | 记事本最后一次修改的日期时间 |

## 🔍 查询功能

### 1. 时间范围查询
- **创建时间**：可设置开始时间和结束时间进行精确查询
- **快速时间选择**：
  - 点击"一小时内"：查询最近1小时内创建的记事本
  - 点击"一天内"：查询最近24小时内创建的记事本
- **默认查询**：页面加载时自动查询最近24小时内的数据

### 2. 关键词查询
- 支持在标题和内容中进行模糊搜索
- 输入关键词后点击"查询"按钮执行搜索

### 3. 分页浏览
- 默认每页显示20条记录
- 支持10、20、30、40、50条记录的分页显示
- 提供页码跳转和记录总数显示

## ✏️ 编辑功能

### 1. 新增记事本
**操作步骤：**
1. 点击工具栏中的"新增"按钮
2. 右侧编辑区域标题变为"新增"
3. 输入记事本标题（必填）
4. 在富文本编辑器中输入内容（必填）
5. 点击"保存"按钮完成新增

**功能特点：**
- 新增时自动清除列表选中状态
- 标题输入框自动获得焦点
- 保存成功后自动切换为修改模式，便于继续编辑
- 支持继续编辑而不清空内容

### 2. 修改记事本
**操作方式：**
- **方式一**：选中列表中的记录，点击"修改"按钮
- **方式二**：双击列表中的记录直接进入修改模式

**操作步骤：**
1. 选择要修改的记事本
2. 右侧编辑区域显示选中记事本的内容
3. 修改标题或内容
4. 点击"保存"按钮保存修改

**功能特点：**
- 保存后保持编辑状态，支持连续修改
- 自动保持列表中的选中状态
- 实时刷新列表数据

### 3. 删除记事本
**操作步骤：**
1. 在列表中选中要删除的记事本
2. 点击工具栏中的"删除"按钮
3. 在确认对话框中点击"确定"

**安全机制：**
- 删除前弹出确认对话框，防止误删
- 如果删除的是当前正在编辑的记事本，自动切换到新增模式
- 删除成功后自动刷新列表

## 🎨 富文本编辑器

### 编辑器特性
- **基于UEditor**：使用百度UEditor富文本编辑器
- **禁用上传功能**：为确保系统稳定性，已禁用所有文件上传功能
- **支持格式**：
  - 字体设置（字体、大小、颜色）
  - 文本格式（粗体、斜体、下划线）
  - 段落格式（对齐方式、行距）
  - 列表功能（有序列表、无序列表）
  - 表格插入和编辑
  - 链接插入

### 禁用功能列表
为避免502错误，以下功能已被禁用：
- 图片上传 (imageActionName)
- 涂鸦上传 (scrawlActionName)
- 视频上传 (videoActionName)
- 文件上传 (fileActionName)
- 远程图片抓取 (catcherActionName)
- 截图上传 (snapscreenActionName)
- 图片管理 (imageManagerActionName)
- 文件管理 (fileManagerActionName)

## 💡 操作技巧

### 1. 高效编辑技巧
- **连续编辑**：保存后编辑器不会清空，可以继续修改
- **快速新增**：新增记事本时标题框自动获得焦点
- **双击编辑**：双击列表项可直接进入编辑模式

### 2. 查询技巧
- **快速查询**：使用"一小时内"、"一天内"链接快速设置时间范围
- **精确查询**：结合时间范围和关键词进行精确搜索
- **默认显示**：页面默认显示最近24小时的记事本

### 3. 数据管理技巧
- **标题规范**：建议使用简洁明了的标题，便于快速识别
- **内容结构**：合理使用编辑器的格式功能，提高内容可读性
- **定期整理**：及时删除过期或无用的记事本，保持数据整洁

## ⚠️ 注意事项

### 1. 数据安全
- **必填验证**：标题和内容都不能为空
- **删除确认**：删除操作需要二次确认
- **自动保存**：已禁用自动保存功能，请及时手动保存

### 2. 系统限制
- **上传限制**：不支持任何文件上传功能
- **字符限制**：标题显示限制15个字符，内容预览限制25个字符
- **分页限制**：最大支持50条记录每页显示

### 3. 兼容性说明
- **浏览器要求**：基于CEF3.2987.1601版本（等同于Chrome浏览器）
- **编辑器兼容**：UEditor编辑器已针对系统环境进行优化配置

## 🔧 故障排除

### 常见问题及解决方案

**问题1：编辑器无法加载**
- 检查网络连接
- 刷新页面重新加载
- 确认浏览器版本兼容性

**问题2：保存失败**
- 检查标题和内容是否为空
- 确认网络连接正常
- 重试保存操作

**问题3：查询无结果**
- 检查时间范围设置是否正确
- 确认关键词输入是否准确
- 尝试扩大查询时间范围

**问题4：列表显示异常**
- 点击查询按钮刷新数据
- 检查分页设置
- 刷新页面重新加载

## 📊 系统性能

### 响应时间
- **页面加载**：通常在2-3秒内完成
- **数据查询**：根据数据量，通常在1-2秒内返回结果
- **保存操作**：通常在1秒内完成

### 数据容量
- **单页记录**：支持10-50条记录显示
- **内容长度**：理论上无限制，建议控制在合理范围内
- **查询范围**：支持任意时间范围查询

## 📝 更新日志

### 最新优化（当前版本）
- ✅ 新增快速时间查询功能（一小时内、一天内）
- ✅ 添加所属人列显示创建者信息
- ✅ 优化保存逻辑，支持连续编辑
- ✅ 禁用UEditor上传功能，避免502错误
- ✅ 改进删除逻辑，智能处理编辑状态
- ✅ 默认查询24小时内数据，提升用户体验

### 技术改进
- 🔧 修复关键词查询输入框类型问题
- 🔧 优化时间参数处理和格式化
- 🔧 改进编辑器高度自适应算法
- 🔧 增强错误处理和用户提示机制

---

**版本信息**：120急救指挥系统记事本模块 v2.0  
**文档更新时间**：2024年12月  
**适用系统版本**：120EDC急救指挥中心客户端第二屏幕界面  
**技术支持**：系统管理员 