# 市县联动指挥调度平台操作手册

## 1. 系统概述

### 1.1 系统简介
市县联动指挥调度平台是一个基于Web的急救调度管理系统，主要用于管理多个急救分中心的车辆、任务、医院、血站等资源，实现市县两级急救资源的统一调度和指挥。

### 1.2 系统特点
- **实时监控**：实时显示车辆位置、状态和任务信息
- **多中心管理**：支持多个急救分中心的统一管理
- **地图可视化**：基于高德地图的可视化调度界面
- **智能过滤**：支持按中心、状态等多种条件过滤资源
- **任务管理**：完整的任务创建、分配、跟踪流程
- **资源管理**：医院、血站、疾控中心等医疗资源管理

### 1.3 系统架构
- **前端技术**：HTML5 + CSS3 + JavaScript + EasyUI
- **地图服务**：高德地图API
- **数据交互**：AJAX异步请求
- **响应式设计**：支持不同屏幕尺寸

## 2. 界面布局

### 2.1 主界面结构
```
┌─────────────────────────────────────────────────────────────┐
│                    页面标题栏                                │
│  [Logo] 120调度指挥市县联动子系统  [任务信息] [数据查询] [数据统计] [页面刷新] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  [中心:5] [车辆:6] [事故:3] [医院:6] [分站:6]              │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │                    地图显示区域                         │ │
│  │                                                         │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  [地图工具栏] [连接状态面板] [重大事故预警窗口]              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 主要区域说明
- **页面标题栏**：系统标题和主要功能按钮
- **状态指示器**：显示各类型资源的数量统计
- **地图显示区域**：核心的地图可视化界面
- **地图工具栏**：地图操作和资源显示控制
- **连接状态面板**：各分中心通信状态监控
- **重大事故预警窗口**：重大事故实时预警

## 3. 功能模块详解

### 3.1 地图工具栏

#### 3.1.1 地图资源组
**功能**：控制地图上各类资源的显示/隐藏

**按钮说明**：
- **车辆** 🚗：显示/隐藏急救车辆
- **事故** 🚨：显示/隐藏事故任务点
- **分站** 🏥：显示/隐藏急救分站
- **医院** 🏥：显示/隐藏医院位置
- **血站** 🩸：显示/隐藏血站位置
- **疾控** 🧪：显示/隐藏疾控中心

**操作方法**：
1. 点击对应按钮切换显示状态
2. 激活状态按钮显示蓝色背景
3. 可同时显示多种资源类型

#### 3.1.2 地图工具组
**功能**：提供地图操作和测量工具

**按钮说明**：
- **放大** 🔍：放大地图视图
- **缩小** 🔎：缩小地图视图
- **比例** 📐：显示/隐藏比例尺
- **路况** 🚦：显示/隐藏实时路况
- **测距** 📏：测量地图上两点间距离
- **标注** 📌：在地图上添加标注点
- **范围** ⭕：绘制指定半径的范围圆
- **鹰眼** 🗺️：显示/隐藏鹰眼地图
- **清除** 🧹：清除所有标注和测量

**测距工具使用**：
1. 点击"测距"按钮激活测距功能
2. 在地图上点击起始位置
3. 继续点击设置途径点（可多个）
4. 双击结束绘制，显示总距离
5. 点击距离标签可删除测距线

**标注工具使用**：
1. 点击"标注"按钮激活标注功能
2. 在地图上点击要标注的位置
3. 在弹出的编辑窗口中输入标注信息
4. 可复制坐标和地址信息
5. 点击标注点可重新编辑

**范围工具使用**：
1. 点击"范围"按钮显示范围菜单
2. 选择需要的半径（1公里-50公里）
3. 系统以地图中心为圆心绘制范围圆
4. 点击范围标签可删除范围圆

### 3.2 状态指示器

#### 3.2.1 资源统计
显示各类型资源的实时数量：
- **中心**：急救分中心数量
- **车辆**：在线车辆数量
- **事故**：正在进行的事故数量
- **医院**：医院数量
- **分站**：急救分站数量

#### 3.2.2 过滤功能
**车辆过滤**：
1. 点击"全部车辆"按钮打开过滤对话框
2. 选择需要的车辆状态：
   - 全部车辆
   - 待命状态
   - 任务状态
   - 暂停状态
   - 下班状态
3. 点击"应用过滤"确认选择

**中心过滤**：
1. 点击"全部中心"按钮打开过滤对话框
2. 选择需要显示的中心
3. 点击"应用过滤"确认选择

### 3.3 任务信息管理

#### 3.3.1 打开任务面板
点击页面标题栏的"任务信息"按钮打开任务管理面板。

#### 3.3.2 任务查询
**查询条件**：
- **急救分中心**：选择特定分中心
- **状态**：选择任务状态（全部、未调度、已调度、已完成等）
- **呼叫时间**：设置时间范围
- **结束时间**：设置结束时间范围

**操作方法**：
1. 设置查询条件
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空条件

#### 3.3.3 任务操作
**联动派车**：
1. 点击"联动派车"按钮
2. 填写任务信息
3. 选择车辆和医院
4. 确认派车

**导出功能**：
1. 点击"导出"按钮
2. 选择导出格式
3. 下载任务数据

### 3.4 数据模块管理

#### 3.4.1 打开数据面板
点击页面标题栏的"数据查询"按钮打开数据管理面板。

#### 3.4.2 医院管理
**功能**：管理医院信息，包括医院等级、床位数、专科设置等

**查询条件**：
- 分中心
- 医院名称
- 医院等级

**操作功能**：
- 查看医院详细信息
- 编辑医院信息
- 定位到医院位置

#### 3.4.3 血站管理
**功能**：管理血站信息，包括血型库存、联系方式等

**查询条件**：
- 分中心
- 血站名称

**操作功能**：
- 查看血站详细信息
- 编辑血站信息
- 定位到血站位置

#### 3.4.4 疾控中心管理
**功能**：管理疾控中心信息，包括专业领域、服务状态等

**查询条件**：
- 分中心
- 中心名称

**操作功能**：
- 查看疾控中心详细信息
- 编辑疾控中心信息
- 定位到疾控中心位置

#### 3.4.5 电话本
**功能**：管理系统内人员联系方式

**功能特点**：
- 按部门分类显示
- 支持搜索功能
- 一键拨号功能

### 3.5 查询模块管理

#### 3.5.1 打开查询面板
点击页面标题栏的"数据统计"按钮打开查询统计面板。

#### 3.5.2 主要功能
- **事件查询与检索**：按条件查询历史事件
- **车辆信息查询**：查询车辆详细信息
- **人员信息查询**：查询人员信息
- **统计报表查询**：查看各类统计报表
- **自定义查询条件**：设置个性化查询条件

#### 3.5.3 快捷查询
- **今日事件**：查看今日发生的事件
- **在线车辆**：查看当前在线车辆
- **值班人员**：查看当前值班人员
- **系统日志**：查看系统操作日志

### 3.6 连接状态监控

#### 3.6.1 状态面板
**位置**：地图左上角

**显示内容**：
- 各分中心的连接状态
- 在线/离线状态指示
- 连接状态汇总

**状态说明**：
- 🟢 **绿色圆点**：在线状态
- 🔴 **红色圆点**：离线状态

#### 3.6.2 操作功能
- 点击面板标题可展开/收起详细信息
- 实时监控各中心通信状态
- 自动更新连接状态

### 3.7 重大事故预警

#### 3.7.1 预警窗口
**位置**：地图右上角

**显示内容**：
- 重大事故数量
- 事故详细信息
- 事故发生时间

#### 3.7.2 预警功能
- **自动检测**：系统自动检测重大事故
- **实时更新**：实时更新事故状态
- **快速定位**：点击事故可快速定位到地图位置
- **展开/收起**：点击标题可展开/收起详细信息

## 4. 操作流程

### 4.1 日常监控流程

#### 4.1.1 系统启动
1. 打开浏览器访问系统地址
2. 等待地图和资源加载完成
3. 检查各分中心连接状态
4. 确认车辆和任务数据正常显示

#### 4.1.2 实时监控
1. 观察地图上的车辆分布
2. 监控重大事故预警信息
3. 检查各资源点的显示状态
4. 关注连接状态面板的更新

#### 4.1.3 资源调度
1. 根据任务需求选择合适的车辆
2. 使用地图工具分析最优路径
3. 通过任务面板创建调度任务
4. 跟踪任务执行状态

### 4.2 应急处理流程

#### 4.2.1 重大事故处理
1. 系统自动检测并显示重大事故预警
2. 点击预警信息查看事故详情
3. 使用地图工具分析事故位置
4. 快速定位到事故现场
5. 选择合适的救援资源
6. 创建紧急调度任务

#### 4.2.2 资源调配
1. 评估事故严重程度
2. 确定需要的医疗资源类型
3. 查找最近的医院、血站等资源
4. 使用测距工具计算距离
5. 协调多中心资源调配

### 4.3 数据管理流程

#### 4.3.1 资源信息维护
1. 打开数据模块面板
2. 选择需要维护的资源类型
3. 使用查询功能查找特定资源
4. 编辑或更新资源信息
5. 保存修改内容

#### 4.3.2 任务数据查询
1. 打开任务信息面板
2. 设置查询条件
3. 执行查询操作
4. 查看任务详细信息
5. 导出需要的数据

## 5. 常用操作技巧

### 5.1 地图操作技巧

#### 5.1.1 快速定位
- **双击地图**：快速放大到指定位置
- **鼠标滚轮**：快速缩放地图
- **拖拽地图**：移动地图视图
- **鹰眼地图**：快速定位到其他区域

#### 5.1.2 信息查看
- **点击车辆**：查看车辆详细信息
- **点击医院**：查看医院信息
- **点击事故点**：查看事故详情
- **悬停标记**：显示简要信息

### 5.2 过滤操作技巧

#### 5.2.1 多条件过滤
- 可同时使用中心过滤和状态过滤
- 过滤条件会实时应用到地图显示
- 使用重置按钮快速清除所有过滤条件

#### 5.2.2 快速切换
- 使用快捷键快速切换资源显示
- 记住常用过滤组合
- 利用过滤按钮的激活状态判断当前显示内容

### 5.3 任务管理技巧

#### 5.3.1 快速查询
- 使用时间范围快速筛选任务
- 利用状态过滤查看特定阶段任务
- 结合中心过滤查看特定区域任务

#### 5.3.2 批量操作
- 选择多个任务进行批量操作
- 使用导出功能备份重要数据
- 定期清理已完成的任务数据

## 6. 故障排除

### 6.1 常见问题

#### 6.1.1 地图无法加载
**问题现象**：地图显示空白或加载失败
**解决方案**：
1. 检查网络连接
2. 刷新页面重新加载
3. 清除浏览器缓存
4. 检查高德地图API密钥

#### 6.1.2 数据不更新
**问题现象**：车辆位置、任务状态等数据不更新
**解决方案**：
1. 检查服务器连接状态
2. 点击"页面刷新"按钮
3. 检查各分中心通信状态
4. 联系技术支持

#### 6.1.3 功能按钮无响应
**问题现象**：点击按钮没有反应
**解决方案**：
1. 检查JavaScript是否启用
2. 刷新页面重新加载
3. 清除浏览器缓存
4. 尝试使用其他浏览器

### 6.2 性能优化

#### 6.2.1 地图性能
- 避免同时显示过多标记点
- 定期清除不需要的标注
- 使用过滤功能减少显示内容
- 关闭不需要的地图图层

#### 6.2.2 数据加载
- 合理设置查询条件减少数据量
- 避免频繁刷新大量数据
- 使用分页功能处理大量数据
- 定期清理历史数据

## 7. 系统配置

### 7.1 地图配置
- **默认中心点**：系统启动时的地图中心位置
- **默认缩放级别**：地图初始缩放比例
- **地图样式**：地图显示样式（普通、卫星等）

### 7.2 刷新配置
- **车辆数据刷新**：每10秒自动刷新
- **任务数据刷新**：每30秒自动刷新
- **连接状态刷新**：每30秒自动刷新

### 7.3 显示配置
- **默认显示资源**：系统启动时默认显示的资源类型
- **标记点大小**：地图上标记点的显示大小
- **信息窗口样式**：点击标记时显示的信息窗口样式

## 8. 安全注意事项

### 8.1 数据安全
- 定期备份重要数据
- 不要在不安全的网络环境下使用系统
- 及时退出系统，避免信息泄露
- 保护登录凭据，定期更换密码

### 8.2 操作安全
- 重要操作前确认操作内容
- 避免误删除重要数据
- 谨慎使用清除功能
- 定期检查系统日志

### 8.3 系统维护
- 定期清理浏览器缓存
- 及时更新浏览器版本
- 保持网络连接稳定
- 定期检查系统更新

## 9. 联系支持

### 9.1 技术支持
- **技术支持电话**：请查看系统内联系方式
- **技术支持邮箱**：请查看系统内联系方式
- **在线帮助**：系统内提供详细的操作说明

### 9.2 问题反馈
- 发现系统问题时及时反馈
- 提供详细的问题描述和操作步骤
- 附上错误截图或日志信息
- 说明使用的浏览器版本和操作系统

---

**文档版本**：v1.0  
**更新日期**：2024年12月  
**适用范围**：市县联动指挥调度平台用户
