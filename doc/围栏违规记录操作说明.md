# 120急救指挥系统围栏违规记录操作说明

## 一、功能简介

围栏违规记录功能用于查看和管理救护车辆的电子围栏违规情况。系统会记录所有车辆超出电子围栏范围的事件，包括违规时间、持续时长、具体位置等信息，并提供统计分析功能。

## 二、界面布局

### 1. 查询条件区域
- 违规开始时间：选择查询的时间范围
- 快捷时间选择：一小时内 | 一天内
- 车牌号：输入车牌号进行筛选
- 所属分站：输入分站名称进行筛选
- 自动刷新：勾选后每分钟自动刷新数据
- 搜索按钮：点击执行查询

### 2. 统计信息区域
显示当前查询条件下的统计数据：
- 总违规次数：所有违规事件的总数
- 总违规时长：累计违规持续时间
- 违规车辆数：发生违规的不同车辆数量
- 平均违规时长：平均每次违规的持续时间

### 3. 违规记录列表
列表显示详细的违规记录，包含以下字段：
- 车辆名称
- 车牌号
- 所属分站
- 违规开始时间
- 违规结束时间
- 违规时长
- 违规类型
- 违规位置（经纬度）
- 操作（轨迹回放）

## 三、操作指南

### 1. 查询违规记录
1. 设置查询条件：
   - 选择时间范围（可使用快捷选择）
   - 输入车牌号（可选）
   - 输入分站名称（可选）
2. 点击"搜索"按钮执行查询
3. 查询结果将显示在下方列表中

### 2. 使用快捷时间选择
- 点击"一小时内"：查询最近1小时的记录
- 点击"一天内"：查询最近24小时的记录

### 3. 查看统计信息
统计区域实时显示当前查询条件下的统计数据：
- 红色高亮表示数值超过预警阈值：
  - 总违规次数 > 10次
  - 总违规时长 > 2小时
  - 违规车辆数 > 5辆
  - 平均违规时长 > 30分钟

### 4. 查看轨迹回放
1. 在列表中找到需要查看的违规记录
2. 点击"轨迹回放"链接
3. 系统将打开轨迹回放窗口，显示违规发生时的车辆轨迹

### 5. 启用自动刷新
1. 勾选"自动刷新"复选框
2. 系统将每分钟自动刷新一次数据
3. 取消勾选可停止自动刷新

## 四、列表字段说明

1. **车辆信息**
   - 车辆名称：救护车的编号或名称
   - 车牌号：车辆牌照号码
   - 所属分站：车辆所属的急救分站

2. **时间信息**
   - 违规开始时间：车辆驶出围栏的时间点
   - 违规结束时间：车辆返回围栏或记录结束的时间点
   - 违规时长：超出围栏的持续时间

3. **位置信息**
   - 违规位置经度：首次超出围栏时的经度坐标
   - 违规位置纬度：首次超出围栏时的纬度坐标

4. **其他信息**
   - 违规类型：目前主要为"超出围栏"
   - 操作：提供轨迹回放功能入口

## 五、注意事项

1. **查询范围**
   - 建议查询时间范围不要过大，以免影响系统性能
   - 可以使用车牌号或分站名称缩小查询范围

2. **自动刷新**
   - 启用自动刷新会每分钟查询一次数据
   - 长时间不使用时建议关闭自动刷新

3. **数据导出**
   - 可以通过浏览器的复制功能导出坐标信息
   - 支持选择性复制表格内容

4. **轨迹回放**
   - 轨迹回放窗口支持调整大小
   - 可以同时打开多个轨迹回放窗口进行对比

## 六、常见问题

1. **为什么统计数据显示为红色？**
   - 表示该统计指标超过了预警阈值
   - 需要重点关注这些异常数据

2. **查询结果为空？**
   - 检查时间范围是否正确
   - 确认车牌号或分站名称是否输入正确
   - 验证是否存在符合条件的违规记录

3. **自动刷新不工作？**
   - 确认是否勾选了自动刷新复选框
   - 检查网络连接是否正常
   - 确认浏览器标签页是否处于活动状态

通过合理使用围栏违规记录功能，可以有效监控和管理车辆的围栏违规情况，及时发现和处理异常情况，提高车辆管理的效率和安全性。 