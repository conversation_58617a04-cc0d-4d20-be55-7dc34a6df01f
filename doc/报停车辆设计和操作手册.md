# 120急救指挥系统 - 报停车辆功能设计与操作手册

## 1. 功能概要

### 1.1 功能简介
车辆报停功能是120急救指挥系统的重要组成部分，用于管理救护车的临时停用状态。当救护车因维修、保养、驻场等原因无法正常执行急救任务时，可通过本功能进行报停登记，确保调度系统能够准确掌握车辆可用状态。

### 1.2 核心功能
- **报停记录管理**：创建、查询、编辑车辆报停记录
- **录音关联**：支持关联电话录音文件，便于追溯报停原因
- **状态监控**：实时监控报停车辆状态，包括报停中、已恢复、已超时
- **预警提醒**：基于报停原因设置的预警阈值，自动提醒相关人员
- **恢复使用**：支持报停车辆恢复正常使用状态

### 1.3 业务价值
- 提高调度效率，避免调度已报停车辆
- 规范报停流程，确保所有停用车辆都有明确记录
- 通过录音关联，提供完整的事件追溯链条
- 智能预警机制，防止车辆超时报停影响服务

## 2. 功能目的

### 2.1 主要目标
1. **提升调度准确性**：确保调度员能够准确识别可用车辆，避免调度已报停车辆
2. **规范化管理**：建立标准化的车辆报停流程和记录体系
3. **信息透明化**：通过录音关联和详细记录，保证报停信息的完整性和可追溯性
4. **智能化监控**：通过预警机制，及时提醒相关人员关注超时报停车辆

### 2.2 解决的问题
- 调度员无法及时了解车辆真实状态
- 报停信息分散，缺乏统一管理
- 车辆超时报停缺乏有效监控
- 报停原因无法有效追溯

## 3. 数据库设计

### 3.1 车辆报停记录表 (p_mobile_stop_records)

```sql
CREATE TABLE `p_mobile_stop_records` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `vehicle_id` varchar(36) NOT NULL COMMENT '车辆id',
  `vehicle_no` varchar(20) NOT NULL COMMENT '车牌号',
  `stop_reason_id` varchar(32) NOT NULL COMMENT '报停原因ID',
  `stop_reason` varchar(100) NOT NULL COMMENT '报停原因',
  `stop_reason_code` varchar(20) NOT NULL COMMENT '报停原因编码',
  `expected_duration` int NOT NULL COMMENT '预计报停时长(小时)',
  `warning_threshold` int NOT NULL COMMENT '预警阈值(小时)',
  `reminder_interval` int NOT NULL COMMENT '提醒间隔(分钟)',
  `start_time` datetime NOT NULL COMMENT '报停开始时间',
  `expected_end_time` datetime DEFAULT NULL COMMENT '预计结束时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-报停中，2-已恢复，3-已超时',
  `audio_file_id` varchar(100) DEFAULT NULL COMMENT '关联录音文件ID，就是表p_call的id',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `seat_user` varchar(30) DEFAULT NULL COMMENT '报停接线员用户真实姓名',
  `seat_id` varchar(36) DEFAULT NULL COMMENT '报停座席ID,对应p_seat的ID',
  `seat_code` varchar(30) DEFAULT NULL COMMENT '报停座席号，例如 2号坐席，对应p_seat的seat_id',
  `seat_user_id` varchar(32) DEFAULT NULL COMMENT '报停接线员用户id',
  `seat_user_name` varchar(30) DEFAULT NULL COMMENT '报停接线员用户登录账号/工号',
  `disabled` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` varchar(36) NOT NULL COMMENT '创建人ID',
  `create_user` varchar(36) NOT NULL COMMENT '创建人',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `last_update_user_id` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `last_update_user` varchar(36) DEFAULT NULL COMMENT '最后更新人',
  PRIMARY KEY (`id`),
  KEY `idx_vehicle_no` (`vehicle_no`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='车辆报停记录表';
```

### 3.2 报停原因表 (p_mobile_stop_reason)

```sql
CREATE TABLE `p_mobile_stop_reason` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `reason_code` varchar(20) NOT NULL COMMENT '原因编码',
  `reason_name` varchar(100) NOT NULL COMMENT '原因名称',
  `reason_desc` varchar(500) DEFAULT NULL COMMENT '原因描述',
  `default_duration` int DEFAULT NULL COMMENT '默认报停时长(小时)',
  `default_warning_threshold` int DEFAULT NULL COMMENT '默认预警阈值(小时)',
  `default_reminder_interval` int DEFAULT NULL COMMENT '默认提醒间隔(分钟)',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序号',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `disabled` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` varchar(36) NOT NULL COMMENT '创建人ID',
  `create_user` varchar(36) NOT NULL COMMENT '创建人',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `last_update_user_id` varchar(36) DEFAULT NULL COMMENT '最后更新人ID',
  `last_update_user` varchar(36) DEFAULT NULL COMMENT '最后更新人',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='报停原因规则表';
```

### 3.3 数据字段说明

#### 报停状态定义
- **1-报停中**：车辆正在报停状态，不可调度
- **2-已恢复**：车辆已恢复正常使用
- **3-已超时**：车辆报停时间超过预期，需要关注

#### 核心字段
- **vehicle_id/vehicle_no**：关联车辆信息
- **stop_reason_id/stop_reason_code**：报停原因标识
- **expected_duration**：预计报停时长（小时）
- **warning_threshold**：预警阈值（小时）
- **audio_file_id**：关联p_call表的录音记录
- **seat_***字段：完整记录操作人员信息

## 4. 系统页面架构

### 4.1 页面组成
1. **index.html** - 120急救指挥主界面，提供Tab方式访问报停功能
2. **vehicleStopList.html** - 报停记录查询和管理主页面
3. **vehicleStopEdit.html** - 报停记录新增和编辑页面
4. **callList.html** - 通话记录页面，支持关联报停车辆

### 4.2 页面关系
- index.html作为主入口，通过Tab方式加载vehicleStopList.html
- vehicleStopList.html通过iframe方式加载vehicleStopEdit.html进行编辑
- callList.html可以查看和处理与报停相关的通话记录

## 5. 操作步骤详解

### 5.1 查看报停车辆列表

#### 前置条件
- 用户已登录120急救指挥系统
- 用户具有查看车辆报停记录的权限

#### 操作步骤
1. 在主界面点击"车辆报停"Tab标签
2. 系统自动加载vehicleStopList.html页面
3. 页面显示所有报停记录，包括：
   - 车牌号、报停原因、开始时间
   - 预计时长、状态、操作人员等信息
4. 使用查询条件筛选：
   - 时间范围：选择开始和结束日期
   - 车牌号：输入完整或部分车牌号
   - 报停原因：选择特定原因
   - 状态：选择报停中/已恢复/已超时

#### 后置条件
- 显示符合条件的报停记录列表
- 支持对记录进行查看、编辑、恢复等操作

### 5.2 新增报停记录

#### 前置条件
- 用户已登录系统并具有新增报停记录权限
- 需要确认车辆当前不在报停状态

#### 操作步骤
1. 在报停列表页面点击"新增报停"按钮
2. 系统打开vehicleStopEdit.html编辑页面（iframe方式）
3. 填写报停信息：
   
   **第一步：选择车辆**
   - 通过查询条件筛选车辆（区域、分站、车牌号、状态）
   - 从列表中选择需要报停的车辆
   - 系统显示选中车辆的详细信息
   
   **第二步：填写报停详情**
   - 选择报停原因（系统自动填入默认时长）
   - 确认或调整预计报停时长
   - 设置报停开始时间
   - 填写联系电话
   - 添加详细原因说明和备注
   
   **第三步：关联录音（可选）**
   - 选择相关的通话录音文件
   - 确认录音与报停事件的关联性

4. 点击"保存"按钮提交报停记录
5. 系统验证数据完整性并保存记录
6. 成功后关闭编辑窗口并刷新列表

#### 后置条件
- 新增报停记录成功保存到数据库
- 车辆状态更新为报停中
- 调度系统可识别该车辆不可调度
- 相关人员收到报停通知

### 5.3 编辑报停记录

#### 前置条件
- 用户具有编辑报停记录权限
- 报停记录存在且状态允许编辑

#### 操作步骤
1. 在报停列表中找到需要编辑的记录
2. 点击"编辑"按钮
3. 系统加载vehicleStopEdit.html编辑页面，并回填原有数据
4. 修改允许编辑的字段：
   - 预计报停时长
   - 联系电话
   - 详细原因说明
   - 备注信息
   - 关联录音
5. 点击"保存"按钮更新记录

#### 后置条件
- 报停记录信息更新成功
- 修改日志记录操作人员和时间
- 如修改了预计时长，预警时间自动调整

### 5.4 恢复车辆使用

#### 前置条件
- 车辆当前处于报停状态
- 用户具有恢复车辆使用权限
- 车辆已完成维修/保养等停用原因

#### 操作步骤
1. 在报停列表中找到需要恢复的车辆记录
2. 点击"恢复使用"按钮
3. 系统弹出确认对话框
4. 确认恢复操作
5. 系统更新记录状态为"已恢复"
6. 设置实际结束时间为当前时间
7. 更新车辆在调度系统中的可用状态

#### 后置条件
- 报停记录状态更新为"已恢复"
- 车辆重新可用于调度
- 生成恢复使用的操作日志

### 5.5 查看和播放关联录音

#### 前置条件
- 报停记录存在关联的录音文件
- 用户具有查看录音权限

#### 操作步骤
1. 在报停记录详情中查看录音信息
2. 点击"播放录音"按钮
3. 系统调用音频播放组件
4. 支持播放控制：播放/暂停/停止/进度调节
5. 可选择下载录音文件到本地

#### 后置条件
- 成功播放录音文件
- 记录录音查看日志

### 5.6 在通话记录中关联报停

#### 前置条件
- 存在相关的通话记录
- 通话内容涉及车辆报停事宜

#### 操作步骤
1. 在callList.html通话记录页面
2. 找到相关的通话记录
3. 如果通话涉及车辆报停，显示车牌号链接
4. 点击车牌号链接查看报停详情
5. 可以在通话记录和报停记录间建立关联

#### 后置条件
- 通话记录与报停记录形成关联
- 便于后续的信息追溯和核查

## 6. 业务规则

### 6.1 报停约束规则
- 同一车辆不能同时存在多条"报停中"状态的记录
- 报停开始时间不能晚于当前时间超过24小时
- 预计报停时长必须在0.5-168小时范围内
- 车辆恢复使用后，不能再次编辑该条报停记录

### 6.2 预警规则
- 当报停时间达到预警阈值时，系统自动发送提醒
- 超过预计结束时间的报停记录自动标记为"已超时"
- 超时报停车辆在调度界面显示特殊标识

### 6.3 权限规则
- 普通调度员可以查看和新增报停记录
- 主管级别用户可以编辑和删除报停记录
- 系统管理员可以管理报停原因配置

## 7. 异常处理

### 7.1 常见异常情况
1. **网络超时**：保存操作超时时显示友好提示
2. **数据冲突**：同时编辑同一记录时的冲突处理
3. **权限不足**：用户权限不足时的提示和限制
4. **车辆状态异常**：车辆已在其他任务中时的处理

### 7.2 错误处理机制
- 所有操作都有完整的错误捕获和用户提示
- 关键操作失败时提供重试机制
- 数据验证失败时提供具体的错误说明
- 系统异常时记录详细日志便于排查

## 8. 技术实现要点

### 8.1 前端技术
- 使用EasyUI框架构建用户界面
- jQuery处理用户交互和Ajax请求
- iframe方式实现页面嵌套和数据传递
- 音频播放组件支持录音回放

### 8.2 数据交互
- RESTful API接口设计
- JSON格式数据传输
- 统一的错误码和消息格式
- 数据分页和缓存机制

### 8.3 性能优化
- 查询结果分页加载，提高页面响应速度
- 音频文件按需加载，避免带宽浪费
- 关键操作使用异步处理，提升用户体验
- 数据缓存减少重复请求

## 9. 系统维护

### 9.1 日常维护
- 定期清理过期的报停记录
- 监控录音文件的存储空间使用情况
- 检查预警提醒功能的正常运行
- 备份重要的报停记录数据

### 9.2 数据统计
- 按时间段统计报停车辆数量和原因分布
- 分析车辆平均报停时长和超时率
- 生成报停原因统计报表
- 监控系统使用情况和性能指标

## 10. 注意事项

### 10.1 操作注意事项
1. 报停车辆时必须确认车辆当前状态，避免重复报停
2. 填写报停原因时应详细准确，便于后续追溯
3. 关联录音文件时要确保录音内容与报停事件相关
4. 车辆恢复使用前应确认维修/保养工作已完成
5. 定期检查超时报停车辆，及时处理异常情况

### 10.2 安全注意事项
1. 严格按照权限操作，不得越权访问或修改数据
2. 录音文件涉及隐私信息，应妥善保管和使用
3. 报停记录可能涉及医疗急救资源配置，操作需谨慎
4. 定期更新密码，保护账号安全

### 10.3 系统限制
1. 录音文件大小限制，超大文件可能影响播放
2. 并发编辑可能导致数据冲突，需要注意操作时序
3. 历史数据量大时查询可能较慢，建议使用时间范围筛选
4. 移动端访问可能存在兼容性问题，建议使用PC端操作 