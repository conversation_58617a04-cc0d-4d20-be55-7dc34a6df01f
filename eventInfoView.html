<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>事件详情查看</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            font-size: 14px;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .container {
            height: 100vh;
            background-color: white;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #2c5aa0;
            color: white;
            padding: 15px 20px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: normal;
        }
        
        .tabs-container {
            flex: 1;
            padding: 10px;
            overflow: hidden;
            min-height: 500px;
        }
        
        #eventTabs {
            height: calc(100vh - 120px);
        }
        
        .tabs-header {
            background-color: #f5f5f5;
            padding: 8px 8px 0 8px;
            border-bottom: 1px solid #ddd;
        }
        
        .tabs li a.tabs-inner {
            padding: 8px 15px;
            border-radius: 4px 4px 0 0;
            font-size: 14px;
        }
        
        .tabs li.tabs-selected a.tabs-inner {
            background: #fff;
            border-bottom: 1px solid #fff;
            color: #333;
            font-weight: bold;
        }
        
        .tabs-panels {
            padding: 0;
            border: none;
            height: calc(100% - 40px);
        }
        
        .tabs-panels .panel {
            overflow: hidden;
            height: 100%;
        }
        
        .panel-body {
            overflow: hidden;
            height: 100%;
        }
        
        .event-content {
            padding: 20px;
            height: calc(100% - 40px);
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c5aa0;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .info-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        .info-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 120px;
            text-align: right;
        }
        
        .info-table .value {
            background-color: white;
        }
        
        .data-grid {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .data-grid th {
            background-color: #f8f9fa;
            padding: 8px 6px;
            border: 1px solid #ddd;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .data-grid td {
            padding: 6px;
            border: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
        }
        
        .data-grid tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-processing { background-color: #d1ecf1; color: #0c5460; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        
        .no-data {
            text-align: center;
            color: #999;
            padding: 20px;
            font-style: italic;
        }
        
        .related-event {
            background-color: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .related-event .title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 8px;
        }
        
        .error-message {
            text-align: center;
            color: #dc3545;
            font-size: 16px;
            padding: 50px;
        }
        
        .loading-message {
            text-align: center;
            color: #6c757d;
            font-size: 16px;
            padding: 50px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1 id="page-title">事件详情查看</h1>
        </div>
        
        <div class="tabs-container">
            <div id="eventTabs">
                <!-- Tabs will be dynamically added here -->
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript" src="script/jQuery.print.js"></script>
    <script type="text/javascript" src="script/symptomData.js"></script>


    <script type="text/javascript">
        var _baseUrl =   window.location.protocol + "//" + window.location.host+"/";
        var _dics = new Array(); // EDC字典表数组
        var eventId = null;
        var eventAcceptanceList = [];
        
        // 获取URL参数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return null;
        }

        // 创建事件标签页
        function createEventTab(eventData, title, index) {
            console.log('开始创建tab:', title);
            var content = createEventContent(eventData);
            console.log('生成的content长度:', content.length);
            
            try {
                $('#eventTabs').tabs('add', {
                    title: title,
                    content: content,
                    closable: false
                });
                console.log('tab创建成功:', title);
            } catch (e) {
                console.error('创建tab失败:', e);
            }
        }

        // 创建事件内容HTML
        function createEventContent(data) {
            var html = '<div class="event-content">';
            
            // 基本信息
            html += '<div class="section">';
            html += '<div class="section-title">基本信息</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">事件编号：</td>';
            html += '<td class="value">' + (data.id || '-') + '</td>';
            html += '<td class="label">事件状态：</td>';
            html += '<td class="value">' + getEventStatusHtml(data.status) + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">事件来源：</td>';
            html += '<td class="value">' + (data.eventSrcName || '-') + '</td>';
            html += '<td class="label">呼叫类型：</td>';
            html += '<td class="value">' + (data.callTypeCodeName || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">所属区域：</td>';
            html += '<td class="value">' + (data.callRegionName || '-') + '</td>';
            html += '<td class="label">来电时间：</td>';
            html += '<td class="value">' + (data.callInTimes || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">紧急程度：</td>';
            html += '<td class="value">' + getEmergencyLevelText(data.emergencyDegree) + '</td>';
            html += '<td class="label">患者人数：</td>';
            html += '<td class="value">' + (data.patientAmount || '1') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">重大事件：</td>';
            html += '<td class="value">' + (data.eventType == '1' ? '是' : '否') + '</td>';
            html += '<td class="label">事件类型：</td>';
            html += '<td class="value">' + (data.eventTypeName || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">事故等级：</td>';
            html += '<td class="value">' + (data.eventLevelName || '-') + '</td>';
            html += '<td class="label">是否测试：</td>';
            html += '<td class="value">' + (data.isTest == 1 ? '是' : '否') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 地址信息
            html += '<div class="section">';
            html += '<div class="section-title">地址信息</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">现场地址：</td>';
            html += '<td class="value" colspan="3">' + (data.address || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">等车地址：</td>';
            html += '<td class="value" colspan="3">' + (data.addressWait || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">送往地址：</td>';
            html += '<td class="value" colspan="3">' + (data.addressDelivery || '-') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 联系信息
            html += '<div class="section">';
            html += '<div class="section-title">联系信息</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">主叫号码：</td>';
            html += '<td class="value">' + (data.callIn || '-') + '</td>';
            html += '<td class="label">联系电话：</td>';
            html += '<td class="value">' + (data.contact || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">联系人：</td>';
            html += '<td class="value">' + (data.contacter || '-') + '</td>';
            html += '<td class="label">外籍人员：</td>';
            html += '<td class="value">' + (data.patientIsForeign == '1' ? '是' : '否') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 患者信息
            html += '<div class="section">';
            html += '<div class="section-title">患者信息</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">患者姓名：</td>';
            html += '<td class="value">' + (data.patientName || '-') + '</td>';
            html += '<td class="label">性别：</td>';
            html += '<td class="value">' + getGenderText(data.patientGender) + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">年龄：</td>';
            html += '<td class="value">' + (data.patientAge || '-') + '</td>';
            html += '<td class="label">国籍：</td>';
            html += '<td class="value">' + (data.patientCountryName || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">民族：</td>';
            html += '<td class="value">' + (data.patientRaceName || '-') + '</td>';
            html += '<td class="label">需要担架：</td>';
            html += '<td class="value">' + (data.isNeedStretcher == 1 ? '是' : '否') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 事件详情
            html += '<div class="section">';
            html += '<div class="section-title">事件详情</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">呼叫原因：</td>';
            html += '<td class="value" colspan="3">' + (data.majorCall ? data.majorCall.replace('_', ' ') : '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">病状判断：</td>';
            html += '<td class="value" colspan="3">' + (data.patientDiagnosis || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">特殊要求：</td>';
            html += '<td class="value" colspan="3">' + (data.specialRequirement || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">120备注：</td>';
            html += '<td class="value" colspan="3">' + (data.centerRemark || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">ALDS汇总：</td>';
            html += '<td class="value" colspan="3">' + (data.mpdsSummary || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">上次来电：</td>';
            html += '<td class="value" colspan="3">' + (data.patientInfoHistory || '-') + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<td class="label">联动类型：</td>';
            html += '<td class="value">' + (data.pushOuterTypeName || '-') + '</td>';
            html += '<td class="label">创建时间：</td>';
            html += '<td class="value">' + (data.createTime || '-') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 关联首次事件
            if (data.firstEventId) {
                html += '<div class="section">';
                html += '<div class="section-title">关联首次事件</div>';
                html += '<div class="related-event">';
                html += '<div class="title">关联的首次事件信息</div>';
                html += '<div id="related-event-details-' + data.id + '">正在加载关联事件信息...</div>';
                html += '</div>';
                html += '</div>';
            }
            
            // 调度员信息
            html += '<div class="section">';
            html += '<div class="section-title">调度员信息</div>';
            html += '<table class="info-table">';
            html += '<tr>';
            html += '<td class="label">受理台：</td>';
            html += '<td class="value">' + (data.seatCode || '-') + '</td>';
            html += '<td class="label">受理员：</td>';
            html += '<td class="value">' + (data.seatUserName || '-') + '</td>';
            html += '</tr>';
            html += '</table>';
            html += '</div>';
            
            // 调度任务
            html += '<div class="section">';
            html += '<div class="section-title">调度任务</div>';
            html += '<div id="dispatch-tasks-' + data.id + '">正在加载调度任务...</div>';
            html += '</div>';
            
            // 通话记录
            html += '<div class="section">';
            html += '<div class="section-title">通话记录</div>';
            html += '<div id="call-records-' + data.id + '">正在加载通话记录...</div>';
            html += '</div>';
            
            html += '</div>';
            
            return html;
        }

        // 异步加载关联首次事件信息
        function loadFirstEventInfo(firstEventId, eventId) {
            getEventById(firstEventId, function(firstEventData) {
                if (firstEventData) {
                    var details = '';
                    details += '<strong>事件编号：</strong>' + firstEventData.id + '&nbsp;&nbsp;';
                    details += '<strong>现场地址：</strong>' + (firstEventData.address || '-') + '&nbsp;&nbsp;';
                    details += '<strong>呼叫原因：</strong>' + (firstEventData.majorCall ? firstEventData.majorCall.replace('_', ' ') : '-') + '&nbsp;&nbsp;';
                    details += '<strong>联系电话：</strong>' + (firstEventData.contact || '-');
                    
                    $('#related-event-details-' + eventId).html(details);
                }
            }, function(e, url, errMsg) {
                console.error('获取关联首次事件失败:', errMsg);
                $('#related-event-details-' + eventId).html('<span style="color: #dc3545;">获取关联事件信息失败</span>');
            });
        }

        // 异步加载调度任务
        function loadDispatchTasks(eventId) {
            var params = {
                "eventId": eventId,
                "beginTime": '',
                "endTime": '',
                "stationId": '',
                "processId": '',
                "stationCode": ''
            };
            
            getMobileProcessByEventId(params, function(data) {
                displayDispatchTasks(data, eventId);
            }, function(e, url, errMsg) {
                console.error('获取调度任务失败:', errMsg);
                $('#dispatch-tasks-' + eventId).html('<div class="no-data">获取调度任务失败</div>');
            });
        }

        // 显示调度任务
        function displayDispatchTasks(tasks, eventId) {
            var container = $('#dispatch-tasks-' + eventId);
            
            if (!tasks || tasks.length === 0) {
                container.html('<div class="no-data">暂无调度任务数据</div>');
                return;
            }
            
            var html = '<table class="data-grid">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>分站名称</th>';
            html += '<th>车辆别名</th>';
            html += '<th>车牌号码</th>';
            html += '<th>任务状态</th>';
            html += '<th>车辆状态</th>';
            html += '<th>出动方式</th>';
            html += '<th>急救半径</th>';
            html += '<th>调度时间</th>';
            html += '<th>调度座席</th>';
            html += '<th>调度员</th>';
            html += '<th>出车地址</th>';
            html += '<th>改派情况</th>';
            html += '<th>分站拒绝</th>';
            html += '<th>备注</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            
            for (var i = 0; i < tasks.length; i++) {
                var task = tasks[i];
                html += '<tr>';
                html += '<td>' + (task.stationName || '-') + '</td>';
                html += '<td>' + (task.carName || '-') + '</td>';
                html += '<td>' + (task.plateNum || '-') + '</td>';
                html += '<td>' + (task.isFinishdStr || '-') + '</td>';
                html += '<td>' + (task.toStatusStr || '-') + '</td>';
                html += '<td>' + (task.callTypeCodeName || '-') + '</td>';
                html += '<td>' + (task.emergencyRadius || '-') + '</td>';
                html += '<td>' + (task.mobileProcessCreateTime || '-') + '</td>';
                html += '<td>' + (task.seatCode || '-') + '</td>';
                html += '<td>' + (task.seatUser || '-') + '</td>';
                html += '<td title="' + (task.address || '') + '">' + (task.address ? (task.address.length > 20 ? task.address.substring(0, 20) + '...' : task.address) : '-') + '</td>';
                html += '<td>' + (task.changeSendCarNumber > 0 ? task.changeSendCarNumber + '次' : '否') + '</td>';
                html += '<td>' + (task.stationRefuseStatus == '1' ? '是' : '否') + '</td>';
                html += '<td title="' + (task.centerRemark || '') + '">' + (task.centerRemark ? (task.centerRemark.length > 15 ? task.centerRemark.substring(0, 15) + '...' : task.centerRemark) : '-') + '</td>';
                html += '</tr>';
            }
            
            html += '</tbody>';
            html += '</table>';
            
            container.html(html);
        }

        // 异步加载通话记录
        function loadCallRecords(eventId) {
            var params = {
                "eventId": eventId
            };
            
            getPhoneAndVideoCallListByEventId(params, function(data) {
                displayCallRecords(data, eventId);
            }, function(e, url, errMsg) {
                console.error('获取通话记录失败:', errMsg);
                $('#call-records-' + eventId).html('<div class="no-data">获取通话记录失败</div>');
            });
        }

        // 显示通话记录
        function displayCallRecords(records, eventId) {
            var container = $('#call-records-' + eventId);
            
            if (!records || records.length === 0) {
                container.html('<div class="no-data">暂无通话记录数据</div>');
                return;
            }
            
            var html = '<table class="data-grid">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>来/去电时间</th>';
            html += '<th>主叫号码</th>';
            html += '<th>被叫号码</th>';
            html += '<th>呼叫类型</th>';
            html += '<th>类型</th>';
            html += '<th>挂断时间</th>';
            html += '<th>处理结果</th>';
            html += '<th>通话时长(秒)</th>';
            html += '<th>是否接听</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            
            for (var i = 0; i < records.length; i++) {
                var record = records[i];
                html += '<tr>';
                html += '<td>' + (record.callTime || '-') + '</td>';
                html += '<td>' + (record.number || '-') + '</td>';
                html += '<td>' + (record.toNumber || '-') + '</td>';
                html += '<td>' + getCallTypeText(record.callType) + '</td>';
                html += '<td>' + (record.type || '-') + '</td>';
                html += '<td>' + (record.endTime || '-') + '</td>';
                html += '<td>' + getHandleStatusText(record.handleStatus) + '</td>';
                html += '<td>' + (record.duration || '-') + '</td>';
                html += '<td>' + (record.isAnswer == '1' ? '已接听' : '未接听') + '</td>';
                html += '</tr>';
            }
            
            html += '</tbody>';
            html += '</table>';
            
            container.html(html);
        }

        // 初始化函数
        function init() {            
            console.log('开始初始化页面');
            
            // 获取传入的事件ID
            eventId = getQueryVariable("eventId");
            console.log('获取到eventId:', eventId);
            
            if (!eventId) {
                console.log('未找到事件ID参数');
                $('#eventTabs').html('<div class="error-message">未找到事件ID参数</div>');
                return;
            }

            // 显示加载状态
            $('#eventTabs').html('<div class="loading-message">正在加载事件信息...</div>');
            console.log('显示加载状态');

            // 加载字典数据
            console.log('开始加载字典数据');
            queryAllDic(["event_source", "call_type", "patient_status", "country", "race", "push_outer_type"],
                function (data) {
                    console.log('字典数据加载成功:', data);
                    _dics = data;
                    // 字典加载完成后，加载事件受理列表
                    loadEventAcceptanceList();
                }, 
                function (e, url, errMsg) {
                    console.error('加载字典失败:', e, url, errMsg);
                    // 即使字典加载失败，也尝试加载事件
                    console.log('字典加载失败，直接加载事件受理列表');
                    loadEventAcceptanceList();
                }
            );
        }

        // 加载事件受理列表
        function loadEventAcceptanceList() {
            console.log('开始加载事件受理列表，eventId:', eventId);
            
            selectEventAcceptanceList({ id: eventId }, function(data) {
                console.log('selectEventAcceptanceList 返回数据:', data);
                
                if (!data || data.length === 0) {
                    console.log('未找到事件受理记录');
                    $('#eventTabs').html('<div class="error-message">未找到相关事件受理记录</div>');
                    return;
                }
                
                eventAcceptanceList = data;
                console.log('找到', data.length, '条事件受理记录');
                
                // 先移除加载提示
                $('#eventTabs').removeClass('loading-message');
                
                // 清空tabs容器的HTML内容但保留结构
                $('#eventTabs').html('');
                
                // 重新初始化tabs组件
                $('#eventTabs').tabs({
                    height: 'auto',
                    border: false,
                    plain: true,
                    onLoad: function(panel) {
                        console.log('tab面板加载完成');
                    }
                });
                
                console.log('tabs组件初始化完成');
                
                // 延迟一点时间确保tabs组件完全初始化
                setTimeout(function() {
                    console.log('开始创建所有tab');
                    
                    // 为每个事件受理记录创建tab
                    data.forEach(function(item, index) {
                        var title = "";
                        if (item.serialNo == 0) {
                            title = "首次事件受理";
                        } else {
                            title = "第" + (item.serialNo + 1) + "次事件受理";
                        }
                        console.log('创建tab:', title, 'item:', item);
                        createEventTab(item, title, item.serialNo);
                        
                        // 异步加载关联数据
                        if (item.firstEventId) {
                            loadFirstEventInfo(item.firstEventId, item.id);
                        }
                        loadDispatchTasks(item.id);
                        loadCallRecords(item.id);
                    });
                    
                    console.log('所有tab创建完成');
                    
                    // 强制刷新tabs布局
                    setTimeout(function() {
                        console.log('刷新tabs布局');
                        $('#eventTabs').tabs('resize');
                        
                        // 检查tabs是否真的创建了
                        var tabCount = $('#eventTabs').tabs('tabs').length;
                        console.log('当前tab数量:', tabCount);
                        
                        if (tabCount === 0) {
                            console.error('tabs创建失败，尝试备用方案');
                            // 备用方案：直接显示HTML内容
                            var content = createEventContent(data[0]);
                            $('#eventTabs').html('<div style="padding: 20px;">' + content + '</div>');
                        }
                    }, 200);
                }, 100); // 延迟100毫秒
                
            }, function(e, url, errMsg) {
                console.error('获取事件受理列表失败:', e, url, errMsg);
                $('#eventTabs').html('<div class="error-message">获取事件受理列表失败：' + errMsg + '</div>');
            });
        }

        // 辅助函数
        function getEventStatusHtml(status) {
            var text = getEventStatusText(status);
            var className = '';
            
            switch (status) {
                case '1': className = 'status-pending'; break;
                case '2': className = 'status-processing'; break;
                case '3': className = 'status-completed'; break;
                case '4': className = 'status-cancelled'; break;
                case '6': className = 'status-pending'; break;
                case '7': className = 'status-pending'; break;
                default: className = 'status-pending'; break;
            }
            
            return '<span class="status-tag ' + className + '">' + text + '</span>';
        }

        function getEventStatusText(status) {
            switch (status) {
                case '1': return '未调度-落单';
                case '2': return '已调度';
                case '3': return '已完成';
                case '4': return '已撤销';
                case '6': return '未调度-待派';
                case '7': return '未调度-预约';
                default: return '未知';
            }
        }

        function getGenderText(gender) {
            switch (gender) {
                case '0': return '男';
                case '1': return '女';
                case '2': return '未知';
                default: return '';
            }
        }

        function getEmergencyLevelText(level) {
            switch (level) {
                case 'A': return 'A级-最高紧急';
                case 'B': return 'B级-高危';
                case 'C': return 'C级-紧急';
                case 'D': return 'D级-较紧急';
                case 'E': return 'E级-急诊';
                case 'O': return 'O级-非紧急';
                default: return '';
            }
        }

        function getCallTypeText(type) {
            switch (type) {
                case 'IN': return '呼入';
                case 'OU': return '呼出';
                case 'LO': return '内部呼叫';
                default: return '';
            }
        }

        function getHandleStatusText(status) {
            switch (status) {
                case '0': return '无效';
                case '1': return '已处理';
                case '2': return '已合并';
                case '3': return '未处理';
                default: return '';
            }
        }

        // 页面加载完成后执行
        $(document).ready(function() {
            // 禁用文本选择
            if (document.body) {
                document.body.onselectstart = function () {
                    return false;
                }
            }
            
            console.log('DOM加载完成，准备初始化');
            init();
        });
    </script>
</body>
</html> 