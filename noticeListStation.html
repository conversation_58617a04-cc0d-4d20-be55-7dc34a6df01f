<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>通知单列表</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/eventReport.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
                color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important;
        }
        #wavPlayWindow{
            overflow: initial;
        }
        .datebox {
            height: 1.7rem; /* 设置 DateTimeBox 输入框的高度 */
        }
        .clear-fix .datebox{
            height: 2.1rem; /* 设置 DateTimeBox 输入框的高度 */
        }
    </style>
</head>
<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;">
                    发布时间： <input id="startTime" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endTime" class="easyui-datetimebox" style="width:200px;" />
                    <span>&nbsp;状态：</span>
                    <select id="isReceive" class="easyui-combobox" style="width:120px;">
                        <option value="">全部</option>
                        <option value="1">已读</option>
                        <option value="0">未读</option>
                    </select>
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px; margin-left: 20px" href="javascript:getNoticeList();">
                        <i class="fa fa-search"></i>&nbsp;查询
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="notice-list" style="height: Calc(100% - 100px)"
                       pagination="true" singleSelect="true" checkOnSelect="false" rownumbers="true" fit="true" toolbar="#tb">
                    <thead>
                        <tr>
                            <th field="ck" checkbox="true"></th>
                            <th field="id" hidden></th>
                            <th field="noticeUserId" hidden></th>
                            <th field="isReceive" width="2%" align="center" formatter="isReceiveFormatter"></th>
                            <th field="noticeTitle" width="30%" align="center" formatter="contentFormatter">标题</th>
                            <th field="noticeContent" width="30%" align="center" formatter="contentFormatter">内容</th>
                            <th field="createUser" width="8%" align="center">发布人</th>
                            <th field="createTime" width="15%" align="center">发布时间</th>
                            <th field="operation" width="15%" formatter="operationFormatter">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat;
        var page = 1;
        var size = 15;
        var largerList = [];
        $(document).ready(function () {
            $('#notice-list').datagrid('hideColumn', 'ck');
            $('#notice-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });
            //分页控件
            var pg = $("#notice-list").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: size,
                    pageNumber: page,
                    pageList: [10, 15, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getNoticeList();
                    }
                });
            }
            getNoticeList();
        })
        
        function operationFormatter(value, row, index){
            let node = `<a href="javascript:doSelect(${index});" class="easyui-linkbutton" style="display: inline-block;height: 22px;line-height: 22px;color: white;background: #39c;width: auto;padding: 0 6px;font-size: 1em;margin: 5px 3px 5px 0;border-radius: 5px;">查看</a>
            `;
            return node
        }
        
        function isReceiveFormatter(value, row, index){
            if(value === "0"){
                return `<span style="display:inline-block;width:10px;height:10px;background:red;border-radius:50%;"></span>`;
            }else{
                return `<span></span>`;
            }
        }

        function contentFormatter(value, row, index){
            if (!value) return '';
            const plainText = value.replaceAll(/<[^>]+>/g, '').replaceAll("&nbsp;", "").replaceAll(/\s+/g, ' ').trim();
            const maxLength = 25;
            return plainText.length > maxLength ? plainText.substring(0, maxLength) + '......' : value;
        }
        
        function doSelect(index){
            let data = largerList[index];
            let urlParams = "id=" + data.id;
            if(data.noticeUserId){
                urlParams += "&noticeUserId=" + data.noticeUserId;
            }
            openTab("查看通知单", "noticeDetail.html?" + urlParams, data.id);
        }

        function openTab(title, href, iframeName) {
            // 在 iframe 页面中调用父页面的元素或 id
            var parentElement = window.parent.$('#main-tabs');
            var e = parentElement.tabs('exists', title);
            if (e) {
                parentElement.tabs('select', title);
                var tab = parentElement.tabs('getSelected');
                parentElement.tabs('update', {
                    tab: tab,
                    options: {
                        id: iframeName,
                        title: title,
                        content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                        closable: true,
                        selected: true
                    }
                });
            } else {
                parentElement.tabs('add', {
                    id: iframeName,
                    title: title,
                    content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                    iconCls: '',
                    closable: true
                });
            }
        }
        
        function editSuccess(){
            $.messager.alert('提示', '保存成功！', 'info');
            getNoticeList();
        }
        
        //获得列表数据
        function getNoticeList() {
            let _user = JSON.parse(window.localStorage.getItem("_user"));
            if(!_user.Station || !_user.Station.id){
                $.messager.alert('提示', '分站ID为空', "warning");
            } 
            //打开进度条：
            $('#notice-list').datagrid('loading');//打开等待div
            var params = {
                "pageNum": page,
                "pageSize": size,
                'startTime': $("#startTime").val(),
                'endTime': $("#endTime").val(),
                'isReceive': $("#isReceive").val(),
                "stationId": _user.Station.id
            };
            selectStationNoticePage(params, function (data) {
                largerList = data.content;
                $('#notice-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#notice-list').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            noticeUserId: data.content[i].noticeUserId,
                            isReceive: data.content[i].isReceive,
                            noticeTitle: data.content[i].noticeTitle,
                            noticeContent: data.content[i].noticeContent,
                            createUser: data.content[i].createUser,
                            createTime: data.content[i].createTime
                        }
                    });
                }
                $("#notice-list").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                $('#notice-list').datagrid('loaded');//关闭loding进度条；

            }, function (e, url, errMsg) {
                $('#notice-list').datagrid('loaded');//关闭loding进度条；
            });
        }
    </script>
</body>
</html>
