<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>街道列表页</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        #treeMenu {
            width: 100%;
            height: 100%;
            font-size: 1.5em;
            margin: 0;
            border: solid #B3D2E1 1px;
        }

        #treeMenu .tree-title {
            vertical-align: top;
            font-size: 1rem;
        }

        #treeMenu .tree-expanded,
        .tree-collapsed,
        .tree-folder,
        .tree-file,
        .tree-checkbox,
        .tree-indent {
            vertical-align: top;
        }
        /* 查询输入框字体大小调整 */
        .textbox .textbox-prompt,.textbox .textbox-text{
            font-size: 16px;
        }
        /* 自定义样式 ------------------------------------------*/
        .tabel{
            height: 100%;
            width: 100%;
            border: 1px solid #B3D2E1;
        }
        .tabel>.tabel-head{
            width: 100%;
            height: 32px;
            font-weight: bold;
        }
        .tabel>.tabel-head>span{
            display: inline-block;
            height: 32px;
            line-height: 32px;
            text-align: center;
            float: left;
            border-right: 1px solid #B3D2E1;
            border-bottom: 1px solid #B3D2E1;
            cursor: pointer;
            padding: 0 5px;
        }
        .tabel>.tabel-head>span:last-child{
            border-right:none;
        }
        .tabel>.tabel-content{
            height: calc(100% - 34px);
            width: 100%;
            overflow: auto;
        }
        .tabel>.tabel-content>.tabel-content-row{
            width: 100%;
        }
        .tabel>.tabel-content>.tabel-content-row>span{
            display: inline-block;
            height: 32px;
            line-height: 32px;
            text-align: center;
            float: left;
            border-right: 1px solid #B3D2E1;
            border-bottom: 1px solid #B3D2E1;
            cursor: pointer;
            padding: 0 5px;
        }
        .tabel>.tabel-content>.tabel-content-row>.hidden{
            overflow : hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
            -webkit-box-orient: vertical; /* 垂直排列 */
            word-break: break-all;  /* 内容自动换行 */
        }
        .tabel>.tabel-content>.tabel-content-row>span:last-child{
            border-right: none;
        }
        .showText{
            position: fixed;
            top: -300px;
            padding: 3px 5px;
            border-radius: 3px;
            background-color: #0081c2;
            color: #ffffff;
            box-shadow: 0 0 3px 0px #6a6666;
            font-size: 1.2rem;
            max-width: 16rem;
            word-wrap: break-word;
        }
        .showText:before {
            content: "";
            width: 0px;
            height: 0px;
            border-top: 10px solid #0081c2;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            position: absolute;
            top: 100%;
            left: calc(50% - 7px);
		}
        .textbox{
            /* border: none !important; */
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false" style="height: 100%;">
        <!-- 左侧菜单树列表 -->
        <div style="width: 22.5em; height: 100%; display: inline-block;float: left;">
            <input id="search-menu" prompt="输入名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;height:35px;font-size: 15px;"></input>
            <!-- 左侧菜单树 -->
            <ul id="treeMenu"></ul>
        </div>
        <div id="tabel-content" style="display: inline-block; width: Calc(100% - 23.5em); height: 100%;float: left;margin-left: .8em;">
            <input id="search-tabel" prompt="输入乡村查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:300px;height:35px;font-size: 15px;"></input>
            <div class="tabel">
                <div class="tabel-head">
                    <span style="width: calc(15%);">行政村名</span>
                    <span style="width: calc(17%);">自然村名</span>
                    <span style="width: calc(17%);">自然村名</span>
                    <span style="width: calc(17%);">自然村名</span>
                    <span style="width: calc(17%);">自然村名</span>
                    <span style="width: calc(17%);">自然村名</span>
                </div>
                <div class="tabel-content" id="cont-list-body"></div>
            </div>
        </div>
        <div class="showText"></div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var menuObj = null
        var streetObj = null
        var townName = ''//镇
        $(document).ready(function () {
            menuObj = new treeMenu() //创建左侧菜单
            streetObj = new streetList() //创建右侧表格
        });
        class config {
            constructor() {
                this.content = document.getElementById('cont-list-body')
                this.head = document.querySelector('.tabel-head')
                this.Text = document.querySelector('.showText')
                this.height = '32'
                this.list = []
            }
        }
        //左侧菜单树类
        class treeMenu extends config {
            constructor(arg) {
                super(arg)
                this.init()
            }
            init() {
                $('#search-menu').textbox('textbox').bind('input', (e) => {
                    let list = JSON.parse(JSON.stringify(this.menuList))
                    //过滤数据
                    let filterlist = list.filter(r => r.name.indexOf(e.target.value) !== -1)
                    if (!filterlist || !filterlist[0]) {
                        //字母过滤
                        filterlist = list.filter(r => upperfirst(r.name, { upperfirst: true }).indexOf(e.target.value) !== -1)
                    }
                    //左边列表数据渲染
                    this.render(filterlist || [])
                    //右边列表数据渲染
                    if (filterlist[0] && filterlist[0].id) {
                        $('#search-tabel').textbox('setValue', '')
                        townName = filterlist[0].name
                        streetObj.list = filterlist
                        streetObj.query(filterlist[0].id)
                    }
                })
            }
            query(data) {
                this.render(data)
            }
            render(data) {
                $('#treeMenu').tree({
                    data,
                    onClick: (node) => {
                        if (node.id) {
                            //查询列表数据
                            townName = node.name
                            streetObj.query(node.id)
                        }
                    },
                    formatter: (node) => {
                        return node.name;
                    }
                });
            }
        }
        // 右侧信息列表
        class streetList extends config {
            constructor(arg) {
                super(arg)
                //监听点击事件
                this.content.addEventListener('click', (e) => {
                    this.click(e)
                }, false)
                //监听双击事件
                this.content.addEventListener('dblclick', (e) => {
                    this.dblclick(e)
                }, false)
                this.init()
            }
            //初始化
            init() {
                $('#search-tabel').textbox('textbox').bind('input', (e) => {
                    let list = JSON.parse(JSON.stringify(this.list)) || []
                    //过滤数据
                    let filterlist = list.filter(r => r.name.indexOf(e.target.value) !== -1 || (Array.isArray(r.children) && r.children.filter(y => y.name.indexOf(e.target.value) !== -1)[0]))
                    if (!filterlist || !filterlist[0]) {
                        //字母过滤
                        filterlist = list.filter(r => upperfirst(r.name, { upperfirst: true }).indexOf(e.target.value) !== -1 || (Array.isArray(r.children) && r.children.filter(y => upperfirst(y.name, { upperfirst: true }).indexOf(e.target.value) !== -1)[0]))
                    }
                    this.render(filterlist)
                })
            }
            //查询数据
            query(id) {
                if (id) {
                    //查询列表数据
                    showProcess(true, '提示', '正在努力加载中，请稍后...');
                    getStreetChildrenList({ id }, list => {
                        this.list = list
                        this.render(JSON.parse(JSON.stringify(this.list)))
                        showProcess(false);
                    })
                }
            }
            //渲染
            render(list) {
                this.content.innerHTML = `${list.map(r => {
                    if (r.children && Array.isArray(r.children)) {
                        if (r.children.length < 5) {
                            do {
                                r.children.push({})
                            } while (r.children.length < 5)
                        } else if (r.children.length > 5) {
                            let { newArray, isBeyond } = this.groupedArray(r.children, 5)
                            r.isBeyond = isBeyond
                            r.children = newArray
                        }
                    } else {
                        r.children = [{}, {}, {}, {}, {}]
                    }
                    return this.twoDimension(r)
                }).join(' ')}`
                this.resize()
            }
            //二维数组处理
            twoDimension(obj) {
                if (obj.isBeyond) {
                    return obj.children.map((r, i) => {
                        if (r.length < 5) {
                            do {
                                r.push({})
                            } while (r.length < 5)
                        }
                        return `
                                <div class="tabel-content-row">
                                    ${this.administrative(obj, i)}
                                    ${r.map(y => `<span style="width: 17%;" class="hidden" id="${y.id}">${y.name || '&nbsp;'}</span>`).join(' ')}
                                </div>
                            `
                    }).join(' ')
                }
                return `
                        <div class="tabel-content-row">
                            <span style="width: 15%;" class="hidden" id="${obj.id}">${obj.name || '&nbsp;'}</span>
                            ${obj.children.map(r => `<span style="width: 17%;" class="hidden" id="${r.id}">${r.name || '&nbsp;'}</span>`).join(' ')}
                        </div>
                    `
            }
            //行政村名显示处理
            administrative(obj, i) {
                if (i >= 1) {
                    return ''
                }
                let height = obj.children.length * this.height
                return `<span style="width: 15%;height:${height}px;line-height:${height}px" id="${obj.id}">${obj.name || '&nbsp;'}</span>`
            }
            //单击事件
            click(ev) {
                ev = ev || window.event
                let x = ev.pageX
                let y = ev.pageY
                let element = ev.srcElement
                //检查表格元素是否溢出显示省略号了 默认是超出12个字符
                if (element.className == 'hidden' && element.clientWidth < element.scrollWidth || element.innerHTML.length > 12) {
                    this.showText(x, y)
                    this.Text.innerHTML = element.innerHTML
                } else if (this.Text) {
                    this.Text.style.display = 'none'
                }
                return;
            }
            //双击事件
            dblclick(ev) {
                let _this = this
                $.messager.confirm('提示', '是否确定同步到创建事件?', (success) => {
                    if (success) {
                        let obj = this.list.filter(r => r.id == ev.srcElement.id)[0]
                        let location = townName
                        if (obj) {
                            location += obj.name
                        } else {
                            this.list.filter(r => r.id == ev.srcElement.id || (Array.isArray(r.children) && r.children.filter(y => {
                                if (y.id == ev.srcElement.id) {
                                    location += r.name + y.name
                                }
                                return y.id == ev.srcElement.id
                            })))
                        }
                        
                        //同步地址信息
                        window.parent.gProxy.synchronizingAddressInformation(location);
                    }
                });
            }

            //显示text
            showText(x, y) {
                setTimeout(() => {
                    this.Text.style.top = (y - this.Text.offsetHeight - (this.Text.offsetHeight / 2)) + 'px'
                    this.Text.style.left = (x - this.Text.offsetWidth / 2) + 'px'
                    this.Text.style.display = 'inline-block'
                }, 100);
            }
            //判断是否出现滚动条 处理头部可能出现的错位
            resize() {
                if (this.content.offsetHeight < this.content.scrollHeight) {
                    this.head.style.width = 'calc(100% - ' + this.getScrollbarWidth() + 'px)'
                } else {
                    this.head.style.width = '100%'
                }
            }
            //获取滚动条宽度
            getScrollbarWidth() {
                let scrollDiv = document.createElement('div');
                scrollDiv.style.cssText = 'width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;'
                document.body.appendChild(scrollDiv)
                let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth
                document.body.removeChild(scrollDiv)
                return scrollbarWidth
            }
            //根据数值大小分隔成二维数组
            groupedArray(array, num = 2) {
                let index = 0;
                let newArray = [];
                while (index < array.length) {
                    newArray.push(array.slice(index, index += num));
                }
                return { newArray, isBeyond: true }
            }
        }
        //暴露给父级修正表头的方法
        function childFnt() {
            //查询左侧菜单列表
            getStreetRoot(res => {
                menuObj.menuList = res || []
                menuObj.query(menuObj.menuList)
                if (menuObj.menuList[0] && menuObj.menuList[0].id) {
                    //查询列表数据
                    townName = menuObj.menuList[0].name
                    streetObj.query(menuObj.menuList[0].id)
                }
            })
            //修正表头
            streetObj && streetObj.resize()
        }
    </script>
</body>

</html>