# 市县联动系统功能改进说明

## 改进概述

根据您的需求，我对 `cityCountyLinkage.html` 进行了以下三个主要功能改进：

1. **viewTaskDetail函数改进** - 支持regionCode参数，动态获取详情页面URL
2. **医院列表双击定位** - 双击医院行自动在地图上定位
3. **智能定时刷新系统** - 优化任务和车辆的定时刷新策略

## 详细改进内容

### 1. viewTaskDetail函数改进

**原始功能：**
```javascript
function viewTaskDetail(eventId) {
    $.messager.alert('任务详情', '查看任务ID: ' + eventId + ' 的详细信息', 'info');
}
```

**改进后功能：**
```javascript
function viewTaskDetail(eventId, regionCode) {
    // 1. 验证参数
    if (!regionCode) {
        $.messager.alert('提示', '缺少regionCode参数，无法打开详情页面', 'warning');
        return;
    }
    
    // 2. 通过regionCode在全局变量中查找eventDetailUrl
    let eventDetailUrl = null;
    if (globalRegionConfigData && Array.isArray(globalRegionConfigData)) {
        const regionConfig = globalRegionConfigData.find(config => 
            config.regionCode === regionCode || config.codeName === regionCode
        );
        if (regionConfig && regionConfig.eventDetailUrl) {
            eventDetailUrl = regionConfig.eventDetailUrl;
        }
    }
    
    // 3. 构建完整URL并用EasyUI window + iframe显示
    const fullUrl = eventDetailUrl + (eventDetailUrl.includes('?') ? '&' : '?') + 'eventId=' + eventId;
    
    $('<div></div>').window({
        title: '任务详情 - ' + eventId,
        width: 1000,
        height: 700,
        modal: true,
        resizable: true,
        maximizable: true,
        content: '<iframe src="' + fullUrl + '" frameborder="0" style="width:100%;height:100%;"></iframe>'
    });
}
```

**调用方式更新：**
- 格式化操作按钮中的调用已更新为包含regionCode参数
- 支持从任务数据的regionCode或regionName字段获取区域代码

### 2. 医院列表双击定位功能

**实现位置：** `setupHospitalsGrid()` 函数中添加双击事件处理

**核心代码：**
```javascript
// 添加双击事件处理
$('#hospitalsGrid').datagrid({
    onDblClickRow: function(index, row) {
        console.log('医院表格双击事件:', index, row);
        locateHospitalOnMap(row);
    }
});

// 医院双击定位函数
function locateHospitalOnMap(hospital) {
    if (hospital.longitude && hospital.latitude) {
        // 关闭数据模块窗口
        $('#dataPanel').window('close');
        
        // 地图定位到医院
        cityCountyMap.setZoomAndCenter(16, [hospital.longitude, hospital.latitude]);
        
        // 显示成功提示
        $.messager.show({
            title: '定位成功',
            msg: `已在地图上定位到: ${hospital.name}`,
            timeout: 3000,
            showType: 'slide'
        });
        
        // 创建临时高亮标记
        const marker = new AMap.Marker({
            position: [hospital.longitude, hospital.latitude],
            icon: 'style/img/hospital_highlight.png',
            animation: 'AMAP_ANIMATION_BOUNCE',
            title: '医院位置: ' + hospital.name
        });
        
        cityCountyMap.add(marker);
        
        // 5秒后移除临时标记
        setTimeout(() => {
            cityCountyMap.remove(marker);
        }, 5000);
    }
}
```

### 3. 智能定时刷新系统

**新增全局变量：**
```javascript
// 定时刷新相关变量
let taskRefreshTimer = null;           // 任务刷新定时器
let vehicleRefreshTimer = null;        // 车辆刷新定时器
let lastTaskData = [];                 // 上次的任务数据，用于智能比较
let vehicleMarkersMap = new Map();     // 车辆标记映射，key为车辆ID，value为marker对象
```

**启动智能刷新系统：**
```javascript
function startSmartRefresh() {
    console.log('[智能刷新] 启动定时刷新系统');
    
    // 任务每30秒刷新一次
    if (taskRefreshTimer) {
        clearInterval(taskRefreshTimer);
    }
    taskRefreshTimer = setInterval(function() {
        smartRefreshTasks();
    }, 30000);
    
    // 车辆每10秒刷新一次
    if (vehicleRefreshTimer) {
        clearInterval(vehicleRefreshTimer);
    }
    vehicleRefreshTimer = setInterval(function() {
        smartRefreshVehicles();
    }, 10000);
}
```

**任务智能刷新：**
- 只有在任务面板打开时才刷新
- 比较新旧数据，只有数据发生变化时才更新表格
- 避免无意义的DOM操作和用户界面闪烁

**车辆智能刷新：**
- 只有在车辆显示开启时才刷新
- 使用Map存储车辆ID与标记的映射关系
- 现有车辆：检查位置变化，只在必要时移动标记
- 消失车辆：删除对应标记
- 新增车辆：创建新标记
- 避免删除重建所有标记，提升性能

## 核心算法

### 数组比较算法
```javascript
function arraysEqual(arr1, arr2, keyField) {
    if (!arr1 || !arr2) return false;
    if (arr1.length !== arr2.length) return false;
    
    // 按指定字段排序后比较
    const sorted1 = arr1.slice().sort((a, b) => (a[keyField] || '').localeCompare(b[keyField] || ''));
    const sorted2 = arr2.slice().sort((a, b) => (a[keyField] || '').localeCompare(b[keyField] || ''));
    
    for (let i = 0; i < sorted1.length; i++) {
        if (JSON.stringify(sorted1[i]) !== JSON.stringify(sorted2[i])) {
            return false;
        }
    }
    return true;
}
```

### 车辆标记智能更新
```javascript
function smartUpdateVehicleMarkers(newVehicleData) {
    // 创建新数据映射
    const newVehicleMap = new Map();
    newVehicleData.forEach(vehicle => {
        const vehicleId = vehicle.plateNum || vehicle.id;
        if (vehicleId) {
            newVehicleMap.set(vehicleId, vehicle);
        }
    });
    
    // 处理现有标记
    vehicleMarkersMap.forEach((marker, vehicleId) => {
        const newVehicle = newVehicleMap.get(vehicleId);
        if (newVehicle && newVehicle.longitude && newVehicle.latitude) {
            // 车辆仍存在，检查是否需要移动
            const newPosition = [newVehicle.longitude, newVehicle.latitude];
            const currentPosition = marker.getPosition();
            
            if (positionChanged(currentPosition, newPosition)) {
                marker.setPosition(newPosition);
                updateVehicleMarkerInfo(marker, newVehicle);
            }
            newVehicleMap.delete(vehicleId);
        } else {
            // 车辆消失，删除标记
            marker.setMap(null);
            vehicleMarkersMap.delete(vehicleId);
        }
    });
    
    // 添加新车辆标记
    newVehicleMap.forEach((vehicle, vehicleId) => {
        if (vehicle.longitude && vehicle.latitude) {
            const marker = createVehicleMarker(vehicle);
            if (marker) {
                vehicleMarkersMap.set(vehicleId, marker);
                vehicleMarkers.push(marker);
            }
        }
    });
}
```

## 兼容性保证

1. **向后兼容：** 所有改进都保持与现有代码的兼容性
2. **数据格式兼容：** 支持多种数据格式和字段名称
3. **功能独立：** 新功能不影响现有功能的正常运行
4. **错误处理：** 完善的错误处理和用户提示

## 性能优化

1. **减少DOM操作：** 智能比较避免不必要的表格更新
2. **减少地图重绘：** 移动标记而不是删除重建
3. **内存管理：** 及时清理定时器和标记映射
4. **用户体验：** 避免界面闪烁和数据跳动

## 测试建议

1. **功能测试：** 验证每个改进功能的正确性
2. **性能测试：** 观察定时刷新的性能表现
3. **兼容性测试：** 确保不影响其他功能
4. **用户体验测试：** 验证界面的流畅性和友好性

## 部署说明

所有改进都已集成到 `cityCountyLinkage.html` 文件中，无需额外的文件或配置。系统启动时会自动启动智能刷新功能。
