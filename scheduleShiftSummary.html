<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 王海锋
* Created: 2025/6/9
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }

        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .window{
            overflow: initial !important;
        }
        #wavPlayWindow{
            overflow: initial;
        }
    </style>
</head>

<body>
    <div style="padding: 10px;">
        <form id="searchForm" style="margin-bottom: 10px;">
            <label>开始时间：</label>
            <input id="scheduleStartTime" name="scheduleStartTime" class="easyui-datetimebox" style="width:180px;">
            <label>结束时间：</label>
            <input id="scheduleEndTime" name="scheduleEndTime" class="easyui-datetimebox" style="width:180px;">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="reloadData()">查询</a>
        </form>
        <div style="position: relative;font-size: 14px;">
            <table id="dataGrid" class="easyui-datagrid" style="height: 900px;width: 100%;" pagination="true" rownumbers="true"
                singleSelect="true" fitColumns="true">
                <!-- <thead>
                    <tr>
                        <th field="op" width="100" align="center" formatter="formatDispatchButton">操作</th>
                        <th align="center" field="scheduleName" width="120">班次</th>
                        <th align="center" field="scheduleStartTime" width="180">开始时间</th>
                        <th align="center" field="scheduleEndTime" width="180">结束时间</th>
                        <th align="center" field="callIn" width="80">呼入数</th>
                        <th align="center" field="callInAccepted" width="80">受理数</th>
                        <th align="center" field="dispatch" width="80">派车数</th>
                        <th align="center" field="vaildDispatch" width="80">有效出车数</th>
                        <th align="center" field="patientNum" width="80">患者人数</th>
                        <th align="center" field="pickRate" width="80">摘机率</th>
                        <th align="center" field="peNum" width="80">突发公共事件次数</th>
                        <th align="center" field="peWoundedNum" width="80">突发公共事件受伤人数</th>
                        <th align="center" field="peDeadNum" width="80">突发公共事件死亡人数</th>
                    </tr>
                </thead> -->
            </table>
        </div>
        <div id="stationStatisticsDialog" class="easyui-dialog" title="分站出车统计" style="width:1200px;height:800px;padding:10px" closed="true" modal="true" inline="false">
            <table id="stationStatisticsTable" class="easyui-datagrid" style="width:100%;height:100%;" fitColumns="true" rownumbers="true" singleSelect="true">
                <thead>
                    <tr>
                        <th field="regionName" width="100" align="center">区域</th>
                        <th field="regionTotal" width="80" align="center">出车统计</th>
                        <th field="stationName" width="200" align="center">分站名称</th>
                        <th field="scheduleInfo" width="280" align="center">班次时间</th>
                        <th field="count" width="100" align="center">出车数</th>
                    </tr>
                </thead>
            </table>
        </div>

        
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        function childFnt() {
            if (!window._initedDataGrid) {
              $('#dataGrid').datagrid({
                loader: dataGridLoader,
                pagination: true,
                rownumbers: true,
                singleSelect: true,
                fitColumns: true,
                columns: [[
                    { field: 'op', title: '操作', width: 120, align: 'center', formatter: formatDispatchButton },
                    { field: 'scheduleName', title: '班次', width: 120, align: 'center' },
                    { field: 'scheduleStartTime', title: '开始时间', width: 180, align: 'center' },
                    { field: 'scheduleEndTime', title: '结束时间', width: 180, align: 'center' },
                    { field: 'callIn', title: '呼入数', width: 80, align: 'center' },
                    { field: 'callInAccepted', title: '受理数', width: 80, align: 'center' },
                    { field: 'dispatch', title: '派车数', width: 80, align: 'center' },
                    { field: 'vaildDispatch', title: '有效出车数', width: 80, align: 'center' },
                    { field: 'patientNum', title: '患者人数', width: 80, align: 'center' },
                    { field: 'pickRate', title: '摘机率(%)', width: 80, align: 'center' },
                    { field: 'peNum', title: '突发公共事件次数', width: 80, align: 'center' },
                    { field: 'peWoundedNum', title: '突发公共事件受伤人数', width: 100, align: 'center' },
                    { field: 'peDeadNum', title: '突发公共事件死亡人数', width: 100, align: 'center' }
                ]]
              });
              window._initedDataGrid = true;
            }
            $('#dataGrid').datagrid('load');
        }

        function formatDispatchButton(value, row, index) {
            return `<a href="javascript:void(0)" onclick="showStationStatisticsDialog({scheduleName: '${row.scheduleName}',scheduleStartTime: '${row.scheduleStartTime}', scheduleEndTime: '${row.scheduleEndTime}'})">分站出车统计</a>`;
        }

        /** 格式化时间为 yyyy-MM-dd HH:mm:ss */
        function formatDateTime(date) {
            const pad = (n) => n < 10 ? '0' + n : n;
            return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
                   `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
        }

        /** 加载数据 */
        function dataGridLoader(param, success, error) {
            if (typeof success !== 'function' || typeof error !== 'function') {
              console.error('[dataGridLoader] 不允许手动调用此方法！');
              return;
            }
            const scheduleStartTime = $('#scheduleStartTime').datetimebox('getValue');
            const scheduleEndTime = $('#scheduleEndTime').datetimebox('getValue');
            const queryParams = {
              scheduleStartTime,
              scheduleEndTime,
              page: param.page || 1,
              size: param.rows || 10
            };
            getScheduleShiftSummary(queryParams, function (res) {
              success({
                total: res.data.total,
                rows: res.data.records
              });
            }, function (e, url, errMsg) {
              error();
              $.messager.alert('错误', '数据加载失败：' + errMsg, 'error');
            });
        }

        /** 点击“查询”按钮触发表格重新加载 */
        function reloadData() {
            $('#dataGrid').datagrid('load');
        }
        //格式化时间
        function formatDateTimeToCompact(dateStr) {
            const date = new Date(dateStr);
            const pad = n => n < 10 ? '0' + n : n;

            const y = date.getFullYear();
            const m = pad(date.getMonth() + 1);
            const d = pad(date.getDate());
            const h = pad(date.getHours());
            const min = pad(date.getMinutes());
            const s = pad(date.getSeconds());

            return `${y}${m}${d}${h}${min}${s}`;
        }

        /** 查询分站出车任务统计 */
        function showStationStatisticsDialog(paramsFromRow) {
            const scheduleName = (paramsFromRow && paramsFromRow.scheduleName) || '';
            const startTime = formatDateTimeToCompact((paramsFromRow && paramsFromRow.scheduleStartTime) || '');
            const endTime = formatDateTimeToCompact((paramsFromRow && paramsFromRow.scheduleEndTime) || '');

            const params = {
                startTime,
                endTime,
                stationRegionId: ''
            };

            stationDispatchStatistics(params, function (res) {
                var rows = [];
                res.data.forEach(function (region) {
                    // 从外层region对象获取regionTotal
                    const regionTotal = region.regionTotal;

                    region.stationStatisticsList.forEach(function (station) {
                        rows.push({
                            regionName: station.regionName,
                            regionTotal: regionTotal, // 使用外层的regionTotal
                            stationName: station.stationName,
                            count: station.count,
                            scheduleInfo: `${scheduleName}（${paramsFromRow.scheduleStartTime} 至 ${paramsFromRow.scheduleEndTime}）`
                        });
                    });
                });

                $('#stationStatisticsTable').datagrid({
                    data: rows,
                    onLoadSuccess: function () {
                        mergeRegionCells(rows);
                    }
                });

                $('#stationStatisticsDialog').dialog({
                    closed: false,
                    modal: true
                }).dialog('center');
            }, function (e, url, msg) {
                $.messager.alert('错误', msg || '加载失败', 'error');
            });
        }


        function mergeRegionCells(data) {
            if (!data || data.length === 0) return;

            var table = $('#stationStatisticsTable');
            var startIndex = 0;
            var currentRegion = data[0].regionName;

            for (var i = 1; i <= data.length; i++) {
                if (i === data.length || data[i].regionName !== currentRegion) {
                    var rowspan = i - startIndex;
                    if (rowspan > 1) {
                        // 合并区域名称列
                        table.datagrid('mergeCells', {
                            index: startIndex,
                            field: 'regionName',
                            rowspan: rowspan
                        });

                        // 合并区域统计列
                        table.datagrid('mergeCells', {
                            index: startIndex,
                            field: 'regionTotal',
                            rowspan: rowspan
                        });
                    }
                    startIndex = i;
                    currentRegion = (i < data.length) ? data[i].regionName : '';
                }
            }
        }

    </script>
</body>

</html>