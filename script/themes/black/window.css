.window {
  overflow: hidden;
  padding: 5px;
  border-width: 1px;
  border-style: solid;
}
.window .window-header {
  background: transparent;
  padding: 0px 0px 6px 0px;
}
.window .window-body {
  border-width: 1px;
  border-style: solid;
  border-top-width: 0px;
}
.window .window-body-noheader {
  border-top-width: 1px;
}
.window .panel-body-nobottom {
  border-bottom-width: 0;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -11px;
}
.window .window-header .panel-icon {
  left: 1px;
}
.window .window-header .panel-tool {
  right: 1px;
}
.window .window-header .panel-with-icon {
  padding-left: 18px;
}
.window-proxy {
  position: absolute;
  overflow: hidden;
}
.window-proxy-mask {
  position: absolute;
  filter: alpha(opacity=5);
  opacity: 0.05;
}
.window-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=40);
  opacity: 0.40;
  font-size: 1px;
  overflow: hidden;
}
.window,
.window-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.window-shadow {
  background: #777;
  -moz-box-shadow: 2px 2px 3px #787878;
  -webkit-box-shadow: 2px 2px 3px #787878;
  box-shadow: 2px 2px 3px #787878;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2,MakeShadow=false,ShadowOpacity=0.2);
}
.window,
.window .window-body {
  border-color: #000;
}
.window {
  background-color: #3d3d3d;
  background: -webkit-linear-gradient(top,#454545 0,#383838 20%);
  background: -moz-linear-gradient(top,#454545 0,#383838 20%);
  background: -o-linear-gradient(top,#454545 0,#383838 20%);
  background: linear-gradient(to bottom,#454545 0,#383838 20%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#383838,GradientType=0);
}
.window-proxy {
  border: 1px dashed #000;
}
.window-proxy-mask,
.window-mask {
  background: #000;
}
.window .panel-footer {
  border: 1px solid #000;
  position: relative;
  top: -1px;
}
.window-thinborder {
  padding: 0;
}
.window-thinborder .window-header {
  padding: 5px 5px 6px 5px;
}
.window-thinborder .window-body {
  border-width: 0px;
}
.window-thinborder .window-footer {
  border-left: transparent;
  border-right: transparent;
  border-bottom: transparent;
}
.window-thinborder .window-header .panel-icon,
.window-thinborder .window-header .panel-tool {
  margin-top: -9px;
  margin-left: 5px;
  margin-right: 5px;
}
.window-noborder {
  border: 0;
}
.window.panel-hleft .window-header {
  padding: 0 6px 0 0;
}
.window.panel-hright .window-header {
  padding: 0 0 0 6px;
}
.window.panel-hleft>.panel-header .panel-title {
  top: auto;
  left: 16px;
}
.window.panel-hright>.panel-header .panel-title {
  top: auto;
  right: 16px;
}
.window.panel-hleft>.panel-header .panel-title-up,
.window.panel-hright>.panel-header .panel-title-up {
  bottom: 0;
}
.window.panel-hleft .window-body {
  border-width: 1px 1px 1px 0;
}
.window.panel-hright .window-body {
  border-width: 1px 0 1px 1px;
}
.window.panel-hleft .window-header .panel-icon {
  top: 1px;
  margin-top: 0;
  left: 0;
}
.window.panel-hright .window-header .panel-icon {
  top: 1px;
  margin-top: 0;
  left: auto;
  right: 1px;
}
.window.panel-hleft .window-header .panel-tool,
.window.panel-hright .window-header .panel-tool {
  margin-top: 0;
  top: auto;
  bottom: 1px;
  right: auto;
  margin-right: 0;
  left: 50%;
  margin-left: -11px;
}
.window.panel-hright .window-header .panel-tool {
  left: auto;
  right: 1px;
}
.window-thinborder.panel-hleft .window-header {
  padding: 5px 6px 5px 5px;
}
.window-thinborder.panel-hright .window-header {
  padding: 5px 5px 5px 6px;
}
.window-thinborder.panel-hleft>.panel-header .panel-title {
  left: 21px;
}
.window-thinborder.panel-hleft>.panel-header .panel-title-up,
.window-thinborder.panel-hright>.panel-header .panel-title-up {
  bottom: 5px;
}
.window-thinborder.panel-hleft .window-header .panel-icon,
.window-thinborder.panel-hright .window-header .panel-icon {
  margin-top: 5px;
}
.window-thinborder.panel-hleft .window-header .panel-tool,
.window-thinborder.panel-hright .window-header .panel-tool {
  left: 16px;
  bottom: 5px;
}
