.icon-blank{
	background:url('icons/blank.gif') no-repeat center center;
}
.icon-add{
	background:url('icons/edit_add.png') no-repeat center center;
}
.icon-edit{
	background:url('icons/pencil.png') no-repeat center center;
}
.icon-clear{
	background:url('icons/clear.png') no-repeat center center;
}
.icon-remove{
	background:url('icons/edit_remove.png') no-repeat center center;
}
.icon-save{
	background:url('icons/filesave.png') no-repeat center center;
}
.icon-cut{
	background:url('icons/cut.png') no-repeat center center;
}
.icon-ok{
	background:url('icons/ok.png') no-repeat center center;
}
.icon-no{
	background:url('icons/no.png') no-repeat center center;
}
.icon-cancel{
	background:url('icons/cancel.png') no-repeat center center;
}
.icon-reload{
	background:url('icons/reload.png') no-repeat center center;
}
.icon-picture{
	background:url('icons/picture.png') no-repeat center center;
	width: 17px;
	height: 16px;
}
.icon-search{
	background:url('icons/search.png') no-repeat center center;
}
.icon-zhuren{
	background:url('icons/zhuren.png') no-repeat center center;
}
.icon-print{
	background:url('icons/print.png') no-repeat center center;
}
.icon-help{
	background:url('icons/help.png') no-repeat center center;
}
.icon-undo{
	background:url('icons/undo.png') no-repeat center center;
}
.icon-redo{
	background:url('icons/redo.png') no-repeat center center;
}
.icon-back{
	background:url('icons/back.png') no-repeat center center;
}
.icon-sum{
	background:url('icons/sum.png') no-repeat center center;
}
.icon-tip{
	background:url('icons/tip.png') no-repeat center center;
}
.icon-filter{
	background:url('icons/filter.png') no-repeat center center;
}
.icon-man{
	background:url('icons/man.png') no-repeat center center;
}
.icon-lock{
	background:url('icons/lock.png') no-repeat center center;
}
.icon-event-unlock{
	background:url('icons/event_unlock.png') no-repeat center center;
}
.icon-event-lock{
	background:url('icons/event_lock.png') no-repeat center center;
}
.icon-more{
	background:url('icons/more.png') no-repeat center center;
}
.icon-ambulance{
    background: url('icons/ambulance.png') no-repeat center center;
}
.icon-map {
    background: url('icons/map.png') no-repeat center center;
}
.icon-phonebooks {
    background: url('icons/phonebooks.png') no-repeat center center;
}
.icon-tasklist {
    background: url('icons/tasklist.png') no-repeat center center;
}
.icon-calllist {
    background: url('icons/calllist.png') no-repeat center center;
}
.icon-calllVideoList {
    background: url('icons/videoList.png') no-repeat center center;
}
.icon-videoCalling {
    background: url('icons/videoCalling.png') no-repeat center center;
}
.icon-largeEvent {
    background: url('icons/largeEvent.png') no-repeat center center;
}
.icon-recover {
    background: url('icons/recover.png') no-repeat center center;
}
.icon-show {
    background: url('icons/show.png') no-repeat center center;
}
.icon-invalid {
    background: url('icons/invalid.png') no-repeat center center;
}
.icon-mini-add{
	background:url('icons/mini_add.png') no-repeat center center;
}
.icon-mini-edit{
	background:url('icons/mini_edit.png') no-repeat center center;
}
.icon-mini-refresh{
	background:url('icons/mini_refresh.png') no-repeat center center;
}

.icon-large-picture{
	background:url('icons/large_picture.png') no-repeat center center;
}
.icon-large-clipart{
	background:url('icons/large_clipart.png') no-repeat center center;
}
.icon-large-shapes{
	background:url('icons/large_shapes.png') no-repeat center center;
}
.icon-large-smartart{
	background:url('icons/large_smartart.png') no-repeat center center;
}
.icon-vehicle-stop{
    background: url('icons/vehicleStop.png') no-repeat center center;
}
.icon-large-chart{
	background:url('icons/large_chart.png') no-repeat center center;
}
.icon-regionLinkage{
	background:url('icons/region_linkage.png') no-repeat center center;
}
.。icon-cityCountyLinkage{
	background:url('icons/region_linkage.png') no-repeat center center;
}
.icon-softphone-dialcall {
    background: url('icons/softphone-dialcall.png') no-repeat center center;
}
.icon-softphone-Hangup {
    background: url('icons/softphone-Hangup.png') no-repeat center center;
}
.icon-softphone-hold {
    background: url('icons/softphone-hold.png') no-repeat center center;
}
.icon-fenceViolationRecord-tab {
    background: url('icons/fenceViolationRecord.png') no-repeat center center;
}
.icon-videoCalling {
    background: url('icons/videoCalling.png') no-repeat center center;
}
.icon-shiftRecord-tab {
    background: url('icons/shiftRecord.png') no-repeat center center;
}

