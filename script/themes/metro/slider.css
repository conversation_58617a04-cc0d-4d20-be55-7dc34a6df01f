.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 0px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: 50%;
  margin-top: -10px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 14px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 14px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 50%;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #ddd;
  background: #ffffff;
}
.slider-rule span {
  border-color: #ddd;
}
.slider-rulelabel span {
  color: #444;
}
