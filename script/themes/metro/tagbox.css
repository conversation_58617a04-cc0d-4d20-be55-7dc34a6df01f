.tagbox {
  cursor: text;
}
.tagbox .textbox-text {
  float: left;
}
.tagbox-label {
  position: relative;
  display: block;
  margin: 4px 0 0 4px;
  padding: 0 20px 0 4px;
  float: left;
  vertical-align: top;
  text-decoration: none;
  -moz-border-radius: 0px 0px 0px 0px;
  -webkit-border-radius: 0px 0px 0px 0px;
  border-radius: 0px 0px 0px 0px;
  background: #E6E6E6;
  color: #444;
}
.tagbox-remove {
  background: url('images/tagbox_icons.png') no-repeat -16px center;
  position: absolute;
  display: block;
  width: 16px;
  height: 16px;
  right: 2px;
  top: 50%;
  margin-top: -8px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tagbox-remove:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}
.textbox-disabled .tagbox-label {
  cursor: default;
}
.textbox-disabled .tagbox-remove:hover {
  cursor: default;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
