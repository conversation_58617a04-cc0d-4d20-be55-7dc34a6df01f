.switchbutton {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  cursor: pointer;
  background: #2196f3;
  border: 1px solid #2196f3;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.switchbutton-inner {
  display: inline-block;
  overflow: hidden;
  position: relative;
  top: -1px;
  left: -1px;
}
.switchbutton-on,
.switchbutton-off,
.switchbutton-handle {
  display: inline-block;
  text-align: center;
  height: 100%;
  float: left;
  font-size: 14px;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.switchbutton-on {
  background: #eee;
  color: #2196f3;
}
.switchbutton-off {
  background-color: #ffffff;
  color: #404040;
}
.switchbutton-on,
.switchbutton-reversed .switchbutton-off {
  -moz-border-radius: 2px 0 0 2px;
  -webkit-border-radius: 2px 0 0 2px;
  border-radius: 2px 0 0 2px;
}
.switchbutton-off,
.switchbutton-reversed .switchbutton-on {
  -moz-border-radius: 0 2px 2px 0;
  -webkit-border-radius: 0 2px 2px 0;
  border-radius: 0 2px 2px 0;
}
.switchbutton-handle {
  position: absolute;
  top: 0;
  left: 50%;
  background-color: #ffffff;
  color: #404040;
  border: 1px solid #2196f3;
  -moz-box-shadow: 0 0 3px 0 #2196f3;
  -webkit-box-shadow: 0 0 3px 0 #2196f3;
  box-shadow: 0 0 3px 0 #2196f3;
}
.switchbutton-value {
  position: absolute;
  top: 0;
  left: -5000px;
}
.switchbutton-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.switchbutton-disabled,
.switchbutton-readonly {
  cursor: default;
}
.switchbutton:focus {
  -moz-box-shadow: 0 0 3px 0 #2196f3;
  -webkit-box-shadow: 0 0 3px 0 #2196f3;
  box-shadow: 0 0 3px 0 #2196f3;
  outline: none;
}
