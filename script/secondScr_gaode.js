/**
 * 多屏幕参数支持说明：
 * - 主屏幕通过 window.open('second.html?screenId=1') 的方式打开副屏幕
 * - 每个副屏幕通过 screenId 参数来区分不同的屏幕
 * - 可以通过 getScreenId() 函数获取当前屏幕的ID
 * - 可以通过 getScreenParam(paramName, defaultValue) 函数获取其他参数
 * - screenId 取值范围：1, 2, 3 分别对应3个不同的副屏幕
 * - 窗口位置：尝试自动放置到第二个显示器，如果浏览器不支持则需要手动拖拽
 * 
 * 屏幕位置说明：
 * - 2个屏幕环境：第1屏尝试放到第二个显示器，第2、3屏错开位置避免重叠
 * - 如果浏览器阻止弹窗或位置设置，需要手动拖拽窗口到合适位置
 * 
 * 使用示例：
 * var screenId = getScreenId(); // 获取屏幕ID
 * if (screenId === 1) {
 *     // 第1号屏幕的特定逻辑
 * }
 */

/** 所有副屏幕的tab */
var allTabs = ['地图调度', '电话簿', '事件列表', '通话列表', '视频呼救','电子围栏', '视频呼救中', '个人交接班', '班组交接班', '重大事件', '主任座席','视频呼入对话','记事本','车辆报停','满意度回访','区域联动','预约事件'];

/** 是否派车状态,主屏幕是否正在派车状态，1：是；0：否 */
var newEventStatus = "0";
/** 是否已经初始化 */
var isInitStations = false;
/**  地图实例 */
var map;
/** 当前位置 */
var _currentLoc = null;
/** 存放选中的派车地点的坐标 */
var _selCarId = null;
/** 存放之前的地图上的车辆的marker，画车辆的时候，先删除掉旧的救护车 */
var carMarkers = [];
/** 派车地点，事件地址 */
var sendCarMarker = null;
var uploadAddress=null
var uploadLng=null
var uploadLat = null

/** 点击右侧站点，显示派车车辆的路线规划(预览) */
var _driving = null
/** 规划路线的对象 */
var _polyline = null
/** 自动完成对象 */
var ac = null;
// 10-7 版本新增
/** 存储该车，用于视口跟随 */
var nowCar = null;
/** 是否视口跟随车辆？ 默认不跟随 */
var isFollow = false;
/** 是否是地图点击的车辆还是 右侧菜单栏点击的车辆 */
var isMapClick = false;
/** 是否全局显示进行任务中的路线 */
var showGuiJi = false;
/** 分站列表 */
var staLists = null;
/** 座席信息:判断主任座席是否展示 */
var _pSeat = null;
/** 存储滚动站点列表的高度，防止每次刷新列表都回到顶部，体验感不好 */
var tableHeight = 0
/** 配置的分站勾选信息 */
var fenzhanChecked = true
/** 配置的车牌勾选信息 */
var chepaiChecked = true
/** 配置的速度勾选信息 */
var suduChecked = true
/** 配置的车辆状态勾选信息 */
var statusChecked = true
/** 配置的弹窗是否打开用于监听 */
var configDialog = false
/** 存储车辆的infoWindow打开中 */
var _carInfoWindow = null
/** 信息窗口的定位 */
var _carInfoWindowPosition = null
/** 信息窗口的定位车辆id */
var _carInfoWindowId = null
/** 存放车辆路径路线的对象：key是车id，每次定时清除 */
var carTrackLines = {}
/** 存放每一个车辆目的地标记的对象：key是车id，每次定时清除 */
var carDestinationMarkers = {}
/** 存放车辆路线信息的对象：key是车id，包含距离和时间信息 */
var carRouteInfo = {}
/** 存放车辆路径状态的对象：key是车id，用于避免重复绘制 */
var carTrackState = {}
/** 记录上次地图居中的位置，避免频繁移动 */
var _lastCenterPosition = null
/** 防止路径重复绘制的标志 */
var _drawingVehicleId = null
/** 救护车icon，总共有4种图标：default、yellow、red、blue */
var carIcons = {}
/** 当前选中的车辆id */
let currentCarId

//当前点击的车辆
var clickCarInfo = {}
//点击右侧车辆获取车辆ID
var clickCarId=null

/** 范围工具相关的选中状态追踪 */
var lastSelectedMarker = null; // 最后选中的标注点
var lastSelectedPosition = null; // 最后选中的位置信息
var lastSelectedType = null; // 最后选中的类型：'vehicle', 'marker', 'event'
var lastSelectedDescription = null; // 最后选中的描述

//H5视频是否有进来
var isActiveH5Video= false
//车载视频是否有进来
var isActiveCarVideo = false

/** 救治能力 checkbox values */
let rescueCodeList = []
/** 符合救治能力的车辆ids */
let rescueIds = []
// 事件信息
var setTaskDispatchData = null
/** 调度状态筛选 */
var dispatchStatusFilter = []
/** 车辆类型筛选 */
var vehicleTypeFilter = []
/** 车辆类型选项（从字典获取） */
var vehicleTypeOptions = []
/** 当前右键选中的车辆信息 */
var currentContextVehicle = null
/** 车组人员信息缓存 */
var crewMembersCache = {}
/** 地图工具状态 */
var mapToolsState = {
    measuring: false,
    marking: false,
    rangeMode: false
}
/** 地图标注存储 */
var mapAnnotations = {
    markers: [],
    circles: [],
    polylines: [],
    mouseTool: null,
    ruler: null,
    overView: null,
    scale: null,
    trafficLayer: null
}
/** 地图资源显示状态 */
var mapResourcesState = {
    vehiclesVisible: true,    // 车辆显示状态，默认显示
    stationsVisible: false,   // 分站显示状态，默认隐藏（避免地图太乱）
    hospitalsVisible: false,  // 医院显示状态，默认隐藏
    bloodstationsVisible: false, // 血站显示状态，默认隐藏
    cdcVisible: false         // 疾控中心显示状态，默认隐藏
}
/** 分站标记存储数组 */
var stationMarkers = [];
/* 所有分站信息 */
var _stations = [];
/** 医院标记存储数组 */
var hospitalMarkers = [];
/* 所有医院信息 */
var _hospitals = [];
/** 血站标记存储数组 */
var bloodstationMarkers = [];
/* 所有血站信息 */
var _bloodstations = [];
/** 疾控中心标记存储数组 */
var cdcMarkers = [];
/* 所有疾控中心信息 */
var _cdcs = [];
/** 车辆状态 */
const carStatusMap = {
    0: {
        title: '待命',
        color: '#000000'
    },
    1: {
        title: '发车',
        color: '#07AE5A'
    },
    2: {
        title: '抵达',
        color: '#07AE5A'
    },
    3: {
        title: '接诊取消',
        color: '#07AE5A'
    },
    4: {
        title: '回院',
        color: '#07AE5A'
    },
    5: {
        title: '调度接收',
        color: '#FF0000'
    },
    6: {
        title: '分站派车',
        color: '#07AE5A'
    },
    7: {
        title: '分站接收',
        color: '#07AE5A'
    },
    8: {
        title: '离开现场',
        color: '#07AE5A'
    }
}

/** 派车模式配置 - 兼容主屏的3种模式 */
var assignTaskMode = '1'; // 1:展示车辆派车  2:展示分站派车

/** 班次列表 - 用于人员选择界面的自动填充功能 */
var dispatchClassesList = null;

/** 辅助类型 - 兼容主屏的派车逻辑 */
var assistType = 2; // 2表示座席派车

/**
 * 判断是否为车辆派车模式
 * 参考主屏 isAssignTaskModeCar 函数
 */
function isAssignTaskModeCar() {
    return !assignTaskMode || assignTaskMode === '1';
}

/**
 * 判断是否为分站派车模式  
 * 参考主屏 isAssignTaskModeStation 函数
 */
function isAssignTaskModeStation() {
    return assignTaskMode === '2';
}

/**
 * 加载派车模式配置
 * 参考主屏的配置加载逻辑
 */
function loadAssignTaskModeConfig() {
    querySysConf('ASSIGN_TASK_MODE', 
        function(data) {
            if (data) {
                assignTaskMode = data;
                console.log('副屏加载派车模式配置:', assignTaskMode === '1' ? '车辆模式' : '分站模式');
            }
        },
        function(e, url, errMsg) {
            console.warn('副屏加载派车模式配置失败，使用默认车辆模式:', errMsg);
            assignTaskMode = '1'; // 默认为车辆模式
        }
    );
}

/**
 * 派车前的统一检查和模式判断
 * 参考主屏 sendTaskBtn 函数的逻辑
 */
function sendTaskBtnSecondScreen(stationId, vehicleId) {
    if (!setTaskDispatchData) {
        $.messager.alert('提示', '未选择事件,请先在主屏双击选择事件。');
        return;
    }

    // 检查事件锁定状态
    if (!canOperateEvent(setTaskDispatchData.isLock, setTaskDispatchData.lockSeatUserId, setTaskDispatchData.lockSeatId)) {
        $.messager.alert('提示', '事件被 ' + (setTaskDispatchData.lockSeatCode || '') + ' (' + (setTaskDispatchData.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    $.messager.confirm("提示", "是否进行派车操作？", function (r) {
        if (r) {
            executeDispatchByMode(stationId, vehicleId);
        }
    });
}

/**
 * 根据配置和车辆信息执行相应的派车模式
 */
function executeDispatchByMode(stationId, vehicleId) {
    console.log('执行派车模式判断:', { stationId, vehicleId, assignTaskMode });
    
    // 调试：输出所有车辆的ID字段，确定正确的字段名
    if (_mobilesWithDistance && _mobilesWithDistance.length > 0) {
        console.log('车辆数据样例:', _mobilesWithDistance[0]);
        console.log('前3辆车的ID字段:', _mobilesWithDistance.slice(0, 3).map(v => ({
            id: v.id,
            carId: v.carId,
            mobileId: v.mobileId,
            plateNum: v.plateNum
        })));
    }
    
    // 查找车辆信息，与givCarWithCheck函数保持一致
    let vehicle = _mobilesWithDistance ? _mobilesWithDistance.find(car => car.id === vehicleId) : null;
    
    // 如果_mobiles中没找到，再尝试staLists
    if (!vehicle) {
        vehicle = staLists ? staLists.find(car => car.id === vehicleId) : null;
    }
    
    console.log('查找车辆ID:', vehicleId);
    console.log('找到的车辆信息:', vehicle);
    
    if (!vehicle) {
        console.error('未找到车辆信息:', vehicleId);
        console.log('所有可用的车辆ID:', _mobilesWithDistance ? _mobilesWithDistance.map(v => ({ 
            id: v.id, 
            carId: v.carId, 
            mobileId: v.mobileId, 
            plateNum: v.plateNum 
        })) : []);
        $.messager.alert('错误', '未找到车辆信息，无法执行派车', 'error');
        return;
    }
    
    console.log('车辆isDispatch值:', vehicle.isDispatch, '类型:', typeof vehicle.isDispatch);
    console.log('当前派车模式:', isAssignTaskModeCar() ? '车辆模式' : '分站模式');
    
    // 模式1：派车给分站
    if (isAssignTaskModeStation()) {
        console.log('选择模式1：派车给分站');
        dispatchToStation(stationId);
        return;
    }
    
    // 模式2：直接派车给车辆，不经过分站确认
    if (vehicle.isDispatch === '0' || vehicle.isDispatch === 0) {
        console.log('选择模式2：直接派车给车辆，弹出人员选择界面');
        confirmReceiveShowSecondScreen(setTaskDispatchData.id, vehicleId, vehicle.currentProcessId, vehicle.stationCode);
        return;
    }
    
    // 模式3：派车到车辆，但要分站确认出车
    if (vehicle.isDispatch === '1' || vehicle.isDispatch === 1) {
        console.log('选择模式3：派车到车辆，等待分站确认');
        dispatchToVehicleWithStationConfirm(stationId, vehicleId);
        return;
    }
    
    // 兜底：使用原有逻辑
    console.log('使用兜底逻辑：原有派车方式');
    givCar(stationId);
}

/**
 * 模式1：派车给分站
 */
function dispatchToStation(stationId) {
    console.log('执行模式1：派车给分站', stationId);
    
    const params = {
        "eventId": setTaskDispatchData.id,
        "stationId": stationId,
        "toStatus": '5',
        "seatId": setTaskDispatchData.seatId,
        "seatCode": setTaskDispatchData.seatCode,
        "seatUserId": setTaskDispatchData.seatUserId,
        "seatUserName": setTaskDispatchData.seatUserName,
        "seatUser": setTaskDispatchData.seatUser
        // 注意：不设置 carId，由分站选择车辆
    };
    
    executeDispatchRequest(params, '分站派车');
}

/**
 * 模式3：派车到车辆，但要分站确认出车
 */
function dispatchToVehicleWithStationConfirm(stationId, vehicleId) {
    console.log('执行模式3：派车到车辆，等待分站确认', { stationId, vehicleId });
    
    const params = {
        "eventId": setTaskDispatchData.id,
        "carId": vehicleId,
        "stationId": stationId,
        "toStatus": '5', // 5表示待分站确认
        "seatId": setTaskDispatchData.seatId,
        "seatCode": setTaskDispatchData.seatCode,
        "seatUserId": setTaskDispatchData.seatUserId,
        "seatUserName": setTaskDispatchData.seatUserName,
        "seatUser": setTaskDispatchData.seatUser
    };
    
    executeDispatchRequest(params, '车辆派车(待分站确认)');
}

/**
 * 副屏人员选择界面显示函数
 * 参考主屏 confirmReceiveShow 函数实现
 */
function confirmReceiveShowSecondScreen(eventId, carId, processId, stationCode) {
    console.log('副屏显示人员选择界面:', { eventId, carId, processId, stationCode });
    
    // 调试：检查界面元素是否存在
    const dialogElement = $('#confirm-receive-editor-second');
    console.log('人员选择界面元素是否存在:', dialogElement.length > 0);
    
    if (dialogElement.length === 0) {
        $.messager.alert('错误', '人员选择界面未找到，请检查HTML结构', 'error');
        return;
    }
    
    // 先测试是否能弹出空窗口
    try {
        $('#confirm-receive-editor-second').window('open');
        console.log('成功打开人员选择界面');
    } catch (e) {
        console.error('打开人员选择界面失败:', e);
        $.messager.alert('错误', '打开人员选择界面失败: ' + e.message, 'error');
        return;
    }
    
    // 加载分站人员数据
    reloadDispatchSecondScreen(stationCode);
    
    // 设置辅助类型为座席派车
    assistType = 2;
    
    // 清理下拉选择框
    $('#dispatchClassesDri-second').combobox('clear');
    $('#dispatchDoctorDri-second').combobox('clear');
    $('#dispatchNurseDri-second').combobox('clear');
    $('#dispatchWorkerDri-second').combobox('clear');
    $('#dispatchDriverDri-second').combobox('clear');
    
    // 设置隐藏字段
    $('#dispatchEventId-second').val(eventId);  // 事件ID
    $('#dispatchCarId-second').val(carId);      // 车辆ID  
    $('#mobileProcessId-second').val(processId); // 车辆事件ID
}

/**
 * 副屏更新出车人员班次选择框列表
 * 参考主屏 reloadDispatch 函数实现
 */
function reloadDispatchSecondScreen(stationCode) {
    console.log('副屏加载分站人员数据:', stationCode);
    
    // 获得分站班次
    var params = { "substationcode": stationCode };
    getDispatchClassesByStation(params,
        function (docs) {
            // 存储班次列表供自动填充使用
            dispatchClassesList = docs;
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({ 'id': docs[i].id, 'name': docs[i].scheduleName });
            }
            $('#dispatchClassesDri-second').combobox('loadData', jsonstr.length > 0 ? jsonstr : []);
        }, 
        function (e, url, errMsg) {
            $.messager.alert('提示', '获得排版信息异常：' + errMsg);
        }
    );

    // 获取医生 1001
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1001", "rows": "999" };
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            $('#dispatchDoctorDri-second').combobox('loadData', jsonstr.length > 0 ? jsonstr : []);
        }, 
        function (e, url, errMsg) {
            $.messager.alert('提示', '获得医生信息异常：' + errMsg);
        }
    );

    // 获取护士 1002
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1002", "rows": "999" };
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            $('#dispatchNurseDri-second').combobox('loadData', jsonstr.length > 0 ? jsonstr : []);
        }, 
        function (e, url, errMsg) {
            $.messager.alert('提示', '获得护士信息异常：' + errMsg);
        }
    );

    // 获取司机 1003
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1003", "rows": "999" };
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            $('#dispatchDriverDri-second').combobox('loadData', jsonstr.length > 0 ? jsonstr : []);
        }, 
        function (e, url, errMsg) {
            $.messager.alert('提示', '获得司机信息异常：' + errMsg);
        }
    );

    // 获取工人(护工) 1004
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1004", "rows": "999" };
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            $('#dispatchWorkerDri-second').combobox('loadData', jsonstr.length > 0 ? jsonstr : []);
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', '获得护工信息异常：' + errMsg);
        }
    );
}

/**
 * 副屏人员选择提交函数
 * 参考主屏 confirmReceiveSubmit 函数实现
 */
function confirmReceiveSubmitSecondScreen() {
    const mobileProcessId = $('#mobileProcessId-second').val();
    const carId = $('#dispatchCarId-second').val();
    const eventId = $('#dispatchEventId-second').val();

    console.log('副屏提交人员选择:', { mobileProcessId, carId, eventId });

    // 辅助函数用于从下拉选择框中获取详细信息
    function getDispatchDetails(selector) {
        const id = $(selector).combobox('getValue');
        const data = $(selector).combobox('getData');
        let name = "", num = "", phone = "";

        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) {
                name = data[i].xm || "";
                num = data[i].username || "";
                phone = data[i].phone || "";
                break;
            }
        }

        return { id, name, num, phone };
    }

    // 获取各类出车人员的信息
    const dispatchDoctor = getDispatchDetails('#dispatchDoctorDri-second');
    const dispatchNurse = getDispatchDetails('#dispatchNurseDri-second');
    const dispatchWorker = getDispatchDetails('#dispatchWorkerDri-second');
    const dispatchDriver = getDispatchDetails('#dispatchDriverDri-second');

    // 验证数据完整性
    const personnelToVerify = [
        { ...dispatchDoctor, msg: '出车医生' },
        { ...dispatchNurse, msg: '出车护士' },
        { ...dispatchDriver, msg: '出车司机' },
        { ...dispatchWorker, msg: '出车护工' }
    ];

    const msgArr = personnelToVerify.filter(person => 
        !(person.name && person.id && person.num) && (person.name || person.id || person.num)
    ).map(person => person.msg);

    if (msgArr.length > 0) {
        return $.messager.alert('温馨提示', `${msgArr.join(',')}数据不完整，请在下拉列表选择，不要手动输入数据`, 'error');
    }

    showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');

    // 获取座席的细节（从主屏传递或获取）
    const seatDetails = {
        seatId: _pSeat ? _pSeat.id : '',
        seatCode: _pSeat ? _pSeat.seatId : '',
        seatUserId: _pSeat ? _pSeat.userId : '',
        seatUserName: _pSeat ? _pSeat.userName : '',
        seatUser: _pSeat ? _pSeat.user : ''
    };

    // 通用接口参数
    const params = {
        id: '',
        eventId,
        carId,
        toStatus: '6',
        ...seatDetails,
        
        dispatchDoctorId: dispatchDoctor.id,
        dispatchDoctorNum: dispatchDoctor.num,
        dispatchDoctor: dispatchDoctor.name,
        dispatchDoctorPhone: dispatchDoctor.phone,

        dispatchNurseId: dispatchNurse.id,
        dispatchNurseNum: dispatchNurse.num,
        dispatchNurse: dispatchNurse.name,
        dispatchNursePhone: dispatchNurse.phone,

        dispatchDriverId: dispatchDriver.id,
        dispatchDriverNum: dispatchDriver.num,
        dispatchDriver: dispatchDriver.name,
        dispatchDriverPhone: dispatchDriver.phone,

        dispatchWorkerId: dispatchWorker.id,
        dispatchWorkerNum: dispatchWorker.num,
        dispatchWorker: dispatchWorker.name,
        dispatchWorkerPhone: dispatchWorker.phone
    };

    // 执行派车API请求
    addMobileProcess(params, 
        function(res) {
            showProcess(false);
            $('#confirm-receive-editor-second').window('close', true);
            $.messager.alert('提示', '副屏派车(增派)车辆成功！', 'info');
            
            // 刷新车辆列表
            setTimeout(() => {
                refreshVehicleList();
            }, 500);
            
            // 通知主屏刷新列表
            try {
                if (typeof gProxy !== "undefined" && gProxy != null) {
                    gProxy.homeScreenListRefreshes("");
                } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                    bsProxy.homeScreenListRefreshes("");
                } else {
                    window.opener && window.opener.homeScreenListRefreshes && window.opener.homeScreenListRefreshes("");
                }
            } catch (e) {
                console.log('通知主屏刷新失败:', e.message);
            }
        }, 
        function(e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', '派车失败：' + errMsg, 'error');
        }
    );
}

/**
 * 统一的派车请求执行函数
 */
function executeDispatchRequest(params, modeDescription) {
    console.log(`执行${modeDescription}请求:`, params);
    
    addMobileProcess(params,
        function (res) {
            $.messager.alert('提示', `${modeDescription}成功！`, 'info');
            setTaskDispatchData = null;
            
            // 派遣成功后立即刷新本页面的车辆列表
            console.log('派遣成功，立即刷新车辆列表');
            setTimeout(() => {
                if (_mobilesWithDistance) {
                    sortAndShowStations(_mobilesWithDistance);
                }
            }, 500);
            
            try {
                // 兼容C#和B/S模式的刷新主屏幕列表
                if (typeof gProxy !== "undefined" && gProxy != null) {
                    gProxy.homeScreenListRefreshes("");
                } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                    bsProxy.homeScreenListRefreshes("");
                } else {
                    window.opener.homeScreenListRefreshes("");
                }
            } catch (e) {
                try {
                    window.opener.homeScreenListRefreshes("");
                } catch (e2) {
                    console.warn('刷新主屏幕列表失败:', e2);
                }
            }
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', `${modeDescription}失败：${errMsg}`, 'error');
        }
    );
}

// 防止重复执行的标志
if (typeof window.secondScr_gaode_initialized === 'undefined') {
    // 先声明 bsProxy 变量，避免 ReferenceError
    var bsProxy = null;
    
    //兼容web，如果非C#中执行，那么gProxy是未定义的，使用自定义的gProxy
    try {
        _baseUrl = gProxy.getBaseUrl();
    } catch (e) {
        if (typeof gBsProxy === 'undefined') {
            console.error('gBsProxy 未定义！可能是脚本加载顺序问题');
            // 延迟重试
            setTimeout(function() {
                if (typeof gBsProxy !== 'undefined') {
                    bsProxy = new gBsProxy(document.body);
                    _baseUrl = bsProxy.getBaseUrl();
                } else {
                    console.error('延迟重试仍然失败，gBsProxy 仍未定义');
                }
            }, 100);
        } else {
            bsProxy = new gBsProxy(document.body);
            _baseUrl = bsProxy.getBaseUrl();
        }
        
        // B/S模式下添加localStorage监听，监听座席信息的变化
        window.addEventListener('storage', function(e) {
            if (e.key === '_pSeat' && e.newValue && e.newValue !== 'null' && e.newValue !== '') {
                try {
                    _pSeat = evalJson(e.newValue);
                    
                    // 如果之前初始化失败，现在重新初始化
                    if (!isInitStations) {
                        initStations();
                        isInitStations = true;
                    }
                } catch (error) {
                    console.error('副屏幕解析座席信息失败:', error);
                }
            }
        });
    }
    
    // 标记已初始化
    window.secondScr_gaode_initialized = true;
}

// 判断是否显示 所属分站 车牌号 速度 状态等
$(document).ready(function () {
    // 监听配置弹窗的事件
    $('#config-dialog').dialog({
        onOpen: function() {
            configDialog = true
            $('#configs').css('display', 'none');
            $('#configsChoose').css('display', 'block');
        },
        onClose: function() {
            configDialog = false
            $('#configs').css('display', 'block');
            $('#configsChoose').css('display', 'none');
        }
    });
    $('#config-dialog').window('close');
    
    // 初始化调度状态筛选事件
    initDispatchStatusFilter();
    
    // 初始化车辆类型选择
    initVehicleTypeSelection();
    
    // 初始化车辆右键菜单
    initVehicleContextMenu();
    
    // 初始化地图工具栏
    initMapToolbar();
    
    // 初始化工具栏状态
    initToolbarState();
    
    // 初始化副屏人员选择界面
    initPersonnelSelectionSecondScreen();

    setMapCurrentLocLngLat()
});

// 点击显示配置分站的easyui处理
$('#fenzhan-checkbox').checkbox({
    onChange: function(checked) {
        fenzhanChecked = checked
        reRangeAndShowStations(staLists) // 重新请求 配置信息
    },
});
// 点击显示配置车牌号的easyui处理
$('#chepai-checkbox').checkbox({
    onChange: function(checked) {
        chepaiChecked = checked
        reRangeAndShowStations(staLists) // 重新请求 配置信息
    },
});
// 点击显示配置车辆速度的easyui处理
$('#sudu-checkbox').checkbox({
    onChange: function(checked) {
        suduChecked = checked
        reRangeAndShowStations(staLists) // 重新请求 配置信息
    },
});
// 点击显示配置车辆速度的easyui处理
$('#status-checkbox').checkbox({
    onChange: function(checked) {
      statusChecked = checked
      reRangeAndShowStations(staLists) // 重新请求 配置信息
    },
});

/** 初始化调度状态筛选功能 */
function initDispatchStatusFilter() {
    // 监听调度状态多选框变化
    $('input[name="dispatch-status"]').on('change', function() {
        dispatchStatusFilter = [];
        $('input[name="dispatch-status"]:checked').each(function() {
            dispatchStatusFilter.push($(this).val());
        });
        
        // 如果选中了"待派"，显示车辆类型选择区域
        if (dispatchStatusFilter.includes('waiting')) {
            $('#vehicle-type-section').show();
        } else {
            $('#vehicle-type-section').hide();
            // 清空车辆类型筛选
            vehicleTypeFilter = [];
            $('input[name="vehicle-type"]').prop('checked', false);
        }
        
        // 重新筛选和显示车辆列表
        if (staLists) {
            reRangeAndShowStations(staLists);
        }
    });
}

/** 初始化车辆类型选择功能 */
function initVehicleTypeSelection() {
    // 从字典获取车辆类型数据
    getVehicleTypes();
}

/** 获取车辆类型列表（从字典获取） */
function getVehicleTypes() {
    // 查询车辆类型字典
    queryAllDic(["car_type"], function(data) {
        var carTypeList = getDicList('car_type', data);
        vehicleTypeOptions = []; // 清空全局变量
        
        // 将字典数据转换为前端需要的格式
        for (var i = 0; i < carTypeList.length; i++) {
            var d = carTypeList[i];
            vehicleTypeOptions.push({
                id: d.codeName,
                name: d.codeVale
            });
        }
        
        // 渲染车辆类型选择框
        renderVehicleTypeCheckboxes(vehicleTypeOptions);
    }, function(e, url, errMsg) {
        console.error('获取车辆类型字典失败:', errMsg);
        // 失败时使用默认选项
        vehicleTypeOptions = [
            { id: 'emergency', name: '急救' },
            { id: 'home', name: '回家' },
            { id: 'transfer', name: '转院' }
        ];
        renderVehicleTypeCheckboxes(vehicleTypeOptions);
    });
}

/** 渲染车辆类型多选框 */
function renderVehicleTypeCheckboxes(types) {
    const container = $('#vehicle-type-checkboxes');
    container.empty();
    
    types.forEach(function(type) {
        const checkboxHtml = `
            <label style="margin-right: 5px; display: inline-block;">
                <input type="checkbox" name="vehicle-type" value="${type.id}" style="margin-right: 3px;"> ${type.name}
            </label>
        `;
        container.append(checkboxHtml);
    });
    
    // 监听车辆类型多选框变化
    $('input[name="vehicle-type"]').on('change', function() {
        vehicleTypeFilter = [];
        $('input[name="vehicle-type"]:checked').each(function() {
            vehicleTypeFilter.push($(this).val());
        });
        
        // 重新筛选和显示车辆列表
        if (staLists) {
            reRangeAndShowStations(staLists);
        }
    });
}

/** 获取车辆类型 */
function getCarVehicleType(car) {
    // 直接返回车辆的carType字段
    return car.carType || '';
}

/** 初始化车辆右键菜单 */
function initVehicleContextMenu() {
    // 初始化右键菜单
    $('#vehicle-context-menu').menu({
        onClick: function(item) {
            handleVehicleContextMenuClick(item);
        }
    });
}

/** 初始化地图工具栏 */
function initMapToolbar() {
    // 检查是否使用新的工具栏系统
    if ($('#follow-btn').length > 0) {
        // 新的工具栏系统
        $('#follow-btn').click(function() {
            toggleFollowMode();
        });
        
        $('#track-btn').click(function() {
            toggleTrackMode();
        });
        
        $('#config-btn').click(function() {
            toggleConfigDialog();
        });
    } else {
        // 旧的按钮系统 (second_gaode.html)
        $('#isFollow').click(function() {
            toggleFollowMode();
        });
        
        $('#noFollow').click(function() {
            toggleFollowMode();
        });
        
        $('#show_guiji').click(function() {
            toggleTrackMode();
        });
        
        $('#hide_guiji').click(function() {
            toggleTrackMode();
        });
        
        $('#configs').click(function() {
            toggleConfigDialog();
        });
    }
    
    // 地图工具按钮事件
    $('#zoomin-btn').click(function() {
        zoomInMap();
    });
    
    $('#zoomout-btn').click(function() {
        zoomOutMap();
    });
    
    $('#scale-btn').click(function() {
        toggleScaleControl();
    });
    
    $('#traffic-btn').click(function() {
        toggleTrafficLayer();
    });
    
    $('#measure-btn').click(function() {
        toggleMeasureTool();
    });
    
    $('#marker-btn').click(function() {
        toggleMarkerTool();
    });
    
    $('#range-btn').click(function() {
        toggleRangeMenu();
    });
    
    $('#overview-btn').click(function() {
        toggleOverviewMap();
    });
    

    
    $('#clear-btn').click(function() {
        clearAllAnnotations();
    });
    
    // 围栏违规监控按钮事件
    $('#fence-monitor-btn').click(function() {
        console.log('围栏监控按钮被点击');
        toggleFenceMonitor();
    });
    
    // 地图资源按钮事件
    $('#vehicles-display-btn').click(function() {
        toggleVehiclesDisplay();
    });
    
    $('#stations-display-btn').click(function() {
        toggleStationsDisplay();
    });
    
    $('#hospitals-display-btn').click(function() {
        toggleHospitalsDisplay();
    });
    
    $('#bloodstations-display-btn').click(function() {
        toggleBloodstationsDisplay();
    });
    
    $('#cdc-display-btn').click(function() {
        toggleCdcDisplay();
    });
    
    // 范围菜单项点击事件
    $('.range-menu-item').click(function() {
        const radius = parseInt($(this).data('radius'));
        const text = $(this).text();
        drawRangeCircle(radius, text);
        $('#range-menu').removeClass('show');
    });
    
    // 点击其他地方关闭范围菜单
    $(document).click(function(e) {
        if (!$(e.target).closest('.range-dropdown').length) {
            $('#range-menu').removeClass('show');
        }
        if (!$(e.target).closest('#config-dialog, #config-btn').length) {
            $('#config-dialog').window('close');
        }
    });
    
    // 初始化状态
    updateToolbarState();
    
    // 确保初始时没有选中任何车辆
    clearVehicleSelection();
}

/** 切换工具栏组展开/收缩状态 */
window.toggleToolbarGroup = function(groupId) {
    const group = document.getElementById(groupId);
    group.classList.toggle('collapsed');
    
    // 保存状态到localStorage
    const isCollapsed = group.classList.contains('collapsed');
    localStorage.setItem(groupId + 'Collapsed', isCollapsed);
};

/** 初始化工具栏状态 */
function initToolbarState() {
    // 初始化车辆工具组状态
    const vehicleGroup = document.getElementById('vehicle-group');
    const vehicleCollapsed = localStorage.getItem('vehicle-groupCollapsed') === 'true';
    if (vehicleCollapsed) {
        vehicleGroup.classList.add('collapsed');
    }
    
    // 初始化地图工具组状态
    const mapToolsGroup = document.getElementById('map-tools-group');
    const mapToolsCollapsed = localStorage.getItem('map-tools-groupCollapsed') === 'true';
    if (mapToolsCollapsed) {
        mapToolsGroup.classList.add('collapsed');
    }
    
    // 初始化地图资源组状态
    const mapResourcesGroup = document.getElementById('map-resources-group');
    const mapResourcesCollapsed = localStorage.getItem('map-resources-groupCollapsed') === 'true';
    if (mapResourcesCollapsed) {
        mapResourcesGroup.classList.add('collapsed');
    }
    
    // 初始化地图资源按钮状态（默认都显示）
    const vehiclesBtn = document.getElementById('vehicles-display-btn');
    const stationsBtn = document.getElementById('stations-display-btn');
    const hospitalsBtn = document.getElementById('hospitals-display-btn');
    const bloodstationsBtn = document.getElementById('bloodstations-display-btn');
    const cdcBtn = document.getElementById('cdc-display-btn');
    
    // 车辆显示默认开启
    if (mapResourcesState.vehiclesVisible && vehiclesBtn) {
        vehiclesBtn.classList.add('active');
    }
    
    // 分站显示默认开启
    if (mapResourcesState.stationsVisible && stationsBtn) {
        stationsBtn.classList.add('active');
    }
    
    // 医院显示默认关闭
    if (mapResourcesState.hospitalsVisible && hospitalsBtn) {
        hospitalsBtn.classList.add('active');
    }
    
    // 血站显示默认关闭
    if (mapResourcesState.bloodstationsVisible && bloodstationsBtn) {
        bloodstationsBtn.classList.add('active');
    }
    
    // 疾控中心显示默认关闭
    if (mapResourcesState.cdcVisible && cdcBtn) {
        cdcBtn.classList.add('active');
    }
    
    // 初始化统计信息显示
    updateMapResourcesStats();
}

/** 初始化副屏人员选择界面 */
function initPersonnelSelectionSecondScreen() {
    // 班次选择框配置
    $('#dispatchClassesDri-second').combobox({
        prompt: '请选择出车班次',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        },
        onSelect: function (record) {
            // 选择班次后自动填充对应的人员信息
            autoFillPersonnelByScheduleSecondScreen(record.id);
        }
    });

    // 医生选择框配置
    $('#dispatchDoctorDri-second').combobox({
        prompt: '请输入医生工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    // 护士选择框配置
    $('#dispatchNurseDri-second').combobox({
        prompt: '请输入护士工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    // 司机选择框配置
    $('#dispatchDriverDri-second').combobox({
        prompt: '请输入司机工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    // 护工选择框配置
    $('#dispatchWorkerDri-second').combobox({
        prompt: '请输入护工工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
}

/** 根据选择的班次自动填充人员信息 - 副屏专用 */
function autoFillPersonnelByScheduleSecondScreen(scheduleId) {
    // 查找对应的班次信息
    if (!dispatchClassesList) return;
    
    var selectedSchedule = dispatchClassesList.find(function(item) {
        return item.id === scheduleId;
    });
    
    if (!selectedSchedule) return;
    
    // 自动选择医生
    if (selectedSchedule.dispatchDoctorId) {
        var doctorData = $('#dispatchDoctorDri-second').combobox('getData');
        for (var i = 0; i < doctorData.length; i++) {
            if (doctorData[i].id == selectedSchedule.dispatchDoctorId) {
                $('#dispatchDoctorDri-second').combobox('select', selectedSchedule.dispatchDoctorId);
                break;
            }
        }
    }

    // 自动选择护士
    if (selectedSchedule.dispatchNurseId) {
        var nurseData = $('#dispatchNurseDri-second').combobox('getData');
        for (var i = 0; i < nurseData.length; i++) {
            if (nurseData[i].id == selectedSchedule.dispatchNurseId) {
                $('#dispatchNurseDri-second').combobox('select', selectedSchedule.dispatchNurseId);
                break;
            }
        }
    }

    // 自动选择司机
    if (selectedSchedule.dispatchDriverId) {
        var driverData = $('#dispatchDriverDri-second').combobox('getData');
        for (var i = 0; i < driverData.length; i++) {
            if (driverData[i].id == selectedSchedule.dispatchDriverId) {
                $('#dispatchDriverDri-second').combobox('select', selectedSchedule.dispatchDriverId);
                break;
            }
        }
    }

    // 自动选择护工
    if (selectedSchedule.dispatchWorkerId) {
        var workerData = $('#dispatchWorkerDri-second').combobox('getData');
        for (var i = 0; i < workerData.length; i++) {
            if (workerData[i].id == selectedSchedule.dispatchWorkerId) {
                $('#dispatchWorkerDri-second').combobox('select', selectedSchedule.dispatchWorkerId);
                break;
            }
        }
    }
}

/** 切换跟随模式 */
function toggleFollowMode() {
    // 如果要开启跟随功能，先检查是否有选中的车辆
    if (!isFollow) {
        var selectedCarId = nowCar?.id || currentCarId || clickCarId || _selCarId;
        if (!selectedCarId) {
            $.messager.alert('提示', 
                '请先选中一辆车辆！<br><br>' +
                '您可以通过以下方式选中车辆：<br>' +
                '• 点击地图上的车辆图标<br>' +
                '• 点击右侧车辆列表中的车辆', 
                'warning'
            );
            return; // 不切换状态，直接返回
        }
        
        // 检查选中的车辆是否存在
        var selectedCar = _mobilesWithDistance ? _mobilesWithDistance.find(car => car.id == selectedCarId) : null;
        if (!selectedCar) {
            $.messager.alert('提示', '选中的车辆信息不存在，请重新选择车辆。', 'warning');
            return; // 不切换状态，直接返回
        }
        
        // 检查是否有筛选条件，给出相应提示
        let filterTip = '';
        if (dispatchStatusFilter.length > 0) {
            const filterLabels = [];
            if (dispatchStatusFilter.includes('waiting')) filterLabels.push('待派');
            if (dispatchStatusFilter.includes('dispatched')) filterLabels.push('调派');
            filterTip = `<br><br><span style="color: #ff9800;">注意：当前列表已筛选（${filterLabels.join('、')}），如需查看所有车辆请取消筛选。</span>`;
        }
        
        // 立即移动地图到选中车辆位置
        if (selectedCar.lng && selectedCar.lat) {
            map.setCenter(new AMap.LngLat(selectedCar.lng, selectedCar.lat));
            // 适当调整缩放级别，确保车辆清晰可见
            if (map.getZoom() < 14) {
                map.setZoom(14);
            }
            _lastCenterPosition = {lng: selectedCar.lng, lat: selectedCar.lat};
        }
        
        // 给出跟随功能使用提示
        $.messager.alert('跟随功能', 
            `跟随功能已开启！<br><br>` +
            `当前跟随车辆：${selectedCar.carName}<br><br>` +
            `• 地图视角将自动跟随车辆移动<br>` +
            `• 地图会始终保持车辆在屏幕中央<br>` +
            `• 您可以随时点击"取消"按钮停止跟随<br>` +
            `• 手动拖拽地图会暂时中断跟随，车辆移动时会重新跟随` +
            filterTip, 
            'info'
        );
    }
    
    isFollow = !isFollow;
    
    // 如果关闭跟随功能，清除跟随状态
    if (!isFollow) {
        _lastCenterPosition = null;
        console.log('跟随功能已关闭');
    }
    
    updateToolbarState();
}

/** 切换路径显示模式 */
function toggleTrackMode() {
    // 如果要开启路径功能，先检查是否有选中的车辆
    if (!showGuiJi) {
        // 检查是否有选中的车辆
        var selectedCarId = nowCar?.id || currentCarId || clickCarId || _selCarId;
        if (!selectedCarId) {
            $.messager.alert('提示', 
                '请先选中一辆车辆！<br><br>' +
                '您可以通过以下方式选中车辆：<br>' +
                '• 点击地图上的车辆图标<br>' +
                '• 点击右侧车辆列表中的车辆', 
                'warning'
            );
            return; // 不切换状态，直接返回
        }
        
        // 检查选中的车辆是否存在
        var selectedCar = _mobilesWithDistance ? _mobilesWithDistance.find(car => car.id == selectedCarId) : null;
        if (!selectedCar) {
            $.messager.alert('提示', '选中的车辆信息不存在，请重新选择车辆。', 'warning');
            return; // 不切换状态，直接返回
        }
        
        // 如果是待派状态的车辆，检查是否选中了任务
        if (selectedCar.status == 0) {
            // 检查是否有选中的任务
            if (!setTaskDispatchData || !setTaskDispatchData.lng || !setTaskDispatchData.lat) {
                $.messager.alert('提示', 
                    '待派车辆需要先选择任务！<br><br>' +
                    '请在主屏幕中双击选择一个事件任务，<br>' +
                    '然后再查看车辆到任务地点的路径。', 
                    'warning'
                );
                return; // 不切换状态，直接返回
            }
            
            }
    }
    
    showGuiJi = !showGuiJi;
    
    // 先清除所有路径
    clearVehicleRoutes();
    
    if (showGuiJi) {
        // 开启路径显示时给出友好提示
        var selectedCar = _mobilesWithDistance.find(car => car.id == selectedCarId);
        var statusMessage = '';
        if (selectedCar.status == 0) {
            statusMessage = `<br>当前选中车辆 ${selectedCar.carName} 处于待派状态，<br>将显示到任务地点 "${setTaskDispatchData.majorCall || '未知任务'}" 的路径。`;
        } else {
            statusMessage = `<br>当前选中车辆 ${selectedCar.carName} 处于运行状态，<br>将显示其路径。`;
        }
        
        $.messager.alert('路径显示', 
            '路径功能已开启！<br><br><br><br>' +
            '• 待派状态车辆：显示到当前任务地点的路径<br>' +
            '• 执行任务车辆：显示到任务目的地的路径<br>' +
            '• 返回状态车辆：显示到分站的路径<br><br>' +
            '路径用绿色线条表示，目的地标记显示距离和预计到达时间。' +
            statusMessage, 
            'info'
        );
        
        // 显示选中车辆的路径
        var selectedCarId = nowCar?.id || currentCarId || clickCarId || _selCarId;
        var selectedCar = _mobilesWithDistance.find(car => car.id == selectedCarId);
        if (selectedCar) {
            drawVehicleTrack(selectedCar, true);
        }
    } else {
        // 关闭路径显示时，清除所有路径
        clearVehicleRoutes();
        $.messager.alert('路径显示', '路径功能已关闭，所有路径已清除。', 'info');
    }
    
    updateToolbarState();
}

/** 清除车辆路线 */
function clearVehicleRoutes() {
    // 清除路径路线
    let trackKeys = Object.keys(carTrackLines);
    trackKeys.forEach(item => {
        carTrackLines[item].setMap(null);
    });
    carTrackLines = {};
    
    // 清除目的地标记
    var markerKeys = Object.keys(carDestinationMarkers);
    markerKeys.forEach(item => {
        carDestinationMarkers[item].setMap(null);
    });
    carDestinationMarkers = {};
    
    // 清除路线信息
    carRouteInfo = {};
    
    // 清除路径状态记录
    carTrackState = {};
    
    // 清除当前路径车辆ID
    window._currentTrackVehicleId = null;
    
    // 清除路径规划对象
    if (window._currentDriving) {
        try {
            window._currentDriving.clear();
        } catch (e) {
            console.warn('清除路径规划对象失败:', e);
        }
        window._currentDriving = null;
    }
}

/** 清除其他车辆的路径路线，只保留指定车辆的路径 */
function clearOtherVehicleRoutes(keepCarId) {
    // 清除其他车辆的路径路线
    let trackKeys = Object.keys(carTrackLines);
    trackKeys.forEach(carId => {
        if (carId != keepCarId) {
            carTrackLines[carId].setMap(null);
            delete carTrackLines[carId];
        }
    });
    
    // 清除其他车辆的目的地标记
    var markerKeys = Object.keys(carDestinationMarkers);
    markerKeys.forEach(carId => {
        if (carId != keepCarId) {
            carDestinationMarkers[carId].setMap(null);
            delete carDestinationMarkers[carId];
        }
    });
    
    // 清除其他车辆的路线信息
    var routeKeys = Object.keys(carRouteInfo);
    routeKeys.forEach(carId => {
        if (carId != keepCarId) {
            delete carRouteInfo[carId];
        }
    });
    
    // 清除其他车辆的路径状态记录
    var stateKeys = Object.keys(carTrackState);
    stateKeys.forEach(carId => {
        if (carId != keepCarId) {
            delete carTrackState[carId];
        }
    });
}



/** 清除车辆选中状态 */
function clearVehicleSelection() {
    nowCar = null;
    currentCarId = null;
    clickCarId = null;
    _selCarId = null;
    
    // 清除列表中的选中状态
    $('#map-station li').removeClass('selected');
}

/** 切换配置对话框 */
function toggleConfigDialog() {
    if ($('#config-dialog').dialog('options').closed) {
        $('#config-dialog').window('open');
    } else {
        $('#config-dialog').window('close');
    }
    updateToolbarState();
}

/** 放大地图 */
function zoomInMap() {
    const currentZoom = map.getZoom();
    if (currentZoom < 20) {
        map.setZoom(currentZoom + 1);
    } else {
        $.messager.alert('提示', '地图已达到最大缩放级别', 'info');
    }
}

/** 缩小地图 */
function zoomOutMap() {
    const currentZoom = map.getZoom();
    if (currentZoom > 3) {
        map.setZoom(currentZoom - 1);
    } else {
        $.messager.alert('提示', '地图已达到最小缩放级别', 'info');
    }
}

/** 切换比例尺控件 */
function toggleScaleControl() {
    if (mapAnnotations.scale) {
        // 关闭比例尺
        map.removeControl(mapAnnotations.scale);
        mapAnnotations.scale = null;
    } else {
        // 开启比例尺
        AMap.plugin('AMap.Scale', function() {
            mapAnnotations.scale = new AMap.Scale({
                position: 'LB' // 左下角显示
            });
            map.addControl(mapAnnotations.scale);
        });
    }
    updateToolbarState();
}

/** 切换路况图层 */
function toggleTrafficLayer() {
    if (mapAnnotations.trafficLayer) {
        // 关闭路况图层
        mapAnnotations.trafficLayer.hide();
        map.remove(mapAnnotations.trafficLayer);
        mapAnnotations.trafficLayer = null;
    } else {
        // 开启路况图层
        mapAnnotations.trafficLayer = new AMap.TileLayer.Traffic({
            zIndex: 10,
            zooms: [7, 20] // 在7-20级显示路况
        });
        mapAnnotations.trafficLayer.setMap(map);
    }
    updateToolbarState();
}

/** 切换测距工具 */
function toggleMeasureTool() {
    if (mapToolsState.measuring) {
        // 关闭测距工具
        if (mapAnnotations.ruler) {
            mapAnnotations.ruler.close();
            mapAnnotations.ruler = null;
        }
        mapToolsState.measuring = false;
    } else {
        // 关闭其他工具
        closeOtherMapTools('measuring');
        
        // 给出测距工具使用提示
        $.messager.alert('测距功能', 
            '测距功能已开启！<br><br>' +
            '📐 <strong>使用方法：</strong><br>' +
            '• 在地图上点击起始位置<br>' +
            '• 继续点击设置途径点（可多个）<br>' +
            '• 双击结束绘制并显示总距离<br>' +
            '• 距离标签会显示在终点位置<br><br>' +
            '🎯 <strong>功能特色：</strong><br>' +
            '• 📏 精确直线距离计算<br>' +
            '• 🎨 橙色虚线路径显示<br>' +
            '• 🔢 自动单位转换（米/公里）<br>' +
            '• 🗑️ 点击距离标签可删除<br><br>' +
            '💡 <strong>温馨提示：</strong><br>' +
            '测距结果为直线距离，非实际道路距离。点击测距线或标签可删除测距结果。', 
            'info'
        );
        
        // 开启测距工具 - 使用MouseTool实现测距
        AMap.plugin('AMap.MouseTool', function() {
            mapAnnotations.ruler = new AMap.MouseTool(map);
            
            // 监听测距完成事件
            mapAnnotations.ruler.on('draw', function(e) {
                // 将测距结果保存到polylines数组中
                if (e.obj) {
                    mapAnnotations.polylines.push(e.obj);
                    
                    // 计算并显示距离
                    const path = e.obj.getPath();
                    if (path && path.length >= 2) {
                        let totalDistance = 0;
                        for (let i = 1; i < path.length; i++) {
                            totalDistance += path[i-1].distance(path[i]);
                        }
                        
                        // 格式化距离显示
                        let distanceText;
                        if (totalDistance < 1000) {
                            distanceText = Math.round(totalDistance) + '米';
                        } else {
                            distanceText = (totalDistance / 1000).toFixed(2) + '公里';
                        }
                        
                        // 在线段末端显示距离标签，包含删除按钮
                        const endPoint = path[path.length - 1];
                        const distanceMarker = new AMap.Marker({
                            position: endPoint,
                            content: `<div style="background: rgba(255,102,0,0.9); color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; border: 1px solid #fff; box-shadow: 0 2px 6px rgba(0,0,0,0.3); white-space: nowrap; cursor: pointer;">总距离: ${distanceText} <span style="margin-left: 5px; font-weight: bold;">✖</span></div>`,
                            offset: new AMap.Pixel(-50, -30)
                        });
                        
                        distanceMarker.setMap(map);
                        mapAnnotations.markers.push(distanceMarker);
                        
                        // 建立测距线段和标签的关联关系
                        e.obj.distanceMarker = distanceMarker;
                        distanceMarker.measureLine = e.obj;
                        
                        // 为测距线段添加点击删除事件
                        e.obj.on('click', function() {
                            deleteMeasureLine(e.obj, distanceMarker);
                        });
                        
                        // 为距离标签添加点击删除事件
                        distanceMarker.on('click', function() {
                            deleteMeasureLine(e.obj, distanceMarker);
                        });
                    }
                }
                
                // 测距完成后自动关闭测距工具
                mapAnnotations.ruler.close();
                mapAnnotations.ruler = null;
                mapToolsState.measuring = false;
                updateToolbarState();
                
                $.messager.alert('提示', '测距完成，双击结束绘制', 'info');
            });
            
            // 开始绘制折线进行测距
            mapAnnotations.ruler.polyline({
                strokeColor: '#FF6600',
                strokeWeight: 3,
                strokeOpacity: 0.8,
                strokeStyle: 'solid',
                strokeDasharray: [5, 5]
            });
            
            mapToolsState.measuring = true;
        });
    }
    
    updateToolbarState();
}

/** 为鹰眼地图添加自定义样式 */
function addHawkEyeCustomStyle() {
    // 查找鹰眼地图的DOM元素
    const hawkEyeElements = document.querySelectorAll('.amap-hawkeye');
    
    hawkEyeElements.forEach(function(element) {
        // 添加灰色边框和圆角
        element.style.border = '1px solid #888888';
        element.style.borderRadius = '8px';
        element.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        element.style.overflow = 'hidden'; // 确保圆角效果
    });
    
    // 如果没有找到元素，可能需要再等待一下
    if (hawkEyeElements.length === 0) {
        setTimeout(function() {
            addHawkEyeCustomStyle();
        }, 200);
    }
}

/** 删除指定的测距线段 */
function deleteMeasureLine(polyline, marker) {
    $.messager.confirm('确认删除', '确定要删除这条测距线段吗？', function(r) {
        if (r) {
            // 从地图上移除测距线段
            if (polyline) {
                polyline.setMap(null);
                // 从数组中移除
                const polylineIndex = mapAnnotations.polylines.indexOf(polyline);
                if (polylineIndex > -1) {
                    mapAnnotations.polylines.splice(polylineIndex, 1);
                }
            }
            
            // 从地图上移除距离标签
            if (marker) {
                marker.setMap(null);
                // 从数组中移除
                const markerIndex = mapAnnotations.markers.indexOf(marker);
                if (markerIndex > -1) {
                    mapAnnotations.markers.splice(markerIndex, 1);
                }
            }
            
            $.messager.alert('提示', '测距线段已删除', 'info');
        }
    });
}

/** 切换标注工具 */
function toggleMarkerTool() {
    if (mapToolsState.marking) {
        // 关闭标注工具
        if (mapAnnotations.mouseTool) {
            mapAnnotations.mouseTool.close();
            mapAnnotations.mouseTool = null;
        }
        mapToolsState.marking = false;
    } else {
        // 关闭其他工具
        closeOtherMapTools('marking');
        
        // 给出标注工具使用提示
        $.messager.alert('标注功能', 
            '标注功能已开启！<br><br>' +
            '📍 <strong>使用方法：</strong><br>' +
            '• 在地图上点击任意位置添加标注点<br>' +
            '• 点击后会自动弹出编辑窗口<br>' +
            '• 可以输入标注描述信息<br>' +
            '• 系统会自动获取坐标和地址信息<br><br>' +
            '🎯 <strong>功能特色：</strong><br>' +
            '• 📋 一键复制坐标信息<br>' +
            '• 📍 一键复制地址信息<br>' +
            '• 🔄 智能地址解析<br>' +
            '• ✏️ 随时编辑标注内容<br><br>' +
            '💡 <strong>温馨提示：</strong><br>' +
            '点击标注点可以重新编辑，标注信息会以红色醒目标签显示在地图上。', 
            'info'
        );
        
        // 开启标注工具
        AMap.plugin('AMap.MouseTool', function() {
            mapAnnotations.mouseTool = new AMap.MouseTool(map);
            
            // 监听标注完成事件
            mapAnnotations.mouseTool.on('draw', function(e) {
                mapAnnotations.markers.push(e.obj);
                
                // 设置更小的美观标注图标
                e.obj.setIcon(new AMap.Icon({
                    image: 'data:image/svg+xml;base64,' + btoa(
                        '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="25" viewBox="0 0 20 25">' +
                            '<defs>' +
                                '<linearGradient id="grad1" x1="0%" y1="0%" x2="0%" y2="100%">' +
                                    '<stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />' +
                                    '<stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />' +
                                '</linearGradient>' +
                            '</defs>' +
                            '<path d="M10 0C4.477 0 0 4.477 0 10c0 10 10 15 10 15s10-5 10-15C20 4.477 15.523 0 10 0z" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>' +
                            '<circle cx="10" cy="10" r="4" fill="#fff"/>' +
                        '</svg>'
                    ),
                    size: new AMap.Size(20, 25),
                    imageSize: new AMap.Size(20, 25),
                    imageOffset: new AMap.Pixel(0, 0)
                }));
                
                // 存储标注信息到标记对象
                e.obj.markerInfo = '';
                e.obj.markerId = mapAnnotations.markers.length;
                e.obj.markerPosition = e.obj.getPosition(); // 存储坐标信息
                
                // 添加点击事件打开编辑窗口
                e.obj.on('click', function() {
                    // 更新范围工具的选中状态
                    lastSelectedMarker = e.obj;
                    lastSelectedPosition = e.obj.getPosition();
                    lastSelectedType = 'marker';
                    lastSelectedDescription = `标注点 ${e.obj.markerInfo || '(未命名)'}`;
                    
                    openMarkerEditWindow(e.obj);
                });
                
                // 标注完成后立即打开编辑窗口
                openMarkerEditWindow(e.obj);
                
                // 标注完成后自动关闭标注工具
                mapAnnotations.mouseTool.close();
                mapAnnotations.mouseTool = null;
                mapToolsState.marking = false;
                updateToolbarState();
            });
            
            mapAnnotations.mouseTool.marker();
            mapToolsState.marking = true;
        });
    }
    
    updateToolbarState();
}

/** 打开标注编辑窗口 */
function openMarkerEditWindow(marker) {
    const currentInfo = marker.markerInfo || '';
    const position = marker.getPosition();
    const isNewMarker = currentInfo === ''; // 判断是否为新建标注
    
    // 获取地址信息
    AMap.plugin('AMap.Geocoder', function() {
        const geocoder = new AMap.Geocoder();
        geocoder.getAddress([position.lng, position.lat], function(status, result) {
            let addressInfo = '获取地址失败';
            if (status === 'complete' && result.info === 'OK') {
                const regeocode = result.regeocode;
                addressInfo = regeocode.formattedAddress;
            }
            
            const windowTitle = isNewMarker ? '📍 新建标注' : '📍 编辑标注';
            const inputPlaceholder = isNewMarker ? '请输入标注信息' : '输入标注信息';
            
            const infoWindow = new AMap.InfoWindow({
                content: `<div style="padding: 15px; min-width: 300px; max-width: 400px;">
                    <div style="margin-bottom: 12px; font-weight: bold; color: #333; border-bottom: 2px solid #FF4444; padding-bottom: 8px; font-size: 14px;">${windowTitle}</div>
                    
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666; font-weight: bold;">标注描述：</label>
                        <input type="text" id="marker-edit-input" 
                               value="${currentInfo}"
                               placeholder="${inputPlaceholder}" 
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; box-sizing: border-box;"
                               autofocus>
                    </div>
                    
                    <div style="margin-bottom: 12px; background: #f8f9fa; padding: 10px; border-radius: 6px; border-left: 4px solid #FF4444;">
                        <div style="margin-bottom: 6px;">
                            <strong style="color: #FF4444;">🌐 坐标信息：</strong>
                        </div>
                        <div style="font-size: 12px; color: #666; line-height: 1.5;">
                            <div><strong>经度：</strong> ${position.lng.toFixed(6)}</div>
                            <div><strong>纬度：</strong> ${position.lat.toFixed(6)}</div>
                            <div><strong>地址：</strong> ${addressInfo}</div>
                        </div>
                        <div style="margin-top: 8px;">
                            <button onclick="copyCoordinates('${position.lng.toFixed(6)}', '${position.lat.toFixed(6)}')" 
                                    style="padding: 4px 8px; background: #FF4444; color: #fff; border: none; border-radius: 3px; cursor: pointer; font-size: 11px; margin-right: 5px;">
                                📋 复制坐标
                            </button>
                            <button id="copy-address-btn" data-address="${addressInfo.replace(/"/g, '&quot;')}" onclick="copyAddressFromData(this);" 
                                    style="padding: 4px 8px; background: #52C41A; color: #fff; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                                📍 复制地址
                            </button>
                        </div>
                    </div>
                    
                    <div style="text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
                        <button onclick="saveMarkerEdit('${marker.markerId}')" 
                                style="padding: 8px 15px; background: #1890ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">💾 保存</button>
                        <button onclick="deleteMarker('${marker.markerId}')" 
                                style="padding: 8px 15px; background: #ff4d4f; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">🗑️ 删除</button>
                        <button onclick="closeMarkerEdit()" 
                                style="padding: 8px 15px; background: #f5f5f5; color: #666; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold;">❌ 取消</button>
                    </div>
                </div>`,
                offset: new AMap.Pixel(0, -25),
                autoMove: false  // 禁用自动移动
            });
            
            infoWindow.open(map, marker.getPosition());
            marker.currentInfoWindow = infoWindow;
            
            // 设置全局当前编辑的标记
            window.currentEditingMarker = marker;
            
            // 如果是新建标注，延迟一下让输入框获得焦点
            if (isNewMarker) {
                setTimeout(function() {
                    const input = document.getElementById('marker-edit-input');
                    if (input) {
                        input.focus();
                        input.select(); // 选中输入框内容
                    }
                }, 100);
            }
        });
    });
}

/** 保存标注编辑 */
window.saveMarkerEdit = function(markerId) {
    const input = document.getElementById('marker-edit-input');
    const text = input.value.trim();
    
    if (window.currentEditingMarker) {
        const marker = window.currentEditingMarker;
        
        // 如果没有输入内容，提示用户
        if (!text) {
            $.messager.confirm('确认', '标注内容为空，是否删除此标注？', function(r) {
                if (r) {
                    deleteMarkerInternal(marker);
                }
            });
            return;
        }
        
        // 保存标注信息
        marker.markerInfo = text;
        
        // 设置标签 - 使用HTML内容确保红色背景白色字体
        marker.setLabel({
            content: `<div style="
                background-color: #FF0000 !important; 
                color: #FFFFFF !important; 
                padding: 12px 18px; 
                border-radius: 15px; 
                font-size: 12px; 
                font-weight: bold; 
                border: 3px solid #FFFFFF; 
                box-shadow: 0 8px 25px rgba(0,0,0,0.8), 0 0 20px rgba(255,0,0,0.6); 
                text-shadow: 2px 2px 6px rgba(0,0,0,0.9); 
                white-space: nowrap; 
                text-align: center; 
                letter-spacing: 1px; 
                position: relative; 
                display: inline-block; 
                min-width: 80px; 
                max-width: 300px;
                z-index: 9999;
            ">${text}</div>`,
            offset: new AMap.Pixel(0, -40)
        });
        
        // 关闭信息窗口
        if (marker.currentInfoWindow) {
            marker.currentInfoWindow.close();
        }
        
        $.messager.alert('提示', '标注信息已保存', 'info');
    }
    
    window.currentEditingMarker = null;
};

/** 删除标注 */
window.deleteMarker = function(markerId) {
    $.messager.confirm('确认删除', '确定要删除这个标注吗？', function(r) {
        if (r) {
            if (window.currentEditingMarker) {
                deleteMarkerInternal(window.currentEditingMarker);
            }
        }
    });
};

/** 内部删除标注函数 */
function deleteMarkerInternal(marker) {
    // 从地图上移除标记
    marker.setMap(null);
    
    // 从数组中移除
    const markerIndex = mapAnnotations.markers.indexOf(marker);
    if (markerIndex > -1) {
        mapAnnotations.markers.splice(markerIndex, 1);
    }
    
    // 关闭信息窗口
    if (marker.currentInfoWindow) {
        marker.currentInfoWindow.close();
    }
    
    $.messager.alert('提示', '标注已删除', 'info');
    window.currentEditingMarker = null;
}

/** 关闭标注编辑窗口 */
window.closeMarkerEdit = function() {
    if (window.currentEditingMarker) {
        const marker = window.currentEditingMarker;
        
        // 如果是新建标注且没有保存（markerInfo为空），则删除标注
        if (marker.markerInfo === '') {
            deleteMarkerInternal(marker);
            return;
        }
        
        // 关闭信息窗口
        if (marker.currentInfoWindow) {
            marker.currentInfoWindow.close();
        }
    }
    window.currentEditingMarker = null;
};

/** 复制坐标信息 */
window.copyCoordinates = function(lng, lat) {
    const coordText = `经度: ${lng}, 纬度: ${lat}`;
    copyToClipboard(coordText, '坐标');
    
    // 自动填写到标注描述输入框
    const input = document.getElementById('marker-edit-input');
    if (input) {
        const currentValue = input.value.trim();
        if (currentValue) {
            input.value = currentValue + ' | ' + coordText;
        } else {
            input.value = coordText;
        }
        input.focus();
    }
};

/** 复制地址信息 */
window.copyAddress = function(address) {
    copyToClipboard(address, '地址');
    
    // 自动填写到标注描述输入框
    const input = document.getElementById('marker-edit-input');
    if (input) {
        const currentValue = input.value.trim();
        if (currentValue) {
            input.value = currentValue + ' | ' + address;
        } else {
            input.value = address;
        }
        input.focus();
    }
};

/** 从data属性复制地址信息 */
window.copyAddressFromData = function(button) {
    const address = button.getAttribute('data-address');
    if (address) {
        copyToClipboard(address, '地址');
        
        // 自动填写到标注描述输入框
        const input = document.getElementById('marker-edit-input');
        if (input) {
            const currentValue = input.value.trim();
            if (currentValue) {
                input.value = currentValue + ' | ' + address;
            } else {
                input.value = address;
            }
            input.focus();
        }
    } else {
        $.messager.alert('提示', '获取地址信息失败', 'warning');
    }
};

/** 通用复制到剪贴板函数 */
function copyToClipboard(text, type) {
    // 尝试使用现代的剪贴板API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(function() {
            $.messager.alert('提示', `${type}已复制到剪贴板！`, 'info');
        }).catch(function(err) {
            // 如果失败，使用传统方法
            fallbackCopyTextToClipboard(text, type);
        });
    } else {
        // 使用传统方法
        fallbackCopyTextToClipboard(text, type);
    }
    
    function fallbackCopyTextToClipboard(text, type) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                $.messager.alert('提示', `${type}已复制到剪贴板！`, 'info');
            } else {
                $.messager.alert('提示', `复制失败，请手动复制${type}信息`, 'warning');
            }
        } catch (err) {
            $.messager.alert('提示', `复制失败，请手动复制${type}信息`, 'warning');
        }
        
        document.body.removeChild(textArea);
    }
}

/** 切换范围菜单 */
function toggleRangeMenu() {
    $('#range-menu').toggleClass('show');
}

/** 绘制范围圆圈 */
function drawRangeCircle(radius, text) {
    let center = null;
    let centerDescription = '';
    
    // 使用优化的选中状态追踪系统 - 优先级：最后选中的位置 > 备选方案
    
    // 1. 优先使用最后选中的位置（最准确）
    if (lastSelectedPosition && lastSelectedType && lastSelectedDescription) {
        center = lastSelectedPosition;
        centerDescription = lastSelectedDescription;
    }
    
    // 2. 备选方案1：当前选中的车辆
    if (!center) {
        const latestCarId = currentCarId || clickCarId || _selCarId;
        if (latestCarId) {
            for (var i = 0; i < carMarkers.length; i++) {
                const marker = carMarkers[i];
                const carData = marker.getExtData();
                if (carData.id === latestCarId) {
                    center = marker.getPosition();
                    centerDescription = `车辆 ${carData.carno || carData.id}`;
                    break;
                }
            }
        }
    }
    
    // 3. 备选方案2：nowCar对象
    if (!center && nowCar && nowCar.lng && nowCar.lat) {
        center = new AMap.LngLat(nowCar.lng, nowCar.lat);
        centerDescription = `车辆 ${nowCar.carno || nowCar.id || ''}`;
    }
    
    // 4. 备选方案3：事件地址标记
    if (!center && sendCarMarker && sendCarMarker.getPosition) {
        center = sendCarMarker.getPosition();
        centerDescription = '事件地址';
    }
    
    // 5. 备选方案4：最新的标注点
    if (!center && mapAnnotations.markers.length > 0) {
        const realMarkers = mapAnnotations.markers.filter(marker => 
            !marker.getContent || !marker.getContent().includes('范围')
        );
        if (realMarkers.length > 0) {
            const lastMarker = realMarkers[realMarkers.length - 1];
            if (lastMarker.getPosition) {
                center = lastMarker.getPosition();
                centerDescription = `标注点 ${lastMarker.markerInfo || '(未命名)'}`;
            }
        }
    }
    
    // 6. 最后备选：当前位置
    if (!center && _currentLoc) {
        center = _currentLoc;
        centerDescription = '当前位置';
    }
    
    if (!center) {
        $.messager.alert('提示', '请先选择一个位置作为圆心：\n1. 点击选中一个车辆\n2. 或者标注一个点\n3. 或者设置事件地址', 'warning');
        return;
    }
    
    // 调试信息
    console.log('范围工具调试信息:', {
        lastSelectedType: lastSelectedType,
        lastSelectedDescription: lastSelectedDescription,
        lastSelectedPosition: lastSelectedPosition,
        centerDescription: centerDescription,
        center: center
    });
    
    // 关闭其他工具
    closeOtherMapTools('range');
    
    const circle = new AMap.Circle({
        center: center,
        radius: radius,
        strokeColor: '#FF6600',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#FF6600',
        fillOpacity: 0.2,
        cursor: 'pointer'
    });
    
    circle.setMap(map);
    mapAnnotations.circles.push(circle);
    
    // 添加标签，包含圆心描述信息
    const marker = new AMap.Marker({
        position: center,
        content: `<div style="background: rgba(255,102,0,0.9); color: #fff; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.3); border: 1px solid #fff;">
            <div style="font-weight: bold; margin-bottom: 2px;">${text}范围</div>
            <div style="font-size: 10px; opacity: 0.9;">圆心: ${centerDescription}</div>
            <span style="position: absolute; top: -2px; right: 2px; font-weight: bold; font-size: 14px;">✖</span>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });
    
    marker.setMap(map);
    mapAnnotations.markers.push(marker);
    
    // 为圆圈添加点击删除事件
    circle.on('click', function() {
        deleteRangeCircle(circle, marker);
    });
    
    // 为标签添加点击删除事件
    marker.on('click', function() {
        deleteRangeCircle(circle, marker);
    });
    
    // 自适应视野
    map.setFitView([circle]);
    
    // 范围工具使用后自动关闭，允许选择新的圆心
    mapToolsState.rangeMode = false;
    updateToolbarState();
    
    // 关闭范围菜单
    $('#range-menu').removeClass('show');
}

/** 删除指定的范围圆圈 */
function deleteRangeCircle(circle, marker) {
    $.messager.confirm('确认删除', '确定要删除这个范围标注吗？', function(r) {
        if (r) {
            // 从地图上移除圆圈
            if (circle) {
                circle.setMap(null);
                // 从数组中移除
                const circleIndex = mapAnnotations.circles.indexOf(circle);
                if (circleIndex > -1) {
                    mapAnnotations.circles.splice(circleIndex, 1);
                }
            }
            
            // 从地图上移除标签
            if (marker) {
                marker.setMap(null);
                // 从数组中移除
                const markerIndex = mapAnnotations.markers.indexOf(marker);
                if (markerIndex > -1) {
                    mapAnnotations.markers.splice(markerIndex, 1);
                }
            }
            
            $.messager.alert('提示', '范围标注已删除', 'info');
        }
    });
}

/** 切换鹰眼地图 */
function toggleOverviewMap() {
    if (mapAnnotations.overView) {
        // 关闭鹰眼地图
        map.removeControl(mapAnnotations.overView);
        mapAnnotations.overView = null;
    } else {
        // 开启鹰眼地图 - 使用普通地图图层
        AMap.plugin(["AMap.HawkEye"], function() {
            mapAnnotations.overView = new AMap.HawkEye({
                autoMove: true,
                showRectangle: true,
                showButton: false,
                mapStyle: 'amap://styles/normal',
                layers: [new AMap.TileLayer()],
                width: '200px',
                height: '150px',
                offset: new AMap.Pixel(20, 20)
            });
            
            map.addControl(mapAnnotations.overView);
            
            // 添加自定义样式到鹰眼地图
            setTimeout(function() {
                addHawkEyeCustomStyle();
            }, 100);
        });
    }
    
    updateToolbarState();
}

/** 刷新地图功能 */


/** 清除所有标注 */
function clearAllAnnotations() {
    // 清除标记
    mapAnnotations.markers.forEach(marker => {
        marker.setMap(null);
    });
    mapAnnotations.markers = [];
    
    // 清除圆圈
    mapAnnotations.circles.forEach(circle => {
        circle.setMap(null);
    });
    mapAnnotations.circles = [];
    
    // 清除测距线
    mapAnnotations.polylines.forEach(polyline => {
        polyline.setMap(null);
    });
    mapAnnotations.polylines = [];
    
    // 关闭比例尺控件
    if (mapAnnotations.scale) {
        map.removeControl(mapAnnotations.scale);
        mapAnnotations.scale = null;
    }
    
    // 关闭路况图层
    if (mapAnnotations.trafficLayer) {
        mapAnnotations.trafficLayer.hide();
        map.remove(mapAnnotations.trafficLayer);
        mapAnnotations.trafficLayer = null;
    }
    
    // 关闭鹰眼地图
    if (mapAnnotations.overView) {
        map.removeControl(mapAnnotations.overView);
        mapAnnotations.overView = null;
    }
    
    // 关闭所有工具
    closeAllMapTools();
    
    $.messager.alert('提示', '已清除所有地图标注和控件', 'info');
}

/** 关闭其他地图工具 */
function closeOtherMapTools(exceptTool) {
    if (exceptTool !== 'measuring' && mapAnnotations.ruler) {
        mapAnnotations.ruler.close();
        mapAnnotations.ruler = null;
        mapToolsState.measuring = false;
    }
    
    if (exceptTool !== 'marking' && mapAnnotations.mouseTool) {
        mapAnnotations.mouseTool.close();
        mapAnnotations.mouseTool = null;
        mapToolsState.marking = false;
    }
    
    if (exceptTool !== 'range') {
        mapToolsState.rangeMode = false;
    }
}

/** 关闭所有地图工具 */
function closeAllMapTools() {
    closeOtherMapTools();
    updateToolbarState();
}

/** 更新工具栏状态 */
function updateToolbarState() {
    // 检查是否使用新的工具栏系统
    if ($('#follow-btn').length > 0) {
        // 新的工具栏系统
        $('#follow-btn').toggleClass('active', isFollow);
        $('#follow-btn').text(isFollow ? '取消' : '跟随');
        
        $('#track-btn').toggleClass('active', showGuiJi);
        $('#track-btn').text(showGuiJi ? '隐藏路径' : '显示路径');
        
        const configOpen = !$('#config-dialog').dialog('options').closed;
        $('#config-btn').toggleClass('active', configOpen);
    } else {
        // 旧的按钮系统 (second_gaode.html)
        if (isFollow) {
            $('#isFollow').hide();
            $('#noFollow').show();
        } else {
            $('#isFollow').show();
            $('#noFollow').hide();
        }
        
        if (showGuiJi) {
            $('#show_guiji').hide();
            $('#hide_guiji').show();
        } else {
            $('#show_guiji').show();
            $('#hide_guiji').hide();
        }
    }
    
    // 其他工具栏状态更新（仅适用于新工具栏系统）
    if ($('#follow-btn').length > 0) {
        // 更新地图控制按钮状态
        $('#scale-btn').toggleClass('active', mapAnnotations.scale !== null);
        $('#traffic-btn').toggleClass('active', mapAnnotations.trafficLayer !== null);
        
        // 更新地图工具按钮
        $('#measure-btn').toggleClass('active', mapToolsState.measuring);
        $('#marker-btn').toggleClass('active', mapToolsState.marking);
        $('#range-btn').toggleClass('active', mapToolsState.rangeMode);
        $('#overview-btn').toggleClass('active', mapAnnotations.overView !== null);
    }
}

/** 处理车辆右键菜单点击事件 */
function handleVehicleContextMenuClick(item) {
    if (!currentContextVehicle) {
        $.messager.alert('提示', '未选择车辆', 'warning');
        return;
    }
    
    switch(item.id) {
        case 'menu-call-station':
            callStation(currentContextVehicle);
            break;
        case 'menu-call-vehicle':
            callVehicle(currentContextVehicle);
            break;
        case 'menu-call-crew':
            showCrewMembersDialog(currentContextVehicle);
            break;
        case 'menu-vehicle-resend':
            resendVehicleTask(currentContextVehicle);
            break;
        case 'menu-vehicle-condition':
            openVehicleConditionDialog(currentContextVehicle);
            break;
        case 'menu-track-replay':
            showTrackReplay(currentContextVehicle);
            break;
        default:
            console.log('未知的菜单项:', item.id);
    }
}

/** 与分站通话 */
function callStation(vehicle) {
    const stationPhone = vehicle.stationContact || vehicle.stationPhone;
    if (!stationPhone || stationPhone.trim() === '') {
        $.messager.alert('提示', '该分站未配置联系电话', 'warning');
        return;
    }
    
    $.messager.confirm('确认通话', 
        `确定要拨打分站电话 ${stationPhone} 吗？`, 
        function(r) {
            if (r) {
                try {
                    // 检查座席信息
                    if (!_pSeat) {
                        $.messager.alert('提示', '未获取到座席信息，无法拨打电话！', 'error');
                        return;
                    }
                    
                    // 兼容C#和B/S模式的拨打电话
                    if (typeof gProxy !== "undefined" && gProxy != null) {
                        gProxy.call(stationPhone);
                        $.messager.alert('提示', '正在拨打分站电话，请稍候...', 'info');
                    } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                        bsProxy.call(stationPhone);
                        $.messager.alert('提示', '正在拨打分站电话，请稍候...', 'info');
                    } else {
                        // 如果没有代理对象，尝试调用callPhone接口
                        if (typeof callPhone !== "undefined" && _pSeat && _pSeat.callOut) {
                            callPhone(_pSeat.callOut, stationPhone,
                                function(res) {
                                    $.messager.alert('提示', '拨打分站电话成功，听到铃声请拿起呼出电话机。', 'info');
                                },
                                function(e, url, errMsg) {
                                    $.messager.alert('错误', '拨打分站电话失败：' + errMsg, 'error');
                                }
                            );
                        } else {
                            // 最后的降级方案：提示用户手动拨打
                            $.messager.alert('提示', 
                                `请手动拨打分站电话：${stationPhone}\n\n系统暂时无法自动拨打电话。`, 
                                'info'
                            );
                        }
                    }
                } catch (e) {
                    console.error('拨打分站电话时出错:', e);
                    $.messager.alert('错误', '拨打分站电话失败: ' + e.message, 'error');
                }
            }
        }
    );
}

/** 与车辆通话（车头平板） */
function callVehicle(vehicle) {
    const vehiclePhone = vehicle.contact || vehicle.carPhone;
    if (!vehiclePhone || vehiclePhone.trim() === '') {
        $.messager.alert('提示', '该车辆未配置联系电话', 'warning');
        return;
    }
    
    $.messager.confirm('确认通话', 
        `确定要拨打车辆电话 ${vehiclePhone} 吗？`, 
        function(r) {
            if (r) {
                try {
                    // 检查座席信息
                    if (!_pSeat) {
                        $.messager.alert('提示', '未获取到座席信息，无法拨打电话！', 'error');
                        return;
                    }
                    
                    // 兼容C#和B/S模式的拨打电话
                    if (typeof gProxy !== "undefined" && gProxy != null) {
                        gProxy.call(vehiclePhone);
                        $.messager.alert('提示', '正在拨打车辆电话，请稍候...', 'info');
                    } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                        bsProxy.call(vehiclePhone);
                        $.messager.alert('提示', '正在拨打车辆电话，请稍候...', 'info');
                    } else {
                        // 如果没有代理对象，尝试调用callPhone接口
                        if (typeof callPhone !== "undefined" && _pSeat && _pSeat.callOut) {
                            callPhone(_pSeat.callOut, vehiclePhone,
                                function(res) {
                                    $.messager.alert('提示', '拨打车辆电话成功，听到铃声请拿起呼出电话机。', 'info');
                                },
                                function(e, url, errMsg) {
                                    $.messager.alert('错误', '拨打车辆电话失败：' + errMsg, 'error');
                                }
                            );
                        } else {
                            // 最后的降级方案：提示用户手动拨打
                            $.messager.alert('提示', 
                                `请手动拨打车辆电话：${vehiclePhone}\n\n系统暂时无法自动拨打电话。`, 
                                'info'
                            );
                        }
                    }
                } catch (e) {
                    console.error('拨打车辆电话时出错:', e);
                    $.messager.alert('错误', '拨打车辆电话失败: ' + e.message, 'error');
                }
            }
        }
    );
}

/** 显示车组人员通话对话框 */
function showCrewMembersDialog(vehicle) {
    // 先获取车组人员信息
    getCrewMembers(vehicle.id, function(crewMembers) {
        if (!crewMembers || crewMembers.length === 0) {
            $.messager.alert('提示', '该车辆暂无车组人员信息', 'warning');
            return;
        }
        
        // 获取职位对应的图标
        function getPositionIcon(position) {
            switch(position) {
                case '医生': return '👨‍⚕️';
                case '护士': return '👩‍⚕️';
                case '司机': return '🚗';
                case '护工': return '🤝';
                default: return '👤';
            }
        }
        
        function getPositionClass(position) {
            switch(position) {
                case '医生': return 'doctor';
                case '护士': return 'nurse';
                case '司机': return 'driver';
                case '护工': return 'worker';
                default: return 'default';
            }
        }
        
        // 构建车组人员列表HTML
        let crewHtml = '<div class="crew-members-container">';
        crewHtml += '<style>';
        crewHtml += `
            .crew-members-container {
                padding: 15px;
                font-family: Arial, sans-serif;
                min-width: 400px;
                max-width: 600px;
            }
            .crew-members-title {
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
                color: #333;
            }
            .crew-members-grid {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                justify-content: center;
                align-items: flex-start;
            }
            .crew-member-card {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 12px;
                text-align: center;
                cursor: pointer;
                background: #f9f9f9;
                transition: all 0.3s ease;
                width: 140px;
                min-height: 160px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }
            .crew-member-card:hover {
                border-color: #007bff;
                background: #f0f8ff;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .crew-member-card.no-phone {
                border-color: #dc3545;
                background: #fff5f5;
                color: #dc3545;
            }
            .crew-member-card.no-phone:hover {
                border-color: #c82333;
                background: #f8d7da;
            }
            .crew-member-icon {
                font-size: 28px;
                margin-bottom: 8px;
            }
            .crew-member-name {
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 4px;
                word-wrap: break-word;
            }
            .crew-member-position {
                font-size: 12px;
                color: #666;
                margin-bottom: 6px;
            }
            .crew-member-phone {
                font-size: 12px;
                color: #333;
                margin-bottom: 4px;
                word-wrap: break-word;
            }
            .crew-member-phone.no-phone-text {
                color: #dc3545;
                font-weight: bold;
            }
            .crew-member-work-number {
                font-size: 10px;
                color: #888;
                margin-bottom: 4px;
            }
            .no-phone-tip {
                font-size: 10px;
                color: #dc3545;
                font-style: italic;
                margin-top: 4px;
            }
            .crew-member-icon.doctor { color: #007bff; }
            .crew-member-icon.nurse { color: #28a745; }
            .crew-member-icon.driver { color: #ffc107; }
            .crew-member-icon.worker { color: #6f42c1; }
            .crew-member-icon.default { color: #6c757d; }
        `;
        crewHtml += '</style>';
        crewHtml += '<div class="crew-members-title">请选择要通话的车组人员</div>';
        crewHtml += '<div class="crew-members-grid">';
        
        crewMembers.forEach(function(member, index) {
            const icon = getPositionIcon(member.position);
            const iconClass = getPositionClass(member.position);
            
            // 根据是否有电话号码设置不同的样式和点击事件
            const hasPhone = member.hasPhone;
            const phoneDisplay = hasPhone ? member.phone : '暂无电话';
            const cardClass = hasPhone ? 'crew-member-card' : 'crew-member-card no-phone';
            const clickHandler = hasPhone ? 
                `callCrewMember('${member.phone}', '${member.name}', '${member.position}')` : 
                `showNoPhoneAlert('${member.name}', '${member.position}')`;
            
            crewHtml += `
                <div class="${cardClass}" onclick="${clickHandler}">
                    <div>
                        <div class="crew-member-icon ${iconClass}">${icon}</div>
                        <div class="crew-member-name">${member.name}</div>
                        <div class="crew-member-position">${member.position}</div>
                        <div class="crew-member-phone ${hasPhone ? '' : 'no-phone-text'}">${phoneDisplay}</div>
                        ${member.workNumber ? `<div class="crew-member-work-number">工号: ${member.workNumber}</div>` : ''}
                    </div>
                    ${!hasPhone ? '<div class="no-phone-tip">点击配置电话</div>' : ''}
                </div>
            `;
        });
        
        crewHtml += '</div></div>';
        
        // 创建并显示对话框
        $('<div id="crew-members-dialog"></div>').html(crewHtml).dialog({
            title: '车组人员通话',
            modal: true,
            resizable: true,
            autoOpen: true,
            draggable: true,
            buttons: [{
                text: '关闭',
                handler: function() {
                    $('#crew-members-dialog').dialog('close');
                    $('#crew-members-dialog').remove();
                }
            }]
        });
    });
}

/** 与车组人员通话 */
window.callCrewMember = function(phone, name, position) {
    if (!phone || phone.trim() === '') {
        $.messager.alert('提示', '电话号码不能为空！', 'warning');
        return;
    }
    
    $.messager.confirm('确认通话', 
        `确定要拨打 ${name}(${position}) 的电话 ${phone} 吗？`, 
        function(r) {
            if (r) {
                try {
                    // 检查座席信息
                    if (!_pSeat) {
                        $.messager.alert('提示', '未获取到座席信息，无法拨打电话！', 'error');
                        return;
                    }
                    
                    // 兼容C#和B/S模式的拨打电话
                    if (typeof gProxy !== "undefined" && gProxy != null) {
                        gProxy.call(phone);
                        $.messager.alert('提示', `正在拨打${name}的电话，请稍候...`, 'info');
                    } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                        bsProxy.call(phone);
                        $.messager.alert('提示', `正在拨打${name}的电话，请稍候...`, 'info');
                    } else {
                        // 如果没有代理对象，尝试调用callPhone接口
                        if (typeof callPhone !== "undefined" && _pSeat && _pSeat.callOut) {
                            callPhone(_pSeat.callOut, phone,
                                function(res) {
                                    $.messager.alert('提示', `拨打${name}的电话成功，听到铃声请拿起呼出电话机。`, 'info');
                                },
                                function(e, url, errMsg) {
                                    $.messager.alert('错误', `拨打${name}的电话失败：` + errMsg, 'error');
                                }
                            );
                        } else {
                            // 最后的降级方案：提示用户手动拨打
                            $.messager.alert('提示', 
                                `请手动拨打${name}的电话：${phone}\n\n系统暂时无法自动拨打电话。`, 
                                'info'
                            );
                        }
                    }
                } catch (e) {
                    console.error(`拨打${name}的电话时出错:`, e);
                    $.messager.alert('错误', `拨打${name}的电话失败: ` + e.message, 'error');
                }
            }
        }
    );
}

/** 处理没有电话号码的车组人员点击事件 */
window.showNoPhoneAlert = function(name, position) {
    $.messager.alert('无法通话', 
        `${name}(${position}) 暂未配置电话号码，请联系管理员配置联系方式。`, 
        'warning'
    );
}

/** 获取车组人员信息 */
function getCrewMembers(vehicleId, callback) {
    console.log('=== 开始获取车组人员信息 ===');
    console.log('vehicleId:', vehicleId);
    console.log('staLists:', staLists);
    
    // 先检查缓存
    if (crewMembersCache[vehicleId]) {
        console.log('从缓存获取车组人员信息:', crewMembersCache[vehicleId]);
        callback(crewMembersCache[vehicleId]);
        return;
    }
    
    // 获取当前选中车辆的信息
    const currentVehicle = staLists ? staLists.find(car => car.id === vehicleId) : null;
    console.log('当前车辆信息:', currentVehicle);
    
    if (!currentVehicle) {
        console.warn('未找到车辆信息，vehicleId:', vehicleId);
        console.log('staLists中的所有车辆ID:', staLists ? staLists.map(car => car.id) : []);
        callback([]);
        return;
    }
    
    // 获取当前车辆的任务ID
    const processId = currentVehicle.currentProcessId;
    console.log('车辆任务ID (currentProcessId):', processId);
    console.log('车辆完整信息:', {
        id: currentVehicle.id,
        carName: currentVehicle.carName,
        plateNum: currentVehicle.plateNum,
        status: currentVehicle.status,
        statusStr: currentVehicle.statusStr,
        currentProcessId: currentVehicle.currentProcessId
    });
    
    if (!processId) {
        console.warn('车辆无当前任务ID，vehicleId:', vehicleId, '车辆状态:', currentVehicle.statusStr);
        // 对于待命状态的车辆，没有任务ID是正常的，但仍然可以尝试获取默认车组信息
        if (currentVehicle.status == 0) {
            console.log('车辆处于待命状态，无任务ID，提供默认车组信息');
            
            // 为待命车辆提供默认车组信息（可以从车辆基础信息或分站信息中获取）
            const defaultCrewMembers = [
                {
                    id: vehicleId + '_default_driver',
                    name: '值班司机',
                    position: '司机',
                    phone: currentVehicle.contact || '',
                    workNumber: '',
                    hasPhone: !!(currentVehicle.contact && currentVehicle.contact.trim() && currentVehicle.contact !== '暂无电话')
                }
            ];
            
            // 如果车辆有联系电话，添加到默认车组
            if (currentVehicle.contact && currentVehicle.contact !== '暂无电话') {
                console.log('车辆有联系电话，添加到默认车组:', currentVehicle.contact);
            } else {
                console.log('车辆无联系电话，建议配置车辆联系方式');
                // 即使没有电话也显示车组信息，提示用户配置
            }
            
            // 缓存数据
            crewMembersCache[vehicleId] = defaultCrewMembers;
            
            console.log('为待命车辆提供默认车组信息:', defaultCrewMembers);
            callback(defaultCrewMembers);
            return;
        } else {
            console.log('车辆非待命状态但无任务ID，可能数据异常');
            callback([]);
            return;
        }
    }
    
    console.log('准备调用 getMobileProcessById 接口，processId:', processId);
    
    // 调用接口获取车组人员信息
    getMobileProcessById(processId, 
        function(processData) {
            console.log('getMobileProcessById 接口调用成功');
            console.log('返回的 processData:', processData);
            
            // 从接口返回的数据中提取车组人员信息
            const crewMembers = [];
            
            // 医生信息 - 始终显示
            console.log('找到医生信息:', processData.dispatchDoctor || '未配置', processData.dispatchDoctorPhone || '无电话');
            crewMembers.push({
                id: processData.dispatchDoctorId || (processId + '_doctor'),
                name: processData.dispatchDoctor || '未配置',
                position: '医生',
                phone: processData.dispatchDoctorPhone || '',
                workNumber: processData.dispatchDoctorNum || '',
                hasPhone: !!(processData.dispatchDoctorPhone && processData.dispatchDoctorPhone.trim())
            });
            
            // 护士信息 - 始终显示
            console.log('找到护士信息:', processData.dispatchNurse || '未配置', processData.dispatchNursePhone || '无电话');
            crewMembers.push({
                id: processData.dispatchNurseId || (processId + '_nurse'),
                name: processData.dispatchNurse || '未配置',
                position: '护士',
                phone: processData.dispatchNursePhone || '',
                workNumber: processData.dispatchNurseNum || '',
                hasPhone: !!(processData.dispatchNursePhone && processData.dispatchNursePhone.trim())
            });
            
            // 司机信息 - 始终显示
            console.log('找到司机信息:', processData.dispatchDriver || '未配置', processData.dispatchDriverPhone || '无电话');
            crewMembers.push({
                id: processData.dispatchDriverId || (processId + '_driver'),
                name: processData.dispatchDriver || '未配置',
                position: '司机',
                phone: processData.dispatchDriverPhone || '',
                workNumber: processData.dispatchDriverNum || '',
                hasPhone: !!(processData.dispatchDriverPhone && processData.dispatchDriverPhone.trim())
            });
            
            // 护工信息 - 始终显示
            console.log('找到护工信息:', processData.dispatchWorker || '未配置', processData.dispatchWorkerPhone || '无电话');
            crewMembers.push({
                id: processData.dispatchWorkerId || (processId + '_worker'),
                name: processData.dispatchWorker || '未配置',
                position: '护工',
                phone: processData.dispatchWorkerPhone || '',
                workNumber: processData.dispatchWorkerNum || '',
                hasPhone: !!(processData.dispatchWorkerPhone && processData.dispatchWorkerPhone.trim())
            });
            
            console.log('提取到的车组人员信息:', crewMembers);
            
            // 缓存数据
            crewMembersCache[vehicleId] = crewMembers;
            
            console.log('获取车组人员信息成功，vehicleId:', vehicleId, 'processId:', processId, 'crewMembers:', crewMembers);
            console.log('=== 车组人员信息获取完成 ===');
            callback(crewMembers);
        },
        function(error, url, errMsg) {
            console.error('=== getMobileProcessById 接口调用失败 ===');
            console.error('错误信息:', errMsg);
            console.error('URL:', url);
            console.error('Error:', error);
            console.error('processId:', processId);
            
            // 接口调用失败时，提供空数组作为降级方案
            callback([]);
        }
    );
}

/** 打开车辆状况修改对话框 */
function openVehicleConditionDialog(vehicle) {
    // 检查车辆状态，只有待派状态（status == 0）的车辆才能修改车况
    if (vehicle.status != 0) {
        $.messager.alert('提示', '当前车辆正在运行中，不能修改车辆状态，只能在待命时才能修改车辆状态。', 'error');
        return;
    }
    
    // 获取车辆当前状况，默认为正常状态
    const currentCondition = vehicle.hCondition || '0';
    
    // 创建车况选择对话框
    const conditionOptions = [
        { value: '0', text: '正常', icon: '✅' },
        //加入了报停功能，暂时隐藏维护和驻场按钮
        // { value: '1', text: '维护中', icon: '🔧' },
        // { value: '2', text: '驻场中', icon: '🏥' },
        { value: '3', text: '未值班', icon: '⏰' }
    ];
    
    let optionsHtml = '';
    conditionOptions.forEach(option => {
        const checked = option.value === currentCondition ? 'checked' : '';
        optionsHtml += `
            <label style="display: block; margin: 10px 0; padding: 8px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; ${checked ? 'background-color: #e6f3ff; border-color: #1890ff;' : ''}">
                <input type="radio" name="vehicle-condition" value="${option.value}" ${checked} style="margin-right: 8px;">
                <span style="font-size: 16px; margin-right: 5px;">${option.icon}</span>
                ${option.text}
            </label>
        `;
    });
    
    const dialogContent = `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
            <div style="margin-bottom: 15px; font-size: 14px; color: #666;">
                <strong>车辆信息：</strong>${vehicle.carName}(${vehicle.plateNum})
            </div>
            <div style="margin-bottom: 15px; font-size: 14px; color: #333;">
                <strong>请选择车辆状况：</strong>
            </div>
            <div id="condition-options" style="margin-bottom: 20px;">
                ${optionsHtml}
            </div>
        </div>
    `;
    
    // 创建并显示对话框
    $('<div id="vehicle-condition-dialog"></div>').html(dialogContent).dialog({
        title: '修改车辆状况',
        modal: true,
        resizable: false,
        autoOpen: true,
        draggable: true,
        width: 400,
        height: 350,
        buttons: [{
            text: '确定',
            handler: function() {
                const selectedCondition = $('input[name="vehicle-condition"]:checked').val();
                if (selectedCondition !== undefined) {
                    updateVehicleCondition(vehicle, selectedCondition);
                    $('#vehicle-condition-dialog').dialog('close');
                    $('#vehicle-condition-dialog').remove();
                } else {
                    $.messager.alert('提示', '请选择车辆状况', 'warning');
                }
            }
        }, {
            text: '取消',
            handler: function() {
                $('#vehicle-condition-dialog').dialog('close');
                $('#vehicle-condition-dialog').remove();
            }
        }]
    });
    
    // 为选项添加点击效果
    $('#condition-options label').click(function() {
        $('#condition-options label').css({
            'background-color': '',
            'border-color': '#ddd'
        });
        $(this).css({
            'background-color': '#e6f3ff',
            'border-color': '#1890ff'
        });
    });
}

/** 更新车辆状况 */
function updateVehicleCondition(vehicle, hCondition) {
    // 如果状况没有变化，直接返回
    if (vehicle.hCondition === hCondition) {
        $.messager.alert('提示', '车辆状况没有变化', 'info');
        return;
    }
    
    const conditionNames = {
        '0': '正常',
        '1': '报停中', 
        // '2': '驻场中',
        '3': '未值班'
    };
    
    $.messager.confirm('确认修改', 
        `确定要将车辆 ${vehicle.carName}(${vehicle.plateNum}) 的状况修改为"${conditionNames[hCondition]}"吗？`, 
        function(r) {
            if (r) {
                try {
                    // 调用修改车况接口
                    const params = {
                        "mobileId": vehicle.id,
                        "hCondition": hCondition
                    };
                    
                    // 显示加载提示
                    $.messager.progress({
                        title: '请稍候',
                        msg: '正在修改车辆状况...'
                    });
                    
                    updateMobileCondition(params,
                        function(res) {
                            $.messager.progress('close');
                            $.messager.alert('成功', '车辆状况修改成功！', 'info');
                            
                            // 更新本地缓存的车辆状况
                            if (staLists) {
                                const vehicleIndex = staLists.findIndex(car => car.id === vehicle.id);
                                if (vehicleIndex > -1) {
                                    staLists[vehicleIndex].hCondition = hCondition;
                                }
                            }
                            
                            // 刷新车辆列表显示
                            if (staLists) {
                                reRangeAndShowStations(staLists);
                            }
                            
                            // 通知主屏幕刷新车辆状态
                            try {
                                if (typeof gProxy !== "undefined" && gProxy != null) {
                                    gProxy.homeScreenListRefreshes("");
                                } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                                    bsProxy.homeScreenListRefreshes("");
                                } else if (window.opener && window.opener.homeScreenListRefreshes) {
                                    window.opener.homeScreenListRefreshes("");
                                }
                            } catch (e) {
                                console.log('通知主屏幕刷新时出错:', e.message);
                            }
                        },
                        function(e, url, errMsg) {
                            $.messager.progress('close');
                            $.messager.alert('错误', '车辆状况修改失败：' + errMsg, 'error');
                        }
                    );
                    
                } catch (e) {
                    $.messager.progress('close');
                    $.messager.alert('错误', '修改车辆状况失败: ' + e.message, 'error');
                }
            }
        }
    );
}

/** 车辆任务补发 */
function resendVehicleTask(vehicle) {
    // 检查车辆状态，只有非待派状态的车辆才能补发
    if (vehicle.status == 0) {
        $.messager.alert('提示', '待派状态的车辆无需补发任务', 'warning');
        return;
    }
    
    $.messager.confirm('补发确认', 
        `确定要对车辆 ${vehicle.carName}(${vehicle.plateNum}) 进行任务补发吗？`, 
        function(r) {
            if (r) {
                try {
                    // 这里调用补发任务的接口
                    // 实际项目中应该调用真实的补发接口
                    console.log('执行车辆任务补发，车辆ID:', vehicle.id);
                    
                    // 模拟补发接口调用
                    // 可以替换为: resendVehicleTaskApi(vehicle.id, successCallback, errorCallback);
                    
                    const params = {
                        vehicleId: vehicle.id,
                        plateNum: vehicle.plateNum,
                        carName: vehicle.carName,
                        stationId: vehicle.stationId,
                        currentProcessId: vehicle.currentProcessId || '',
                        resendTime: new Date().toISOString()
                    };
                    
                    // 模拟API调用
                    setTimeout(function() {
                        $.messager.alert('提示', 
                            `车辆 ${vehicle.carName}(${vehicle.plateNum}) 任务补发成功！`, 
                            'info'
                        );
                        
                        // 补发成功后可以刷新相关列表
                        try {
                            // 兼容C#和B/S模式的刷新主屏幕列表
                            if (typeof gProxy !== "undefined" && gProxy != null) {
                                gProxy.homeScreenListRefreshes("");
                            } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                                bsProxy.homeScreenListRefreshes("");
                            } else if (window.opener && window.opener.homeScreenListRefreshes) {
                                window.opener.homeScreenListRefreshes("");
                            }
                        } catch (e) {
                            console.log('刷新主屏幕列表时出错:', e.message);
                        }
                    }, 500);
                    
                    // 真实接口调用示例（注释掉的代码供参考）
                    /*
                    resendVehicleTaskApi(params, 
                        function(response) {
                            $.messager.alert('提示', '车辆任务补发成功！', 'info');
                            // 刷新相关列表
                            if (typeof gProxy !== "undefined" && gProxy != null) {
                                gProxy.homeScreenListRefreshes("");
                            } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                                bsProxy.homeScreenListRefreshes("");
                            }
                        },
                        function(error) {
                            $.messager.alert('错误', '补发任务失败: ' + error.message, 'error');
                        }
                    );
                    */
                    
                } catch (e) {
                    $.messager.alert('错误', '补发任务失败: ' + e.message, 'error');
                }
            }
        }
    );
}

/** 显示轨迹回放 */
function showTrackReplay(vehicle) {
    // 打开轨迹回放对话框
    openTrackReplayDialog(vehicle);
}

/** 打开轨迹回放对话框 */
function openTrackReplayDialog(vehicle) {
    // 创建对话框HTML
    const dialogId = 'track-replay-dialog';
    const dialogHtml = `
        <div id="${dialogId}" title="车辆轨迹回放 - ${vehicle.carName}">
            <iframe id="track-replay-iframe" 
                    src="trackReplay.html?vehicleId=${vehicle.id}&carName=${encodeURIComponent(vehicle.carName)}&plateNum=${encodeURIComponent(vehicle.plateNum || '')}" 
                    style="width:100%;height:100%;border:none;" 
                    frameborder="0">
            </iframe>
        </div>
    `;
    
    // 如果对话框已存在，先关闭
    if ($('#' + dialogId).length > 0) {
        $('#' + dialogId).dialog('destroy').remove();
    }
    
    // 添加对话框到页面
    $('body').append(dialogHtml);
    
    // 初始化对话框
    $('#' + dialogId).dialog({
        width: 1200,
        height: 800,
        modal: true,
        resizable: true,
        maximizable: true,
        closable: true,
        onClose: function() {
            // 关闭时销毁对话框
            $(this).dialog('destroy').remove();
        }
    });
}

/** 设置车辆信息刷新计时器,定时刷新座席和分站状态 */
function onLoginSuccess(token, tabsShow) {
    _token = token;
    // 是否展示座席端的tab逻辑
    _pSeat = null;
    
    tryGetSeatInfo();
    
    // 添加重试机制获取座席信息
    function tryGetSeatInfo(retryCount = 0) {
        const maxRetries = 10; // 最大重试次数
        const retryDelay = 500; // 重试间隔（毫秒）
        
        try {
            let seatData = null;
    try {
                seatData = gProxy.getSeatInfo();
        } catch (e) {
                seatData = bsProxy.getSeatInfo();
            }
            
            if (seatData && seatData !== 'null' && seatData !== '') {
                _pSeat = evalJson(seatData);
                proceedWithInitialization();
            } else if (retryCount < maxRetries) {
                setTimeout(() => tryGetSeatInfo(retryCount + 1), retryDelay);
            } else {
                console.error('副屏幕获取座席信息失败，已达到最大重试次数，获取到的数据:', seatData);
                // 即使获取座席信息失败，也要初始化基本功能
                proceedWithInitialization();
            }
        } catch (error) {
            console.error('tryGetSeatInfo 执行异常:', error);
            if (retryCount < maxRetries) {
                setTimeout(() => tryGetSeatInfo(retryCount + 1), retryDelay);
            } else {
                console.error('副屏幕获取座席信息异常，已达到最大重试次数:', error);
                proceedWithInitialization();
            }
        }
    }
    
    function proceedWithInitialization() {
        getIsActiveVideo();
        
        // 只有当_pSeat存在且有directorPermission时才添加主任座席tab
        if (_pSeat && _pSeat.directorPermission && _pSeat.directorPermission == '1' && tabsShow && tabsShow.includes('主任座席')) {
        var isTabExists = $('#second-screen-tabs').tabs('exists', '主任座席');
        if (!isTabExists) {
            setTimeout(() => {
                $('#second-screen-tabs').tabs('add', {
                    title:'主任座席',
                    content:`<iframe name="seatsList"  id="seatsListIframe" scrolling="auto" src="seatsList.html" frameborder="0" style="width:100%;height:100%;"></iframe>`,
                    iconCls: 'icon-zhuren',
                });
                $('#second-screen-tabs').tabs('select', 0);
            },1000)
        }
    }
        
    if (!isInitStations) {
        initStations();

        //调用并刷新事件列表的iframe
        var iframe1 = document.getElementById("eventListIframe");
            if (iframe1 && iframe1.contentWindow) {
        var iwindow1 = iframe1.contentWindow;
        //调用iframe的方法
                if (iwindow1.getDgEventListDatas) {
        iwindow1.getDgEventListDatas();
                }
            }

        //获取iframe
        var iframe3 = document.getElementById("shiftRecordIframe");
            if (iframe3 && iframe3.contentWindow) {
        var iwindow3 = iframe3.contentWindow;
        //调用iframe的方法
                if (iwindow3.selectShiftSeatContentListDatas) {
        iwindow3.selectShiftSeatContentListDatas();
                }
            }

        //获取iframe
        var iframe4 = document.getElementById("largerEventIframe");
            if (iframe4 && iframe4.contentWindow) {
        var iwindow4 = iframe4.contentWindow;
        //调用iframe的方法
                if (iwindow4.getLargerEventList) {
        iwindow4.getLargerEventList();
                }
            }

        //已经初始化过地图，不用再次初始化地图，否则地图上面某些信息会丢失
        isInitStations = true;
    }
        

        
    $('#map-search-box').keydown(function (e) {
        if (e.keyCode == 13) {
            manualSearch();
        }
    });

    //根据tabsShow参数显示对应的tab页
    if (tabsShow) {
        let tabArray = tabsShow.split(',').map(item => item.trim());
        // 循环关闭不在tabsShow中的tab页
        
        allTabs.forEach(tab => {
            if (!tabArray.includes(tab)) {
                $('#second-screen-tabs').tabs('close', tab);
            }
        });
        // 选中tabArray中的第一个tab
        if (tabArray.length > 0) {
            $('#second-screen-tabs').tabs('select', tabArray[0]);
            }
        }
    }
    
    // 加载派车模式配置
    loadAssignTaskModeConfig();
}
/** 电话簿搜索按钮 */
function manualSearch() {
    setPlace($('#map-search-box').val());
    _selCarId = null;
}

/** 救治能力重置按钮 */
function cleanRescueSelected() {
    $('#rescue-center-form input[type="checkbox"]').each((index, item) => {
        item.checked = false
    })
    $('.title-toolbar-reset').addClass('hide')
    const selectAllBtnEl = $('#rescue-all-checkbox')
    selectAllBtnEl.prop("checked", false)
    selectAllBtnEl.prop("indeterminate", false)
    getRegionStations((getRegionStationsData) => {
        let result = [];
        _stations = getRegionStationsData.selectMany(function (s) { return s.stations; });
        for (const item of getRegionStationsData) {
            if (item.stations) {
                for (const station of item.stations) {
                    if (station.mobiles) {
                        result = result.concat(station.mobiles);
                    }
                }
            }
        }
        rescueIds = result.map(r => r.id)
        setTimeout(() => sortAndShowStations(_mobilesWithDistance), 0)
    }, () => {}, []);
}

// 工具：动态加载高德脚本
function loadGaodeScript(onLoad) {
    const cfg = getMapSysConfig();                // 取 localStorage 里的 sysConfigMap
    const key = cfg.gaode?.key;
    if (!key) {
        console.error('未配置高德地图 key');
        return;
    }
    const s = document.createElement('script');
    s.type = 'text/javascript';
    s.src = `https://webapi.amap.com/maps?v=2.0&key=${key}`;
    s.onload = () => onLoad && onLoad();
    s.onerror = e => $.messager.alert('错误', '高德地图脚本加载失败');
    document.head.appendChild(s);
}
/** 
 * 初始化相关信息
 * 注意：可以在此函数中根据屏幕ID执行不同的初始化逻辑
 * 例如：
 * var screenId = getScreenId();
 * if (screenId === 1) {
 *     // 第1号屏幕的特殊初始化
 * }
 */
function initStations() {
    // 第一步：动态加载地图脚本
    loadGaodeScript(function () {
        getRegionStations(function (data) {
            // 首先设置分站数据
            _stations = data.selectMany(function (s) { return s.stations; });
            console.log('获取到分站数据，分站数量:', _stations.length);

            // 初始化地图，并在完成后执行回调
            initMap(function () {
                console.log('地图初始化完成，开始绘制分站和车辆');

                // 地图初始化完成后再执行这些操作
                setAutoComplete(); // 初始化搜索功能
                drawStations();//画地图上的站点

                // 延时一秒调用 排序站点
                setTimeout(function () {
                    if (_mobilesWithDistance) {
                        sortAndShowStations(_mobilesWithDistance);
                    } else {
                        console.warn('_mobilesWithDistance 为空，无法显示救护车列表');
                    }
                }, 1000);

                //开始画地图上面的车
                drawMobileIntervalTimeout();
            });
        });
        // 获取救治能力列表
        queryAllDic(['rescue_treatment_capacity'],
            (data) => {
                try {
                    getRegionStations((getRegionStationsData) => {
                        let result = [];
                        for (const item of getRegionStationsData) {
                            if (item.stations) {
                                for (const station of item.stations) {
                                    if (station.mobiles) {
                                        result = result.concat(station.mobiles);
                                    }
                                }
                            }
                        }
                        rescueIds = result.map(r => r.id)
                        //有车辆信息，进行排序
                        setTimeout(() => { if (_mobilesWithDistance) sortAndShowStations(_mobilesWithDistance) }, 0)
                    }, () => { }, rescueCodeList);
                    // [{054: '创'},...]
                    const parentEl = $('#rescue-center-form .checkbox-list')
                    parentEl.empty()
                    // 渲染救治能力列表项
                    const html = data.map(item => {
                        // item.codeName 和 item.codeVale
                        return `
                        <li>
                    <input type="checkbox" id="${item.codeName}" name="rescueCenter" value="${item.codeName}"/>
                    <label for="${item.codeName}">${item.codeVale}</label>
                        </li>
                `;
                    }).join('');

                    parentEl.append(html);
                    // 列表渲染成功后显示全选按钮，移除 .hide
                    $('.rescue-all-select').removeClass('hide')
                    const resetBtnEl = $('.title-toolbar-reset')
                    const selectAllBtnEl = $('#rescue-all-checkbox')
                    // 监听input的变化
                    $('#rescue-center-form input').on('change', (e) => {
                        // 获取所有input
                        const inputEls = Array.from($('#rescue-center-form input[type="checkbox"]'))

                        // 得到所有已选中的id
                        rescueCodeList = inputEls.filter(_ => _.checked).map(_ => _.id)

                        if (rescueCodeList.length === inputEls.length) { // 当全部选中时，将全选按钮的属性设置为checked
                            selectAllBtnEl.prop("checked", true)
                            selectAllBtnEl.prop("indeterminate", false)
                        } else if (rescueCodeList.length === 0 && !e.currentTarget.checked) { // 如果列表为空，则拦截掉本次事件，因为必须选中一项
                            selectAllBtnEl.prop("checked", false)
                            selectAllBtnEl.prop("indeterminate", false)
                            resetBtnEl.addClass('hide')
                        } else { // 中间情况统一设置为半选中状态
                            selectAllBtnEl.prop("checked", false)
                            selectAllBtnEl.prop("indeterminate", true)
                            resetBtnEl.removeClass('hide')
                        }
                        getRegionStations((getRegionStationsData) => {
                            let result = [];
                            for (const item of getRegionStationsData) {
                                if (item.stations) {
                                    for (const station of item.stations) {
                                        if (station.mobiles) {
                                            result = result.concat(station.mobiles);
                                        }
                                    }
                                }
                            }
                            rescueIds = result.map(r => r.id)
                            setTimeout(() => sortAndShowStations(_mobilesWithDistance), 0)
                        }, () => { }, rescueCodeList);
                    })
                    // 监听全选按钮变化
                    selectAllBtnEl.on('change', (e) => {
                        let inputEls = $('#rescue-center-form input[type="checkbox"]')
                        if (e.target.checked) {
                            inputEls.prop("checked", true)
                        } else {
                            // 除了第一个选中，其他全部取消
                            inputEls.each((index, item) => {
                                item.checked = false
                            })
                            resetBtnEl.addClass('hide')
                        }
                        // 获取所有input
                        inputEls = Array.from(inputEls)
                        // 得到所有已选中的id
                        rescueCodeList = inputEls.filter(_ => _.checked).map(_ => _.id)
                        getRegionStations((getRegionStationsData) => {
                            let result = [];
                            for (const item of getRegionStationsData) {
                                if (item.stations) {
                                    for (const station of item.stations) {
                                        if (station.mobiles) {
                                            result = result.concat(station.mobiles);
                                        }
                                    }
                                }
                            }
                            rescueIds = result.map(r => r.id)
                            setTimeout(() => sortAndShowStations(_mobilesWithDistance), 0)
                        }, () => { }, rescueCodeList);
                    })
                } catch (error) {
                    $.messager.alert('异常', '解析 JSON 字符串时发生错误：' + error);
                }
            },
            (e, url, errMsg) => {
                //失败才做处理
                $.messager.alert('异常', '获取rescue_treatment_capacity字典失败，异常信息：' + errMsg);
            });
    })

}
/** 初始化高德地图 */
function initMap(callback) {
    map = new AMap.Map("the-map", {
        enableMapClick: false,
        minZoom: 1,
        maxZoom: 100,
    }); // 创建Map实例
    
    var initCompleted = 0; // 用于跟踪初始化完成的步骤
    var totalSteps = 3;    // 总共需要完成的步骤：城市设置、比例尺、鹰眼地图
    
    function checkInitComplete() {
        initCompleted++;
        if (initCompleted >= totalSteps && callback) {
            callback();
        }
    }
    
    querySysConf('map_city',
        function (data) {
            map.setCity(data); // 设置地图显示的城市 此项是必须设置的
            // //行政区域边界绘制（已注释）
            // AMap.plugin('AMap.DistrictSearch', function () {
            //     // 创建行政区查询对象
            //     var district = new AMap.DistrictSearch({
            //         // 返回行政区边界坐标等具体信息
            //         extensions: 'all',
            //         // 设置查询行政区级别为 区
            //         level: 'city'
            //     })
            //     district.search(data, function(status, result) {
            //         // 获取朝阳区的边界信息
            //         var bounds = result.districtList[0].boundaries
            //         var polygons = []
            //         if (bounds) {
            //             for (var i = 0, l = bounds.length; i < l; i++) {
            //                 //生成行政区划polygon
            //                 var polygon = new AMap.Polygon({
            //                     map: map,
            //                     strokeWeight: 2,
            //                     path: bounds[i],
            //                     fillOpacity: 0.01,
            //                     strokeColor: '#ff0000'
            //                 })
            //                 polygons.push(polygon)
            //             }
            //             // 地图自适应
            //             map.setFitView(polygons) // 以这个区域为中心适配视角
            //         }
            //         checkInitComplete(); // 城市设置完成
            //    })
            // })
            
            // 围栏边界绘制
            drawFenceBoundaries();
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
            checkInitComplete(); // 即使失败也要继续
        });

/** 
 * 绘制围栏边界
 */
function drawFenceBoundaries() {
    try {
        // 获取围栏配置列表
        var fenceList = getFenceListConfig();
        console.log('获取到围栏列表:', fenceList);
        
        if (!fenceList || !Array.isArray(fenceList) || fenceList.length === 0) {
            console.log('没有围栏数据，跳过围栏绘制');
            checkInitComplete(); // 没有围栏也要继续初始化
            return;
        }
        
        var fenceShapes = []; // 存储所有围栏图形，用于地图适配
        
        // 遍历所有围栏配置
        for (var i = 0; i < fenceList.length; i++) {
            var fence = fenceList[i];
            console.log(`开始处理第 ${i + 1} 个围栏:`, {
                名称: fence.fenceName,
                类型: fence.shapeType,
                状态: fence.status,
                原始数据: fence
            });
            
            try {
                // 解析坐标数据（处理字符串或对象格式）
                var coordinateData;
                if (typeof fence.coordinateData === 'string') {
                    coordinateData = JSON.parse(fence.coordinateData);
                } else {
                    coordinateData = fence.coordinateData;
                }
                console.log(`围栏 ${fence.fenceName} 的坐标数据:`, coordinateData);
                
                var fenceColor = fence.fenceColor || '#ff0000'; // 默认红色
                console.log(`围栏 ${fence.fenceName} 使用颜色:`, fenceColor);
                
                if (fence.shapeType === 'polygon') {
                    // 绘制多边形围栏
                    console.log(`准备绘制多边形围栏: ${fence.fenceName}`);
                    drawPolygonFence(fence, coordinateData, fenceColor, fenceShapes);
                } else if (fence.shapeType === 'circle') {
                    // 绘制圆形围栏
                    console.log(`准备绘制圆形围栏: ${fence.fenceName}`);
                    drawCircleFence(fence, coordinateData, fenceColor, fenceShapes);
                } else {
                    console.warn(`围栏 ${fence.fenceName} 的类型 ${fence.shapeType} 不支持`);
                }
            } catch (parseError) {
                console.error(`解析围栏 ${fence.fenceName} 的数据失败:`, {
                    错误: parseError,
                    原始数据: fence.coordinateData
                });
            }
        }
        
        // 如果有围栏图形，以所有围栏为中心适配地图视角
        if (fenceShapes.length > 0) {
            console.log(`成功绘制 ${fenceShapes.length} 个围栏，调整地图视角`, {
                所有围栏图形: fenceShapes
            });
            map.setFitView(fenceShapes);
        } else {
            console.warn('没有成功绘制任何围栏');
        }
        
        checkInitComplete(); // 围栏绘制完成
        
    } catch (error) {
        console.error('绘制围栏边界失败:', error);
        checkInitComplete(); // 即使失败也要继续初始化
    }
}

/**
 * 绘制多边形围栏
 */
function drawPolygonFence(fence, coordinateData, fenceColor, fenceShapes) {
    if (!coordinateData || coordinateData.length < 3) {
        console.warn(`多边形围栏 ${fence.fenceName} 坐标点不足，需要至少3个点`, {
            坐标数据: coordinateData
        });
        return;
    }
    
    console.log(`开始转换多边形围栏 ${fence.fenceName} 的坐标格式`);
    // 转换坐标格式
    var path = coordinateData.map(function(coord) {
        return new AMap.LngLat(coord.lng, coord.lat);
    });
    console.log(`转换后的坐标路径:`, path);
    
    // 创建多边形
    var polygon = new AMap.Polygon({
        map: map,
        strokeWeight: 2,
        path: path,
        fillOpacity: 0, // 设置为0，完全透明
        strokeColor: fenceColor
    });
    
    fenceShapes.push(polygon);
    console.log(`成功绘制多边形围栏: ${fence.fenceName}`, {
        路径点数: path.length,
        边框颜色: fenceColor
    });
}

/**
 * 绘制圆形围栏
 */
function drawCircleFence(fence, coordinateData, fenceColor, fenceShapes) {
    try {
        console.log(`开始处理圆形围栏 ${fence.fenceName} 的数据`, {
            原始数据: coordinateData
        });
        
        // 检查coordinateData的格式
        if (!coordinateData || !coordinateData.center || typeof coordinateData.radius !== 'number') {
            console.warn(`圆形围栏 ${fence.fenceName} 数据格式不正确`, {
                数据: coordinateData
            });
            return;
        }
        
        // 获取中心点和半径
        var centerPoint = coordinateData.center;
        var radius = coordinateData.radius;
        
        console.log(`圆形围栏参数:`, {
            中心点: centerPoint,
            半径: radius,
            颜色: fenceColor
        });
        
        if (!centerPoint.lng || !centerPoint.lat) {
            console.warn(`圆形围栏 ${fence.fenceName} 中心点坐标无效`, {
                中心点数据: centerPoint
            });
            return;
        }
        
        // 创建圆形
        var circle = new AMap.Circle({
            center: new AMap.LngLat(centerPoint.lng, centerPoint.lat),
            radius: radius,
            strokeColor: fenceColor,
            strokeWeight: 2,
            fillOpacity: 0, // 设置为0，完全透明
            strokeStyle: 'solid',
            zIndex: 50,
            map: map  // 直接设置map属性，确保添加到地图上
        });
        
        // 确保圆形被添加到地图上
        if (!circle.getMap()) {
            map.add(circle);
        }
        
        fenceShapes.push(circle);
        console.log(`成功绘制圆形围栏: ${fence.fenceName}`, {
            中心点: centerPoint,
            半径: Math.round(radius),
            边框颜色: fenceColor,
            是否已添加到地图: circle.getMap() ? '是' : '否'
        });
        
        // 调整视野以适应围栏
        map.setFitView([circle], {
            padding: [50, 50, 50, 50]
        });
        
    } catch (error) {
        console.error(`绘制圆形围栏 ${fence.fenceName} 失败:`, {
            错误: error,
            原始数据: coordinateData
        });
    }
}
    //初始化救护车icon
    carIcons = {
        default: new AMap.Icon({ image: "style/img/ambul.png", size: new AMap.Size(48, 48) }),
        yellow: new AMap.Icon({ image: "style/img/ambul_yellow.png", size: new AMap.Size(48, 48) }),
        red: new AMap.Icon({ image: "style/img/ambul_red.png", size: new AMap.Size(48, 48) }),
        blue: new AMap.Icon({ image: "style/img/ambul_blue.png", size: new AMap.Size(48, 48) })
    };
    
    // 默认打开比例尺控件
    AMap.plugin(['AMap.Scale'], function() {
        mapAnnotations.scale = new AMap.Scale({
            position: 'LB'
        });
        map.addControl(mapAnnotations.scale);
        checkInitComplete(); // 比例尺控件加载完成
    });
    
    // 默认打开鹰眼地图
    AMap.plugin(["AMap.HawkEye"], function() {
        mapAnnotations.overView = new AMap.HawkEye({
            autoMove: true,
            showRectangle: true,
            showButton: false,
            mapStyle: 'amap://styles/normal',
            layers: [new AMap.TileLayer()],
            width: '200px',
            height: '150px',
            offset: new AMap.Pixel(20, 20)
        });
        
        map.addControl(mapAnnotations.overView);
        
        // 添加自定义样式到鹰眼地图
        setTimeout(function() {
            addHawkEyeCustomStyle();
            
            // 延时更新工具栏状态，确保控件都已加载完成
            setTimeout(function() {
                updateToolbarState();
                // 清除初始的车辆选中状态
                clearVehicleSelection();
                checkInitComplete(); // 鹰眼地图和工具栏初始化完成
            }, 100);
        }, 100);
    });


}

/**
 * 将地图当前的位置_currentLoc经纬度存储到localStorage，方便其他页面进行调用
 */
function setMapCurrentLocLngLat() {
    if (_currentLoc) {
        //存储到localStorage，方便其他页面进行调用
        window.localStorage.setItem("_MapCurrentLocLngLat", JSON.stringify({"lng": _currentLoc.getLng(), "lat": _currentLoc.getLat()}));
    } else {
        window.localStorage.setItem("_MapCurrentLocLngLat", "");
    }
}

/** 初始化地图搜索功能 */
function setAutoComplete() {
    querySysConf('map_city', // 查询配置的哪个城市
        function (data) {
            AMap.plugin('AMap.AutoComplete', function() {
                ac = new AMap.AutoComplete(    //建立一个自动完成的对象
                {
                    "input": "map-search-box",
                    "location": map,
                    "city": data
                });
                var myValue;
                ac.on("select", function (e) {     //鼠标点击下拉列表后的事件
                    var _value = e.poi;
                    myValue = _value.name;
                    if(_value.location) {
                        _currentLoc = new AMap.LngLat(_value.location.lng,_value.location.lat) // 设置
                        setMapCurrentLocLngLat()
                        setPlace(myValue);
                    } else {
                        $.messager.alert('提示', '未搜索到事件地址经纬度，无法在地图上显示。');
                    }
                });
            })
            //如果删除了搜索文本中的内容，那么取消掉点
            $('#map-search-box').keyup(function (e) {
                var _value = $("#map-search-box").val();
                if (_value == "") {
                    //移除掉派车的点
                    if (sendCarMarker != null) {
                        sendCarMarker.remove()
                        sendCarMarker = null;
                    }
                }
            });
        },
        function (e, url, errMsg) {
            $('#update-det-address').val(eventInfo.address);
            //失败才做处理
            $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
        }
    );
}
/** 地图搜索功能 */
function setPlace(value, type) {
    // 检查地图是否已初始化
    if (!map) {
        console.warn('地图尚未初始化，跳过地点设置');
        return;
    }
    
    if (!value && uploadAddress) {
        sendCarMarker = new AMap.Marker({position:new AMap.LngLat(parseFloat(uploadLng), parseFloat(uploadLat)),zIndex: 999});
        map.add(sendCarMarker); //添加标注
        var text = "位置：" + uploadAddress + "<br/>" + "纬度: " + parseFloat(uploadLng) + ", " + "经度：" + parseFloat(uploadLat);
        //创建信息窗口对象
        var infoWindow = new AMap.InfoWindow({
            content: text, //传入字符串拼接的 DOM 元素
            // anchor: "top-left",
            offset: new AMap.Pixel(0, -30), // 标签偏移量
            autoMove: false  // 禁用自动移动
        });
        infoWindow.open(map, new AMap.LngLat(parseFloat(uploadLng), parseFloat(uploadLat)))
        setTimeout(() => {
            map.setFitView(sendCarMarker)
        }, 500);
    } else {
        AMap.plugin(['AMap.PlaceSearch'], function(){
            var placeSearch = new AMap.PlaceSearch({
                map: map
            });  //构造地点查询类
            placeSearch.search(value);  //关键字查询查询
            setTextMarker(value,type)
        });
    }

}
/**
 * 设置地图地址（有经纬度时使用经纬度，没坐标时使用地址）
 * @param {any} address 地址
 * @param {any} longitude 经度，有可能为空
 * @param {any} latitude 纬度，有可能为空
 */
function setAddress(address, longitude, latitude) {
    $('#map-search-box').val(address)
    // console.log('经纬度',longitude, latitude);
    if(longitude && latitude){
        _currentLoc = new AMap.LngLat(parseFloat(longitude),parseFloat(latitude));
        setMapCurrentLocLngLat()
        setPlace(address, 'first');
    } else {
        setPlace(address, 'first');
    }
    _selCarId = null;
}
/** 将分站图标放到地图上面去 */
function drawStations() {
    console.log('开始绘制分站，分站数量:', _stations.length);
    
    // 清空之前的分站标记
    clearStationMarkers();
    
    //绘制所有分站图标
    for (var i = 0; i < _stations.length; i++) {
        var sta = _stations[i];
        var pt = new AMap.LngLat(sta.stationLongitude, sta.stationLatitude);
        var myIcon = new AMap.Icon({image:"style/img/hospital.png",size: new AMap.Size(24, 24)});
        var marker = new AMap.Marker({position:pt, icon: myIcon, extData: {stationData: sta} });  // 创建标注，存储分站数据
        map.add(marker);// 将标注添加到地图中
        stationMarkers.push(marker); // 将分站标记保存到数组中
        
        marker.setLabel({
            offset: new AMap.Pixel(0, 0), // 标签偏移量
            content: `<span style="color:green;font-size:1.2em">${sta.stationName}</span>`, // 标签文本内容
            direction: 'top', // 标签指向
            style:'color:#008000,border: none,fontSize:12px,height:20px,lineHeight:20px,fontFamily:微软雅黑,background:rgba(0,0,0,0)'
        });
        
        // 添加点击事件显示分站信息
        marker.on('click', function(e) {
            var stationData = e.target.getExtData().stationData;
            showStationInfo(stationData, e.target.getPosition());
        });
        
        // 根据分站显示状态决定是否可见
        if (!mapResourcesState.stationsVisible) {
            marker.hide();
        }
    }
    
    console.log('分站绘制完成，已创建', stationMarkers.length, '个分站标记');
    
    // 绘制完成后更新统计信息
    updateMapResourcesStats();
}
/** 排序站点，算距离等 */
function sortAndShowStations(staListParam) {
    // 统一使用_mobiles作为数据源，确保数据一致性
    if (!staListParam && _mobilesWithDistance) {
        staListParam = _mobilesWithDistance;
    }
    
    // 修复救治能力筛选逻辑：只有在有选中的救治能力时才进行筛选
    staListParam = rescueIds && rescueIds.length > 0 ? staListParam.filter(i => rescueIds.includes(i.id)) : staListParam

    // 检查是否有可用的代理对象（兼容C#和B/S模式）
    var hasValidProxy = false;
    try {
        hasValidProxy = (typeof gProxy !== "undefined" && gProxy != null) || 
                        (typeof bsProxy !== "undefined" && bsProxy != null);
    } catch (e) {
        // 检查代理对象时出错
    }
    
    if (!hasValidProxy || (!staListParam)) {
        return;
    }
    var temp = [].slice.call(staListParam);
    /*for (var i = 0; i < temp.length; i++) {
        var data = temp[i];
        data.distance = MAX_DISTANCE;
        if (_currentLoc != null) {
            //算距离
            caculateRouteDistance(data,
                function (dist, staData) {
                    staData.distance = dist;
                });
        }
    }*/
    staLists = temp
    //重新排序并展示
    reRangeAndShowStations(staLists);
}

/** 站点算距离 */
// function caculateRouteDistance(carData, callback) {
//     var distance = MAX_DISTANCE;
//     if (_currentLoc != null) {
//         // console.log("经纬度计算距离：" + carData.id +";"+ carData.lat + ";" + carData.lng + ";" + _currentLoc.lat + ";" + _currentLoc.lng);
//         distance = getDistance(carData.lat, carData.lng, _currentLoc.lat, _currentLoc.lng);
//     }
//     callback(distance, carData);
// }

/**
 * 渲染右侧站点 排序等逻辑
 * @param {Object} staListParam - 车辆状态列表参数
 */
function reRangeAndShowStations(staListParam) {
    // 检查地图是否已初始化（防止通过storage事件等异步调用时地图未初始化）
    if (!map) {
        console.warn('地图尚未初始化，跳过车辆列表渲染');
        return;
    }
    
    // 统一使用_mobilesWithDistance作为数据源，确保数据一致性
    if (!staListParam && _mobilesWithDistance) {
        staListParam = _mobilesWithDistance;
    }
    const disStation = $('#map-station');
    const scrollContainer = disStation.parent();
    tableHeight = scrollContainer.scrollTop()

    // 应用调度状态和车辆类型筛选
    let filteredData = staListParam;
    
    // 调度状态筛选
    if (dispatchStatusFilter.length > 0) {
        filteredData = filteredData.filter(car => {
            if (dispatchStatusFilter.includes('waiting') && (car.status == "0" || car.status == 0)) {
                // 待派：状态为0（待命）且车况正常（兼容字符串和数字类型）
                return !car.hCondition || car.hCondition === "0";
            }
            if (dispatchStatusFilter.includes('dispatched') && car.status != "0" && car.status != 0) {
                return true; // 调派：状态不为0（已派出）
            }
            return false;
        });
    }
    
    // 车辆类型筛选（仅在选中"待派"且有车辆类型筛选时生效）
    if (dispatchStatusFilter.includes('waiting') && vehicleTypeFilter.length > 0) {
        filteredData = filteredData.filter(car => {
            const carVehicleType = getCarVehicleType(car);
            return vehicleTypeFilter.includes(carVehicleType);
        });
    }

    // 根据stationId分组
    const groupedData = filteredData.reduce((accumulator, current) => {
        const stationId = current.stationId;
        if (!accumulator[stationId]) {
            accumulator[stationId] = [];
        }
        accumulator[stationId].push(current);
        return accumulator;
    }, {})
    // 分组完成后按照每个分组第一项的距离来排序 distance
    const formattedData = Object.keys(groupedData).map(stationId => ({
        stationId,
        stationName: groupedData[stationId][0].stationName,
        data: groupedData[stationId]
    })).sort((a, b) => a.data[0].distance - b.data[0].distance)

    disStation.empty();
    formattedData.forEach(item => {
         // 根据 item.isLogin 判断显示的图片路径
        const imgSrc = item.isLogin == 1  ? './style/img/yiyuan_zaixian_ic.png' : './style/img/yiyuan_lixian_ic.png'; //判断分站是否在线
        
        // 检查是否为全能分站，添加分站类型标识
        const isAllRoundStation = item.data[0].stationAllRound === "1";
        const stationTypeText = isAllRoundStation 
            ? '<span style="color: #00C851; font-weight: bold; margin-left: 5px;">全能</span>' 
            : '<span style="color: #999999; font-weight: bold; margin-left: 5px;">普通</span>';
        
        const itemHtml = `
            <li class="station-group">
              <h3>
                <img src="${imgSrc}" alt="状态图标" style="vertical-align: middle; margin-right: 5px;" />
                ${item.stationName}：${stationTypeText}
              </h3>
              <ul class="station-group-list">
                ${item.data.map(_ => {
                    drawMobile(_);
                    
                    // 获取车辆类型显示名称
                    const carVehicleType = getCarVehicleType(_);
                    const vehicleTypeName = vehicleTypeOptions.find(type => type.id === carVehicleType)?.name || '';
                    const vehicleTypeDisplay = vehicleTypeName ? `<span style="font-size: 12px; color: #666; margin-left: 5px;">[${vehicleTypeName}]</span>` : '';
                    
                    // 统一处理状态显示和操作按钮
                    let statusAndActionContent = '';
                    if (_.hCondition && _.hCondition != "0") {
                        // 车况非正常：显示车况状态文字
                        const conditionText = {
                            '1': '报停中',
                            // '2': '驻场中', 
                            '3': '未值班'
                        }[_.hCondition] || '异常';
                        statusAndActionContent = `<div><span style="color: #1890ff;">${conditionText}</span></div>`;
                    } else if (_.status == "0" || _.status == 0) {
                        // 车况正常且待命状态：显示待命文字和派遣按钮（兼容字符串和数字类型）
                        statusAndActionContent = `
                        <div>${carStatusMap[_.status] ? carStatusMap[_.status].title : '待命'}</div>
                            <button class="givCarBtn" onclick="event.stopPropagation(); givCarWithCheck('${_.stationId}', '${_.id}')">任务派遣</button>
                        `;
                    } else {
                        // 车况正常但非待命状态：只显示运行状态
                        statusAndActionContent = `<div>${carStatusMap[_.status].title}</div>`;
                    }
                    
                    return `
                      <li class="line ${_.id === currentCarId ? 'selected' : ''}" data-id="${_.id}" data-status="${_.status}" data-condition="${_.hCondition || '0'}" style="color: ${carStatusMap[_.status].color};">
                        <div>${_.carName}<span style="font-size: 14px">（${(_.mobileToEventMapDistance ? _.mobileToEventMapDistance : 0).toFixed(1)}km）</span>${vehicleTypeDisplay}</div>
                        ${statusAndActionContent}
                      </li>
                    `
                }).join('')}
              </ul>
            </li>
        `
        disStation.append(itemHtml)
    })
    scrollContainer.scrollTop(tableHeight)

    //绑定事件
    disStation.find('li').click(function (e) {
        const id = $(e.target).closest('[data-id]').data('id');
        
        // 点击车辆时存储车辆对象而不是事件对象
        const selectedCar = _mobilesWithDistance ? _mobilesWithDistance.find(function (m) { return m.id == id; }) : null;
        if (selectedCar) {
            // 统一更新所有选中状态变量
            nowCar = selectedCar;
            currentCarId = id;
            clickCarId = id;
            _selCarId = id;
            
            // 更新车辆列表中的选中状态
            $('#map-station li').removeClass('selected');
            $(e.target).closest('li').addClass('selected');
            
            // 更新范围工具的选中状态
            lastSelectedMarker = null;
            lastSelectedPosition = new AMap.LngLat(selectedCar.lng, selectedCar.lat);
            lastSelectedType = 'vehicle';
            lastSelectedDescription = `车辆 ${selectedCar.carno || selectedCar.id}`;
            
            console.log(`已选中车辆: ${selectedCar.carName} (ID: ${selectedCar.id})`);
            
            // 如果轨迹功能开启，手动选择车辆时检查并提示轨迹状态
            if (showGuiJi) {
                drawVehicleTrack(selectedCar, true);
            }
        }

        if (newEventStatus === "1") {
            // 派车状态下的处理
            edcui_selectLi(e);
            clickCarId = id;
            
            // 获取车辆信息用于地图移动
            const car = _mobilesWithDistance ? _mobilesWithDistance.first(function (m) { return m.id == id; }) : null;
            
            //寻找marker，通过ID
            var startMarker = null;
            for (var i = 0; i < carMarkers.length; i++) {
                if (carMarkers[i].getExtData().id === id) {//找到地图上的车，然后显示label
                    startMarker = carMarkers[i];
                    // 设置车辆显示在最上面
                    carMarkers[i].setTop(true);
                    break;
                }
            }
            
            // 派车状态下也要移动地图到车辆位置
            if (car && car.lng && car.lat) {
                map.setCenter(new AMap.LngLat(car.lng, car.lat));
                if (map.getZoom() < 14) {
                    map.setZoom(14);
                }
                console.log(`派车状态 - 地图已移动到车辆位置: ${car.carName} (${car.lng}, ${car.lat})`);
            }
            
            // 如果有派车目标点，显示出一条路线
            if (sendCarMarker != null && startMarker) {
                AMap.plugin("AMap.Driving", function () {
                    _driving = new AMap.Driving({
                        policy: 0, //驾车路线规划策略，0是速度优先的策略
                    });
                    _driving.search(startMarker._position, sendCarMarker._position, function (status, result) {
                        //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
                        //查询成功时，result 即为对应的驾车导航信息
                        if (result.routes && result.routes.length) {
                            var path = parseRouteToPath(result.routes[0])
                            if (_polyline) _polyline.setMap(null) // 之前有点击其它车辆规划过路线则清除一下，不然地图上一大堆路线
                            _polyline = new AMap.Polyline({
                                path: path,
                                showDir: true, // 显示箭头指示
                                strokeColor: '#67C23A',
                                strokeOpacity: 1,
                                strokeWeight: 5
                            });
                            _polyline.setMap(map);
                            map.setFitView(_polyline)
                        }
                    });
                });
            }
        } else {
            //非派车状态，那么找到地图上面的车，并将地图中心点放到车辆上。
            edcui_selectLi(e);
            clickCarId = id;
            _selCarId = id;
            const car = _mobilesWithDistance ? _mobilesWithDistance.first(function (m) { return m.id == id; }) : null;
            
            // 检查车辆是否存在
            if (!car) {
                console.warn('未找到车辆信息，ID:', id);
                return;
            }
            
            //寻找marker，通过ID
            for (var i = 0; i < carMarkers.length; i++) {
                if (carMarkers[i].getExtData().id === id) {//找到地图上的车，然后显示label
                    carMarkers[i].setTop(true);//设置显示在最上面，防止多图层显示在相同位置时 无法知道时哪个图层
                    _carInfoWindow = carMarkers[i].getExtData().infoWindow
                    _carInfoWindowId = carMarkers[i].getExtData().id
                    _carInfoWindowPosition = new AMap.LngLat(carMarkers[i].getPosition().lng, carMarkers[i].getPosition().lat)
                    _carInfoWindow.open(map, _carInfoWindowPosition)
                    // 移除不必要的列表重新渲染，避免应用筛选条件导致车辆消失
                    // reRangeAndShowStations(staLists) // 重新请求 配置信息
                    break;
                }
            }
            
            // 轨迹显示将由 drawMobile 函数中的逻辑统一处理
            
            // 点击车辆列表时地图移动到车辆位置
            if (car.lng && car.lat) {
                // 设置地图中心点到车辆位置
                map.setCenter(new AMap.LngLat(car.lng, car.lat));
                // 适当调整缩放级别，确保车辆清晰可见
                if (map.getZoom() < 14) {
                    map.setZoom(14);
                }
                console.log(`地图已移动到车辆位置: ${car.carName} (${car.lng}, ${car.lat})`);
            }
        }
    });
    // 注释掉双击派车功能 - 已有任务派遣按钮替代
    /*
    disStation.find('li').dblclick(function (e) {
        edcui_selectLi(e);
        const $li = $(e.target).closest('li');
        const status = $li.data('status');
        const condition = $li.data('condition');
        const id = $li.data('id');
        
        // 只有车况正常（hCondition为0或空）且待命状态（status为0）的车辆才能派遣
        if (status === 0 && (!condition || condition === "0")) {
            $.messager.confirm('',
              '要将此车派给当前事件吗？',
              function (res) {
                  if (res) {
                      // 兼容C#和B/S模式的自动选择站点
                      try {
                          if (typeof gProxy !== "undefined" && gProxy != null) {
                      gProxy.autoSelectStation(id);
                          } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                              bsProxy.autoSelectStation(id);
                          }
                      } catch (e) {
                          // 调用 autoSelectStation 时出错
                      }
                  }
              });
        } else if (condition && condition !== "0") {
            // 车况非正常，提示不能派遣
            const conditionText = {
                '1': '维护中',
                '2': '驻场中', 
                '3': '未值班'
            }[condition] || '异常';
            $.messager.alert('提示', `该车辆当前状态为"${conditionText}"，不能派遣任务。`, 'warning');
        } else if (status !== 0) {
            // 非待命状态，提示不能派遣
            $.messager.alert('提示', '该车辆当前不是待命状态，不能派遣任务。', 'warning');
        }
    });
    */
    
    // 绑定右键菜单事件
    disStation.find('li').on('contextmenu', function (e) {
        e.preventDefault();
        const $li = $(e.target).closest('li');
        const vehicleId = $li.data('id');
        const vehicleStatus = $li.data('status');
        
        // 找到对应的车辆信息
        const vehicle = staLists ? staLists.find(car => car.id === vehicleId) : null;
        if (!vehicle) return;
        
        // 保存当前右键选中的车辆信息
        currentContextVehicle = vehicle;
        
        // 根据车辆状态动态调整菜单项显示
        const menu = $('#vehicle-context-menu');
        
        // 待命状态：显示与分站通话，隐藏补发功能
        if (vehicleStatus == 0) {
            menu.menu('enableItem', '#menu-call-station');
            menu.menu('disableItem', '#menu-call-vehicle');
            menu.menu('disableItem', '#menu-call-crew');
            menu.menu('disableItem', '#menu-vehicle-resend');
        } else {
            // 运行状态：显示与车辆通话、车组人员通话和补发功能
            menu.menu('disableItem', '#menu-call-station');
            menu.menu('enableItem', '#menu-call-vehicle');
            menu.menu('enableItem', '#menu-call-crew');
            menu.menu('enableItem', '#menu-vehicle-resend');
        }


        
        // 根据车辆状态调整修改车况菜单项
        if (vehicleStatus == 0) {
            // 待派状态：可以修改车况
            menu.menu('enableItem', '#menu-vehicle-condition');

            if(vehicle.hCondition == '3' || vehicle.hCondition == '0'){
                menu.menu('enableItem', '#menu-vehicle-condition');
            }else{
                menu.menu('disableItem', '#menu-vehicle-condition');
            }
        } else {
            // 运行状态：不能修改车况
            menu.menu('disableItem', '#menu-vehicle-condition');
        }
        
        // 显示右键菜单
        menu.menu('show', {
            left: e.pageX,
            top: e.pageY
        });
    });
}

/** 带车况检查的任务派遣 - 兼容3种派车模式 */
function givCarWithCheck(stationId, vehicleId) {
    // 先检查车辆状态，优先使用_mobiles数据源，它更可靠
    let vehicle = _mobilesWithDistance ? _mobilesWithDistance.find(car => car.id === vehicleId) : null;
    
    // 如果_mobiles中没找到，再尝试staLists
    if (!vehicle) {
        vehicle = staLists ? staLists.find(car => car.id === vehicleId) : null;
    }
    
    if (!vehicle) {
        $.messager.alert('提示', '未找到车辆信息，无法派遣。请刷新页面后重试。', 'error');
        console.warn('未找到车辆信息:', {
            vehicleId: vehicleId,
            stationId: stationId,
            _mobilesWithDistance: _mobilesWithDistance ? _mobilesWithDistance.map(car => car.id) : null,
            staLists: staLists ? staLists.map(car => car.id) : null
        });
        return;
    }
    
    // 检查车况是否正常
    if (vehicle.hCondition && vehicle.hCondition !== "0") {
        const conditionText = {
            '1': '报停中',
            // '2': '驻场中', 
            '3': '未值班'
        }[vehicle.hCondition] || '异常';
        $.messager.alert('提示', `该车辆当前状态为"${conditionText}"，不能派遣任务。`, 'error');
        return;
    }
    
    // 检查是否为待命状态，兼容字符串和数字类型
    console.log('车辆状态检查:', {
        vehicleId: vehicleId,
        carName: vehicle.carName,
        status: vehicle.status,
        statusType: typeof vehicle.status,
        hCondition: vehicle.hCondition,
        hConditionType: typeof vehicle.hCondition,
        isDispatch: vehicle.isDispatch,
        timestamp: new Date().toLocaleTimeString(),
        dataSource: _mobilesWithDistance ? '来自_mobiles' : '来自staLists',
        assignTaskMode: assignTaskMode
    });
    
    if (vehicle.status != "0" && vehicle.status != 0) {
        console.warn('车辆状态不符合派遣条件:', {
            vehicleId: vehicleId,
            status: vehicle.status,
            expected: '0（待命）'
        });
        $.messager.alert('提示', `该车辆当前不是待命状态（当前状态：${vehicle.status}），不能派遣任务。`, 'error');
        return;
    }
    
    // 状态检查通过，使用新的模式判断逻辑
    console.log(`✓ 车辆 ${vehicle.carName}(ID:${vehicleId}) 状态检查通过，开始执行派车模式判断`);
    sendTaskBtnSecondScreen(stationId, vehicleId);
}

//任务派遣 (事件id eventId) (车辆所属的分站id  stationId)   (toStatus写死   5) (seatId  座席id)  (seatCode  座席号) (seatUserId  座席用户id)  (seatUserName  用户工号)  (seatUser  用户姓名) 
function givCar(stationId) {
    if (!setTaskDispatchData) {
        $.messager.alert('提示', '未选择事件,请先在主屏双击选择事件。');
        return
    }
    
    const params = {}
    params.eventId =  setTaskDispatchData.id
    params.stationId = stationId
    params.toStatus = '5'
    params.seatId = setTaskDispatchData.seatId
    params.seatCode = setTaskDispatchData.seatCode
    params.seatUserId = setTaskDispatchData.seatUserId
    params.seatUserName = setTaskDispatchData.seatUserName
    params.seatUser = setTaskDispatchData.seatUser

    if(!canOperateEvent(setTaskDispatchData.isLock,setTaskDispatchData.lockSeatUserId,setTaskDispatchData.lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (setTaskDispatchData.lockSeatCode || '') + ' (' + (setTaskDispatchData.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    $.messager.confirm('确认', `确定派遣此车执行事件为 "${setTaskDispatchData.majorCall}" 的任务吗？`, function (r) {
        if (r) {
            // 点击确认,参数提交
            addMobileProcess(params,
                function (res) {
                    $.messager.alert('提示', '派车(增派)车辆成功！', 'info');
                    setTaskDispatchData = null
                    
                    // 派遣成功后立即刷新本页面的车辆列表
                    console.log('派遣成功，立即刷新车辆列表');
                    setTimeout(() => {
                        if (_mobilesWithDistance) {
                            sortAndShowStations(_mobilesWithDistance);
                        }
                    }, 500); // 延迟500ms确保后端状态已更新
                    
                    try {
                        // 兼容C#和B/S模式的刷新主屏幕列表
                        if (typeof gProxy !== "undefined" && gProxy != null) {
                            gProxy.homeScreenListRefreshes("");
                        } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                            bsProxy.homeScreenListRefreshes("");
                        } else {
                            window.opener.homeScreenListRefreshes("");
                        }
                    } catch (e) {
                        try {
                            window.opener.homeScreenListRefreshes("");
                        } catch (e2) {
                            // 调用 homeScreenListRefreshes 时出错
                        }
                    }
                },
                function (e, url, errMsg) {
                    $.messager.alert('提示', message, 'error');
                }
            );
        } else {
                // 用户取消了任务派遣
        }
    });
         
}

function parseRouteToPath(route) {
    let path = []
    for (let i = 0, l = route.steps.length; i < l; i++) {
        let step = route.steps[i]
        for (let j = 0, n = step.path.length; j < n; j++) {
            path.push(step.path[j])
        }
    }

    return path
}
/** 强制刷新车辆列表 */
function refreshVehicleList() {
    if (_mobilesWithDistance != null) {
        console.log('强制刷新车辆列表，车辆数量:', _mobilesWithDistance.length);
        sortAndShowStations(_mobilesWithDistance);
    }
}

/** 开始画地图上面的车 */
function drawMobileIntervalTimeout() {
    clearTimeout(window.drawMobileInterval);
    window.drawMobileInterval = null;
    if (_token != "" && _mobilesWithDistance != null) {
        sortAndShowStations(_mobilesWithDistance);
    }
    window.drawMobileInterval = setTimeout(drawMobileIntervalTimeout, 4000)
}
/** 画车辆 */
function drawMobile(car) {
    if (car.lng == 0 && car.lat == 0) return;//没有正确gps数据的车不绘制，后端会直接取医院的坐标绘制
    
    // 检查地图是否已初始化（防止通过storage事件等异步调用时地图未初始化）
    if (!map) {
        console.warn('地图尚未初始化，跳过车辆绘制:', car.id);
        return;
    }
    
    // 检查车辆显示状态
    if (!mapResourcesState.vehiclesVisible) {
        // 如果车辆显示被关闭，不绘制车辆
        return;
    }
    var oldMarker = null;
    //寻找marker，通过ID
    for (var i = 0; i < carMarkers.length; i++) {
        if (carMarkers[i].getExtData().id == car.id) {//找到地图上的车，然后显示label
            oldMarker = carMarkers[i];
            break;
        }
    }
    var marker = null;
    if (oldMarker != null) {
        //直接使用旧的marker
        marker = oldMarker;
        var myIcon = null;
        if (car.hCondition == "0") {
            if (car.status == "0") {
                //未出车超过1公里，那么车辆变成黄色
                var distance = getDistance(car.lat, car.lng, car.stationLatitude, car.stationLongitude);
                if (distance > 1000) {
                    //myIcon = new AMap.Icon({image:"style/img/ambul_yellow.png", size: new AMap.Size(48, 48)});
                    myIcon = carIcons.yellow;
                } else {
                    //待命图标
                    //myIcon = new AMap.Icon({image:"style/img/ambul.png",size:  new AMap.Size(48, 48)});
                    myIcon = carIcons.default;
                }
            } else {
                //非待命图标
                //myIcon = new AMap.Icon({image:"style/img/ambul_red.png",size:  new AMap.Size(48, 48)});
                myIcon = carIcons.red;
            }
        } else {//车辆非正常状态
            //myIcon = new AMap.Icon({image:"style/img/ambul_blue.png", size: new AMap.Size(48, 48)});
            myIcon = carIcons.blue;
        }
        var pt = new AMap.LngLat(car.lng, car.lat);
        marker.setIcon(myIcon);
        marker.setPosition(pt);
        //marker.getExtData().id = car.id;//marker的id；就是救护车的id
    } else {
        //绘制车辆图标
        var pt = new AMap.LngLat(car.lng, car.lat);
        if (car.hCondition == "0") {
            if (car.status == "0") {
                //未出车超过1公里，那么车辆变成黄色
                var distance = getDistance(car.lat, car.lng, car.stationLatitude, car.stationLongitude);
                if (distance > 1000) {
                    //myIcon = new AMap.Icon({image:"style/img/ambul_yellow.png",size: new AMap.Size(48, 48)});
                    myIcon = carIcons.yellow;
                } else {
                    //待命图标
                    //myIcon = new AMap.Icon({image:"style/img/ambul.png",size: new AMap.Size(48, 48)});
                    myIcon = carIcons.default;
                }
            } else {
                //非待命图标
                //myIcon = new AMap.Icon({image:"style/img/ambul_red.png",size:  new AMap.Size(48, 48)});
                myIcon = carIcons.red;
            }
        } else {//车辆非正常状态
            //myIcon = new AMap.Icon({image:"style/img/ambul_blue.png", size: new AMap.Size(48, 48)});
            myIcon = carIcons.blue;
        }
        //创建新的marker。extData：用户自定义属性 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。
        //marker = new AMap.Marker({ id: car.id, position: pt, icon: myIcon });
        marker = new AMap.Marker({ position: pt, icon: myIcon, extData: { id: car.id } });
        carMarkers.push(marker); //将car的marker放到数组中
        // console.log("创建新的marker，车辆id：" + car.id + "，id = " + marker.id + "，extData.id = " + marker.getExtData().id)
        map.add(marker);// 将标注添加到地图中，需要先删除掉之前绘制的车辆图标。不然车辆图标会一致画，导致卡死
    }

    marker.setAngle(car.direct);//角度
    //添加windowInfo
    //车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-接诊完成、5-调度接收、6、已派车
    var carStatus = "";
    if (car.hCondition != "0") {
        switch (car.hCondition) {
            case "1":
                carStatus = "报停中";
                break;
            // case "2":
            //     carStatus = "驻场";
			// 	break;
            case "3":
                carStatus = "未值班";
				break;
        }
    } else {
        carStatus = car.statusStr;
    }
    var fenZhan = `
        <font size='2' color='#0066FF'>分站：</font>
        <font size='2'>${car.stationName}</font>
        <hr style='color:#0066FF'>
        `
    var chePai = `
        <font size='2' color='#0066FF'>车牌：</font>
        <font size='2'>${car.plateNum}(${car.carName})</font>
        <hr style='color:#0066FF'>
        `
    var suDu = `
        <font size='2' color='#0066FF'>速度：</font>
        ${(Math.round(car.speed * 100) / 100)}
        <font size='2'>km/h</font>
        <hr style='color:#0066FF'>
        `
    // debugger;
    // var renWuShu = `
    //     <font size='2' color='#0066FF'>任务数：</font>
    //     <font size='2'>${car.taskCount || 0}</font>
    //     <hr style='color:#0066FF'>
    //     `
    var status = `
        <font size='2' color='#0066FF'>状态：</font>
        <font size='2'>${carStatus}</font>
        <hr style='color:#0066FF'>
        `
    var text = ""
    if (fenzhanChecked) {
        text+=fenZhan
    }
    if (chepaiChecked) {
        text+=chePai
    }
    if (suduChecked) {
        text+=suDu
    }
    if (statusChecked) {
        text+=status
    }
    
    // 添加轨迹信息显示
    if (showGuiJi && carRouteInfo[car.id]) {
        var routeInfo = carRouteInfo[car.id];
        var distanceKm = (routeInfo.distance / 1000).toFixed(1);
        var durationMin = Math.ceil(routeInfo.duration / 60);
        text += `
        <font size='2' color='#52C41A'>目的地：</font>
        <font size='2'>${routeInfo.destinationName}</font>
        <hr style='color:#52C41A'>
        <font size='2' color='#52C41A'>距离：</font>
        <font size='2'>${distanceKm}km</font>
        <hr style='color:#52C41A'>
        <font size='2' color='#52C41A'>预计：</font>
        <font size='2'>${durationMin}分钟</font>
        <hr style='color:#52C41A'>
        `;
    }
    if (isActiveCarVideo){

        text +=`<span><img src='style/img/hasVideo.gif' width="24" onclick="openCarVideo()"/></span>`
    }else{
        text +=`<span><img src='style/img/carVideo.png' /></span>`
    }
    if (isActiveH5Video){

        text += `<span><img src='style/img/hasVideo.gif' width="24" onclick="openPadVideo()" style="margin-left: 5px"/></span>`
    }else{
        text += `<span><img src='style/img/padVideo.png' style="margin-left: 5px"/></span>`
    }

    text += `<span style="float: right">${car.acc == '0'?'熄火':car.acc == '1'?'点火':''}</span>`
    //信息窗口对象
    var infoWindow = null;
    if (marker.getExtData().infoWindow) {
        //复用信息窗口对象
        infoWindow = marker.getExtData().infoWindow;
        infoWindow.setContent(text);//传入字符串拼接的 DOM 元素
    } else {
        //创建信息窗口对象
        infoWindow = new AMap.InfoWindow({
            content: text, //传入字符串拼接的 DOM 元素
            // anchor: "top-left",
            offset: new AMap.Pixel(0, -30), // 标签偏移量
            autoMove: false  // 禁用自动移动 - 解决拖动地图时自动回到车辆位置的问题
        });
    }

    marker.getExtData().infoWindow = infoWindow;
    if(car.id == _carInfoWindowId && _carInfoWindow && _carInfoWindow.getIsOpen()) {
        infoWindow.open(map, pt)
        _carInfoWindow = infoWindow
        _carInfoWindowPosition = pt
    }

    marker.clearEvents('click');
    marker.on("click", function (e) {
        clickCarInfo = car
        infoWindow.open(map, pt)
        _carInfoWindow = infoWindow
        _carInfoWindowPosition = pt
        _carInfoWindowId = car.id;
        
        // 统一更新所有选中状态变量
        nowCar = car;
        currentCarId = car.id;
        clickCarId = car.id;
        _selCarId = car.id;
        
        // 更新车辆列表中的选中状态
        $('#map-station li').removeClass('selected');
        $(`#map-station li[data-id="${car.id}"]`).addClass('selected');
        
        // 更新范围工具的选中状态
        lastSelectedMarker = null;
        lastSelectedPosition = pt;
        lastSelectedType = 'vehicle';
        lastSelectedDescription = `车辆 ${car.carno || car.id}`;
        
        // 如果轨迹功能开启，手动选择车辆时检查并提示轨迹状态
        if (showGuiJi) {
            drawVehicleTrack(car, true);
        }
        
        console.log(`已选中车辆: ${car.carName} (ID: ${car.id})`);
    })
    // 车辆跟随功能：当开启跟随模式时，地图自动跟随选中车辆移动
    if(isFollow && nowCar) {
        // 检查是否是当前跟随的车辆
        if(car.id == nowCar.id || car.id == currentCarId || car.id == clickCarId || car.id == _selCarId) {
            // 车辆位置发生变化时移动地图，降低距离阈值使跟随更灵敏
            if (!_lastCenterPosition || 
                getDistance(_lastCenterPosition.lat, _lastCenterPosition.lng, car.lat, car.lng) > 50) {
                // 立即移动地图到车辆位置，移除延迟
                map.setCenter(new AMap.LngLat(car.lng, car.lat));
                _lastCenterPosition = {lng: car.lng, lat: car.lng};
                console.log(`跟随模式 - 地图已移动到车辆位置: ${car.carName} (${car.lng}, ${car.lat})`);
            }
        }
    }

    // 判断车辆是否展示轨迹路线（只对选中的车辆显示）
    if(showGuiJi && (car.id == nowCar?.id || car.id == currentCarId || car.id == clickCarId || car.id == _selCarId)) {
        // 检查是否切换了车辆，如果切换了就绘制轨迹
        if (window._currentTrackVehicleId !== car.id) {
            drawVehicleTrack(car);
        }
    }

}





/** 绘制车辆轨迹路线 */
function drawVehicleTrack(car, isManualSelection = false) {
    console.log(`开始绘制车辆 ${car.carName} (ID: ${car.id}) 的轨迹`);
    console.log(`车辆状态: ${car.status}, 位置: (${car.lng}, ${car.lat})`);
    
    // 如果当前车辆ID已经是正在绘制的车辆ID，直接返回，避免重复绘制
    if (window._currentTrackVehicleId === car.id) {
        console.log(`车辆 ${car.carName} 的轨迹正在绘制中，跳过重复调用`);
        return;
    }
    
    // 取消之前的路径规划请求
    if (window._currentDriving) {
        try {
            window._currentDriving.clear();
        } catch (e) {
            console.warn('清除之前的路径规划对象失败:', e);
        }
        window._currentDriving = null;
    }
    
    // 第一步：无论如何都要清除所有现有轨迹，确保地图干净
    console.log('开始清除所有现有轨迹...');
    clearVehicleRoutes();
    
    // 记录当前显示轨迹的车辆ID
    window._currentTrackVehicleId = car.id;
    
    // 确定起点和终点
    var startPoint, endPoint, destinationName;
    var currentPosition = new AMap.LngLat(car.lng, car.lat);
    
    // 根据车辆状态确定目的地
    if (car.status == 0) {
        // 待命状态：显示到当前任务地点的轨迹
        if (setTaskDispatchData && setTaskDispatchData.lng && setTaskDispatchData.lat) {
            startPoint = currentPosition;
            endPoint = new AMap.LngLat(parseFloat(setTaskDispatchData.lng), parseFloat(setTaskDispatchData.lat));
            destinationName = `任务地点 (${setTaskDispatchData.majorCall || '未知任务'})`;
            console.log(`待派车辆 ${car.carName} - 目的地: ${destinationName}, 坐标: (${setTaskDispatchData.lng}, ${setTaskDispatchData.lat})`);
        } else {
            // 没有任务时不显示轨迹
            console.warn(`待派车辆 ${car.carName} 没有选中任务，无法显示轨迹`);
            console.warn(`setTaskDispatchData:`, setTaskDispatchData);
            
            // 只在手动选择时给出提示
            if (isManualSelection) {
                $.messager.alert('提示', 
                    `车辆 ${car.carName} 处于待派状态，但未选择任务！<br><br>` +
                    '请在主屏幕中双击选择一个事件任务。', 
                    'warning'
                );
            }
            
            return;
        }
    } else if (car.status == 1 || car.status == 5 || car.status == 6 || car.status == 7) {
        // 执行任务状态：显示到任务目的地的轨迹
        if (car.destinationLng && car.destinationLag) {
            startPoint = currentPosition;
            endPoint = new AMap.LngLat(Number(car.destinationLng), Number(car.destinationLag));
            destinationName = '任务目的地';
            console.log(`执行任务车辆 ${car.carName} - 目的地: ${destinationName}, 坐标: (${car.destinationLng}, ${car.destinationLag})`);
        } else {
            console.warn(`执行任务车辆 ${car.carName} 没有目的地坐标: destinationLng=${car.destinationLng}, destinationLag=${car.destinationLag}`);
            
            // 只在手动选择时给出提示
            if (isManualSelection) {
                $.messager.alert('提示', 
                    `车辆 ${car.carName} 正在执行任务，但没有目的地坐标！<br><br>` +
                    '无法绘制轨迹路径。', 
                    'warning'
                );
            }
            

            return;
        }
    } else if (car.status == 2 || car.status == 3 || car.status == 4 || car.status == 8) {
        // 返回分站状态：显示到分站的轨迹
        if (car.stationLongitude && car.stationLatitude) {
            startPoint = currentPosition;
            endPoint = new AMap.LngLat(Number(car.stationLongitude), Number(car.stationLatitude));
            destinationName = '分站';
            console.log(`返回分站车辆 ${car.carName} - 目的地: ${destinationName}, 坐标: (${car.stationLongitude}, ${car.stationLatitude})`);
        } else {
            console.warn(`返回分站车辆 ${car.carName} 没有分站坐标: stationLongitude=${car.stationLongitude}, stationLatitude=${car.stationLatitude}`);
            
            // 只在手动选择时给出提示
            if (isManualSelection) {
                $.messager.alert('提示', 
                    `车辆 ${car.carName} 需要返回分站，但没有分站坐标！<br><br>` +
                    '无法绘制轨迹路径。', 
                    'warning'
                );
            }
            

            return;
        }
    } else {
        // 其他状态不显示轨迹，但确保轨迹状态已清除
        console.warn(`车辆 ${car.carName} 状态 ${car.status} 不支持轨迹显示`);
        
        // 只在手动选择时给出提示
        if (isManualSelection) {
            const statusText = {
                '9': '离线',
                '10': '故障'
            }[car.status] || `状态${car.status}`;
            
            $.messager.alert('提示', 
                `车辆 ${car.carName} 当前处于"${statusText}"状态！<br><br>` +
                '该状态下不支持轨迹显示。', 
                'info'
            );
        }
        

        return;
    }
    
    console.log(`准备进行路径规划: 起点(${startPoint.lng}, ${startPoint.lat}) -> 终点(${endPoint.lng}, ${endPoint.lat})`);
    
    // 使用高德地图路径规划API
                AMap.plugin("AMap.Driving", function () {
        var driving = new AMap.Driving({
            policy: 0, // 驾车路线规划策略，0是速度优先
            showTraffic: false,
            hideMarkers: true // 隐藏默认的起终点标记
        });
        
        // 保存当前的Driving对象引用，以便后续取消
        window._currentDriving = driving;
        
        driving.search(startPoint, endPoint, function (status, result) {
            console.log(`路径规划回调: status=${status}, result=`, result);
            
            // 检查当前车辆是否还是选中的车辆，防止异步回调混乱
            if (window._currentTrackVehicleId !== car.id) {
                console.log(`路径规划回调被忽略，当前选中车辆已变更: ${window._currentTrackVehicleId} != ${car.id}`);
                return;
            }
            
            if (status === 'complete' && result.routes && result.routes.length > 0) {
                var route = result.routes[0];
                var path = parseRouteToPath(route);
                
                // 绘制轨迹路线（绿色）
                carTrackLines[car.id] = new AMap.Polyline({
                                path: path,
                                showDir: true, // 显示箭头指示
                    strokeColor: '#52C41A', // 绿色
                    strokeOpacity: 0.8,
                    strokeWeight: 4
                });
                carTrackLines[car.id].setMap(map);
                
                // 计算距离和时间
                var distance = route.distance; // 距离（米）
                var duration = route.time; // 时间（秒）
                
                // 存储路线信息
                carRouteInfo[car.id] = {
                    distance: distance,
                    duration: duration,
                    destinationName: destinationName
                };
                
                // 创建目的地标记
                            var endIcon = new AMap.Icon({
                                size: new AMap.Size(25, 34),
                                image: "style/img/dir-marker.png",
                                imageSize: new AMap.Size(135, 40),
                                imageOffset: new AMap.Pixel(-95, -3)
                            });
                
                carDestinationMarkers[car.id] = new AMap.Marker({
                    position: endPoint,
                                zIndex: 999,
                                icon: endIcon,
                                offset: new AMap.Pixel(-13, -30)
                            });
                
                // 为目的地标记添加标签显示距离和时间信息
                var distanceKm = (distance / 1000).toFixed(1);
                var durationMin = Math.ceil(duration / 60);
                var labelContent = `<div style="background: rgba(82,196,26,0.9); color: #fff; padding: 6px 10px; border-radius: 4px; font-size: 12px; font-weight: bold; border: 1px solid #fff; box-shadow: 0 2px 6px rgba(0,0,0,0.3); white-space: nowrap;">
                    <div>${destinationName}</div>
                    <div style="font-size: 10px; margin-top: 2px;">距离: ${distanceKm}km</div>
                    <div style="font-size: 10px;">预计: ${durationMin}分钟</div>
                </div>`;
                
                carDestinationMarkers[car.id].setLabel({
                    content: labelContent,
                    offset: new AMap.Pixel(0, -40)
                });
                
                carDestinationMarkers[car.id].setMap(map);
                
                // 如果该车辆的信息窗口正在打开，更新信息窗口内容
                if (car.id == _carInfoWindowId && _carInfoWindow && _carInfoWindow.getIsOpen()) {
                    // 重新构建信息窗口内容
                    updateCarInfoWindow(car);
                }
                

                
                console.log(`车辆 ${car.carName} 轨迹绘制完成：距离 ${distanceKm}km，预计 ${durationMin}分钟到达${destinationName}`);
            } else {
                console.warn(`车辆 ${car.carName} 路径规划失败:`, status, result);
                        }
                    });
                });
            }

/** 更新车辆信息窗口内容 */
function updateCarInfoWindow(car) {
    // 重新构建车辆状态文本
    var carStatus = "";
    if (car.hCondition != "0") {
        switch (car.hCondition) {
            case "1":
                carStatus = "报停中";
                break;
            // case "2":
            //     carStatus = "驻场";
            //     break;
            case "3":
                carStatus = "未值班";
                break;
        }
    } else {
        carStatus = car.statusStr;
    }
    
    var fenZhan = `
        <font size='2' color='#0066FF'>分站：</font>
        <font size='2'>${car.stationName}</font>
        <hr style='color:#0066FF'>
        `
    var chePai = `
        <font size='2' color='#0066FF'>车牌：</font>
        <font size='2'>${car.plateNum}(${car.carName})</font>
        <hr style='color:#0066FF'>
        `
    var suDu = `
        <font size='2' color='#0066FF'>速度：</font>
        ${(Math.round(car.speed * 100) / 100)}
        <font size='2'>km/h</font>
        <hr style='color:#0066FF'>
        `
    var status = `
        <font size='2' color='#0066FF'>状态：</font>
        <font size='2'>${carStatus}</font>
        <hr style='color:#0066FF'>
        `
    
    var text = ""
    if (fenzhanChecked) {
        text+=fenZhan
    }
    if (chepaiChecked) {
        text+=chePai
    }
    if (suduChecked) {
        text+=suDu
    }
    if (statusChecked) {
        text+=status
    }
    
    // 添加轨迹信息显示
    if (showGuiJi && carRouteInfo[car.id]) {
        var routeInfo = carRouteInfo[car.id];
        var distanceKm = (routeInfo.distance / 1000).toFixed(1);
        var durationMin = Math.ceil(routeInfo.duration / 60);
        text += `
        <font size='2' color='#52C41A'>目的地：</font>
        <font size='2'>${routeInfo.destinationName}</font>
        <hr style='color:#52C41A'>
        <font size='2' color='#52C41A'>距离：</font>
        <font size='2'>${distanceKm}km</font>
        <hr style='color:#52C41A'>
        <font size='2' color='#52C41A'>预计：</font>
        <font size='2'>${durationMin}分钟</font>
        <hr style='color:#52C41A'>
        `;
    }
    
    if (isActiveCarVideo){
        text +=`<span><img src='style/img/hasVideo.gif' width="24" onclick="openCarVideo()"/></span>`
    } else {
        text +=`<span><img src='style/img/carVideo.png' /></span>`
    }
    if (isActiveH5Video){
        text += `<span><img src='style/img/hasVideo.gif' width="24" onclick="openPadVideo()" style="margin-left: 5px"/></span>`
    } else {
        text += `<span><img src='style/img/padVideo.png' style="margin-left: 5px"/></span>`
    }

    text += `<span style="float: right">${car.acc == '0'?'熄火':car.acc == '1'?'点火':''}</span>`
    
    // 更新信息窗口内容
    if (_carInfoWindow) {
        _carInfoWindow.setContent(text);
    }
}

/** 设置事故目的地渲染等逻辑 */
function setTextMarker(value,type){
    // 检查地图是否已初始化
    if (!map) {
        console.warn('地图尚未初始化，跳过文本标记设置');
        return;
    }
    
    sortAndShowStations(_mobilesWithDistance); // 搜索时 重新排列下站点
    //自动选中
    if (_selCarId != null) { //存放选中的派车地点的坐标不为空时
        $('#map-station li[data=' + _selCarId + ']').mouseup();
    } else {
        if (_currentLoc != null) {
            map.setCenter(_currentLoc);
        }
    }
    if (_currentLoc != null) {
        //添加派车地点
        if (sendCarMarker != null) {
            // 不做处理
        } else {
            //添加派车地点
            sendCarMarker = new AMap.Marker({position:new AMap.LngLat(_currentLoc.lng, _currentLoc.lat),zIndex: 999});
            //绘制图标
            map.add(sendCarMarker); //添加标注
        }
        var text = "位置：" + value + "<br/>" +
            "纬度: " + _currentLoc.lat + ", " + "经度：" + _currentLoc.lng;
        //创建信息窗口对象
        var infoWindow = new AMap.InfoWindow({
            content: text, //传入字符串拼接的 DOM 元素
            // anchor: "top-left",
            offset: new AMap.Pixel(0, -30), // 标签偏移量
            autoMove: false  // 禁用自动移动
        });
        infoWindow.open(map, new AMap.LngLat(_currentLoc.lng, _currentLoc.lat))
        // setTimeout(function () {
        //     map.setZoom(17);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
        // }, 500);  //2秒后放大
        //移动派车地点
        sendCarMarker.setPosition(new AMap.LngLat(_currentLoc.lng, _currentLoc.lat));
        sendCarMarker.getExtData().infoWindow = infoWindow
        
        // 更新范围工具的选中状态
        lastSelectedMarker = null;
        lastSelectedPosition = new AMap.LngLat(_currentLoc.lng, _currentLoc.lat);
        lastSelectedType = 'event';
        lastSelectedDescription = `事件地址 ${value}`;
        
        //跳转到那个中心点， 视口自动适配：必须延时器
        setTimeout(() => {
            map.setFitView(sendCarMarker)
        }, 500);
        //如果创建窗口打开才发送给主屏幕（这样写会有一个问题，就是当打开了多个创建事件的窗口后会所有窗口都被覆盖掉。弃用）
        // try {
        //     // 兼容C#和B/S模式的回传地图选择信息
        //     if (typeof gProxy !== "undefined" && gProxy != null) {
        //     if (!gProxy.getIsCreateNewEventWinIsOpen()) {
        //         gProxy.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
        //         }
        //     } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
        //         if (!bsProxy.getIsCreateNewEventWinIsOpen()) {
        //             bsProxy.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
        //         }
        //     } else {
        //         //调用主窗口的函数，判断是否打开了新建页面
        //         if (!window.opener.isCreateNewEventWinIsOpen()) {
        //             window.opener.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
        //         }
        //     }
        // } catch (e) {
        //     try {
        //     //调用主窗口的函数，判断是否打开了新建页面
        //     if (!window.opener.isCreateNewEventWinIsOpen()) {
        //         window.opener.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
        //     }
        //     } catch (e2) {
        //         console.log('回传地图选择信息时出错:', e2);
        //     }
        // }

        saveMapSelectedAddress(value, _currentLoc.lng, _currentLoc.lat);
        if(type == 'first') { // 页面打开第一次传值才保存否则后续会被覆盖
            uploadAddress =  value
            uploadLng = _currentLoc.lng
            uploadLat = _currentLoc.lat
        }
        //marker开启拖拽功能。
        sendCarMarker.setDraggable(true);
        //监听标注的dragend事件来捕获拖拽后标注的最新位置
        var gc
        AMap.plugin('AMap.Geocoder', function() {
            gc = new AMap.Geocoder();//地址解析类
        })
        sendCarMarker.on("dragend", function (e) {
            if (sendCarMarker != null) {
                gc.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, rs) {
                    if (status === 'complete' && rs.info === 'OK') {
                        // rs中对应详细地理坐标信息
                        _currentLoc = e.lnglat;
                        setMapCurrentLocLngLat()
                        showLocationInfo(e.lnglat, rs.regeocode);
                        saveMapSelectedAddress(rs.regeocode.formattedAddress, e.lnglat.lng, e.lnglat.lat);
                    } else {
                        // 地址解析失败
                    }

                });
            }
        });
    }
}

/**
 * 保存当前地图选择的地址信息到localStorage
 * @param {string} address - 详细地址
 * @param {number} lng - 经度 
 * @param {number} lat - 纬度
 */
function saveMapSelectedAddress(address, lng, lat) {
    try {
        // 构建地址信息对象
        const addressInfo = {
            address: address, // 详细地址
            longitude: lng,   // 经度
            latitude: lat,    // 纬度
            timestamp: new Date().getTime() // 保存时间戳
        };

        // 转换为JSON字符串并保存到localStorage
        localStorage.setItem('mapSelectedEventAddress', JSON.stringify(addressInfo));
        
        console.log('地图选择地址信息已保存:', addressInfo);
    } catch (error) {
        console.error('保存地图选择地址信息时出错:', error);
    }
}

/** 显示地址信息窗口 */
function showLocationInfo(pt, rs) {
    // 检查地图是否已初始化
    if (!map) {
        console.warn('地图尚未初始化，跳过地址信息显示');
        return;
    }
    
    sortAndShowStations(_mobilesWithDistance);
    var addComp = rs.addressComponent;
    var addressStr = rs.formattedAddress.replace(addComp.province, '').replace(addComp.city, '');
    // var text = "当前位置：" + addComp.province + ", " + addComp.city + ", " + addComp.district + ", " + addComp.street + ", " + addComp.streetNumber + "<br/>" +"纬度: " + pt.lat + ", " + "经度：" + pt.lng;
    var text = "当前位置：" + addressStr + "<br/>" +"纬度: " + pt.lat + ", " + "经度：" + pt.lng;
    //创建信息窗口对象
    var infoWindow = new AMap.InfoWindow({
        content: text, //传入字符串拼接的 DOM 元素
        // anchor: "top-left",
        offset: new AMap.Pixel(0, -30), // 标签偏移量
        autoMove: false  // 禁用自动移动
    });
    infoWindow.open(map, new AMap.LngLat(pt.lng, pt.lat))
    // $('#map-search-box').val(addComp.district + addComp.street + addComp.streetNumber)
    $('#map-search-box').val(addressStr)
    // 保存当前地图事件定位信息到localStorage
    try {
        const locationInfo = {
            address: addressStr, // 详细地址
            longitude: pt.lng, // 经度
            latitude: pt.lat, // 纬度
            district: addComp.district, // 区域
            street: addComp.street, // 街道
            streetNumber: addComp.streetNumber, // 门牌号
            timestamp: new Date().getTime() // 保存时间戳
        };

        // 转换为JSON字符串并保存
        localStorage.setItem('currentMapEventLocation', JSON.stringify(locationInfo));
        
        console.log('当前地图事件定位信息已保存:', locationInfo);
    } catch (error) {
        console.error('保存当前地图事件定位信息时出错:', error); 
    }
    //将地图屏幕的地址，经纬度等信息发送回给派车屏幕
    // try {
    //     // 兼容C#和B/S模式的回传地图选择信息
    //     if (typeof gProxy !== "undefined" && gProxy != null) {
    //     if (!gProxy.getIsCreateNewEventWinIsOpen()) {
    //         gProxy.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    //         }
    //     } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
    //         if (!bsProxy.getIsCreateNewEventWinIsOpen()) {
    //             bsProxy.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    //         }
    //     } else {
    //         //调用主窗口的函数，判断是否打开了新建页面
    //         if (!window.opener.isCreateNewEventWinIsOpen()) {
    //             window.opener.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    //         }
    //     }
    // } catch (e) {
    //     try {
    //     //调用主窗口的函数，判断是否打开了新建页面
    //     if (!window.opener.isCreateNewEventWinIsOpen()) {
    //         window.opener.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    //     }
    //     } catch (e2) {
    //         // 回传地图选择信息时出错
    //     }
    // }

}

/**
 * c#主动调用功能通过事件ID，显示事件在地图上面的位置
 * @param {any} eventId
 */
function setPlaceBylonAndLat(eventId) {
    // 检查地图是否已初始化
    if (!map) {
        console.warn('地图尚未初始化，跳过事件位置设置');
        return;
    }
    
    getEventById(eventId,        
        function (res) {
            //成功不做任何处理
            if (!(res.lat != null && res.lng != null)) {
                $.messager.alert('提示', '事件无经纬度，无法在地图上显示。');
                if (sendCarMarker != null) {
                    // console.log('c#主动调用功能通过事件ID，显示事件在地图上面的位置')
                    sendCarMarker.remove()
                    sendCarMarker = null;
                }
                return null;
            }

            _currentLoc = new AMap.LngLat( parseFloat(res.lng),parseFloat(res.lat));
            setMapCurrentLocLngLat()

            //添加派车地点
            if (sendCarMarker != null) {

            } else {
                //添加派车地点
                // sendCarMarker = new AMap.Marker(_currentLoc);
                //绘制图标
                // map.add([sendCarMarker]); //添加标注


                // 以 icon URL 的形式创建一个途经点
                 sendCarMarker = new AMap.Marker({
                    // position: new AMap.LngLat(parseFloat(res.lng),parseFloat(res.lat)),
                    position: _currentLoc,
                    // 将一张图片的地址设置为 icon
                    icon: 'style/img/gisTask.png',
                    // 设置了 icon 以后，设置 icon 的偏移量，以 icon 的 [center bottom] 为原点
                    offset: new AMap.Pixel(-14, -15)
                });
                map.add(sendCarMarker); //添加标注
            }
            var text = "事件: " + res.majorCall.replace("_", " ") + "<br/>" +
                "位置：" + res.address + "<br/>" +
                "纬度: " + res.lat + ", " + "经度：" + res.lng;
            //创建信息窗口对象
            var infoWindow = new AMap.InfoWindow({
                content: text, //传入字符串拼接的 DOM 元素
                // anchor: "top-left",
                // offset: new AMap.Pixel(0, -30), // 标签偏移量
                offset: new AMap.Pixel(0, -16), // 标签偏移量
                autoMove: false  // 禁用自动移动
            });
            infoWindow.open(map, new AMap.LngLat(_currentLoc.lng, _currentLoc.lat))
            //移动派车地点
            sendCarMarker.setPosition(_currentLoc);
            sendCarMarker.getExtData().infoWindow = infoWindow
            
            // 更新范围工具的选中状态
            lastSelectedMarker = null;
            lastSelectedPosition = _currentLoc;
            lastSelectedType = 'event';
            lastSelectedDescription = `事件 ${res.majorCall.replace("_", " ")}`;
            
            //跳转到那个中心点
            map.setCenter(_currentLoc);
            //计算距离
            sortAndShowStations(_mobilesWithDistance);
            //事件信息赋值保存
            setTaskDispatchData = res
            
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获取事件id错误，无法定位：' + errMsg);
        });
}

function G(id) {
    return document.getElementById(id);
}
/** c#主动调用功能，设置全局车辆信息 */
function syncMobiles(data) {
    if (typeof data === 'string') {
        _mobilesWithDistance = JSON.parse(data);
    } else {
        _mobilesWithDistance = data; // 如果已经是对象，直接赋值
    }
    // console.log("syncMobiles================",_mobilesWithDistance)
    
    // 数据更新后立即刷新车辆列表，确保状态同步
    if (_mobilesWithDistance && _mobilesWithDistance.length > 0) {
        // 使用setTimeout确保在下一个事件循环中执行，避免阻塞
        setTimeout(() => {
            sortAndShowStations(_mobilesWithDistance);
            // 更新统计信息
            updateMapResourcesStats();
        }, 0);
    }
}
/** c#主动调用功能 创建事件打开时调用，设置当前是否派车状态，派车状态 */
function sendToNewEventStatus(status) {
    // console.log('c#主动调用功能设置当前是否派车状态，派车状态')
    newEventStatus = status;
    //移除掉派车的点
    if (sendCarMarker != null) {
        sendCarMarker.remove()
        sendCarMarker = null;
    }
}


/** 挂起调度 */
function hangUpBtn() {
    $('#hangUpDialog').window('open');
}
/** 解除挂起调度 */
function unHangUpBtn() {
    $('#hangUpDialog').window('close', true);
}

//打开车载视频
function openCarVideo(){
    if (clickCarInfo.statusStr == '发车' || clickCarInfo.statusStr == '抵达'){
        let params = {
            taskId: clickCarInfo.currentProcessId,
            platformType: 0,
            seatId: _pSeat.id,
        }
        // console.log('打开与车载pad进行视频的web界面',params);
        createRoom(params, function (data) {
            //打开与车载pad进行视频的web界面
            querySysConf('car_vedio_web_url',
                function (data) {
                    //data值例子：https://192.168.2.130:18085/dev-3d/#/Consultation
                    let url = data + '?token=' + encodeURIComponent(_token) + '&seatId=' + _pSeat.id + "&taskId=" + clickCarInfo.currentProcessId
                    try {
                        // 兼容C#和B/S模式的打开浏览器
                        if (typeof gProxy !== "undefined" && gProxy != null) {
                        gProxy.openBrowser(url);
                        } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                            bsProxy.openBrowser(url);
                        } else {
                            window.open(url, '_blank');
                        }
                    } catch (e) {
                        window.open(url, '_blank');
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
                });
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '异常信息：' + errMsg);
        });
    }
}
//打开平板视频
function openPadVideo(){
    if (clickCarInfo.currentEventId){
        //打开与指导短信h5进行视频的web界面
        querySysConf('pphi_vedio_web_url',
            function (data) {
                //data值例子：https://192.168.2.130:18085/dev-3d/#/Telemedicine
                let url = data + '?answerUserName=' + _pSeat.seatId + '&answerUserId=' + _pSeat.id + "&taskId=" + clickCarInfo.currentEventId;
                // console.log(data);
                try {
                    // 兼容C#和B/S模式的打开浏览器
                    if (typeof gProxy !== "undefined" && gProxy != null) {
                    gProxy.openBrowser(url);
                    } else if (typeof bsProxy !== "undefined" && bsProxy != null) {
                        bsProxy.openBrowser(url);
                    } else {
                        window.open(url, '_blank');
                    }
                } catch (e) {
                    window.open(url, '_blank');
                }
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
            });
    }else{

    }
}
/**
 * 轮询查看视频请求
 */
function getIsActiveVideo() {
    clearTimeout(window.setIntervalH5);

    getMsgInfo({ seatId: _pSeat.id }, function (data) {
        let clickCarList = []
        if (staLists) {
            clickCarList = staLists.filter(item => {
                return item.id == clickCarId
            })
        }
        if (clickCarInfo || clickCarList.length > 0) {
            //指导视频H5视频
            if (data.getH5AVInviteVoList.length > 0) {
                for (let g = 0; g < data.getH5AVInviteVoList.length; g++) {
                    let item = data.getH5AVInviteVoList[g]
                    if (item.callsUserId == clickCarInfo.currentEventId || item.callsUserId == clickCarList[0].currentEventId) {
                        isActiveH5Video = true
                        break
                    } else {
                        isActiveH5Video = false
                    }
                }
            } else {
                isActiveH5Video = false
            }

            //车载平板视频
            if (data.seatOnlineRoomVoList.length > 0) {
                for (let g = 0; g < data.seatOnlineRoomVoList.length; g++) {
                    let item = data.seatOnlineRoomVoList[g]
                    if (item.taskId == clickCarInfo.currentEventId || item.callsUserId == clickCarList[0].currentEventId) {
                        isActiveCarVideo = true
                        break
                    } else {
                        isActiveCarVideo = false
                    }
                }
            } else {
                isActiveCarVideo = false
            }
        } else {
            isActiveCarVideo = false
            isActiveH5Video = false
        }
        
    }, function (e, url, errMsg) {
        //失败才做处理
        $.messager.alert('异常', '查看视频请求，异常信息：' + errMsg);
    });

    window.setIntervalH5 = setTimeout(getIsActiveVideo, 2000)
}

/**
 * 打开视频呼入对话页面（提供给其他屏幕调用打开视频通话界面）
 * @param {string} callVideoId 视频通话记录Id 
 * @param {boolean} tabsShow 是否显示视频呼入对话tab
 */
function openVideoCallingPage(callVideoId, tabsShow) {
    console.log('openVideoCallingPage 被调用:', { callVideoId, tabsShow, includes: tabsShow && tabsShow.split(',').includes('视频呼入对话') });
    //包含了视频呼入对话的话那么才会显示这个页面
    if(tabsShow && tabsShow.split(',').includes('视频呼入对话')){
        console.log('条件满足，准备创建tab');
        let title = '视频呼救中';
        
        // 检查是否已存在相同的tab
        var exists = $('#second-screen-tabs').tabs('exists', title);
        if (exists) {
            // 如果已存在则选中该tab
            $("#second-screen-tabs").tabs('select', title);
        } else {
            // 不存在则新建tab
            $('#second-screen-tabs').tabs('add', {
                title: title,
                content: `<iframe name="videoCalling" id="videoCallingIframe" scrolling="auto" src="videoCalling.html?callVideoId=${callVideoId}" frameborder="0" style="width:100%;height:100%;"></iframe>`,
                iconCls: 'icon-videoCalling',
                closable: true,
                selected: true
            });

            // 为新添加的tab绑定关闭事件
            $('.tabs-close').last().off('click').on('click', function(e) {
                e.stopPropagation();
                $.messager.confirm('提示', '是否确认关闭该视频通话?', function(r){
                    if (r) {
                        $('#second-screen-tabs').tabs('close', title);
                    }
                });
            });
        }
    }
}

//Bs主屏幕与副屏幕通信，通过storage来监听
window.addEventListener('storage', event => {
    
    //解除挂起副屏幕
    if (event.key === 'unHangUpSecondScreen') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            unHangUpBtn();
        }
    }

    //挂起副屏幕
    if (event.key === 'hangUpSecondScreen') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            hangUpBtn();
        }
    }

    //登录成功后调用
    if (event.key === 'loginSuccess') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            //每个屏幕配置什么需要显示的tab，根据配置动态生成
            var screenId = getScreenId();
            var screenTabs = '';
            
            try {
                var subScreenConfig = localStorage.getItem('subScreenConfig');
                if (subScreenConfig) {
                    var config = JSON.parse(subScreenConfig);
                    // 查找当前screenId对应的配置
                    var currentScreen = config.subScreens.find(function(screen) {
                        return screen.id === parseInt(screenId);
                    });
                    
                    if (currentScreen && currentScreen.modules) {
                        // 提取所有值为true的模块名称，组成逗号分隔的字符串
                        screenTabs = Object.entries(currentScreen.modules)
                            .filter(function([_, isEnabled]) {
                                return isEnabled === true;
                            })
                            .map(function([moduleName]) {
                                return moduleName;
                            })
                            .join(',');
                        
                        console.log('当前副屏(' + screenId + ')启用的模块:', screenTabs);
                    } else {
                        console.warn('未找到副屏' + screenId + '的配置，使用默认配置');
                        screenTabs = '地图调度,电话簿,通话列表';
                    }
                } else {
                    console.warn('未找到副屏配置，使用默认配置');
                    screenTabs = '地图调度,电话簿,通话列表';
                }
            } catch (e) {
                console.error('解析副屏配置失败:', e);
                screenTabs = '地图调度,电话簿,通话列表';
            }
            
            onLoginSuccess(eventData.token, screenTabs);
        }
    }

    //同步车辆数据
    if (event.key === 'syncMobiles') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            syncMobiles(eventData.data);
            
            // 如果救护车列表还没有显示，现在重新显示
            if (_mobilesWithDistance && _mobilesWithDistance.length > 0) {
                // 检查地图是否已初始化，如果没有则延迟执行
                if (map) {
                    sortAndShowStations(_mobilesWithDistance);
                } else {
                    console.log('地图尚未初始化，延迟执行车辆显示');
                    // 延迟执行，等待地图初始化
                    var checkMapInterval = setInterval(function() {
                        if (map) {
                            clearInterval(checkMapInterval);
                            console.log('地图已初始化，开始显示车辆');
                            sortAndShowStations(_mobilesWithDistance);
                        }
                    }, 100);
                    
                    // 设置超时，避免无限等待
                    setTimeout(function() {
                        clearInterval(checkMapInterval);
                        console.warn('等待地图初始化超时，跳过车辆显示');
                    }, 10000);
                }
            }
        }
    }

    //通过经纬度设置选中车辆在地图上的定位
    if (event.key === 'setPlaceBylonAndLat') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            setPlaceBylonAndLat(eventData.data);
        }
    }

    //发送地址到副屏的输入框中
    if (event.key === 'sendToMap') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            setAddress(eventData.address, eventData.longitude, eventData.latitude);
        }
    }

    //打开视频呼入对话页面
    if (event.key === 'openVideoCallingPage') {
        console.log('接收到 openVideoCallingPage 事件:', event.newValue);
        let eventData = evalJson(event.newValue);
        console.log('解析后的数据:', eventData);
        if (eventData != null) {
            openVideoCallingPage(eventData.callVideoId, eventData.tabsShow);
        }
    }
})

// ========== 围栏违规监控功能 ==========

// 围栏违规监控相关变量
var fenceMonitorActive = false;
var fenceMonitorTimer = null;
var currentViolationVehicles = [];

/**
 * 切换围栏违规监控状态
 */
function toggleFenceMonitor() {
    const btn = $('#fence-monitor-btn');
    
    if (fenceMonitorActive) {
        // 关闭监控
        stopFenceMonitor();
        btn.removeClass('active');
        console.log('围栏违规监控已关闭');
        
        // 显示关闭提示
        $.messager.alert('围栏监控', 
            '围栏违规监控已关闭！<br><br>' +
            '⭕ <strong>已停止：</strong><br>' +
            '• 停止自动检查车辆围栏违规情况<br>' +
            '• 清除当前显示的违规提醒信息<br>' +
            '• 围栏监控按钮恢复正常状态<br><br>' +
            '💡 <strong>提示：</strong><br>' +
            '如需重新开启监控，请再次点击围栏监控按钮。', 
            'warning'
        );
    } else {
        // 开启监控
        startFenceMonitor();
        btn.addClass('active');
        console.log('围栏违规监控已开启');
        
        // 显示开启提示
        $.messager.alert('围栏监控', 
            '围栏违规监控已开启！<br><br>' +
            '🚨 <strong>监控功能：</strong><br>' +
            '• 系统将每10秒自动检查车辆围栏违规情况<br>' +
            '• 发现违规车辆时会在地图底部显示闪烁提醒<br>' +
            '• 点击提醒可查看详细的违规车辆信息<br><br>' +
            '⚠️ <strong>温馨提示：</strong><br>' +
            '围栏监控按钮将显示为红色激活状态，再次点击可关闭监控。', 
            'info'
        );
    }
}

/**
 * 开启围栏违规监控
 */
function startFenceMonitor() {
    fenceMonitorActive = true;
    
    // 立即执行一次检查
    checkFenceViolations();
    
    // 设置定时器，每10秒检查一次
    fenceMonitorTimer = setInterval(function() {
        checkFenceViolations();
    }, 10000);
}

/**
 * 停止围栏违规监控
 */
function stopFenceMonitor() {
    fenceMonitorActive = false;
    
    // 清除定时器
    if (fenceMonitorTimer) {
        clearInterval(fenceMonitorTimer);
        fenceMonitorTimer = null;
    }
    
    // 隐藏违规提醒
    hideViolationAlert();
    
    // 清空违规车辆数据
    currentViolationVehicles = [];
}

/**
 * 检查围栏违规情况
 */
function checkFenceViolations() {
    if (!fenceMonitorActive) {
        return;
    }
    
    console.log('正在检查围栏违规情况...');
    
    // 调用getOutFenceVehicles接口获取违规车辆
    getOutFenceVehicles({}, function(data) {
        console.log('围栏违规检查结果:', data);
        
        if (data && data.length > 0) {
            // 有违规车辆
            currentViolationVehicles = data;
            showViolationAlert(data.length);
        } else {
            // 无违规车辆
            currentViolationVehicles = [];
            hideViolationAlert();
        }
    }, function(error) {
        console.error('获取围栏违规车辆失败:', error);
        // 可以选择是否显示错误提示
        // $.messager.alert('错误', '获取围栏违规信息失败', 'error');
    });
}

/**
 * 显示违规提醒
 * @param {number} count - 违规车辆数量
 */
function showViolationAlert(count) {
    const alertDiv = $('#fence-violation-alert');
    const countSpan = $('#violation-count');
    
    countSpan.text(count);
    alertDiv.show();
}

/**
 * 隐藏违规提醒
 */
function hideViolationAlert() {
    $('#fence-violation-alert').hide();
}

/**
 * 显示围栏违规详情
 */
function showFenceViolationDetails() {
    if (!currentViolationVehicles || currentViolationVehicles.length === 0) {
        $.messager.alert('提示', '当前无违规车辆', 'info');
        return;
    }
    
    // 构建违规详情HTML
    let detailsHtml = '<div class="fence-violation-details">';
    
    currentViolationVehicles.forEach(function(vehicle, index) {
        const duration = formatDuration(vehicle.durationSeconds || 0);
        const startTime = formatDateTime(vehicle.startTime);
        const speedText = vehicle.speed !== null ? vehicle.speed + ' km/h' : '-';
        const statusText = getVehicleStatusText(vehicle.status);
        
        detailsHtml += `
            <div class="violation-item">
                <div class="violation-header">
                    <div class="violation-title">${vehicle.carName || '未知车辆'}</div>
                    <div class="violation-duration">${duration}</div>
                    <div class="violation-actions">
                        <button onclick="openVehicleTrackReplay('${vehicle.vehicleId}', '${vehicle.carName}', '${vehicle.plateNum}')" 
                                style="padding: 4px 12px; background: #1890ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            📊 轨迹回放
                        </button>
                    </div>
                </div>
                <div class="violation-info">
                    <span class="label">违规开始时间：</span>
                    <span>${startTime}</span>
                    <span class="label">所属分站：</span>
                    <span>${vehicle.stationName || '-'}</span>
                    <span class="label">车牌号：</span>
                    <span>${vehicle.plateNum || '-'}</span>
                    <span class="label">车辆类型：</span>
                    <span>${getVehicleTypeText(vehicle.carType) || '-'}</span>
                    <span class="label">车辆状态：</span>
                    <span>${statusText}</span>
                    <span class="label">当前位置：</span>
                    <span>${vehicle.longitude && vehicle.latitude ? vehicle.longitude + ', ' + vehicle.latitude : '-'}</span>
                    <span class="label">当前速度：</span>
                    <span>${speedText}</span>
                </div>
            </div>
        `;
    });
    
    detailsHtml += '</div>';
    
    // 显示详情对话框
    var dlg = $('<div></div>').dialog({
        title: '围栏违规详情',
        width: 420,
        height: 500,
        modal: true,
        resizable: true,
        content: detailsHtml,
        buttons: [{
            text: '关闭',
            handler: function() {
                dlg.dialog('close');
            }
        }],
        onClose: function() {
            $(this).dialog('destroy');
        }
    });
}

/**
 * 格式化持续时间
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
function formatDuration(seconds) {
    if (!seconds || seconds < 0) {
        return '0秒';
    }
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}小时${minutes}分${secs}秒`;
    } else if (minutes > 0) {
        return `${minutes}分${secs}秒`;
    } else {
        return `${secs}秒`;
    }
}

/**
 * 格式化日期时间
 * @param {string} dateTimeStr - 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) {
        return '-';
    }
    
    try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        return dateTimeStr;
    }
}

/**
 * 获取车辆状态文本
 * @param {string} status - 车辆状态代码
 * @returns {string} 状态文本
 */
function getVehicleStatusText(status) {
    const statusMap = {
        '0': '空闲',
        '1': '待派',
        '2': '出发',
        '3': '到达现场',
        '4': '接到病人',
        '5': '到达医院',
        '6': '任务完成',
        '7': '返回车站',
        '8': '故障',
        '9': '离线'
    };
    return statusMap[status] || '未知状态';
}

/**
 * 获取车辆类型文本
 * @param {string} carType - 车辆类型代码
 * @returns {string} 类型文本
 */
function getVehicleTypeText(carType) {
    const typeMap = {
        '01': '普通救护车',
        '02': 'ICU救护车',
        '03': '急救车',
        '04': '转运车',
        '05': '指挥车',
        '06': '医疗巡逻车'
    };
    return typeMap[carType] || '未知类型';
}

/**
 * 打开车辆轨迹回放界面
 * @param {string} vehicleId - 车辆ID
 * @param {string} carName - 车辆名称
 * @param {string} plateNum - 车牌号
 */
function openVehicleTrackReplay(vehicleId, carName, plateNum) {
    if (!vehicleId) {
        $.messager.alert('错误', '车辆ID为空，无法打开轨迹回放', 'error');
        return;
    }
    
    // 构造车辆对象，模拟现有轨迹回放功能的车辆格式
    const vehicle = {
        id: vehicleId,
        carName: carName || '未知车辆',
        plateNum: plateNum || ''
    };
    
    // 调用现有的轨迹回放功能
    openTrackReplayDialog(vehicle);
}

// 围栏违规监控初始化函数（在页面加载完成后调用）
function initFenceMonitor() {
    console.log('围栏违规监控功能已初始化完成');
}

// ========== 地图资源显示控制功能 ==========

/**
 * 切换车辆显示状态
 */
function toggleVehiclesDisplay() {
    const btn = $('#vehicles-display-btn');
    
    if (mapResourcesState.vehiclesVisible) {
        // 隐藏车辆
        hideAllVehicles();
        mapResourcesState.vehiclesVisible = false;
        btn.removeClass('active');
        console.log('车辆显示已关闭');
        
        // 显示提示
        // $.messager.alert('车辆显示', 
        //     '车辆显示已关闭！<br><br>' +
        //     '🚗 <strong>已隐藏：</strong><br>' +
        //     '• 地图上所有救护车图标<br>' +
        //     '• 车辆行驶轨迹和路径信息<br>' +
        //     '• 车辆信息窗口和状态显示<br><br>' +
        //     '💡 <strong>提示：</strong><br>' +
        //     '如需重新显示车辆，请再次点击车辆显示按钮。', 
        //     'warning'
        // );
    } else {
        // 显示车辆
        showAllVehicles();
        mapResourcesState.vehiclesVisible = true;
        btn.addClass('active');
        console.log('车辆显示已开启');
        
        // 显示提示
        // $.messager.alert('车辆显示', 
        //     '车辆显示已开启！<br><br>' +
        //     '🚗 <strong>已显示：</strong><br>' +
        //     '• 地图上将显示所有救护车图标<br>' +
        //     '• 显示车辆实时位置和状态信息<br>' +
        //     '• 显示车辆行驶轨迹和路径规划<br><br>' +
        //     '⚡ <strong>功能特点：</strong><br>' +
        //     '车辆信息将根据右侧列表筛选条件动态显示。', 
        //     'info'
        // );
    }
    
    // 更新统计信息
    updateMapResourcesStats();
}

/**
 * 切换分站显示状态
 */
function toggleStationsDisplay() {
    const btn = $('#stations-display-btn');
    
    if (mapResourcesState.stationsVisible) {
        // 隐藏分站
        hideAllStations();
        mapResourcesState.stationsVisible = false;
        btn.removeClass('active');
        console.log('分站显示已关闭');
        
        // 显示提示
        // $.messager.alert('分站显示', 
        //     '分站显示已关闭！<br><br>' +
        //     '🏥 <strong>已隐藏：</strong><br>' +
        //     '• 地图上所有分站图标<br>' +
        //     '• 分站名称标签和位置信息<br>' +
        //     '• 分站在线状态指示器<br><br>' +
        //     '💡 <strong>提示：</strong><br>' +
        //     '如需重新显示分站，请再次点击分站显示按钮。', 
        //     'warning'
        // );
    } else {
        // 显示分站
        showAllStations();
        mapResourcesState.stationsVisible = true;
        btn.addClass('active');
        console.log('分站显示已开启');
        
        // 显示提示
        // $.messager.alert('分站显示', 
        //     '分站显示已开启！<br><br>' +
        //     '🏥 <strong>已显示：</strong><br>' +
        //     '• 地图上将显示所有分站图标<br>' +
        //     '• 显示分站名称和位置信息<br>' +
        //     '• 显示分站在线状态和类型标识<br><br>' +
        //     '📋 <strong>功能特点：</strong><br>' +
        //     '点击分站图标可查看详细的分站信息。', 
        //     'info'
        // );
    }
    
    // 更新统计信息
    updateMapResourcesStats();
}

/**
 * 隐藏地图上所有车辆
 */
function hideAllVehicles() {
    // 隐藏所有车辆marker
    if (carMarkers && carMarkers.length > 0) {
        carMarkers.forEach(function(marker) {
            if (marker && marker.hide) {
                marker.hide();
            }
        });
        console.log('已隐藏', carMarkers.length, '个车辆标记');
    }
    
    // 隐藏车辆轨迹
    if (carTrackLines) {
        Object.values(carTrackLines).forEach(function(trackLine) {
            if (trackLine && trackLine.hide) {
                trackLine.hide();
            }
        });
        console.log('已隐藏车辆轨迹线');
    }
    
    // 隐藏车辆目的地标记
    if (carDestinationMarkers) {
        Object.values(carDestinationMarkers).forEach(function(marker) {
            if (marker && marker.hide) {
                marker.hide();
            }
        });
        console.log('已隐藏车辆目的地标记');
    }
    
    // 隐藏车辆信息窗口
    if (_carInfoWindow && _carInfoWindow.close) {
        _carInfoWindow.close();
    }
}

/**
 * 显示地图上所有车辆
 */
function showAllVehicles() {
    // 显示所有车辆marker
    if (carMarkers && carMarkers.length > 0) {
        carMarkers.forEach(function(marker) {
            if (marker && marker.show) {
                marker.show();
            }
        });
        console.log('已显示', carMarkers.length, '个车辆标记');
    }
    
    // 显示车辆轨迹
    if (carTrackLines) {
        Object.values(carTrackLines).forEach(function(trackLine) {
            if (trackLine && trackLine.show) {
                trackLine.show();
            }
        });
        console.log('已显示车辆轨迹线');
    }
    
    // 显示车辆目的地标记
    if (carDestinationMarkers) {
        Object.values(carDestinationMarkers).forEach(function(marker) {
            if (marker && marker.show) {
                marker.show();
            }
        });
        console.log('已显示车辆目的地标记');
    }
    
    // 重新刷新车辆列表以确保显示正确
    if (_mobilesWithDistance && _mobilesWithDistance.length > 0) {
        setTimeout(() => {
            sortAndShowStations(_mobilesWithDistance);
        }, 100);
    }
}

/**
 * 隐藏地图上所有分站
 */
function hideAllStations() {
    if (!stationMarkers || stationMarkers.length === 0) {
        console.log('没有找到分站标记，无需隐藏');
        return;
    }
    
    let hiddenStationCount = 0;
    stationMarkers.forEach(function(marker) {
        if (marker && marker.hide) {
            marker.hide();
            hiddenStationCount++;
        }
    });
    
    console.log('已隐藏', hiddenStationCount, '个分站标记');
}

/**
 * 显示地图上所有分站
 */
function showAllStations() {
    if (!stationMarkers || stationMarkers.length === 0) {
        // 如果没有分站标记，重新绘制分站
        if (_stations && _stations.length > 0) {
            console.log('未找到分站标记，重新绘制分站');
            drawStations();
        } else {
            console.log('没有分站数据，无法显示分站');
        }
        return;
    }
    
    let shownStationCount = 0;
    stationMarkers.forEach(function(marker) {
        if (marker && marker.show) {
            marker.show();
            shownStationCount++;
        }
    });
    
    console.log('已显示', shownStationCount, '个分站标记');
}

/**
 * 清空所有分站标记
 */
function clearStationMarkers() {
    if (stationMarkers && stationMarkers.length > 0) {
        stationMarkers.forEach(function(marker) {
            if (marker && map) {
                map.remove(marker);
            }
        });
        console.log('已清除', stationMarkers.length, '个分站标记');
        stationMarkers = [];
    }
}

/**
 * 显示分站详细信息
 * @param {Object} stationData - 分站数据
 * @param {AMap.LngLat} position - 分站位置
 */
function showStationInfo(stationData, position) {
    console.log('显示分站信息:', stationData);
    
    // 检查分站数据是否存在
    if (!stationData) {
        console.warn('分站数据为空，无法显示分站信息');
        return;
    }
    
    // 获取分站类型标识
    const isAllRoundStation = stationData.stationAllRound === "1";
    const stationTypeText = isAllRoundStation ? '全能分站' : '普通分站';
    const stationTypeColor = isAllRoundStation ? '#00C851' : '#999999';
    
    // 获取在线状态
    const isOnline = stationData.isLogin == 1;
    const onlineStatus = isOnline ? '在线' : '离线';
    const onlineColor = isOnline ? '#52c41a' : '#ff4d4f';
    
    // 构建分站信息HTML
    var infoContent = `
        <div style="min-width: 200px; padding: 10px;">
            <div style="border-bottom: 2px solid #1890ff; padding-bottom: 8px; margin-bottom: 12px;">
                <h3 style="margin: 0; color: #1890ff; font-size: 16px; font-weight: bold;">
                    🏥 ${stationData.stationName || '未知分站'}
                </h3>
            </div>
            
            <div style="line-height: 1.8; font-size: 13px;">
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">分站类型：</span>
                    <span style="color: ${stationTypeColor}; font-weight: bold;">${stationTypeText}</span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">在线状态：</span>
                    <span style="color: ${onlineColor}; font-weight: bold;">${onlineStatus}</span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">分站编码：</span>
                    <span style="color: #333;">${stationData.stationCode || '-'}</span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">联系电话：</span>
                    <span style="color: #333;">${stationData.stationTelephone || '-'}</span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">分站地址：</span>
                    <span style="color: #333;">${stationData.stationAddress || '-'}</span>
                </div>
                
                <div style="margin-bottom: 8px;">
                    <span style="color: #666; font-weight: bold;">坐标信息：</span>
                    <span style="color: #333; font-size: 12px;">${stationData.stationLongitude || '-'}, ${stationData.stationLatitude || '-'}</span>
                </div>
                
                <div style="margin-bottom: 12px;">
                    <span style="color: #666; font-weight: bold;">分站描述：</span>
                    <span style="color: #333;">${stationData.stationDescription || '暂无描述'}</span>
                </div>
                
                <div style="border-top: 1px solid #f0f0f0; padding-top: 8px; text-align: center;">
                    <button onclick="centerMapToStation(${stationData.stationLongitude}, ${stationData.stationLatitude})" 
                            style="padding: 6px 12px; background: #1890ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; margin-right: 8px;">
                        📍 定位分站
                    </button>
                    <button onclick="showStationVehicles('${stationData.id}', '${stationData.stationName}')" 
                            style="padding: 6px 12px; background: #52c41a; color: #fff; border: none; border-radius: 4px; cursor: pointer;">
                        🚑 查看车辆
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // 创建信息窗口
    var infoWindow = new AMap.InfoWindow({
        content: infoContent,
        offset: new AMap.Pixel(0, -30),
        autoMove: true,
        closeWhenClickMap: true
    });
    
    // 在分站位置显示信息窗口
    infoWindow.open(map, position);
    
    // 保存当前分站信息（用于范围工具等功能）
    lastSelectedPosition = {
        longitude: stationData.stationLongitude,
        latitude: stationData.stationLatitude
    };
    lastSelectedType = 'station';
    lastSelectedDescription = stationData.stationName + ' 分站';
}

/**
 * 将地图中心定位到指定分站
 * @param {number} longitude - 经度
 * @param {number} latitude - 纬度
 */
function centerMapToStation(longitude, latitude) {
    if (!map || !longitude || !latitude) {
        console.warn('无法定位到分站：地图或坐标信息无效');
        return;
    }
    
    const position = new AMap.LngLat(longitude, latitude);
    map.setCenter(position);
    map.setZoom(16); // 设置合适的缩放级别
    
    console.log('地图已定位到分站:', longitude, latitude);
    $.messager.show({
        title: '地图定位',
        msg: '已定位到分站位置',
        timeout: 2000,
        showType: 'slide'
    });
}

/**
 * 显示指定分站的车辆列表
 * @param {string} stationId - 分站ID
 * @param {string} stationName - 分站名称
 */
function showStationVehicles(stationId, stationName) {
    if (!_mobilesWithDistance || !_mobilesWithDistance.length) {
        $.messager.alert('提示', '当前没有车辆数据', 'info');
        return;
    }
    
    // 筛选出该分站的车辆
    const stationVehicles = _mobilesWithDistance.filter(vehicle => vehicle.stationId === stationId);
    
    if (stationVehicles.length === 0) {
        $.messager.alert('提示', `${stationName} 当前没有车辆`, 'info');
        return;
    }
    
    // 构建车辆列表HTML
    let vehicleListHtml = `
        <div style="max-height: 400px; overflow-y: auto; padding: 10px;">
            <div style="border-bottom: 2px solid #1890ff; padding-bottom: 8px; margin-bottom: 12px;">
                <h3 style="margin: 0; color: #1890ff; font-size: 16px; font-weight: bold;">
                    🏥 ${stationName} - 车辆列表
                </h3>
                <p style="margin: 4px 0 0 0; color: #666; font-size: 12px;">共 ${stationVehicles.length} 辆车</p>
            </div>
    `;
    
    stationVehicles.forEach((vehicle, index) => {
        // 获取车辆状态信息
        const statusInfo = carStatusMap[vehicle.status] || { title: '未知状态', color: '#666' };
        let conditionText = '';
        if (vehicle.hCondition && vehicle.hCondition != "0") {
            const conditionMap = {
                '1': '报停中',
                '3': '未值班'
            };
            conditionText = `<span style="color: #ff4d4f; font-weight: bold;">[${conditionMap[vehicle.hCondition] || '异常'}]</span>`;
        }
        
        // 获取车辆类型
        const carVehicleType = getCarVehicleType(vehicle);
        const vehicleTypeName = vehicleTypeOptions.find(type => type.id === carVehicleType)?.name || '';
        const vehicleTypeDisplay = vehicleTypeName ? `<span style="color: #1890ff; font-size: 11px;">[${vehicleTypeName}]</span>` : '';
        
        vehicleListHtml += `
            <div style="border: 1px solid #f0f0f0; border-radius: 6px; padding: 10px; margin-bottom: 8px; background: #fafafa;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                    <div style="font-weight: bold; color: #333; font-size: 14px;">
                        🚑 ${vehicle.carName} ${conditionText}
                    </div>
                    <div style="color: ${statusInfo.color}; font-weight: bold; font-size: 12px;">
                        ${statusInfo.title}
                    </div>
                </div>
                
                <div style="font-size: 12px; color: #666; line-height: 1.6;">
                    <div>车牌号：<span style="color: #333;">${vehicle.plateNum || '-'}</span> ${vehicleTypeDisplay}</div>
                    <div>当前位置：<span style="color: #333;">${vehicle.lng || '-'}, ${vehicle.lat || '-'}</span></div>
                    <div>行驶速度：<span style="color: #333;">${Math.round((vehicle.speed || 0) * 100) / 100} km/h</span></div>
                    <div>联系电话：<span style="color: #333;">${vehicle.contact || '-'}</span></div>
                </div>
                
                <div style="margin-top: 8px; text-align: center;">
                    <button onclick="locateVehicleOnMap('${vehicle.id}')" 
                            style="padding: 4px 10px; background: #1890ff; color: #fff; border: none; border-radius: 3px; cursor: pointer; margin-right: 6px; font-size: 11px;">
                        📍 定位车辆
                    </button>
                    <button onclick="selectVehicleInList('${vehicle.id}')" 
                            style="padding: 4px 10px; background: #52c41a; color: #fff; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                        ✅ 选中车辆
                    </button>
                </div>
            </div>
        `;
    });
    
    vehicleListHtml += '</div>';
    
    // 显示车辆列表对话框
    var dlg = $('<div></div>').dialog({
        title: '分站车辆列表',
        width: 480,
        height: 520,
        modal: true,
        resizable: true,
        content: vehicleListHtml,
        buttons: [{
            text: '关闭',
            handler: function() {
                dlg.dialog('close');
            }
        }],
        onClose: function() {
            $(this).dialog('destroy');
        }
    });
}

/**
 * 在地图上定位指定车辆
 * @param {string} vehicleId - 车辆ID
 */
function locateVehicleOnMap(vehicleId) {
    const vehicle = _mobilesWithDistance ? _mobilesWithDistance.find(v => v.id === vehicleId) : null;
    if (!vehicle) {
        $.messager.alert('错误', '未找到指定车辆', 'error');
        return;
    }
    
    if (!vehicle.lng || !vehicle.lat) {
        $.messager.alert('提示', '该车辆暂无位置信息', 'warning');
        return;
    }
    
    const position = new AMap.LngLat(vehicle.lng, vehicle.lat);
    map.setCenter(position);
    map.setZoom(17);
    
    console.log('地图已定位到车辆:', vehicle.carName, vehicle.lng, vehicle.lat);
    $.messager.show({
        title: '车辆定位',
        msg: `已定位到车辆：${vehicle.carName}`,
        timeout: 2000,
        showType: 'slide'
    });
}

/**
 * 在右侧列表中选中指定车辆
 * @param {string} vehicleId - 车辆ID
 */
function selectVehicleInList(vehicleId) {
    // 在右侧车辆列表中找到并选中该车辆
    const vehicleElement = $(`[data-id="${vehicleId}"]`);
    if (vehicleElement.length > 0) {
        // 清除之前的选中状态
        $('.line.selected').removeClass('selected');
        
        // 选中当前车辆
        vehicleElement.addClass('selected');
        currentCarId = vehicleId;
        
        // 滚动到该车辆位置
        const container = vehicleElement.closest('.edc-listbox section > div');
        const elementTop = vehicleElement.position().top;
        const containerHeight = container.height();
        const scrollTop = container.scrollTop();
        
        if (elementTop < 0 || elementTop > containerHeight) {
            container.scrollTop(scrollTop + elementTop - containerHeight / 2);
        }
        
        console.log('已在列表中选中车辆:', vehicleId);
        $.messager.show({
            title: '车辆选择',
            msg: '已在右侧列表选中该车辆',
            timeout: 2000,
            showType: 'slide'
        });
    } else {
        $.messager.alert('提示', '在右侧列表中未找到该车辆', 'warning');
    }
}


/**
 * 切换医院显示状态
 */
function toggleHospitalsDisplay() {
    const btn = $('#hospitals-display-btn');
    
    if (mapResourcesState.hospitalsVisible) {
        // 隐藏医院
        hideAllHospitals();
        mapResourcesState.hospitalsVisible = false;
        btn.removeClass('active');
        console.log('医院显示已关闭');
        
        // 显示提示
        // $.messager.alert('医院显示', 
        //     '医院显示已关闭！<br><br>' +
        //     '🏥 <strong>已隐藏：</strong><br>' +
        //     '• 地图上所有医院图标<br>' +
        //     '• 医院名称标签和位置信息<br>' +
        //     '• 医院联系方式和地址信息<br><br>' +
        //     '💡 <strong>提示：</strong><br>' +
        //     '如需重新显示医院，请再次点击医院显示按钮。', 
        //     'warning'
        // );
        
        // 更新统计信息
        updateMapResourcesStats();
    } else {
        // 显示医院
        if (_hospitals.length === 0) {
            // 首次显示时获取数据
            getBasicResourceInfoByTypeCode({"typeCode": "ZYLX_06"}, function(data) {
                _hospitals = data;
                drawHospitals();
                mapResourcesState.hospitalsVisible = true;
                btn.addClass('active');
                console.log('医院显示已开启');
                
                // 显示提示
                // $.messager.alert('医院显示', 
                //     '医院显示已开启！<br><br>' +
                //     '🏥 <strong>已显示：</strong><br>' +
                //     '• 地图上将显示所有医院图标<br>' +
                //     '• 显示医院名称和位置信息<br>' +
                //     '• 显示医院联系方式和地址<br><br>' +
                //     '📋 <strong>功能特点：</strong><br>' +
                //     '点击医院图标可查看详细的医院信息。', 
                //     'info'
                // );
                
                // 更新统计信息
                updateMapResourcesStats();
            });
        } else {
            // 已有数据时直接显示
            showAllHospitals();
            mapResourcesState.hospitalsVisible = true;
            btn.addClass('active');
            console.log('医院显示已开启');
            
            // 更新统计信息
            updateMapResourcesStats();
        }
    }
}

/**
 * 切换血站显示状态
 */
function toggleBloodstationsDisplay() {
    const btn = $('#bloodstations-display-btn');
    
    if (mapResourcesState.bloodstationsVisible) {
        // 隐藏血站
        hideAllBloodstations();
        mapResourcesState.bloodstationsVisible = false;
        btn.removeClass('active');
        console.log('血站显示已关闭');
        
        // 显示提示
        // $.messager.alert('血站显示', 
        //     '血站显示已关闭！<br><br>' +
        //     '🏥 <strong>已隐藏：</strong><br>' +
        //     '• 地图上所有血站图标<br>' +
        //     '• 血站名称标签和位置信息<br>' +
        //     '• 血站联系方式和地址信息<br><br>' +
        //     '💡 <strong>提示：</strong><br>' +
        //     '如需重新显示血站，请再次点击血站显示按钮。', 
        //     'warning'
        // );
        
        // 更新统计信息
        updateMapResourcesStats();
    } else {
        // 显示血站
        if (_bloodstations.length === 0) {
            // 首次显示时获取数据
            getBasicResourceInfoByTypeCode({"typeCode": "ZYLX_10"}, function(data) {
                _bloodstations = data;
                drawBloodstations();
                mapResourcesState.bloodstationsVisible = true;
                btn.addClass('active');
                console.log('血站显示已开启');
                
                // 显示提示
                // $.messager.alert('血站显示', 
                //     '血站显示已开启！<br><br>' +
                //     '🏥 <strong>已显示：</strong><br>' +
                //     '• 地图上将显示所有血站图标<br>' +
                //     '• 显示血站名称和位置信息<br>' +
                //     '• 显示血站联系方式和地址<br><br>' +
                //     '📋 <strong>功能特点：</strong><br>' +
                //     '点击血站图标可查看详细的血站信息。', 
                //     'info'
                // );
                
                // 更新统计信息
                updateMapResourcesStats();
            });
        } else {
            // 已有数据时直接显示
            showAllBloodstations();
            mapResourcesState.bloodstationsVisible = true;
            btn.addClass('active');
            console.log('血站显示已开启');
            
            // 更新统计信息
            updateMapResourcesStats();
        }
    }
}

/**
 * 切换疾控中心显示状态
 */
function toggleCdcDisplay() {
    const btn = $('#cdc-display-btn');
    
    if (mapResourcesState.cdcVisible) {
        // 隐藏疾控中心
        hideAllCdcs();
        mapResourcesState.cdcVisible = false;
        btn.removeClass('active');
        console.log('疾控中心显示已关闭');
        
        // 显示提示
        // $.messager.alert('疾控中心显示', 
        //     '疾控中心显示已关闭！<br><br>' +
        //     '🏥 <strong>已隐藏：</strong><br>' +
        //     '• 地图上所有疾控中心图标<br>' +
        //     '• 疾控中心名称标签和位置信息<br>' +
        //     '• 疾控中心联系方式和地址信息<br><br>' +
        //     '💡 <strong>提示：</strong><br>' +
        //     '如需重新显示疾控中心，请再次点击疾控中心显示按钮。', 
        //     'warning'
        // );
        
        // 更新统计信息
        updateMapResourcesStats();
    } else {
        // 显示疾控中心
        if (_cdcs.length === 0) {
            // 首次显示时获取数据
            getBasicResourceInfoByTypeCode({"typeCode": "ZYLX_09"}, function(data) {
                _cdcs = data;
                drawCdcs();
                mapResourcesState.cdcVisible = true;
                btn.addClass('active');
                console.log('疾控中心显示已开启');
                
                // 显示提示
                // $.messager.alert('疾控中心显示', 
                //     '疾控中心显示已开启！<br><br>' +
                //     '🏥 <strong>已显示：</strong><br>' +
                //     '• 地图上将显示所有疾控中心图标<br>' +
                //     '• 显示疾控中心名称和位置信息<br>' +
                //     '• 显示疾控中心联系方式和地址<br><br>' +
                //     '📋 <strong>功能特点：</strong><br>' +
                //     '点击疾控中心图标可查看详细的疾控中心信息。', 
                //     'info'
                // );
                
                // 更新统计信息
                updateMapResourcesStats();
            });
        } else {
            // 已有数据时直接显示
            showAllCdcs();
            mapResourcesState.cdcVisible = true;
            btn.addClass('active');
            console.log('疾控中心显示已开启');
            
            // 更新统计信息
            updateMapResourcesStats();
        }
    }
}

/**
 * 绘制医院标记
 */
function drawHospitals() {
    // 清除现有的医院标记
    clearHospitalMarkers();
    
    if (!_hospitals || _hospitals.length === 0) {
        console.log('没有医院数据需要绘制');
        return;
    }
    
    _hospitals.forEach(function(hospital) {
        if (!hospital.longitude || !hospital.latitude) {
            console.warn('医院缺少坐标信息:', hospital);
            return;
        }
        
        const position = new AMap.LngLat(parseFloat(hospital.longitude), parseFloat(hospital.latitude));
        
        // 创建医院标记
        const marker = new AMap.Marker({
            position: position,
            title: hospital.name,
            icon: new AMap.Icon({
                image: 'style/img/hospital.png',
                size: new AMap.Size(24, 24),
                imageSize: new AMap.Size(24, 24)
            })
        });
        
        // 添加信息窗口
        const infoWindow = new AMap.InfoWindow({
            content: createResourceInfoContent(hospital, '医院'),
            offset: new AMap.Pixel(0, -30)
        });
        
        marker.on('click', function() {
            infoWindow.open(map, position);
        });
        
        map.add(marker);
        hospitalMarkers.push(marker);
    });
    
    console.log('已绘制', hospitalMarkers.length, '个医院标记');
}

/**
 * 绘制血站标记
 */
function drawBloodstations() {
    // 清除现有的血站标记
    clearBloodstationMarkers();
    
    if (!_bloodstations || _bloodstations.length === 0) {
        console.log('没有血站数据需要绘制');
        return;
    }
    
    _bloodstations.forEach(function(bloodstation) {
        if (!bloodstation.longitude || !bloodstation.latitude) {
            console.warn('血站缺少坐标信息:', bloodstation);
            return;
        }
        
        const position = new AMap.LngLat(parseFloat(bloodstation.longitude), parseFloat(bloodstation.latitude));
        
        // 创建血站标记
        const marker = new AMap.Marker({
            position: position,
            title: bloodstation.name,
            icon: new AMap.Icon({
                image: 'style/img/hospital.png',
                size: new AMap.Size(24, 24),
                imageSize: new AMap.Size(24, 24)
            })
        });
        
        // 添加信息窗口
        const infoWindow = new AMap.InfoWindow({
            content: createResourceInfoContent(bloodstation, '血站'),
            offset: new AMap.Pixel(0, -30)
        });
        
        marker.on('click', function() {
            infoWindow.open(map, position);
        });
        
        map.add(marker);
        bloodstationMarkers.push(marker);
    });
    
    console.log('已绘制', bloodstationMarkers.length, '个血站标记');
}

/**
 * 绘制疾控中心标记
 */
function drawCdcs() {
    // 清除现有的疾控中心标记
    clearCdcMarkers();
    
    if (!_cdcs || _cdcs.length === 0) {
        console.log('没有疾控中心数据需要绘制');
        return;
    }
    
    _cdcs.forEach(function(cdc) {
        if (!cdc.longitude || !cdc.latitude) {
            console.warn('疾控中心缺少坐标信息:', cdc);
            return;
        }
        
        const position = new AMap.LngLat(parseFloat(cdc.longitude), parseFloat(cdc.latitude));
        
        // 创建疾控中心标记
        const marker = new AMap.Marker({
            position: position,
            title: cdc.name,
            icon: new AMap.Icon({
                image: 'style/img/hospital.png',
                size: new AMap.Size(24, 24),
                imageSize: new AMap.Size(24, 24)
            })
        });
        
        // 添加信息窗口
        const infoWindow = new AMap.InfoWindow({
            content: createResourceInfoContent(cdc, '疾控中心'),
            offset: new AMap.Pixel(0, -30)
        });
        
        marker.on('click', function() {
            infoWindow.open(map, position);
        });
        
        map.add(marker);
        cdcMarkers.push(marker);
    });
    
    console.log('已绘制', cdcMarkers.length, '个疾控中心标记');
}

/**
 * 创建资源信息内容
 */
function createResourceInfoContent(resource, type) {
    const content = `
        <div style="padding: 10px; font-size: 14px; min-width: 200px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #1890ff;">
                ${type}：${resource.name}
            </div>
            <div style="margin-bottom: 5px;">
                <strong>地址：</strong>${resource.address || '暂无'}
            </div>
            <div style="margin-bottom: 5px;">
                <strong>联系人：</strong>${resource.linkman || '暂无'}
            </div>
            <div style="margin-bottom: 5px;">
                <strong>联系电话：</strong>${resource.contactNumber || '暂无'}
            </div>
            <div style="margin-bottom: 5px;">
                <strong>坐标：</strong>${resource.longitude}, ${resource.latitude}
            </div>
            ${resource.descript ? `<div style="margin-bottom: 5px;"><strong>描述：</strong>${resource.descript}</div>` : ''}
        </div>
    `;
    return content;
}

/**
 * 显示所有医院
 */
function showAllHospitals() {
    if (!hospitalMarkers || hospitalMarkers.length === 0) {
        // 如果没有医院标记，重新绘制
        if (_hospitals && _hospitals.length > 0) {
            console.log('未找到医院标记，重新绘制医院');
            drawHospitals();
        }
        return;
    }
    
    let shownCount = 0;
    hospitalMarkers.forEach(function(marker) {
        if (marker && marker.show) {
            marker.show();
            shownCount++;
        }
    });
    
    console.log('已显示', shownCount, '个医院标记');
}

/**
 * 隐藏所有医院
 */
function hideAllHospitals() {
    if (!hospitalMarkers || hospitalMarkers.length === 0) {
        console.log('没有找到医院标记，无需隐藏');
        return;
    }
    
    let hiddenCount = 0;
    hospitalMarkers.forEach(function(marker) {
        if (marker && marker.hide) {
            marker.hide();
            hiddenCount++;
        }
    });
    
    console.log('已隐藏', hiddenCount, '个医院标记');
}

/**
 * 显示所有血站
 */
function showAllBloodstations() {
    if (!bloodstationMarkers || bloodstationMarkers.length === 0) {
        // 如果没有血站标记，重新绘制
        if (_bloodstations && _bloodstations.length > 0) {
            console.log('未找到血站标记，重新绘制血站');
            drawBloodstations();
        }
        return;
    }
    
    let shownCount = 0;
    bloodstationMarkers.forEach(function(marker) {
        if (marker && marker.show) {
            marker.show();
            shownCount++;
        }
    });
    
    console.log('已显示', shownCount, '个血站标记');
}

/**
 * 隐藏所有血站
 */
function hideAllBloodstations() {
    if (!bloodstationMarkers || bloodstationMarkers.length === 0) {
        console.log('没有找到血站标记，无需隐藏');
        return;
    }
    
    let hiddenCount = 0;
    bloodstationMarkers.forEach(function(marker) {
        if (marker && marker.hide) {
            marker.hide();
            hiddenCount++;
        }
    });
    
    console.log('已隐藏', hiddenCount, '个血站标记');
}

/**
 * 显示所有疾控中心
 */
function showAllCdcs() {
    if (!cdcMarkers || cdcMarkers.length === 0) {
        // 如果没有疾控中心标记，重新绘制
        if (_cdcs && _cdcs.length > 0) {
            console.log('未找到疾控中心标记，重新绘制疾控中心');
            drawCdcs();
        }
        return;
    }
    
    let shownCount = 0;
    cdcMarkers.forEach(function(marker) {
        if (marker && marker.show) {
            marker.show();
            shownCount++;
        }
    });
    
    console.log('已显示', shownCount, '个疾控中心标记');
}

/**
 * 隐藏所有疾控中心
 */
function hideAllCdcs() {
    if (!cdcMarkers || cdcMarkers.length === 0) {
        console.log('没有找到疾控中心标记，无需隐藏');
        return;
    }
    
    let hiddenCount = 0;
    cdcMarkers.forEach(function(marker) {
        if (marker && marker.hide) {
            marker.hide();
            hiddenCount++;
        }
    });
    
    console.log('已隐藏', hiddenCount, '个疾控中心标记');
}

/**
 * 清除医院标记
 */
function clearHospitalMarkers() {
    if (hospitalMarkers && hospitalMarkers.length > 0) {
        hospitalMarkers.forEach(function(marker) {
            if (marker) {
                map.remove(marker);
            }
        });
        hospitalMarkers = [];
        console.log('已清除医院标记');
    }
}

/**
 * 清除血站标记
 */
function clearBloodstationMarkers() {
    if (bloodstationMarkers && bloodstationMarkers.length > 0) {
        bloodstationMarkers.forEach(function(marker) {
            if (marker) {
                map.remove(marker);
            }
        });
        bloodstationMarkers = [];
        console.log('已清除血站标记');
    }
}

/**
 * 清除疾控中心标记
 */
function clearCdcMarkers() {
    if (cdcMarkers && cdcMarkers.length > 0) {
        cdcMarkers.forEach(function(marker) {
            if (marker) {
                map.remove(marker);
            }
        });
        cdcMarkers = [];
        console.log('已清除疾控中心标记');
    }
}

/**
 * 更新地图资源统计信息
 */
function updateMapResourcesStats() {
    try {
        // 统计车辆数量
        let vehicleCount = 0;
        if (mapResourcesState.vehiclesVisible) {
            vehicleCount = getAllVisibleVehicles().length;
        }
        
        // 统计医院数量
        let hospitalCount = 0;
        if (mapResourcesState.hospitalsVisible) {
            hospitalCount = hospitalMarkers.length;
        }
        
        // 统计分站数量
        let stationCount = 0;
        if (mapResourcesState.stationsVisible) {
            stationCount = stationMarkers.length;
        }
        
        // 统计血站数量
        let bloodstationCount = 0;
        if (mapResourcesState.bloodstationsVisible) {
            bloodstationCount = bloodstationMarkers.length;
        }
        
        // 统计疾控中心数量
        let cdcCount = 0;
        if (mapResourcesState.cdcVisible) {
            cdcCount = cdcMarkers.length;
        }
        
        // 更新显示
        $('#vehicles-count').text(vehicleCount);
        $('#hospitals-count').text(hospitalCount);
        $('#stations-count').text(stationCount);
        $('#bloodstations-count').text(bloodstationCount);
        $('#cdc-count').text(cdcCount);
        
        // 根据状态显示/隐藏相应的统计项
        if (mapResourcesState.vehiclesVisible && vehicleCount > 0) {
            $('#vehicles-stats').show();
        } else {
            $('#vehicles-stats').hide();
        }
        
        if (mapResourcesState.hospitalsVisible && hospitalCount > 0) {
            $('#hospitals-stats').show();
        } else {
            $('#hospitals-stats').hide();
        }
        
        if (mapResourcesState.stationsVisible && stationCount > 0) {
            $('#stations-stats').show();
        } else {
            $('#stations-stats').hide();
        }
        
        if (mapResourcesState.bloodstationsVisible && bloodstationCount > 0) {
            $('#bloodstations-stats').show();
        } else {
            $('#bloodstations-stats').hide();
        }
        
        if (mapResourcesState.cdcVisible && cdcCount > 0) {
            $('#cdc-stats').show();
        } else {
            $('#cdc-stats').hide();
        }
        
        console.log('资源统计已更新:', {
            vehicle: vehicleCount,
            hospital: hospitalCount,
            station: stationCount,
            bloodstation: bloodstationCount,
            cdc: cdcCount
        });
        
    } catch (error) {
        console.error('更新地图资源统计信息错误:', error);
    }
}

/**
 * 获取所有可见的车辆
 */
function getAllVisibleVehicles() {
    try {
        if (!carMarkers || carMarkers.length === 0) {
            return [];
        }
        
        // 返回所有在地图上显示的车辆标记
        return carMarkers.filter(function(marker) {
            return marker && marker.getMap && marker.getMap();
        });
    } catch (error) {
        console.error('获取可见车辆错误:', error);
        return [];
    }
}
