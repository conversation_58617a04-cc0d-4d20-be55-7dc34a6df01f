// var fontEventStatas = true
// //数组扁平化处理
// if(!Array.flat){
//     Array.prototype.flat = function(){
//         this.result = []
//         this.forEach(item => {
//             if (Array.isArray(item)) {
//                 this.result = this.result.concat(item.flat())
//             } else {
//                 this.result.push(item)
//             }
//         })
//         return this.result
//     }
// }
// //处理树形数据格式
// function processingData(list, parentText, leve = 0) {
//     leve++
//     return list.map(r => {
//         r.leve = leve
//         r.text = r.t
//         if (parentText) {
//             r.text = parentText + '-' + r.t
//         }
//         r.id = r.text
//         if (r.c && Array.isArray(r.c)) {
//             r.children = processingData(r.c, r.text, leve)
//         }
//         return r
//     })
// }
// //多选框字体点击触发事件
// function fontEvent(ev) {
//     if(ev && Boolean(ev) && fontEventStatas){
//         let obj = query(morbidity, ev.value)
//         if (obj.children && obj.children[0]) {
//             loadRegionTree(obj.children, "#patientDiagnosislevel" + obj.leve)
//         } else {
//             $("#patientDiagnosislevel" + obj.leve).empty()
//         }
//     }
// }
// //查找多选框子数组
// function query(list, value) {
//     let children = []
//     let leve = 2
//     list.forEach(r => {
//         if (value == r.id) {
//             children = r.children || []
//             leve = r.leve + 1
//         } else if (r.children && Array.isArray(r.children)) {
//             let {
//                 children,
//                 leve
//             } = query(r.children, value)
//             children = children
//             leve = leve
//         }
//     })
//     return {
//         children,
//         leve
//     }
// }
// //多选框change事件
// function testcheange(ev) {
//     filterChecked(morbidity, ev.value, ev.checked)
// }
// //定义change事件位置
// function filterChecked(list, value, checked, leve = 0) {
//     leve++
//     list.forEach(r => {
//         if (r.id === value) {
//             setChecked(r, checked)
//         } else if (r.children && Array.isArray(r.children)) {
//             filterChecked(r.children, value, checked, leve)
//         }
//     })
// }

// //设置勾选与不勾选
// function setChecked(row, checked) {
//     let index = row.text.indexOf('-')
//     if (checked) {
//         if (index !== -1) {
//             let stata = true
//             let test = row.text.split('-')[0]
//             checkedList.forEach(r => {
//                 if (Array.isArray(r) && r[0] == test) {
//                     r.push(row.t)
//                     stata = false
//                 }
//             })
//             if (stata) {
//                 let i = checkedList.indexOf(test)
//                 if (i !== -1) {
//                     checkedList.splice(i, 1)
//                 }
//                 checkedList.push(row.text.split('-'))
//                 $('#' + test.replace("/","")).prop("checked", true);
//             }
//         } else {
//             checkedList.push(row.text)
//         }
//     } else {
//         if (index !== -1) {
//             checkedList.forEach((r, i) => {
//                 if (Array.isArray(r) && r[0] == row.text.split('-')[0]) {
//                     if (row.t == r[0] || r.length < 3) {
//                         checkedList.splice(i, 1)
//                         checkedList.push(row.text.split('-')[0])
//                     } else {
//                         let index = r.indexOf(row.t)
//                         r.splice(index, 1)
//                     }
//                 }
//             })
//         } else {
//             let stata = true
//             checkedList.forEach((r, i) => {
//                 if (Array.isArray(r) && r[0] == row.text.split('-')[0]) {
//                     stata = false
//                     r.forEach((y) => {
//                         $('#' + y.replace("/","")).prop("checked", false);
//                     })
//                     checkedList.splice(i, 1)
//                 }
//             })
//             if (stata) {
//                 checkedList.splice(index, 1)
//             }
//         }
//     }
//     if(showCheck && showCheck instanceof Function){
//         showCheck(processingParameterFormat(checkedList))
//     }
//     console.log(checkedList)
// }
// //处理传参格式
// function processingParameterFormat(list,symbol='-'){
//     return list.map(r=>{
//         if(Array.isArray(r)){
//             let jointStr = ''
//             let arr =  r.map((y,i)=>{
//                 if(i==0){
//                     jointStr = y
//                 } else{
//                     y = jointStr+symbol+y
//                 }
//                 return y
//             })
//             arr.splice(0,1)
//             return arr
//         }
//         return r
//     }).flat().join(' ')
// }
// //处理回显格式
// function processFormat(str){
//     if(!str){
//         return []
//     }
//     let result = []
//     let filter = []
//     str.split(',').forEach((r,i)=>{
//         if(r.indexOf('-') !== -1){
//             let partition =  r.split('-')
//             let index = result.indexOf(partition[0])
//             if(index < 0){
//                 result.push(partition[0])
//                 filter.push(partition)
//             } else{
//                 partition.shift()
//                 filter[index] = filter[index].concat(partition)
//             }
//         } else{
//             result.push(r)
//             filter.push(r)
//         }
//     })
//     return filter
// }
// //渲染多选框
// function loadRegionTree(list, el) {
//     let html = ''
//     list.forEach(r => {
//         html += `<div style="white-space:nowrap;display: inline-block;"><input type="checkbox" class="checked-input" id="${r.t.replace("/","")}" name="${r.t.replace("/","")}" value="${r.id}"  onchange="testcheange(this)" style="height: 1em;width: 1em;" /> <span class="checkSpan" onclick="fontEvent(${r.id})">${r.text}</span></div>`
//     })
//     $(el).html(html)
//     list.forEach(r => {
//         checkedList.forEach(y => {
//             if (Array.isArray(y)) {
//                 y.forEach(i => {
//                     if (i === r.t) {
//                         $('#' + r.t.replace("/","")).prop("checked", true);
//                     }
//                 })
//             } else if (y === r.t) {
//                 $('#' + r.t.replace("/","")).prop("checked", true);
//             }
//         })
//     })
// }


/**
 * 2023 12 15 之前存在bug，无法修复，重构多选框
 */
// //渲染多选框
function loadRegionTree(list, el) {
  let checkboxList = JSON.parse(list)
  let html = ''
  let isWhoChild = '' //记录当前渲染的子集 是谁的
  //console.log('查看第一次渲染时，数据是什么', checkedList)
  checkboxList.forEach(r => {
    if (checkedList.indexOf(r.t) != -1) {
      html += `<input type="checkbox" checked="true" style="margin-bottom:8px" name="${r.t}" title="${r.t}" lay-skin="primary" lay-filter="checkboxBQ">`
    } else {
      html += `<input type="checkbox" style="margin-bottom:8px" name="${r.t}" title="${r.t}" lay-skin="primary" lay-filter="checkboxBQ">`
    }
  })
  $(el).html(html)
  layui.form.render('checkbox');
  // 多选框勾选事件
  layui.use(['form'], function() {
    var form = layui.form;
    form.on('checkbox(checkboxBQ)', function(data){
      if (data.elem.checked) {
        if (checkedList.indexOf(data.elem.name) == -1) { //勾选的多选框丢进数组里，
          checkedList.push(data.elem.name)
        }
        // 判断下勾选的值有没有子集
        for (let i = 0; i < checkboxList.length;i++) {
          if (data.elem.name == checkboxList[i].t) { // 找到勾选数组里对应的值
            if (checkboxList[i].c) { // 并判断他是否有子集
              let htmlChild = ''
              checkboxList[i].c.forEach(item => {
                if (checkedList.indexOf(data.elem.name + '-' + item.t) != -1) {
                  htmlChild+= `<input type="checkbox" checked='true' style="margin-bottom:8px" name="${item.t}" title="${data.elem.name + '-' + item.t}" lay-skin="primary" lay-filter="checkboxBQChild">`
                } else {
                  htmlChild+= `<input type="checkbox" style="margin-bottom:8px" name="${item.t}" title="${data.elem.name + '-' + item.t}" lay-skin="primary" lay-filter="checkboxBQChild">`
                }
              })
              $('#patientDiagnosislevel2').html(htmlChild)
              isWhoChild = checkboxList[i].t // 记录当前渲染的是谁的子集
              layui.form.render('checkbox');
              // 子集的多选框勾选事件
              form.on('checkbox(checkboxBQChild)', function(dataChild){
                if (dataChild.elem.checked) { // 子集勾选的值一并丢进数组里
                  if (checkedList.indexOf(data.elem.name+'-'+dataChild.elem.name) == -1) {
                    checkedList.push(data.elem.name+'-'+dataChild.elem.name)
                  }
                } else { // 取消勾选的值 都删掉
                  if (checkedList.indexOf(data.elem.name+'-'+dataChild.elem.name) != -1) {
                    checkedList.splice(checkedList.indexOf(data.elem.name+'-'+dataChild.elem.name), 1)
                  }
                }
                // 点击事件完元素追加到呼叫原因下面的框
                let choosedText = checkedList.join(' ')
                $('#mainSuit').html(choosedText)
              });
            } else {
              $('#patientDiagnosislevel2').html('')
            }
          }
        }
      } else { //点击已勾选的值时，他的子集都清空 并且丢进数组里的值也要清空
        if (data.elem.name == isWhoChild) {
          $('#patientDiagnosislevel2').html('')
          isWhoChild = ''
        }
        if (checkedList.indexOf(data.elem.name) != -1) {
          checkedList.splice(checkedList.indexOf(data.elem.name), 1)
        }
      }
      // 点击事件完元素追加到呼叫原因下面的框
      let choosedText = checkedList.join(' ')
      $('#mainSuit').html(choosedText)
    });
  });
}