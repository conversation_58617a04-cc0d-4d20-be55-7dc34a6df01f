/* *****************************************************************
 * 重大事件上报
 * -----------------------------------------------------------------
 * Creator: 黄锦成
 * Created: 2024/1/9
 * -----------------------------------------------------------------
 * Modification:
 * Modifier:
 * Modified:
 * *****************************************************************/

//将yyyyMMddHHmmss转换为yyyy-MM-dd HH:mm:ss
function convertTimeToRead(reportTime) {
    let timeArr = reportTime.split('')
    let y = timeArr.slice(0, 4).join('')
    let m = timeArr.slice(4, 6).join('')
    let d = timeArr.slice(6, 8).join('')
    let hh = timeArr.slice(8, 10).join('')
    let mm = timeArr.slice(10, 12).join('')
    let ss = timeArr.slice(12, 14).join('')
    return y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
}
//将yyyyMMddHHmmss转换为yyyy-MM-dd HH:mm
function convertTimeToRead2(reportTime) {
    let timeArr = reportTime.split('')
    let y = timeArr.slice(0, 4).join('')
    let m = timeArr.slice(4, 6).join('')
    let d = timeArr.slice(6, 8).join('')
    let hh = timeArr.slice(8, 10).join('')
    let mm = timeArr.slice(10, 12).join('')
    let ss = timeArr.slice(12, 14).join('')
    return y + '-' + m + '-' + d + ' ' + hh + ':' + mm
}


//取当前时间，格式：y-m-d hh:mm:ss
function getNowTimeStr() {
    let nowTime = new Date()
    let y = nowTime.getFullYear()
    let m = nowTime.getMonth() + 1
    let d = nowTime.getDate()
    let hh = nowTime.getHours()
    let mm = nowTime.getMinutes()
    let ss = nowTime.getSeconds()
    let reportTime = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
    return reportTime;
}

//默认上报类型：没上报过时默认首报；上报过一条默认追报
function getDefaultReportTypeCode(itemList) {
    if (itemList == null || itemList.length == 0) {
        //没上报过时默认首报
        return "01";
    } else if (itemList.length > 0) {
        for (var i = 0; i < itemList.length; i++) {
            //有上报过终报，就不默认选中上报类型
            if (itemList[i].reportTypeCode === '03') {
                return '';
            }
        }
        //上报过一条默认追报
        return "02";
    } else {
        return "01";
    }
}

//重大事件首报的汇报内容（模板）
function createEventFirstReportContent(callInTimes, eventAddress, eventLevelName, eventTypeName) {
    let str = `在【${callInTimes}】接到来电，在【${eventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
    return str;
}
/**
 * 重大事件追报&终报的汇报内容（模板）
 * @param {any} reportTime 上报时间
 * @param {any} vehicleDispatchedNum 出动车辆数量
 * @param {any} woundedNum 伤员人数
 * @param {any} deadNum 死亡人数
 * @param {any} woundedSevereNum 重伤人数
 * @param {any} woundedSlightNum 轻伤人数
 * @param {any} transferNum 转送人数
 */
function createEventReportContent(reportTime, vehicleDispatchedNum, woundedNum, deadNum, woundedSevereNum, woundedSlightNum, transferNum) {
    let str = `截止至【${reportTime}】，共出动车辆【${vehicleDispatchedNum}】辆，伤员【${woundedNum}】人，死亡【${deadNum}】人，重伤【${woundedSevereNum}】人，轻伤【${woundedSlightNum}】人，转送【${transferNum}】人。`
    return str;
}
/*
//重大事件追报的汇报内容（模板）
function createEventContinuousReportContent(callInTimes, eventAddress, eventLevelName, eventTypeName) {
    //let str = `在【${callInTimes}】接到来电，在【${eventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
    let str = '';
    return str;
}

//重大事件终报的汇报内容（模板）
function createEventContinuousReportContent(callInTimes, eventAddress, eventLevelName, eventTypeName) {
    //let str = `在【${callInTimes}】接到来电，在【${eventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
    let str = '';
    return str;
}
*/
