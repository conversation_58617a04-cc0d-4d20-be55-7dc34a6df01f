/* *****************************************************************
 * 120EDC全局Javascript脚本
 * 兼容性：该脚本将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
 * -----------------------------------------------------------------
 * Creator: 陈蒋耀
 * Created: 2019/5/27
 * -----------------------------------------------------------------
 * Modification:
 * Modifier:
 * Modified:
 * *****************************************************************/

var _user = null; //用户数据
var _currentInCall = null; //当前来电信息
var _currentCallData = null; //当前呼入电话的实时信息
var _pSeat = null; //获取登录后的座席信息
var _taskMobileInterval = true; //车辆定时任务开关
var _taskMsgInterval = true; //消息定时任务开关
var _h5AVInviteInterval = true //指导视频H5音视频请求定时任务开关
var _noticeListInterval = true //通知定时任务开关
var _carVideoInterval = true; //车载视频任务开关
var _servertimeInterval = true; //网络延时任务开关
var _uploadAddInterval = true; //指导视频H5上报地址定时任务开关
var _taskCallStatusInterval = true; //分机状态定时任务开关
var _taskHeartbeatInterval = true; //心跳定时任务开关
var _refreshEventListInterval = true;//自动刷新事件列表开关
var _waitingFeedbackListInterval = true;//待反馈联动信息定时任务开关
var _successorList = [] //接班人字典列表
var _successorData = null //接班人数据
var _inCarVideoList = []// 车载视频进入的房间的信息
var _titleStates = 0 //tabel刷新状态
var _missedCallsData = [] //记录未接来电列表方便调用接口拿id
var backgroundColor = ''//记录事件列表背景色
var colorIndex = null//记录事件列表背景色下标
var _sentencesHospital = null //常用医院数据
var eventList = []
var mobileStatusList = []
var dispatchClassesList = [] // 出车班次
var allArr = []
var lastLoginUser //最后一次登录的用户和密码
var greatEventID = '' //重大事件ID
var greatEventWindow = null //重大事件弹窗
var confirmedMsgId = null   //重大事件取消ID
var _selectedRegionId = null
var _selectedRegion = null;
var _stationRegionList = []//分站列表
var assignTaskMode = '1' //当前系统配置(区分展示车/分站 1:展示车 2:展示分站)
var isCreatingEvent = true //是否正在创建事件
var assistType = null  //判断是座席派车还是协助分站派车   1:协助分站派车   2:座席派车（座席端直接派车给车辆，不经过分站确认）
var seatFontSize = null; //事件列表字体

var _isVideoSeat = false; //是否是视频座席
var _taskVideoStatusInterval = true; //视频线路定时任务开关

/** 事件监控列表首次加载是否成功 */
let isEventListFirstLoaded = false;
/** 主屏车辆列表/分站列表首次加载是否成功 */
let isMobilesOrStationsFirstLoaded = false;

/**
 * 是否模式1。
 * 座席派任务模式：1-座席派任务给车、2-座席派任务给分站。
 * 
 * 1、座席派任务给车，座席端界面要展示车辆。派任务又细分为2种情况：
 * （1）座席派任务给车，并指定人，不需要分站确认出车，判断分站isDispatch为 0；
 * （2）座席派任务给车，不指定人，由分站指定人并确认出车，判断分站isDispatch为 1
 * 
 * 2、座席派任务给分站，座席端界面展示分站。派任务现只有1种情况：
 * （1）座席派任务给分站，由分站指定车和人并确认出车
 */
function isAssignTaskModeCar() {
    return !assignTaskMode || assignTaskMode === '1'
}

/**
 * 是否模式2。
 * 座席派任务模式：1-座席派任务给车、2-座席派任务给分站。
 * 
 * 1、座席派任务给车，座席端界面要展示车辆。派任务又细分为2种情况：
 * （1）座席派任务给车，并指定人，不需要分站确认出车，判断分站isDispatch为 0；
 * （2）座席派任务给车，不指定人，由分站指定人并确认出车，判断分站isDispatch为 1
 * 
 * 2、座席派任务给分站，座席端界面展示分站。派任务现只有1种情况：
 * （1）座席派任务给分站，由分站指定车和人并确认出车
 */
function isAssignTaskModeStation() {
    return assignTaskMode === '2'
}

//初始化
$(document).ready(function () {
    showProcess(true, '温馨提示', '正在初始化数据，请稍后...');
    try {
        $('#version-number').html("版本号" + gProxy.getExeVersionInfo());
    } catch (e) {
        $('#version-number').html("版本号" + bsProxy.getExeVersionInfo());
        // B/S版本固定
    }

    //$('#eventDetailRegion').hide();
    isCreatingEvent = false
    //读取接口服务器基地址
    //_baseUrl= window.gProxy.getBaseUrl();

    getLastLoginUser()

    if (lastLoginUser != null) {
        //回填用户，密码
        $("#login-un").val(lastLoginUser.Username);
        if (lastLoginUser.SavePsw) {
            $("#is-save-psw").attr("checked", "checked");
            $("#login-pwd").val(lastLoginUser.Password);
        }
    }

    //网络延时
    getNetworkDelayTimeout();

    //获取座席的所有用户
    getAllSeatUser({},
        function (res) {
            $('#login-un').append('<option value="{0}">{1}</option>'.formatStr("", "请选择调度员"));
            _successorList = res
            //成功不做任何处理
            for (var i = 0; i < res.length; i++) {
                var d = res[i];
                if (lastLoginUser != null && d.username == lastLoginUser.Username) {
                    //回显登录用户
                    $('#login-un').append('<option value="{0}" selected="selected">{1}</option>'.formatStr(d.username, d.displayName + "[" + d.username + "]"));
                } else {
                    $('#login-un').append('<option value="{0}">{1}</option>'.formatStr(d.username, d.displayName + "[" + d.username + "]"));
                }
            }
            showProcess(false);
        },
        function (e, url, errMsg) {
            showProcess(false);
            //失败才做处理
            $.messager.alert('提示', '获取用户失败：' + errMsg);
        });

    // 获得座席列表
    getSeatsList({},
        function (res) {
            $('#login-seat').append('<option value="{0}">{1}</option>'.formatStr("", "请选择座席"));
            //成功不做任何处理
            for (var i = 0; i < res.length; i++) {
                var d = res[i];
                var seatStatusStr = getSeatStatusStr(d.seatStatus, "离线");;
                if (lastLoginUser != null && d.id == lastLoginUser.SeatId) {
                    //回显座席
                    $('#login-seat').append('<option value="{0}" selected="selected">{1}</option>'.formatStr(d.id, d.seatId + "[" + seatStatusStr + "]"));
                } else {
                    $('#login-seat').append('<option value="{0}">{1}</option>'.formatStr(d.id, d.seatId + "[" + seatStatusStr + "]"));
                }

            }
            checkActiveShiftSeat();
            showProcess(false);
        },
        function (e, url, errMsg) {
            showProcess(false);
            //失败才做处理
            $.messager.alert('提示', '获取座席列表失败：' + errMsg);
        });

    //获得班次列表
    getScheduleTimeConfigList({},
        function (res) {
            $('#login-schedule-time-config-id').append('<option value="{0}">{1}</option>'.formatStr("", "请选择班次"));
            //成功不做任何处理
            for (var i = 0; i < res.length; i++) {
                var d = res[i];
                //$('#login-schedule-time-config-id').append('<option value="{0}">{1}</option>'.formatStr(d.id, d.scheduleName + "[" + d.startTime + " - " + d.endTime + "]"));
                $('#login-schedule-time-config-id').append('<option value="{0}">{1}</option>'.formatStr(d.id, d.scheduleName));
            }
            showProcess(false);
        },
        function (e, url, errMsg) {
            showProcess(false);
            //失败才做处理
            $.messager.alert('提示', '获取班次列表失败：' + errMsg);
        });

    //登录界面选中座席的事件
    $('#login-seat').on('change', function () {
        checkActiveShiftSeat();
    })

    $('.key-item').on('click', function () {
        // 点击'0-9的数字按钮'，则对应的输入框输入对应的值
        var numVal = $(this)[0].children[0].innerText;
        if (!numVal || $('#soft-keyboard-input').val().length > 14 || numVal == '呼叫') return
        $('#soft-keyboard-input').val($('#soft-keyboard-input').val() + numVal);
    })

    $('#delPhone').on('click', function () {
        $('#soft-keyboard-input').val('');
    })
    $('.delPhone-line').on('click', function () {
        // 点击'删除按钮'，则输入框中的数据减少一位
        var val = $('#soft-keyboard-input').val(); // 原有的输入框的值，
        var newVal;
        if (val) {
            newVal = val.replace(/.$/, ''); // 去掉最后一位字符
            $('#soft-keyboard-input').val(newVal);
        }
    })
});

/**
 * 获取座席状态描述（中文） 0-离线；1-在线；2-挂起
 * @param {any} seatStatus 状态值
 * @param {any} defaultDesc 默认描述。如果不传，找不到对应的状态描述时返回空字符串
 */
function getSeatStatusStr(seatStatus, defaultDesc) {
    if (seatStatus === '0') {
        return '离线'
    } else if (seatStatus === '1') {
        return '在线';
    } else if (seatStatus === '2') {
        return '挂起'
    } else {
        if (defaultDesc) {
            return defaultDesc
        } else {
            return ''
        }
    }
}
/**
 * 获取分站登录状态中文描述：0-离线，1-在线，其他的未知
 * @param {any} isLogin
 */
function getStationIsLoginStr(isLogin) {
    var str = "未知";
    switch (isLogin) {
        case "0":
            str = "离线";
            break;
        case "1":
            str = "在线";
            break;
        default:
            str = "未知";
            break;
    }
    return str;
}

/** 获取最后一次登录的用户和密码 */
function getLastLoginUser() {
    try {
        lastLoginUser = evalJson(gProxy.getLastLoginUser());
        //console.log("获取最后一次登录的用户和密码:" + gProxy.getLastLoginUser());
    } catch (e) {
        //从cookie中读取最后一次登录的信息
        lastLoginUser = evalJson(bsProxy.getLastLoginUser());
    }
}

/** 登录界面检测是否有待交班的交接班 */
function checkActiveShiftSeat() {
    var seatId = $('#login-seat').val();
    if (!seatId) {
        //没选择调度台
        $('#login-schedule-time-config-id').val("");
        $("#login-un").val("");
        $("#login-pwd").val("");
    } else {
        getActiveShiftSeat({ "seatId": seatId, "offGoingId": "" },
            function (res) {
                if (res) {
                    //回填班次和登录名
                    $('#login-schedule-time-config-id').val(res.offGoingScheduleId);
                    $("#login-un").val(res.offGoingUsername);
                    //班次不能选、用户不能选
                    //$('#login-schedule-time-config-id').prop("disabled", true);
                    //$('#login-un').prop("disabled", true);
                    //回填密码
                    if (lastLoginUser && lastLoginUser.SavePsw && lastLoginUser.Username === res.offGoingUsername) {
                        $("#is-save-psw").attr("checked", "checked");
                        $("#login-pwd").val(lastLoginUser.Password);
                    } else {
                        $("#is-save-psw").attr("checked", "");
                        $("#login-pwd").val("");
                    }
                } else {
                    $('#login-schedule-time-config-id').val("");
                    $("#login-un").val("");
                    $("#login-pwd").val("");
                    $('#login-schedule-time-config-id').prop("disabled", false);
                    $('#login-un').prop("disabled", false);
                }
            },
            function (e, url, errMsg) {
                showProcess(false);
                //失败才做处理
                $.messager.alert('提示', '获取交接班数据失败：' + errMsg);
            });
    }
}

function numberFormatter(value, row, index) {
    return `<span style="color: #0AA0FB; cursor: pointer;" onclick="callingMissedPhone(${index})" title="拨打">${row.number}</span>`
}
/** 电话主页的左侧回拨按钮 */
function phoneButtonFormatter(value, row, index) {
    return `<img style='color: #0AA0FB; cursor: pointer;' onclick="callingMissedPhone(${index})" src="style/img/reCall.png" alt="" title="拨打">`
}
/**
 * 拨打未接来电电话号码（非通用方法）
 * @param {any} index
 */
function callingMissedPhone(index) {
    var number = _missedCallsData[index].number
    var id = _missedCallsData[index].id
    if (number == null || number == '') {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }
    $.messager.confirm("提示", "是否拨打电话" + number + "？", function (r) {
        if (r) {
            if (!_pSeat) {
                $.messager.alert('提示', '未配置呼出分机号，请先配置！');
                return null;
            }

            showProcess(true, '温馨提示', '正在拨打电话...');

            callPhone(_pSeat.callOut, number,
                function (res) {
                    showProcess(false);
                    //正在拨打电话
                    $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                });

            updateHandleStatusAndType({ id: id, handleStatus: '1' },
                function (res) {
                    showProcess(false);
                    missedCallsUpload()
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '保存通话记录失败' + errMsg, 'error');
                }
            )
        }
    });
}

function isAnswerFormatter(value, row, index) {
    switch (row.isAnswer) {
        case "0":
            return "未接听";
        case "1":
            return "已接听";
    }
}

function handleStatusFormatter(value, row, index) {
    switch (row.handleStatus) {
        case "0":
            return "无效";
        case "1":
            return "已处理";
        case "2":
            return "已合并";
        case "3":
            return "未处理";
    }
}
$('#left-list').tabs({
    onSelect: function (title, index) {
        _titleStates = index
        if (_token != "") {
            if (_titleStates) {
                refreshSelectSeatAndStationUpload(); //设置定时刷新座席和分站状态
            } else {
                missedCallsUpload() //未接来电信息更新
            }
            //   return confirm('Are you sure you want to close ' + title);
        }
    }
});
/** 初始化座席与分站状态数据（强制初始化，不管tab是否被选中） */
function initSeatStationStatusData() {
    // console.log('强制初始化座席与分站状态数据');
    
    // 检查表格是否存在
    if ($('#dg-seat-station-status-list').length === 0) {
        // console.log('座席与分站状态表格不存在，跳过初始化');
        return;
    }
    
    // 强制显示tab内容（临时显示以便初始化数据）
    var currentSelectedTab = $('#left-list').tabs('getSelected');
    var seatStationTabIndex = -1;
    var tabs = $('#left-list').tabs('tabs');
    for (var i = 0; i < tabs.length; i++) {
        if (tabs[i].panel('options').title === '座席与分站状态') {
            seatStationTabIndex = i;
            break;
        }
    }
    
    if (seatStationTabIndex >= 0) {
        // 临时选中座席与分站状态tab以便初始化数据
        $('#left-list').tabs('select', seatStationTabIndex);
        
        // 初始化数据
        refreshSelectSeatAndStation();
        
        // 恢复到原来选中的tab
        setTimeout(function() {
            if (currentSelectedTab && currentSelectedTab.panel('options').title !== '座席与分站状态') {
                var originalTabIndex = -1;
                for (var i = 0; i < tabs.length; i++) {
                    if (tabs[i].panel('options').title === currentSelectedTab.panel('options').title) {
                        originalTabIndex = i;
                        break;
                    }
                }
                if (originalTabIndex >= 0) {
                    $('#left-list').tabs('select', originalTabIndex);
                }
            }
        }, 100);
    }
}

/** 第一次获得刷新座席和分站状态 */
function refreshSelectSeatAndStation() {
    // 检查表格是否存在
    if ($('#dg-seat-station-status-list').length === 0) {
        // console.log('座席与分站状态表格不存在，跳过数据加载');
        return;
    }
    
    $('#dg-seat-station-status-list').datagrid('loadData', {
        total: 0,
        rows: []
    }); //清理数据
    //获取座席和分站状态
    selectSeatAndStation({}, function (data) {
        // 再次检查表格是否存在
        if ($('#dg-seat-station-status-list').length === 0) {
            // console.log('座席与分站状态表格在请求期间消失，跳过数据加载');
            return;
        }
        
        var numbersI = 0;

        //显示座席情况
        if (data.seatList.length > 0) {
            for (var i = 0; i < data.seatList.length; i++) {
                var seatStationStatusStatus = getSeatStatusStr(data.seatList[i].seatStatus, "未知");

                var seatStationStatusOtherInfo = "";
                switch (data.seatList[i].isBusy) {
                    case "0":
                        seatStationStatusOtherInfo = "线路空闲";
                        break;
                    case "1":
                        seatStationStatusOtherInfo = "线路忙";
                        break;
                    default:
                        break;
                }

                $('#dg-seat-station-status-list').datagrid('insertRow', {
                    index: i, // 索引从0开始
                    row: {
                        type: '1',
                        seatStationStatusName: data.seatList[i].seatId,
                        seatStationStatusStatus: seatStationStatusStatus,
                        seatStationStatusOtherInfo: seatStationStatusOtherInfo,
                        seatStatus: data.seatList[i].seatStatus,
                        isBusy: data.seatList[i].isBusy
                    }
                });
                numbersI = i;
            }

            //添加一行，作为座席和分站的分隔符
            numbersI += 1;
            $('#dg-seat-station-status-list').datagrid('insertRow', {
                index: numbersI, // 索引从0开始
                row: {
                    type: '0',
                    seatStationStatusName: "----------------------------",
                    seatStationStatusStatus: "--------",
                    seatStationStatusOtherInfo: "----------------------------",
                }
            });

        }

        //显示分站情况
        if (data.stationList.length > 0) {
            for (var i = 0; i < data.stationList.length; i++) {
                var seatStationStatusStatus = getStationIsLoginStr(data.stationList[i].isLogin);
                numbersI += 1;
                $('#dg-seat-station-status-list').datagrid('insertRow', {
                    index: numbersI, // 索引从0开始
                    row: {
                        type: '2',
                        seatStationStatusName: data.stationList[i].stationName,
                        seatStationStatusStatus: seatStationStatusStatus,
                        seatStationStatusOtherInfo: "",
                    }
                });
            }
        }
        
        // console.log('座席与分站状态数据加载完成，座席数：' + (data.seatList ? data.seatList.length : 0) + '，分站数：' + (data.stationList ? data.stationList.length : 0));
    }, function (e, url, errMsg) {
        console.error('获取座席与分站状态数据失败：', errMsg);
    });
}

/**
 * 格式化日期为 yyyy-MM-dd HH:mm:ss
 * @param {any} date
 */
let formatDate = (date) => {
    let pad = (num) => num.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

/** 未接来电更新 */
function missedCallsUpload() {
    // 检查未接来电表格是否存在且可见
    if ($('#missedCalls').length === 0 || $('#missedCalls').is(':hidden')) {
        // console.log('未接来电表格不存在或已隐藏，跳过更新');
        return;
    }

    let now = new Date();
    let hours8Ago = new Date(now - 8 * 60 * 60 * 1000);

    var params = {
        "callType": "IN",
        "isAnswer": '0',
        "handleStatus": '3',
        "beginCallTime": formatDate(hours8Ago),
        "endCallTime": formatDate(now)
    };
    callAnswerList(params, function (data) {
        // 再次检查元素是否存在，防止在异步请求期间被隐藏
        if ($('#missedCalls').length === 0 || $('#missedCalls').is(':hidden')) {
            // console.log('未接来电表格在请求期间被隐藏，跳过数据更新');
            return;
        }

        let wornData = $('#missedCalls').datagrid('getData').rows
        if (data.content && data.content[0]) {
            _missedCallsData = data.content || []
            for (var i = 0; i < data.content.length; i++) {
                if (data.content.length == wornData.length) {
                    setMissedCalls('updateRow', i, data.content[i])
                } else if (data.content.length > wornData.length) {
                    setMissedCalls('insertRow', i, data.content[i])
                } else if (data.content.length < wornData.length) {
                    setMissedCalls('deleteRow', i)
                }
            }
        } else {
            //如果没有返回数据则空清空数据
            $('#missedCalls').datagrid('loadData', {
                total: 0,
                rows: []
            });
        }
    }, function (e, url, errMsg) {
        //失败才做处理
        $.messager.alert('异常', '获取未接来电，异常信息：' + errMsg);
        setTimeout(function () {
            $(".messager-body").window('close', true);
        }, 1000);
    });
}
/** 设置添加更新未接来电列表 */
function setMissedCalls(type, i, row) {
    // 检查未接来电表格是否存在且可见
    if ($('#missedCalls').length === 0 || $('#missedCalls').is(':hidden')) {
        // console.log('未接来电表格不存在或已隐藏，跳过数据操作');
        return;
    }

    if (type !== 'deleteRow') {
        $('#missedCalls').datagrid(type, {
            index: i, // 索引从0开始
            row: {
                phoneButton: '',
                id: row.id,
                number: row.number,
                toNumber: row.toNumber,
                isAnswer: row.isAnswer,
                callTime: row.callTime,
                handleStatus: row.handleStatus
            }
        })
    } else {
        $('#missedCalls').datagrid(type, i)
    }
}
/** 座席分站信息更新 */
function refreshSelectSeatAndStationUpload() {
    // 检查座席与分站状态表格是否存在且可见
    if ($('#dg-seat-station-status-list').length === 0 || $('#dg-seat-station-status-list').is(':hidden')) {
        // console.log('座席与分站状态表格不存在或已隐藏，跳过更新');
        return;
    }

    //获取座席和分站状态
    selectSeatAndStation({}, function (data) {
        // 再次检查元素是否存在，防止在异步请求期间被隐藏
        if ($('#dg-seat-station-status-list').length === 0 || $('#dg-seat-station-status-list').is(':hidden')) {
            // console.log('座席与分站状态表格在请求期间被隐藏，跳过数据更新');
            return;
        }

        //显示座席情况
        if (data.seatList.length > 0) {
            for (var i = 0; i < data.seatList.length; i++) {
                var seatStationStatusStatus = getSeatStatusStr(data.seatList[i].seatStatus, "未知");

                var seatStationStatusOtherInfo = ""
                switch (data.seatList[i].isBusy) {
                    case "0":
                        seatStationStatusOtherInfo = "线路空闲";
                        break;
                    case "1":
                        seatStationStatusOtherInfo = "线路忙";
                        break;
                    default:
                        break;
                }

                var rows = $("#dg-seat-station-status-list").datagrid("getRows");
                for (var j = 0; j < rows.length; j++) {
                    if (rows[j].seatStationStatusName == data.seatList[i].seatId) {
                        let needUpdate = false;
                        let updateRowParam = {}
                        if (seatStationStatusStatus != rows[j].seatStationStatusStatus) { //当状态改变的时候修改状态
                            updateRowParam.seatStationStatusStatus = seatStationStatusStatus
                            needUpdate = true;
                        }
                        if (seatStationStatusOtherInfo != rows[j].seatStationStatusOtherInfo) { //当线路改变的时候修改线路状态
                            updateRowParam.seatStationStatusOtherInfo = seatStationStatusOtherInfo
                            needUpdate = true;
                        }
                        //修改
                        if (needUpdate) {
                            $("#dg-seat-station-status-list").datagrid("updateRow", {
                                index: j,
                                row: updateRowParam
                            });
                        }

                        /*if (seatStationStatusStatus != rows[j].seatStationStatusStatus) { //当状态改变的时候修改状态
                            //修改状态
                            $("#dg-seat-station-status-list").datagrid("updateRow", {
                                index: j,
                                row: {
                                    seatStationStatusStatus: seatStationStatusStatus
                                }
                            });
                        }
                        if (seatStationStatusOtherInfo != rows[j].seatStationStatusOtherInfo) { //当线路改变的时候修改线路状态
                            //修改状态
                            $("#dg-seat-station-status-list").datagrid("updateRow", {
                                index: j,
                                row: {
                                    seatStationStatusOtherInfo: seatStationStatusOtherInfo
                                }
                            });
                        }*/
                        break;
                    }
                }

            }

        }
        //显示分站情况
        if (data.stationList.length > 0) {
            for (var i = 0; i < data.stationList.length; i++) {
                var seatStationStatusStatus = getStationIsLoginStr(data.stationList[i].isLogin);
                var rows = $("#dg-seat-station-status-list").datagrid("getRows");
                for (var j = 0; j < rows.length; j++) {
                    if (rows[j].seatStationStatusName == data.stationList[i].stationName) {
                        if (seatStationStatusStatus != rows[j].seatStationStatusStatus) { //当状态改变的时候修改状态
                            //修改状态
                            $("#dg-seat-station-status-list").datagrid("updateRow", {
                                index: j,
                                row: {
                                    seatStationStatusStatus: seatStationStatusStatus
                                }
                            });
                        }
                        break;
                    }
                }
            }
        }

    }, function (e, url, errMsg) {

    });
}

/**
 * 设置添加更新车辆列表数据
 * @param {any} type 取值为：insertRow/updateRow/deleteRow
 * @param {any} i
 * @param {any} row
 */
function setMobielsList(type, i, row) {
    // 检查车辆列表是否存在且可见
    if ($('#dg-mobiels-list').length === 0 || $('#vehicleList').is(':hidden')) {
        // console.log('车辆列表不存在或已隐藏，跳过数据操作');
        return;
    }

    if (type !== 'deleteRow') {
        $('#dg-mobiels-list').datagrid(type, {
            index: i, // 索引从0开始
            row: {
                carId: row.id,
                seatId: row.seatId,
                stationName: row.stationName,
                stationCode: row.stationCode,
                carName: row.carName,
                carType: row.carType,
                plateNum: row.plateNum,
                dispatchDoctor: row.dispatchDoctor,
                dispatchNurse: row.dispatchNurse,
                dispatchDriver: row.dispatchDriver,
                mobileStatusStr: row.mobileStatusStr,
                mobileStatus: row.mobileStatus,
                statusStr: row.statusStr,
                status: row.status,
                h5AVInvite: _inCarVideoList.indexOf(row.currentProcessId) != -1 ? '1' : '0', // 1就是有人进入视频通话 就亮起来
                sendTime: row.sendTime,
                isDispatch: row.isDispatch,
                outOfTime: row.outOfTime,
                contact: row.contact,
                currentProcessId: row.currentProcessId,
                address: row.address,
                remark: row.remark,
                lng: row.lng,
                lat: row.lat,
                hCondition: row.hCondition,
                mobileToEventDistance: row.mobileToEventDistance ? row.mobileToEventDistance.toFixed(1) + "km" : "",
                mobileToEventMapDistance: row.mobileToEventMapDistance ? row.mobileToEventMapDistance.toFixed(1) + "km" : "",
                currentEventId: row.currentEventId,
            }
        })
    } else {
        $('#dg-mobiels-list').datagrid(type, i)
    }
}

/** JSON.stringify对象序列化，解决undefined、函数和NaN 丢失问题 解决\n符号换行后副屏解析报错问题 */
function JSONStringify(option) {
    return JSON.stringify(option, (key, val) => {
        if (typeof val === 'string') {
            val = val.replace(/\n/g, "")
        }
        // 处理undefined丢失问题
        if (typeof val === 'undefined') {
            return 'undefined';
        }
        // 处理NaN转为null的情况（注意： 这里如果使用isNaN的话，那么对象也会走进去）
        if (val !== val) {
            return `${val}`
        }
        return val;
    })
}

/**
 * 同步车辆数据给第二屏幕
 */
function syncMobilesToSecondScreen() {

    var params = {}
    //计算距离获取的经纬度
    // var eventRows = $('#dg-event-list').datagrid('getSelections');
    // var num = eventRows.length;
    // if (num == 1) {
    //     if (eventRows[0].lat != null && eventRows[0].lng != null) {
    //         params.eventLat = eventRows[0].lat
    //         params.eventLng = eventRows[0].lng
    //     }
    // }
    // console.log("选中事件的经纬度 = ", params)

    let mapCurrentLocLngLat = null;
    try {
        mapCurrentLocLngLat = gProxy.getMapCurrentLocLngLat();
    } catch (e) {
        // B/S模式
        mapCurrentLocLngLat = bsProxy.getMapCurrentLocLngLat();
    }
    if (mapCurrentLocLngLat) {
        let mapCurrentLocLngLatJson = JSON.parse(mapCurrentLocLngLat);
        console.log("获取副屏幕地图当前的位置经纬度 = ", mapCurrentLocLngLatJson)
        if (mapCurrentLocLngLatJson.lng && mapCurrentLocLngLatJson.lat) {
            params.eventLng = mapCurrentLocLngLatJson.lng
            params.eventLat = mapCurrentLocLngLatJson.lat
        }
    }

    getMobilesStatusListWithDistance(params, function (data) {
        //同步车辆数据给第二屏幕
        try {
            gProxy.syncMobiles(JSONStringify(data));
        } catch (e) {
            // B/S模式
            bsProxy.syncMobiles(JSONStringify(data));
        }
    });
}

/**
 * 同步车辆数据给第二屏幕（创建事件页面会调用该方法）
 * @param {*} callback 
 */
function refreshMobileStatusCallBack(callback) {
    getMobilesStatusListWithDistance({}, function (data) {
        //同步车辆数据给第二屏幕
        try {
            gProxy.syncMobiles(JSONStringify(data));
        } catch (e) {
            // B/S模式
            bsProxy.syncMobiles(JSONStringify(data));
        }

        if (callback) {
            callback();
        }
    });
}

/**
 * 应用主屏配置（从localStorage获取）
 */
function applyMainScreenConfigFromStorage() {
    try {
        var subScreenConfig = localStorage.getItem('subScreenConfig');
        if (subScreenConfig) {
            var config = JSON.parse(subScreenConfig);
            if (config && config.mainScreen) {
                console.log('从localStorage应用主屏配置:', config.mainScreen);
                
                // 如果在index.html中，直接调用applyMainScreenConfig
                if (typeof applyMainScreenConfig === 'function') {
                    applyMainScreenConfig(config.mainScreen);
                } else {
                    // 如果不在index.html中，手动应用配置
                    applyMainScreenConfigManually(config.mainScreen);
                }
            } else {
                console.log('未找到主屏配置，使用默认显示');
            }
        } else {
            console.log('未找到屏幕配置，使用默认显示');
        }
    } catch (e) {
        console.error('应用主屏配置失败:', e);
    }
}

/**
 * 手动应用主屏配置（用于非index.html页面）
 * @param {Object} mainScreenConfig - 主屏配置对象
 */
function applyMainScreenConfigManually(mainScreenConfig) {
    console.log('手动应用主屏配置:', mainScreenConfig);
    
    try {
        // 左侧功能配置
        if (mainScreenConfig.leftSide) {
            var leftConfig = mainScreenConfig.leftSide;
            
            // 消息提醒区域
            if (leftConfig.showMessageReminder === false) {
                $('#messageArea').hide();
            }
            
            // 未接来电tab
            if (leftConfig.showMissedCalls === false) {
                $('#left-list').tabs('close', '未接来电');
            }
            
            // 座席与分站状态tab
            if (leftConfig.showSeatStationStatus === false) {
                $('#left-list').tabs('close', '座席与分站状态');
                console.log('隐藏座席与分站状态tab');
            } else {
                console.log('显示座席与分站状态tab');
                // 如果座席与分站状态tab可见，确保数据已初始化
                setTimeout(function() {
                    if ($('#dg-seat-station-status-list').length > 0) {
                        initSeatStationStatusData();
                    }
                }, 100);
            }
        }
        
        // 右侧功能配置
        if (mainScreenConfig.rightSide) {
            var rightConfig = mainScreenConfig.rightSide;
            
            // 事件列表
            if (rightConfig.showEventList === false) {
                $('#eventList').hide();
                if (rightConfig.showVehicleList !== false) {
                    $('#vehicleList').css('height', 'calc(100% - 5px)');
                    $('#dragSeparator').hide();
                }
            }
            
            // 车辆列表
            if (rightConfig.showVehicleList === false) {
                $('#vehicleList').hide();
                if (rightConfig.showEventList !== false) {
                    $('#eventList').css('height', 'calc(100% - 5px)');
                    $('#dragSeparator').hide();
                }
            }
        }
        
        // 重新调整布局
        setTimeout(function() {
            try {
                // 只对可见的表格进行resize操作
                if ($('#dg-event-list').length && $('#dg-event-list').is(':visible')) $('#dg-event-list').datagrid('resize');
                if ($('#dg-mobiels-list').length && $('#dg-mobiels-list').is(':visible')) $('#dg-mobiels-list').datagrid('resize');
                if ($('#station-list').length && $('#station-list').is(':visible')) $('#station-list').datagrid('resize');
                if ($('#missedCalls').length && $('#missedCalls').is(':visible')) $('#missedCalls').datagrid('resize');
                if ($('#dg-seat-station-status-list').length && $('#dg-seat-station-status-list').is(':visible')) $('#dg-seat-station-status-list').datagrid('resize');
                if ($('#dl-msg-list').length && $('#dl-msg-list').is(':visible')) $('#dl-msg-list').datalist('resize');
                $(window).trigger('resize');
            } catch(ex) {
                console.warn('调整表格大小时出错:', ex);
            }
        }, 100);
        
    } catch (e) {
        console.error('手动应用主屏配置时出错:', e);
    }
}

/**
 * 刷新车辆列表或分站列表，通过 assignTaskMode 来区分
 */
function refreshMobilesOrStations() {
    //计算记录分站部分
    if (isAssignTaskModeStation()) {
        //获取分站列表
        loadStationList()
    } else {
        // 检查车辆列表是否存在且可见
        if ($('#dg-mobiels-list').length === 0 || $('#vehicleList').is(':hidden')) {
            // console.log('车辆列表不存在或已隐藏，跳过刷新');
            return;
        }

        // $('#dg-mobiels-list').datagrid('loading'); //打开进度条：打开等待div

        var statusList = []
        if ($('#mobile-status-all').is(':checked')) {
            statusList.push($('#mobile-status-all').val());
        }
        if ($('#mobile-status-await-orders').is(':checked')) {
            statusList.push($('#mobile-status-await-orders').val());
        }
        if ($('#mobile-status-running-task').is(':checked')) {
            statusList.push($('#mobile-status-running-task').val());
        }
        if ($('#mobile-status-pause').is(':checked')) {
            statusList.push($('#mobile-status-pause').val());
        }
        if ($('#mobile-status-not-on-duty').is(':checked')) {
            statusList.push($('#mobile-status-not-on-duty').val());
        }

        $('#refreshMobilesOrStationsBtn').attr('disabled', 'disabled'); //按钮变成不能使用

        //拿到原来的列表数据
        let wornData = $('#dg-mobiels-list').datagrid('getData')

        var params = {};
        params.statusList = statusList
        if ($('#plateNum').val()) {
            params.plateNum = $('#plateNum').val()
        }
        if ($('#stationRegionId').val()) {
            if ($('#stationRegionId').val() == "全部") {
                params.stationRegionId = "";
            } else {
                params.stationRegionId = $('#stationRegionId').combobox('getValue');
            }
        }
        if ($('#stationName').val()) {
            if ($('#stationName').val() == "全部") {
                params.stationName = "";
            } else {
                params.stationName = $('#stationName').combobox('getValue');
            }
        }

        //计算距离获取的经纬度
        var eventRows = $('#dg-event-list').datagrid('getSelections');
        var num = eventRows.length;
        if (num == 1) {
            if (eventRows[0].lat != null && eventRows[0].lng != null) {
                params.eventLat = eventRows[0].lat
                params.eventLng = eventRows[0].lng
            }
        }

        //获取本站车辆信息
        getMobilesStatusListWithDistance(params, function (data) {
            // 再次检查元素是否存在，防止在异步请求期间被隐藏
            if ($('#dg-mobiels-list').length === 0 || $('#vehicleList').is(':hidden')) {
                // console.log('车辆列表在请求期间被隐藏，跳过数据更新');
                // $('#dg-mobiels-list').datagrid('loaded'); //关闭loding进度条；
                $('#refreshMobilesOrStationsBtn').removeAttr('disabled'); //按钮变成可用
                return;
            }

            /*
            //刷新所有车辆
            $('#dg-mobiels-list').datagrid('loadData', { total: 0, rows: [] }); //清理数据

            for (var i = 0; i < data.length; i++) {
                setMobielsList('insertRow', i, data[i])
            }
            mobileStatusList = data
            */

            //列表有数据了才更新列表
            if (data[0]) {
                mobileStatusList = data

                //先删除数据（新查询出来的数据比列表里的少）
                if (data.length < wornData.total) {
                    //删除多余的几条，从尾部开始删除
                    for (var i = wornData.total - 1; i >= data.length; i--) {
                        // console.log("----------删除数据 " + i + "----------总记录数" + data.length + "----------原总记录数 " + wornData.total);
                        setMobielsList('deleteRow', i)
                    }
                    //console.log("重新当前事件任务数据：" + wornData.length)
                }

                for (var j = 0; j < data.length; j++) {
                    //insertRow/updateRow/deleteRow操作后，wornData的数据量会实时变化
                    if (data.length == wornData.total) {
                        setMobielsList('updateRow', j, data[j])
                        // console.log("----------更新数据 " + j + "----------总记录数" + data.length + "----------原总记录数 " + wornData.total);
                    } else if (data.length > wornData.total) {
                        setMobielsList('insertRow', j, data[j])
                        // console.log("----------新增数据 " + j + "----------总记录数" + data.length + "----------原总记录数 " + wornData.total);
                    } else if (data.length < wornData.total) {
                        setMobielsList('deleteRow', j)
                        // console.log("----------删除数据 " + j + "----------总记录数" + data.length + "----------原总记录数 " + wornData.total);
                    }
                }
            } else {
                //如果没有返回数据则空清空数据
                $('#dg-mobiels-list').datagrid('loadData', {
                    total: 0,
                    rows: []
                });
            }
            
            // 检查元素是否存在再更新计数
            if ($('#mobile_counts').length > 0) {
                $('#mobile_counts').html(data.length);
            }

            // $('#dg-mobiels-list').datagrid('loaded'); //关闭loding进度条；
            $('#refreshMobilesOrStationsBtn').removeAttr('disabled'); //按钮变成可用

            //主屏车辆列表/分站列表首次加载成功
            if (!isMobilesOrStationsFirstLoaded) {
                isMobilesOrStationsFirstLoaded = true;
            }
        }, function (e, url, errMsg) {
            // $('#dg-mobiels-list').datagrid('loaded'); //关闭loding进度条；
            $('#refreshMobilesOrStationsBtn').removeAttr('disabled'); //按钮变成可用
            //失败才做处理
            $.messager.alert('异常', '获取分站车辆，异常信息：' + errMsg);
        });
    
    }
   
};
/** 刷新车辆视频列的实时信息，判断是否有新的视频请求（车头平板） */
function updateCarVideoLineTimeout() {
    clearTimeout(window.setIntervalUpdateCarLine);
    if (_carVideoInterval) {
        window.setIntervalUpdateCarLine = null
        var params = {};
        // 获取新的数据值
        getProcessIdByCanVideo(params, function (data) {
            // 检查车辆列表是否存在且可见
            if ($('#dg-mobiels-list').length === 0 || $('#vehicleList').is(':hidden')) {
                // console.log('车辆列表不存在或已隐藏，跳过视频状态更新');
                return;
            }

            _inCarVideoList = data
            for (var i = 0; i < mobileStatusList.length; i++) {
                if (data.indexOf(mobileStatusList[i].currentProcessId) != -1) {
                    $('#dg-mobiels-list').datagrid('updateRow', {
                        index: i,
                        row: {
                            h5AVInvite: '1'
                        }
                    });
                    mobileStatusList[i].h5AVInvite = '1'
                } else {
                    $('#dg-mobiels-list').datagrid('updateRow', {
                        index: i,
                        row: {
                            h5AVInvite: '0'
                        }
                    });
                    mobileStatusList[i].h5AVInvite = '0'
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取车头平板视频请求，异常信息：' + errMsg);
        });

        window.setIntervalUpdateCarLine = setTimeout(updateCarVideoLineTimeout, 3000)
    }
}

/** 获得消息信息（定时获取） */
function refreshMsgInfolist() {
    //获取正在进行的事件的列表
    getMsgInfo({ seatId: _pSeat.id }, function (data) {
        let alreadyHaveId = $("#dl-msg-list").datagrid("getRows").map(r => r.processId)
        let dlMsgIdList = []
        //获取正在进行的事件的列表
        var modifiedArray = data.getH5AVInviteVoList.map(function (item) {
            return {
                processId: String(item.taskId),
                callsUserName: item.callsUserName,
                type: 'H5'
            };
        });
        var carModifiedArray = data.seatOnlineRoomVoList.map(function (item) {
            return {
                processId: String(item.taskId),
                carName: item.carName,
                type: 'CAR'
            };
        });
        // 如果车载或者H5视频有数据 就声音提示
        if (modifiedArray.length > 0 || carModifiedArray.length > 0) {
            const audio = document.getElementById('audioPlay');
            audio.play();
        }
        allArr = [...carModifiedArray, ...modifiedArray, ...data.mobileProcessVoList]
        for (let i = 0; i < allArr.length; i++) {
            if (allArr[i].type && allArr[i].type == 'H5') {
                let { callsUserName, processId } = allArr[i]
                dlMsgIdList.push(processId)
                if (alreadyHaveId.indexOf(processId) == -1) {
                    $('#dl-msg-list').datalist('insertRow', { index: 0, row: { text: dlMsgRow2(processId, callsUserName, i), processId } });
                }
            } else if (allArr[i].type && allArr[i].type == 'CAR') {
                let { carName, processId } = allArr[i]
                dlMsgIdList.push(processId)
                if (alreadyHaveId.indexOf(processId) == -1) {
                    $('#dl-msg-list').datalist('insertRow', { index: 0, row: { text: dlMsgRow3(processId, carName, i), processId } });
                }
            } else {
                let { processId, schedulingTime, toStatus, plateNum, stationName, carName } = allArr[i]
                let year = schedulingTime.slice(0, 4);
                let month = schedulingTime.slice(5, 7);
                let day = schedulingTime.slice(8, 10);
                let hour = schedulingTime.slice(11, 13);
                let minute = schedulingTime.slice(14, 16);
                let second = schedulingTime.slice(-2);
                let timer = leftTimer(parseInt(year), parseInt(month), parseInt(day), parseInt(hour), parseInt(minute), parseInt(second))
                dlMsgIdList.push(processId)
                if (alreadyHaveId.indexOf(processId) !== -1) {
                    $("#li-msg-" + processId + "").html(dlMsgRow(processId, timer, toStatus, plateNum ? plateNum : "", stationName, carName ? carName : ""));
                } else {
                    $('#dl-msg-list').datalist('appendRow', { text: dlMsgRow(processId, timer, toStatus,  plateNum ? plateNum : "", stationName, carName ? carName : ""), processId });
                }
            }
        }
        //检查是否有要删除
        alreadyHaveId = $("#dl-msg-list").datagrid("getRows").map(r => r.processId)
        let deleteRow = []
        alreadyHaveId.forEach((r, i) => {
            if (dlMsgIdList.indexOf(r) == -1) {
                deleteRow.push(i)
            }
        })
        deleteRow.reverse().forEach(r => {
            $('#dl-msg-list').datalist('deleteRow', r); //删除行
        })
        alreadyHaveId = null
        dlMsgIdList = null
    }, function (e, url, errMsg) { });
}

/** 呼叫与消息每行处理 */
function dlMsgRow(processId, timer, toStatus, plateNum, stationName, carName) {
    return '<li id="li-msg-' + processId + '" style="white-space:nowrap;list-style: none;color: #fff;line-height:30px;height:30px;background-color: ' + (toStatus == 5 ? '#ec0808;' : toStatus == 7 ? '#ffab04' : '#48c400;') + '" value=' + plateNum + '><span style="margin-left: 10px;">' + plateNum + '[' + stationName + '][' + carName + ']</span><span style="margin-right: 10px;">' + timer + '</span><span style="margin-right: 10px;">' + (toStatus == 5 ? '受理中' : toStatus == 7 ? '待派车' : '已派车') + '</span></li>';
}
function dlMsgRow2(processId, callsUserName, i) {
    return `<li onclick="javascript:openVideoPage(${i})" id="li-msg-${processId}" style="white-space:nowrap;list-style: none;color: #fff;line-height:30px;height:30px;background-color: #EE9200;padding-left: 9px;font-size: 15px;cursor: pointer;display:flex; align-items:center;justify-content:space-between"><div>${callsUserName} 通过移动端发起了远程视频邀请</div><img style="margin-right:5px;width:20px;height:20px" src="style/img/hasVideo.gif"/></li>`;
}
function dlMsgRow3(processId, carName, i) {
    return `<li onclick="javascript:openCarVideoPage(${i})" id="li-msg-${processId}" style="white-space:nowrap;list-style: none;color: #fff;line-height:30px;height:30px;background-color: #EE9200;padding-left: 9px;font-size: 15px;cursor: pointer;display:flex; align-items:center;justify-content:space-between"><div>车牌号${carName} 通过车载平板发起了远程视频邀请</div><img src="style/img/hasVideo.gif" style="margin-right:5px;width:20px;height:20px"/></li>`;
}
/** 打开与指导短信h5进行视频的web界面 */
function openVideoPage(i) {
    let newStr = allArr[i].processId.slice(2);
    querySysConf('pphi_vedio_web_url',
        function (data) {
            //data值例子：https://*************:18085/dev-3d/#/Telemedicine
            let url = data + '?answerUserName=' + _pSeat.seatId + '&answerUserId=' + _pSeat.id + '&taskId=' + newStr;
            try {
                gProxy.openBrowser(url);
            } catch (e) {
                window.open(url, '_blank');
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
        });
}
/** 打开与车载pad进行视频的web界面 */
function openCarVideoPage(i) {
    let newStr = allArr[i].processId.slice(2);
    querySysConf('car_vedio_web_url',
        function (data) {
            // 本地调试地址
            // let data = 'http://localhost:8888/dev-3d/#/Consultation'
            //data值例子：https://*************:18085/dev-3d/#/Consultation
            let url = data + '?token=' + encodeURIComponent(_token) + '&seatId=' + _pSeat.id + "&taskId=" + newStr;
            try {
                gProxy.openBrowser(url);
            } catch (e) {
                window.open(url, '_blank');
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
        });
}

function homeScreenListRefreshes() {
    refreshEventlist()
}

/** 刷新主屏事件监控列表 */
function refreshEventlist() {
    //$('#dg-event-list').datagrid('loading'); //打开进度条：打开等待div
    var params = [];
    if ($('#event-status-all').is(':checked')) {
        params.push($('#event-status-all').val());
    }
    if ($('#event-status-unscheduled').is(':checked')) {
        params.push($('#event-status-unscheduled').val());
    }
    if ($('#event-status-scheduled').is(':checked')) {
        params.push($('#event-status-scheduled').val());
    }
    if ($('#event-status-appointment').is(':checked')) {
        params.push($('#event-status-appointment').val());
    }
    if ($('#event-status-waiting').is(':checked')) {
        params.push($('#event-status-waiting').val());
    }
    //获取正在进行的事件的列表
    $('#refresh-event-btn').attr('disabled', 'disabled'); //按钮变成不能使用
    getEventProcessList(params, _pSeat.id, function (data) {
        eventList = data
        //$('#dg-event-list').datagrid('loadData', { total: 0, rows: [] }); //清理数据
        if (data && data.length > 0) {
            let wornData = $('#dg-event-list').datagrid('getData').rows || []

            //原来选中的行
            let rowsSelected = $('#dg-event-list').datagrid('getSelections')
            //console.log("==========行数：" + rowsSelected.length)
            let eventIdSelected, processIdSelected
            if (rowsSelected[0]) {
                //console.log("==========原来选中的行，事件id：" + rowsSelected[0].eventId + "，任务id：" + rowsSelected[0].processId)
                eventIdSelected = rowsSelected[0].eventId
                processIdSelected = rowsSelected[0].processId
            }
            $('#dg-event-list').datagrid('clearSelections')
            //console.log("==========清除选中后，行数：" + rowsSelected.length)

            //先删除数据（新查询出来的数据比列表里的少）
            if (data.length < wornData.length) {
                //删除多余的几条，从尾部开始删除
                for (var i = wornData.length - 1; i >= data.length; i--) {
                    //console.log("删除事件任务数据：" + i)
                    setEventList('deleteRow', i)
                }
                //console.log("重新当前事件任务数据：" + wornData.length)
            }

            //insertRow / updateRow / deleteRow操作后，wornData的数据量会实时变化

            for (var i = 0; i < data.length; i++) {
                if (data.length == wornData.length) {
                    //console.log("更新事件任务数据：" + i)
                    setEventList('updateRow', i, data[i])
                } else if (data.length > wornData.length) {
                    //console.log("插入事件任务数据：" + i)
                    setEventList('insertRow', i, data[i])
                }
            }

            trigerEventListRowStyler(data.length)

            //回选操作
            if (eventIdSelected) {
                //console.log("==========回选行，事件id：" + eventIdSelected + "，任务id：" + processIdSelected)
                for (let j = 0; j < wornData.length; j++) {
                    if (wornData[j].eventId === eventIdSelected && wornData[j].processId === processIdSelected) {
                        //let index = $('#dg-event-list').datagrid('getRowIndex', wornData[j])
                        //$('#dg-event-list').datagrid('selectRow', index)
                        //console.log("==========设置背景色，行数：" + index)

                        $('#dg-event-list').datagrid('selectRow', j)
                        setListColor(j)
                        colorIndex = j;
                        //console.log("==========回选设置背景色，行数：" + j)

                    }
                }
            } else {
                //console.log("==========无需回选行")
            }

            //只刷新事件第二列视频状态（指导视频H5）
            updateVideoLineTimeout()

            //事件监控列表首次加载成功
            if (!isEventListFirstLoaded) {
                isEventListFirstLoaded = true;
                //console.log("=====isEventListFirstLoaded set true", isEventListFirstLoaded)
            }
        } else {
            //如果没有返回数据则空清空数据
            $('#dg-event-list').datagrid('loadData', { total: 0, rows: [] });
        }
        
        //右边派车界面消失
        //副屏地图状态变成派车中的页面
        try {
            gProxy.sendToNewEventStatus("0"); //非派车状态
        } catch (e) {
            bsProxy.sendToNewEventStatus("0"); //派车状态
        }
        
        // 统计各状态事件数量
        let appointmentCount = 0; // 预约(7)
        let waitingCount = 0;     // 挂起-未调度(6) 
        let unscheduledCount = 0; // 落单-未调度(1)
        let scheduledCount = 0;   // 任务中-已调度(2)
        data.forEach(item => {
            switch(item.eventStatus) {
                case "7":
                    appointmentCount++;
                    break;
                case "6":
                    waitingCount++;
                    break;    
                case "1":
                    unscheduledCount++;
                    break;
                case "2":
                    scheduledCount++;
                    break;
            }
        });

        // 更新各状态数量显示
        $('#event_appointment_counts').html(appointmentCount);
        $('#event_waiting_counts').html(waitingCount);
        $('#event_unscheduled_counts').html(unscheduledCount); 
        $('#event_scheduled_counts').html(scheduledCount);
        $('#event_counts').html(data.length);

        $('#dg-event-list').datagrid('loaded'); //关闭loding进度条；
        $('#refresh-event-btn').removeAttr('disabled'); //按钮变成可用
    }, function (e, url, errMsg) {
        $('#dg-event-list').datagrid('loaded'); //关闭loding进度条；
        $('#refresh-event-btn').removeAttr('disabled'); //按钮变成可用
        //失败才做处理
        $.messager.alert('异常', '获取事件列表，异常信息：' + errMsg);
    });
};

function trigerEventListRowStyler(indexNotExists) {
    //console.log("==========插入空白行，用于触发rowStyler：" + indexNotExists)
    setEventList('insertRow', indexNotExists, {})
    setEventList('deleteRow', indexNotExists)
}

/**
 * 
 * @param {any} type insertRow/updateRow/deleteRow
 * @param {any} i
 * @param {any} row
 */
function setEventList(type, i, row) {
    if (type !== 'deleteRow') {
        $('#dg-event-list').datagrid(type, {
            index: i, // 索引从0开始
            row: {
                processId: row.processId,
                eventId: row.eventId,
                eventStatus: row.eventStatus,
                eventStatusStr: row.eventStatusStr,
                callIn: row.callIn,
                callInTimes: row.callInTimes,
                majorCall: row.majorCall ? row.majorCall.replace("_", " ") : "",
                eventName: row.address + " + " + (row.patientName ? row.patientName : "无名氏") + " + " + (row.majorCall ? row.majorCall.replace("_", " ") : ""),
                eventSrcName: row.eventSrcName,
                callTypeName: row.callTypeName,
                contact: row.contact,
                mobileStatus: row.mobileStatus,
                mobileStatusStr: row.mobileStatusStr,
                h5AVInvite: row.h5AVInvite,
                seatUser: row.seatUser,
                processTime: row.processTime,
                stationName: row.stationName,
                plateNum: row.plateNum,
                address: row.address,
                processIsFinishd: row.isFinishd,
                lng: row.lng,
                lat: row.lat,
                eventType: row.eventType,
                eventTypeStr: row.eventType == '1' ? '重大事件' : '普通事件', //0-普通事件 1-重大事件
                taskPhone: row.taskPhone,
                stationCode: row.stationCode,
                carContact: row.carContact,
                stationId: row.stationId,
                carId: row.carId,
                isLock: row.isLock, // 是否锁定 0否1是
                lockSeatId: row.lockSeatId, // 锁定事件座席id
                lockSeatCode: row.lockSeatCode, // 锁定事件座席code
                lockSeatUserId: row.lockSeatUserId, // 锁定事件座席用户id
                lockSeatUserName: row.lockSeatUserName, // 锁定事件座席用户名称
            }
        });
    } else {
        $('#dg-event-list').datagrid(type, i)
    }
}

/** 自动刷新事件列表定时器 */
function refreshEventListIntervalTimeout() {
    clearTimeout(window.refreshEventListInterval);
    window.refreshEventListInterval = null;
    if (_refreshEventListInterval) {
        //事件监控列表首次加载成功后才可以执行定时任务
        if (isEventListFirstLoaded) {
            refreshEventlist()
        }
        window.refreshEventListInterval = setTimeout(refreshEventListIntervalTimeout, 6000)
    }
}

/** 只刷新事件第二列视频状态（指导视频H5） */
function updateVideoLineTimeout() {
    clearTimeout(window.setIntervalRush);
    if (_h5AVInviteInterval) {
        window.setIntervalRush = null;
        // 获取新的数据值
        eventColorIndex = null
        var params = [];
        if ($('#event-status-all').is(':checked')) {
            params.push($('#event-status-all').val());
        }
        if ($('#event-status-unscheduled').is(':checked')) {
            params.push($('#event-status-unscheduled').val());
        }
        if ($('#event-status-scheduled').is(':checked')) {
            params.push($('#event-status-scheduled').val());
        }
        if ($('#event-status-appointment').is(':checked')) {
            params.push($('#event-status-appointment').val());
        }
        if ($('#event-status-waiting').is(':checked')) {
            params.push($('#event-status-waiting').val());
        }
        h5AVInvite(params, _pSeat.id, function (data) {
            //获取正在进行的事件的列表
            var selectedIndex = $('#dg-event-list').datagrid('getRowIndex', $('#dg-event-list').datagrid('getSelected'));
            var rows = $('#dg-event-list').datagrid('getPanel').find('.datagrid-view2 tr');

            /*//后台返回任务列表字体大小
            var allDiv = $('#dg-event-list').datagrid('getPanel').find('.datagrid-view2 div')
            for (let i=0;i<allDiv.length;i++){
                allDiv[i].style.fontSize='20px'
            }*/
            for (var i = 0; i < eventList.length; i++) {
                if (data[eventList[i].eventId] == 1) {
                    $('#dg-event-list').datagrid('updateRow', {
                        index: i,
                        row: {
                            h5AVInvite: '1'
                        }
                    });
                    eventList[i].h5AVInvite = '1'
                } else {
                    $('#dg-event-list').datagrid('updateRow', {
                        index: i,
                        row: {
                            h5AVInvite: '0'
                        }
                    });
                    eventList[i].h5AVInvite = '0'
                }

                if (selectedIndex != -1 && selectedIndex == i) {
                    rows[i + 1].style.backgroundColor = '#0074d8'
                }

            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取指导视频请求，异常信息：' + errMsg);
        });

        window.setIntervalRush = setTimeout(updateVideoLineTimeout, 3000)
    }
}
/** 设置列中字体的颜色，可以根据行索引row和列索index做判断 */
function setDgEventColumnForeColor(value, row, index) {
    if (value == null) {
        value = "";
    }
    switch (row.eventStatus) {
        case '1':
            return "<span style='color: #fd0404'>" + value + "</span>";
        case '7':
            return "<span style='color: #ffec99'>" + value + "</span>";
        case '6':
            return "<span style='color: #ffec99'>" + value + "</span>";
        case "2":
            switch (row.mobileStatus) {
                case '5':
                    return "<span style='color: blue'>" + value + "</span>";
                case "6":
                    return "<span style='color: blue'>" + value + "</span>";
                case "1":
                    return "<span style='color: green'>" + value + "</span>";
                case "2":
                    return "<span style='color: green'>" + value + "</span>";
                case "3":
                    return "<span style='color: green'>" + value + "</span>";
                case "8":
                    return "<span style='color: green'>" + value + "</span>";
                case "4":
                    return "<span style='color: green'>" + value + "</span>";
                case "7":
                    return "<span style='color: blue'>" + value + "</span>";
            }
    }
}

/** 事件监控表格的状态颜色格 */
function setDgEventStatusIconColumnFormatter(value, row, index) {
    if(row.isLock && row.isLock == '1'){//锁了，那么只能这个座席操作，其他坐席不能操作
        var lockInfo = '已锁定';
        if(row.lockSeatCode || row.lockSeatUserName) {
            lockInfo = '由座席 ' + (row.lockSeatCode || '') + ' (' + (row.lockSeatUserName || '') + ') 锁定';
        }
        //如果锁定事件的座席id和当前座席id一致，且锁定事件的座席用户id和当前座席用户id一致，那么显示绿色的锁定的图片
        if((row.lockSeatId == _pSeat.id && row.lockSeatUserId == _pSeat.userId)){
            return '<img src="./style/img/lock_event_green.png" title="' + lockInfo + '" alt="事件已锁定"/>';
        }else if(row.lockSeatId == _pSeat.id && row.lockSeatUserId != _pSeat.userId){
            //如果锁定事件的座席id和当前座席id一致，且锁定事件的座席用户id和当前座席用户id不一致，那么显示蓝色的锁定的图片
            return '<img src="./style/img/lock_event_blue.png" title="' + lockInfo + '" alt="事件已锁定"/>';
        }else{  
            //如果锁定事件的座席id和当前座席id不一致，那么显示红色的锁定的图片
            return '<img src="./style/img/lock_event_red.png" title="' + lockInfo + '" alt="事件已锁定"/>';
        }
    }else{
    //0-普通事件 1-重大事件
    switch (row.eventType) {
        case '1':
            return '<img src="./style/img/120_chexiao_ic.png"/>';
        case '0':
            switch (row.eventStatus) {
                case '1':
                    return '<img src="./style/img/120_chexiao_ic.png"/>';
                case '7':
                    return '<img src="./style/img/120_chexiao_ic.png"/>';
                case '6':
                    return '<img src="./style/img/120_chexiao_ic.png"/>';
                case "2":
                    switch (row.mobileStatus) {
                        case '5':
                            return '<img src="./style/img/120_weidiaodu_ic.png"/>';
                        case "6":
                            return '<img src="./style/img/120_yiwancheng_ic.png"/>';
                        case "1":
                            return '<img src="./style/img/120_yidiaodu_ic.png"/>';
                        case "2":
                            return '<img src="./style/img/120_yidiaodu_ic.png"/>';
                        case "3":
                            return '<img src="./style/img/120_yiwancheng_ic.png"/>';
                        case "8":
                            return '<img src="./style/img/120_yidiaodu_ic.png"/>';
                        case "4":
                            return '<img src="./style/img/120_yidiaodu_ic.png"/>';
                        case "7":
                            return '<img src="./style/img/120_weidiaodu_ic.png"/>';
                        }
                    }
            }
    }
}
function videoFormatter(value, row, index) {
    if (row.h5AVInvite == '1') {
        return `<img id="videoH5Id${index}" onmouseover="videoH5Id(${index})" style="cursor: pointer;width:20px;height:20px" src="./style/img/hasVideo.gif"/>`;
    } else {
        return `<img style="cursor: pointer;" src="./style/img/notVideo.png"/>`;
    }
}
function videoH5Id(index) {
    layui.use(['layer'], function () {
        var layer = layui.layer;
        // 创建提示
        layer.tips("<span style='color:black'>" + '患者指导视频' + "</span>", `#videoH5Id${index}`, {
            tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
            time: 2000, // 提示持续时间，单位为毫秒
        });
    });
}
/** 车辆监控表格的状态颜色格 */
function setDgMobileStatusIconColumnFormatter(value, row, index) {
    switch (row.mobileStatus) {
        case '1':
            return '<img src="./style/img/che_yipaiche_ic.png"/>';
        case "2":
            switch (row.status) {
                case '5':
                    return '<img src="./style/img/che_weipaiche_ic.png"/>';
                case "6":
                    return '<img src="./style/img/che_dida_ic.png"/>';
                case "1":
                    return '<img src="./style/img/che_huiyuan_ic.png"/>';
                case "2":
                    return '<img src="./style/img/che_huiyuan_ic.png"/>';
                case "3":
                    return '<img src="./style/img/che_yipaiche_ic.png"/>';
                case "8":
                    return '<img src="./style/img/che_huiyuan_ic.png"/>';
                case "4":
                    return '<img src="./style/img/che_yipaiche_ic.png"/>';
                case "7":
                    return '<img src="./style/img/che_weipaiche_ic.png"/>';
            }
        case "3":
            switch (row.hCondition) {
                case "1":
                    return '<img src="./style/img/che_yichuiche_ic.png"/>';
                case "2":
                    return '<img src="./style/img/che_yichuiche_ic.png"/>';
            }
        case "4":
            return '<img src="./style/img/che_quxiao_ic.png"/>';
    }
}

/** 座席状态图标 */
function setDgseatStationIconColumnFormatter(value, row, index) {
    if (value == null) {
        value = "";
    }
    if (row.type == '1') { //座席
        switch (row.seatStationStatusStatus) {
            case '离线':
                return '<img src="./style/img/zuoxi_liixian_ic.png"/>';
            case "在线":
                if (row.seatStationStatusOtherInfo == '线路忙') {
                    return '<img src="./style/img/zuoxi_duankai_ic.png"/>';
                } else {
                    return '<img src="./style/img/zuoxi_zaixian_ic.png"/>';
                }
            case '挂起':
                return '<img src="./style/img/zuoxi_duankai_ic.png"/>';
            default:
                return '<img src="./style/img/zuoxi_liixian_ic.png"/>';
        }
    }
    if (row.type == '2') { //分站
        switch (row.seatStationStatusStatus) {
            case '离线':
                return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
            case "在线":
                return '<img src="./style/img/yiyuan_zaixian_ic.png"/>';
            default:
                return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
        }
    }
}

/** 设置列单元格的背景颜色，可以根据行索引row和列索index做判断 */
function setDgEventColumnBackColor(value, row, index) {
    //switch (row.eventStatus) {
    //    case '1':
    //        return 'background-color:#ecdfdf;';
    //    case "2":
    //        return 'background-color:#d8f5ab;';
    //}
}

/** 车辆查询选择器，如果全部选中，那么其他的就自动选中，如果其中的选中单个，那么全部不选中 */
function mobileSelectChecked() {
    if ($('#mobile-status-await-orders').is(':checked') && $('#mobile-status-running-task').is(':checked') && $('#mobile-status-pause').is(':checked') && $('#mobile-status-not-on-duty').is(':checked')) {
        $("#mobile-status-all").prop("checked", true);
    } else {
        $("#mobile-status-all").prop("checked", false);
    }
    if (!$('#mobile-status-await-orders').is(':checked') && !$('#mobile-status-running-task').is(':checked') && !$('#mobile-status-pause').is(':checked') && !$('#mobile-status-not-on-duty').is(':checked')) {
        $("#mobile-status-all").prop("checked", true);
    }
}
/** 车辆查询选择器，如果全部选中，那么其他的就自动选中，如果其中的选中单个，那么全部不选中 */
function eventSelectChecked() {
    if ($('#event-status-unscheduled').is(':checked') && $('#event-status-scheduled').is(':checked') && $('#event-status-appointment').is(':checked') && $('#event-status-waiting').is(':checked')) {
        //所有子选项都选中了，要把全部选中
        $("#event-status-all").prop("checked", true);
    } else {
        $("#event-status-all").prop("checked", false);
    }
    if (!$('#event-status-unscheduled').is(':checked') && !$('#event-status-scheduled').is(':checked') && !$('#event-status-appointment').is(':checked') && !$('#event-status-waiting').is(':checked')) {
        //所有子选项都没选中了，要把全部取消选中
        $("#event-status-all").prop("checked", true);
    }
}

/** 打开撤销事件填写原因界面 */
function cancelEventDialog() {
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-event-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条事件进行撤销操作!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条事件进行修改!', 'error');
        return;
    }

    if(!canOperateEvent(rows[0].isLock,rows[0].lockSeatUserId,rows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (rows[0].lockSeatCode || '') + ' (' + (rows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    $('#revocation-event-id').val(rows[0].eventId);
    $("#revocationEventReason").textbox("setValue", "");
    $('#revocationEventDialog').window('open');
}

/** 关闭撤销事件填写原因界面 */
function closeCancelEventBtnBtn() {
    $('#revocationEventDialog').window('close', true);
}

/** 撤销事件保存按钮点击 */
function cancelEventBtn() {
    //撤销事件
    var params = {
        'eventId': $('#revocation-event-id').val(),
        'reason': $('#revocationEventReason').val(),
        'isNoCar': $('#cancelEventNoCar').val()
    };
    updateCancelEvent(params,
        function (res) {
            //成功
            refreshEventlist();
            $('#revocationEventDialog').window('close', true);
            $.messager.alert('提示', '撤销事件成功！', 'info');
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', errMsg, 'error');
        });
}

/** 打开撤销调度填写原因界面 */
function cancelMobileProcessDialog() {
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-event-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条调度任务进行撤销操作!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条调度任务进行撤销操作!', 'error');
        return null;
    }

    if (rows[0].processId == null) {
        $.messager.alert('提示', '不能撤销调度，该事件未进行调度，请先进行调度!', 'error');
        return null;
    }

    if(!canOperateEvent(rows[0].isLock,rows[0].lockSeatUserId,rows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (rows[0].lockSeatCode || '') + ' (' + (rows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    $('#revocation-scheduling-process-id').val(rows[0].processId);
    $('#revocation-scheduling-msg-id').val("");
    
    //获取配置判单撤销调度是input还是select
    let isReasonInput = true;
    querySysConfSync('CANCEL_MOBILE_PROCESS_REASONS', function (data) {
        // 获取数据并判断是否为空
        if (data) {
            let arr = data.split("||");  // 分割成数组
            let selectHtml = '<select class="select-common" id="revocationSchedulingReason" style="height:28px;width:200px;font-size:16px;">';
    
            // 遍历数组，构建下拉框的选项
            arr.forEach(function(item) {
                selectHtml += `<option value="${item}">${item}</option>`;
            });
    
            selectHtml += '</select>';
    
            // 将 input 替换为 select
            $('#reasonContainer').html(selectHtml);
            
            // 确保 select 元素加载后再初始化 combobox
            /*$('#revocationSchedulingReason').combobox({
                editable: false, // 禁止编辑
                valueField: 'value',
                textField: 'text'
            });*/

            isReasonInput = false
        } else {
            // 如果 data 为空，则恢复为 input
            let inputHtml = '<input class="easyui-textbox" id="revocationSchedulingReason" style="height:28px;width:200px;font-size:16px" />';
            $('#reasonContainer').html(inputHtml);
            $('#revocationSchedulingReason').textbox({
                // 初始化选项
            });
            isReasonInput = true
        }
    },
        function (e, url, errMsg) {
            let inputHtml = '<input class="easyui-textbox" id="revocationSchedulingReason" style="height:28px;width:200px;font-size:16px" />';
            $('#reasonContainer').html(inputHtml);
            $('#revocationSchedulingReason').textbox({
                // 初始化选项
            });
            isReasonInput = true
        });

    $('#revocationSchedulingDialog').window('open');

    // 获取调度信息
    getMobileProcessById(rows[0].processId, (content) => {
        if (content) {
            if (isReasonInput) {
                $("#revocationSchedulingReason").textbox("setValue", content.revocationSchedulingReason || '');
            } else {
                $("#revocationSchedulingReason").val(content.revocationSchedulingReason || '');
            }
            $('#stationRefuseDispatch').val(content.stationRefuseDispatch || '0');
        } else {
            if (isReasonInput) {
                $("#revocationSchedulingReason").textbox("setValue", '');
            } else {
                $("#revocationSchedulingReason").val('');
            }
            $('#stationRefuseDispatch').val('0');
        }
    },
        (e, url, errMsg) => {
            if (isReasonInput) {
                $("#revocationSchedulingReason").textbox("setValue", '');
            } else {
                $("#revocationSchedulingReason").val('');
            }
            $('#stationRefuseDispatch').val('0');
        })
}

/**
 * 打开撤销调度填写原因界面（通过分站拒绝出车，显示的界面）
 * @param {any} msgId 消息id
 * @param {any} msgContentOne 分站拒绝派车原因
 */
function cancelMobileProcessMsgDialog(msgId, msgContentOne) {
    $('#revocation-scheduling-msg-id').val(msgId);
    $('#revocation-scheduling-process-id').val("");

    //分站拒绝派车，只给input，不要select
    let inputHtml = '<input class="easyui-textbox" id="revocationSchedulingReason" style="height:28px;width:200px;font-size:16px" />';
    $('#reasonContainer').html(inputHtml);
    $('#revocationSchedulingReason').textbox({
        // 初始化选项
        editable: false,
        readonly: true
    });

    $("#revocationSchedulingReason").textbox("setValue", msgContentOne);
    $('#stationRefuseDispatch').val('1');

    //不能修改
    $("#stationRefuseDispatch").attr("disabled", true);

    $('#revocationSchedulingDialog').window('open');
    
}
/** 驳回拒绝派车 */
function refuseProcessCancel(msgId, processId) {
    $.messager.confirm("提示", "是否驳回拒绝派车？", function (r) {
        if (r) {
            showProcess(true, '温馨提示', '正在撤销拒绝派车操作，请稍后...');
            var params = {
                "msgId": msgId,
                "processId": processId
            }
            rejectRefuseSendCar(params,
                function (res) {
                    //成功
                    showProcess(false);
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', errMsg, 'error');
                });
        }
    });
}
/** 关闭撤销调度填写原因界面 */
function closeCancelMobileProcessBtn() {
    $('#revocationSchedulingDialog').window('close', true);
}

/** 撤销调度任务保存按钮，只有在分站未派车的情况下才能由调度座席撤销 */
function cancelMobileProcessBtn() {
    showProcess(true, '温馨提示', '正在进行撤销调度任务操作，请稍后...');
    //撤销调度任务，撤销调度任务时候不能撤销事件，如果需要撤销事件，那么需要撤销所有的分站未派车的任务后才能进行撤销事件的操作。
    var params = {
        'processId': $('#revocation-scheduling-process-id').val(),
        'msgId': $('#revocation-scheduling-msg-id').val(),
        'reason': $('#revocationSchedulingReason').val(),
        'stationRefuseDispatch': $('#stationRefuseDispatch').val()
    };
    updateCancelMobileProcess(params,
        function (res) {
            //成功
            showProcess(false);
            refreshEventlist();
            if (messagerWindow != null) {//如果是消息提示框的撤销调度，那么需要把该提示框设置未空
                messagerWindow.window("close");
                messagerWindow = null;
            }
            $('#revocationSchedulingDialog').window('close', true);
            $.messager.alert('提示', '撤销调度任务成功！', 'info');
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        });
}

/** 恢复取消接诊任务后可以恢复此调度 */
function recoverMobileProcessBtn() {

    var eventRows = $('#dg-event-list').datagrid('getSelections');
    if(!canOperateEvent(eventRows[0].isLock,eventRows[0].lockSeatUserId,eventRows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (eventRows[0].lockSeatCode || '') + ' (' + (eventRows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    $.messager.confirm("提示", "是否恢复此撤销调度任务？", function (r) {
        if (r) {
            //获得当前选中的事件的列表的当前行，datagrid的行
            var rows = $('#dg-event-list').datagrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条取消接诊的调度任务进行恢复操作!', 'error');
                return null;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条取消接诊的调度任务进行恢复操作!', 'error');
                return null;
            }

            if (rows[0].processId == null) {
                $.messager.alert('提示', '不能恢复调度，该事件非撤销调度的任务!', 'error');
                return null;
            }

            showProcess(true, '温馨提示', '正在进行恢复取消接诊调度任务操作，请稍后...');
            //撤销调度任务，撤销调度任务时候不能撤销事件，如果需要撤销事件，那么需要撤销所有的分站未派车的任务后才能进行撤销事件的操作。
            var params = {
                'processId': rows[0].processId
            };
            recoverMobileProcess(params,
                function (res) {
                    //成功
                    showProcess(false);
                    refreshEventlist();
                    refreshMobilesOrStations();//刷新车辆列表或分站列表
                    $.messager.alert('提示', '恢复接诊成功！', 'info');
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', errMsg, 'error');
                });
        }
    });
}

/**
 * 协助派车，代替分站接收任务
 * 模式1 :得先判断是否选择车辆  如果选择了车辆则继续
 * 模式2:增加一个车辆下拉选项
 */
function assistStationDispatchBtn(){
    assistType = 1
    //获取选中的事件的信息
    var eventRows = $('#dg-event-list').datagrid('getSelections');
    
    var numEvent = eventRows.length
    
    //模式1:协助分站派车操作
    if (isAssignTaskModeCar()) {
        if (numEvent == 0) {
            $.messager.alert('提示', '请选择一个任务对其进行协助派车操作!', 'error');
            return null
        }
        $('#dispatchEventId').val(eventRows[0].processId);//事件ID

        //获取选中的车辆信息
        // var carRows = $('#dg-mobiels-list').datagrid('getSelections');
        // var numCar = carRows.length
        // if (numCar == 0) {
        //     $.messager.alert('提示', '请选择车辆进行协助派车操作!', 'error');
        //     return null
        // }
        $('#confirm-receive-editor').window('open')
        $('#vehicleRow').show();
        // 将获取到的值赋值给选择车辆的下拉框
        $('#checkCar').combobox('setValue', eventRows[0].plateNum);
        $('#dispatchCarId').val(eventRows[0].carId); //车辆id
        // 禁止点开,只是查看数据
        $('#checkCar').combobox('disable');
    }

    //模式2:协助分站派车操作
    if (isAssignTaskModeStation()) {
        if (numEvent == 0) {
            $.messager.alert('提示', '请选择一个任务对其进行协助派车操作!', 'error');
            return null
        }
        $('#dispatchEventId').val(eventRows[0].processId);//事件ID

        $('#confirm-receive-editor').window('open')
        $('#vehicleRow').show();
        //使用分站id获取分站所属的车辆信息
        
        var stationId = eventRows[0].stationId
        //根据分站id获取分站车辆信息
        const statusList = []
        statusList.push('1')  
        const params = {
            stationName:eventRows[0].stationName,
            statusList   
        }
        getMobilesStatusListWithDistance(params, function (data) {
            $('#checkCar').combobox('clear');
            $('#checkCar').combobox('loadData', data);
        });
    }
    
    reloadDispatch(eventRows[0].stationCode)
    
}
/** 纠正任务车辆-更换任务车辆(纠正出车) */
function changeMobileProcessMobileBtn() {
    var eventRows = $('#dg-event-list').datagrid('getSelections');
    var num = eventRows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一个任务对其进行纠正车辆操作（在出现出车和实际出车车辆不符合的情况下使用）!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一个任务进行纠正车辆操作!', 'error');
        return null;
    }

    if(!canOperateEvent(eventRows[0].isLock,eventRows[0].lockSeatUserId,eventRows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (eventRows[0].lockSeatCode || '') + ' (' + (eventRows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    //获得需要派车的车辆的信息
    var mobileRows = $('#dg-mobiels-list').datagrid('getSelections');
    num = mobileRows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择需要纠正的救护车进行纠正车辆操作（在出现出车和实际出车车辆不符合的情况下使用）!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多辆车,只能选择一辆救护车进行纠正车辆操作!', 'error');
        return null;
    }
    //将改派车辆的原因显示成选择框
    $('#changeMobileReasonDialog').window('open');
    $("#changeMobileReasonFormRadio").html("");
    queryAllDic(["process_mobile_change_reason"],
        function (data) {
            //改派车辆原因
            var processMobileChangeReasonList = getDicList("process_mobile_change_reason", data);
            if(processMobileChangeReasonList.length > 0){
                // console.log(processMobileChangeReasonList);  
                var html = '';
                for (var i = 0; i < processMobileChangeReasonList.length; i++) {
                    var value = processMobileChangeReasonList[i].codeVale;
                    var code = processMobileChangeReasonList[i].codeName;
                    var id = processMobileChangeReasonList[i].uuid;
                    $("#changeMobileReasonFormRadio").append('<div style="margin-top:0px;margin-left:20px;"><input class="easyui-radiobutton" type="radio" name="changeMobileReason" style="margin-left:0px;" value="' + value + '" ><span style="margin-left:10px;font-size:14px">' + value + '</span></input></div><br>');
                }
                $('.easyui-radiobutton').radiobutton();
                $("#otherChangeMobileReason").next().hide(); //隐藏掉其他的输入框
                $('#change-mobile-process-id').val(eventRows[0].processId);
                $('#change-mobile-car-id').val(mobileRows[0].carId);
                //将改派车辆的原因显示成选择框
                $('#changeMobileReasonDialog').window('open');
            }

            
        }); //查询改派车辆的字典       
}

/** 改派车辆的弹窗显示 */
function changeCarForProcessDialogOpen() {
    var eventRows = $('#dg-event-list').datagrid('getSelections');
    
    var num = eventRows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一个任务对其进行改派车辆操作!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一个任务进行改派车辆操作!', 'error');
        return null;
    }

    if(!canOperateEvent(eventRows[0].isLock,eventRows[0].lockSeatUserId,eventRows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (eventRows[0].lockSeatCode || '') + ' (' + (eventRows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }

    //获得需要派车的车辆的信息
    var mobileRows = $('#dg-mobiels-list').datagrid('getSelections');
    num = mobileRows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择需要改派的救护车进行改派车辆操作!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多辆车,只能选择一辆救护车进行改派车辆操作!', 'error');
        return null;
    }
    $('#change-car-process-id').val(eventRows[0].processId);
    $('#change-newcar-id').val(mobileRows[0].carId);
    $("#changeCarForProcessReason").textbox("setValue", "");
    $('#changeCarForProcessDialog').window('open');
}

/** 关闭改派车辆填写取消原因界面 */
function closeChangeCarForProcessBtn() {
    $('#changeCarForProcessDialog').window('close', true);
}

/** 改派车辆 */
function changeCarForProcessBtn() {
    showProcess(true, '温馨提示', '正在进行改派车辆操作，请稍后...');
    var params = {
        'processId': $('#change-car-process-id').val(),
        'msgId': "",
        'reason': $('#changeCarForProcessReason').val(),
        'addMobileProcess': {
            "carId": $('#change-newcar-id').val(),
            "toStatus": '5',
            "seatId": _pSeat.id, //座席id
            "seatCode": _pSeat.seatId, //座席code
            "seatUserId": _pSeat.userId, //当前座席用户id
            "seatUserName": _pSeat.userName, //当前座席用户工号
            "seatUser": _pSeat.user //当前座席用户姓名
        }
    }
    updateReassignment(params,
        function (res) {
            //成功
            showProcess(false);
            refreshEventlist();
            refreshMobilesOrStations();//刷新车辆列表或分站列表
            $('#changeCarForProcessDialog').window('close', true);
            $.messager.alert('提示', '改派车辆成功！', 'info');
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        });

}

/** 选中了改派车辆类型的其他按钮，那么显示输入框 */
function otherCahrgeMobileReasonSelectBtn() {
    if ($('input[type="radio"][name="changeMobileReason"]:checked').val() == "-1") {
        $("#otherChangeMobileReason").next().show();
    } else {
        $("#otherChangeMobileReason").next().hide();
    }
};

/** 确认纠正出车按钮 */
function changeMobileProcessMobileExecBtn(processId, carId) {
    //撤销调度任务，撤销调度任务时候不能撤销事件，如果需要撤销事件，那么需要撤销所有的分站未派车的任务后才能进行撤销事件的操作。
    var reason = "";
    reason = $('input[type="radio"][name="changeMobileReason"]:checked').val();
    if (typeof reason === 'undefined' || reason == "") {
        $.messager.alert('提示', '请选择纠正车辆原因！', 'error');
        return false;
    }
    if ($('input[type="radio"][name="changeMobileReason"]:checked').val() == "-1") {
        reason = reason + ":" + $("#otherChangeMobileReason").textbox('getValue');
    }
    showProcess(true, '温馨提示', '正在进行纠正出车任务操作，请稍后...');
    var params = {
        'processId': processId,
        'carId': carId,
        'reason': reason,
    };
    changeMobileProcessMobile(params,
        function (res) {
            //成功
            showProcess(false);
            refreshEventlist();
            refreshMobilesOrStations();//刷新车辆列表或分站列表
            $('#changeMobileReasonDialog').window('close', true);
            $.messager.alert('提示', '纠正出车任务车辆成功！', 'info');
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        });
}

/** 改派车辆关闭按钮 */
function closeReassigningMobileProcessExecBtn() {
    $('#changeMobileReasonDialog').window('close', true);
}

/** 座席指派车辆或座席调度派遣 */
function sendTaskBtn(stationId) {
    var eventRows = $('#dg-event-list').datagrid('getSelections');

    if(!canOperateEvent(eventRows[0].isLock,eventRows[0].lockSeatUserId,eventRows[0].lockSeatId)){
        $.messager.alert('提示', '事件被 ' + (eventRows[0].lockSeatCode || '') + ' (' + (eventRows[0].lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
        return;
    }
    $.messager.confirm("提示", "是否进行派车操作？", function (r) {
        if (r) {
            var eventRows = $('#dg-event-list').datagrid('getSelections');

            if (eventRows.length !== 1) {
                $.messager.alert('提示', '请选择一条事件进行派车(增派)操作!', 'error');
                return;
            }

            var mobileRows;
            var carId;
            if (isAssignTaskModeCar()) {
                mobileRows = $('#dg-mobiels-list').datagrid('getSelections');
                if (mobileRows.length !== 1) {
                    $.messager.alert('提示', '请选择一辆救护车进行派车(增派)操作!', 'error');
                    return;
                }
                carId = mobileRows[0].carId;
                
                // 派车增援逻辑判断
                if (mobileRows[0].isDispatch === '0') {
                    // 座席端直接派车给车辆，不经过分站确认
                    confirmReceiveShow(eventRows[0].eventId, carId, mobileRows[0].currentProcessId, mobileRows[0].stationCode);
                    return;
                }
            }
            
            // 统一的派车逻辑（包括分站派车）
            showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');
            var params = {
                "id": '',
                "eventId": eventRows[0].eventId,
                // "carId": mobileRows[0].carId,
                "toStatus": '5',
                "dispatchDoctorId": '',
                "dispatchDoctor": '',
                "dispatchDoctorPhone": '',
                "dispatchNurseId": '',
                "dispatchNurse": '',
                "dispatchNursePhone": '',
                "dispatchDriverId": '',
                "dispatchDriver": '',
                "dispatchDriverPhone": '',
                "dispatchWorkerId": '',
                "dispatchWorker": '',
                "dispatchWorkerPhone": '',
                "stationId": stationId,
                "seatId": _pSeat.id, //座席id
                "seatCode": _pSeat.seatId, //座席code
                "seatUserId": _pSeat.userId, //当前座席用户id
                "seatUserName": _pSeat.userName, //当前座席用户工号
                "seatUser": _pSeat.user //当前座席用户姓名
            };
            if (isAssignTaskModeCar()) {
                params.carId = mobileRows[0].carId;
            }
            addMobileProcess(params,
                function (res) {
                    // 成功处理
                    showProcess(false);
                    refreshEventlist();
                    refreshMobilesOrStations();//刷新车辆列表或分站列表
                    $.messager.alert('提示', '派车(增派)车辆成功！', 'info');
                },
                function (e, url, errMsg) {
                    // 错误处理
                    showProcess(false);
                    $.messager.alert('提示', msg, 'error');
                }
            );
        }
    });
}
/** 派车的弹窗处理   打开确认接收弹窗  
座席端直接派车给车辆，不经过分站确认 */
function confirmReceiveShow(eventId, carId, processId, stationCode) {
    reloadDispatch(stationCode);
    //清理下拉选择框
    assistType = 2
    $('#dispatchClassesDri').combobox('clear');
    $('#dispatchDoctorDri').combobox('clear');
    $('#dispatchNurseDri').combobox('clear');
    $('#dispatchWorkerDri').combobox('clear');
    $('#dispatchDriverDri').combobox('clear');
    $('#dispatchEventId').val(eventId);//事件ID
    $('#dispatchCarId').val(carId);//车辆ID
    $('#mobileProcessId').val(processId);//车辆事件ID
    $('#confirm-receive-editor').window('open')
}

/** 更新出车人员班次选择框列表 */
function reloadDispatch(stationCode) {
    // 获得分站班次
    var params = { "substationcode": stationCode }
    getDispatchClassesByStation(params,
        function (docs) {
            dispatchClassesList = docs;
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({ 'id': docs[i].id, 'name': docs[i].scheduleName });
            }
            if (jsonstr.length == 0) {
                //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                $('#dispatchClassesDri').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchClassesDri').combobox('loadData', jsonstr);
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得排版信息，异常信息：' + errMsg);
        });

    //获取医生1001
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1001", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (jsonstr.length == 0) {
                $('#dispatchDoctorDri').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchDoctorDri').combobox('loadData', jsonstr);
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得医生信息，异常信息：' + errMsg);
        });

    //获取护士1002
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1002", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (jsonstr.length == 0) {
                //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                $('#dispatchNurseDri').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchNurseDri').combobox('loadData', jsonstr);
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得护士信息，异常信息：' + errMsg);
        });

    //获取司机1003
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1003", "rows": "999", }
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (jsonstr.length == 0) {
                $('#dispatchDriverDri').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchDriverDri').combobox('loadData', jsonstr);
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得司机信息，异常信息：' + errMsg);
        });

    //获取工人1004
    params = { "substationcode": stationCode, "page": "1", "hospitalDutyCode": "1004", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            var jsonstr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonstr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (jsonstr.length == 0) {
                $('#dispatchWorkerDri').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchWorkerDri').combobox('loadData', jsonstr);
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得工人信息，异常信息：' + errMsg);
        });
}

/** 父屏幕调用子屏幕 */
function autoSelectStation(id, carId) {
    let iframe = $("#main-tabs").find("iframe")[$(".tabs-header ul li").index($('.tabs-selected')) - 1];
    if (iframe && iframe.src.indexOf('createEvents') !== -1) {
        iframe.contentWindow && iframe.contentWindow.modifyMobileStatus && iframe.contentWindow.modifyMobileStatus('', id, "5")
    } else {
        $.messager.alert('提示', '当前没有打开可派车的事件。');
    }
}
function synchronizingAddressInformation(location) {
    //获得选中的tab的iframe的dom节点
    let iframe = $("#main-tabs").find("iframe")[$(".tabs-header ul li").index($('.tabs-selected')) - 1];
    if (iframe && iframe.src.indexOf('createEvents') !== -1) {
        iframe.contentWindow && iframe.contentWindow.addressBackfilling && iframe.contentWindow.addressBackfilling(location)
    } else {
        $.messager.alert('提示', '当前没有找到有效的创建事件。');
    }
}

/** 从电话列表中点开 */
function createNewEventByPhone(phone, callUid, callInTimes) {
    //判断是否已经打开了创建事件的页面
    if (isCreateNewEventWinIsOpen()) {
        return false;
    }
    isCreatingEvent = true

    openCreateNewEventTabByPhone(phone, callUid, callInTimes);
}

/** 从视频呼救列表中点开 */
function createNewEventByVideo(phone, callUid, callInTimes,address, lng, lat, nickName, avatarUrl, gender) {
    if (isCreateNewEventWinIsOpen()) {
        return false;
    }
    isCreatingEvent = true
    openCreateNewEventTabByVideo(phone, callUid, callInTimes, 1, address, lng, lat, nickName, avatarUrl, gender);
}

/** 从区域联动列表中点开 */
function createNewEventByRegionLinkage(regionLinkageId, externalBizId) {
    if (isCreateNewEventWinIsOpen()) {
        return false;
    }
    isCreatingEvent = true
    openCreateNewEventTabByRegionLinkage(regionLinkageId, externalBizId);
}

/** 判断创建事件的窗口是否打开 */
function isCreateNewEventWinIsOpen() {
    return isCreatingEvent
}

/** 电话事件-挂机 */
function onHangOut(callNum) {
    $('#currentCallNum').html('空闲');
    _currentCall = null;
    _currentCallData = null;
}

/** 事件-被踢下线 */
//function onKicked(seatSrc) {
//    showLogin();
//    _token = null;
//    $.messager.alert('账号', '你的账号在【' + seatSrc + '】重复登录，你已经被踢下线。');
//}

/** 显示登录界面 */
function showLogin() {
    $('#loginPanel').show();
    $('#loginForm').show();
    $('#changePwdForm').hide();
    checkActiveShiftSeat();
}

function showChangePwd() {
    $('#loginForm').hide();
    $('#changePwdForm').show();
    $('#cp-un').val('');
    $('#cp-pwd').val('');
    $('#cp-newPwd1').val('');
    $('#cp-newPwd2').val('');
}

/** 设置计时器，定时刷新车辆列表（分站列表）、座席状态、分站状态 */
function taskMobileIntervalTimeout() {
    clearTimeout(window.taskMobileInterval);
    if (_taskMobileInterval) {
        window.taskMobileInterval = null;
        if (_token != "") {
            
            if (_titleStates) {
                refreshSelectSeatAndStationUpload(); //设置定时刷新座席和分站状态
            } else {
                missedCallsUpload() //未接来电信息更新
            }

            if (isMobilesOrStationsFirstLoaded) {
                refreshMobilesOrStations();//刷新车辆列表或分站列表
                syncMobilesToSecondScreen();//同步车辆数据给第二屏幕
            }
        }
        window.taskMobileInterval = setTimeout(taskMobileIntervalTimeout, 5000);
    }
}
/** 设置定时刷新消息通知 */
function getNoticeAlertTimeout() {
    clearTimeout(window.noticeInterval);
    window.noticeInterval = null;
    let params = {
        receiveType: "3",
        receiverId: _pSeat.id
    }
    seatUnreceivedNum(params, function (data) {
        if (data) {
            let numNotice = data
            if (numNotice > 0) {
                $("#xiaoxi_badge").show()
                $("#xiaoxi_badge").text(numNotice)
            } else {
                $("#xiaoxi_badge").hide()
            }
        }
    }, function (e, url, errMsg) {
        $.messager.alert('异常', '获取通知列表，异常信息：' + errMsg);
    });
    if (_noticeListInterval) {
        window.noticeInterval = setTimeout(getNoticeAlertTimeout, 3000);
    }
}


// 全局变量，记录已提醒的联动ID，防止重复弹窗
var _waitingFeedbackRemindIds = [];

/** 定时获取待反馈联动信息并弹窗提醒 */
function getWaitingFeedbackListTimeout() {
    clearTimeout(window.waitingFeedbackListInterval);
    window.waitingFeedbackListInterval = null;

    getWaitingFeedbackEventRegionLinkageList({} ,function (data) {
        if (Array.isArray(data) && data.length > 0) {
            // 按sendRegionName分组统计数量
            var regionMap = {};
            data.forEach(function(item) {
                if (!_waitingFeedbackRemindIds.includes(item.id)) {
                    regionMap[item.sendRegionName] = (regionMap[item.sendRegionName] || 0) + 1;
                    _waitingFeedbackRemindIds.push(item.id);
                }
            });
            // 弹窗提醒
            for (var region in regionMap) {
                $.messager.show({
                    title: '<div style="display:flex;align-items:center;"><img style="width:20px;height:20px;margin-right:5px" src="style/img/qiangcha.png"/>区域联动提醒</div>',
                    msg: `<div style="font-size:1.1rem;text-align:left;">${region}发送了${regionMap[region]}条联动信息，请及时处理。</div>`,
                    timeout: 0,
                    showType: 'slide',
                    closable: false,
                    width: '400px',
                    height: '120px',
                    style: {
                        right: '',
                        top: '',
                        left: '0px',
                        bottom: '0px'
                    }
                });
            }
        }
    }, function (e, url, errMsg) {
        // 可选：异常不弹窗
    });

    // 10秒后再次执行
    window.waitingFeedbackListInterval = setTimeout(getWaitingFeedbackListTimeout, 10000);
}

/** 设置定时刷新消息 */
function taskMsgIntervalTimeout() {
    clearTimeout(window.taskMsgInterval);
    if (_taskMsgInterval) {
        window.taskMsgInterval = null;
        $('#cur-date-time').html(new Date().formatString('yyyy-MM-dd hh:mm:ss'));
        _token && refreshMsgInfolist();
        window.taskMsgInterval = setTimeout(taskMsgIntervalTimeout, 3000)
    }
}

/** 座席上线心跳定时器 */
function taskHeartbeatIntervalTimeout() {
    clearTimeout(window.taskHeartbeatInterval);
    window.taskHeartbeatInterval = null;
    if (_taskHeartbeatInterval) {
        updateHeartbeat(_pSeat.id,
            function (res) {
                //成功不做任何处理
            },
            function (e, url, errMsg) {
                //失败才做处理
                //$.messager.alert('异常', '座席心跳，异常信息：' + errMsg);
            });
        window.taskHeartbeatInterval = setTimeout(taskHeartbeatIntervalTimeout, 8000)
    }
}

function turnOnAlert() {
    try {
        gProxy.playAlert(true);
    } catch (e) {
        bsProxy.playAlert(true);
    }
}

function turnOffAlert() {
    try {
        gProxy.playAlert(false);
    } catch (e) {
        bsProxy.playAlert(false);
    }
}

function playRefuseToDispatchSound(openIs) {
    try {
        gProxy.playRefuseToDispatchSound(openIs);
    } catch (e) {
        // B/S模式下使用bsProxy
        try {
            if (typeof bsProxy === 'undefined' || !bsProxy) {
                bsProxy = new gBsProxy(document.body);
            }
            bsProxy.playRefuseToDispatchSound(openIs);
        } catch (bsError) {
            console.error('播放拒绝派遣音频失败:', bsError);
        }
    }
}

var PhoneStatus = true
/** 定时获取电话状态与消息提醒的定时器 */
function taskCallStatusIntervalTimeout() {
    clearTimeout(window.taskCallStatusInterval);
    if (_taskCallStatusInterval) {
        window.taskCallStatusInterval = null;

        //获取接警的分机的电话状态
        getExtPhoneStatusInfo(_pSeat.callIn,
            function (res) {
                $("#isHarass").hide();
                //成功处理
                if (res.extStatus == '0') {
                    //if ('0' == '0') {
                    $('#currentCallNum').html("检查120线路！！！");
                    $('#currentCallNum').attr("class", "redblink");
                    dialTest();//拨测异常弹窗
                    turnOnAlert();

                    return null;
                } else {
                    //关闭拨测异常弹窗
                    if (dialWindow) {
                        dialWindow.window('close', true);
                        dialWindow = null
                        playRefuseToDispatchSound(false);
                    }
                    turnOffAlert();
                }
                if (res.noDisturb == "on") {
                    $('#currentCallNum').html("限制呼入");
                    $('#currentCallNum').attr("class", "redblink");
                    var ele = $("#callInStatus").children(".move");
                    ele.animate({ left: "0" }, 300, function () {
                        ele.attr("data-state", "off");
                    });
                    $("#callInStatus").removeClass("on").removeClass("none").addClass("off");
                } else if (res.extStatus == "OFFLINE") { //离线
                    $('#currentCallNum').html("离线");
                    $('#currentCallNum').attr("class", "grayblink");
                    _currentInCall = null;
                    var ele = $("#callInStatus").children(".move");
                    ele.animate({ left: "0" }, 300, function () {
                        ele.attr("data-state", "off");
                    });
                    $("#callInStatus").removeClass("on").addClass("off").addClass("none");
                    //软电话-铃声关闭
                    if (_pSeat.callInExt.isSoftphone == "1") {
                        gProxy.ringSoftPhone(false, "ring_call_in_softphone.wav");
                    }
                    //接入软电话按钮显示处理
                    if (_pSeat.callInExt.isSoftphone == "1") {
                        $("#soft-phone-accept-call-in-btn").hide();
                        $("#soft-phone-reject-call-in-btn").hide();
                        $("#soft-phone-hold-call-in-btn").hide();
                    } else {
                        $("#soft-phone-accept-call-in-btn").hide();
                        $("#soft-phone-reject-call-in-btn").hide();
                        $("#soft-phone-hold-call-in-btn").hide();
                    }
                } else {
                    var ele = $("#callInStatus").children(".move");
                    $("#callInStatus").removeClass("none").addClass("on");
                    ele.animate({ left: '25px' }, 300, function () {
                        $(this).attr("data-state", "on");
                    });
                    $("#callInStatus").removeClass("off").addClass("on");

                    //免打扰是否开启
                    if (res.extStatus == "IDLE" || res.extStatus == "ONLINE") { //空闲
                        $('#currentCallNum').html("空闲");
                        $('#currentCallNum').attr("class", "blueblink");
                        _currentInCall = null;
                        //软电话-铃声关闭
                        if (_pSeat.callInExt.isSoftphone == "1") {
                            gProxy.ringSoftPhone(false, "ring_call_in_softphone.wav");
                        }
                        //接入软电话按钮显示处理
                        if (_pSeat.callInExt.isSoftphone == "1") {
                            $("#soft-phone-accept-call-in-btn").hide();
                            $("#soft-phone-reject-call-in-btn").hide();
                            $("#soft-phone-hold-call-in-btn").hide();
                        } else {
                            $("#soft-phone-accept-call-in-btn").hide();
                            $("#soft-phone-reject-call-in-btn").hide();
                            $("#soft-phone-hold-call-in-btn").hide();
                        }
                    } else { //忙
                        switch (res.callStatus) {
                            case "ANSWER":
                                if (res.phone) { //填写电话号码
                                    $('#currentCallNum').html("接听:" + res.phone);
                                    $('#currentCallNum').attr("class", "greenblink");
                                    if(res.isHarass == '1'){
                                        $("#isHarass").show()
                                        $("#isHarass").html("骚扰");
                                        $('#isHarass').attr("class", "redblink");
                                    }
                                    //设置当前的全局电话号码
                                    _currentInCall = null;
                                    _currentInCall = res;
                                    //显示创建事件的弹窗
                                    let domeStr = document.getElementById('createEventsIframe' + res.callUid)
                                    if (!domeStr) {
                                        openCreateNewEventTabByPhone(res.phone, res.callUid, res.callInTimes, 1);
                                    }
                                }
                                break;
                            case "RING":
                                $('#currentCallNum').html("响铃:" + res.phone);
                                $('#currentCallNum').attr("class", "greenblink");
                                if(res.isHarass == '1'){
                                    $("#isHarass").show()
                                    $("#isHarass").html("骚扰");
                                    $('#isHarass').attr("class", "redblink");
                                }
                                _currentInCall = null;
                                _currentInCall = res;
                                //需要判断是否开启了软电话的功能
                                if (_pSeat.callInExt.isSoftphone == "1") {
                                    //调用软电话响铃获取callId来接听电话
                                    var softPhoneAcceptCallId = gProxy.getLastCallInIdSoftPhone();
                                    //console.log("当前报警软电话的呼入callID：" + softPhoneAcceptCallId);
                                    $('#soft-phone-accept-call-id').val(softPhoneAcceptCallId);
                                    //响铃
                                    gProxy.ringSoftPhone(true, "ring_call_in_softphone.wav");

                                    //$("#soft-phone-accept-call-in-btn").show();
                                    //$("#soft-phone-reject-call-in-btn").show();
                                    //$("#soft-phone-hold-call-in-btn").hide();

                                    $("#soft-phone-accept-call-in-btn").hide();
                                    $("#soft-phone-reject-call-in-btn").hide();
                                    $("#soft-phone-hold-call-in-btn").hide();
                                }
                                break;
                            default:
                                //软电话按钮隐藏
                                $("#soft-phone-accept-call-in-btn").hide();
                                $("#soft-phone-reject-call-in-btn").hide();
                                $("#soft-phone-hold-call-in-btn").hide();

                                if (res.isVisitor == '0') { //如果是非来电，那么就不处理
                                    return null;
                                }
                                $('#currentCallNum').html("线路忙...");
                                $('#currentCallNum').attr("class", "redblink");
                                _currentInCall = null;
                                break;
                        }
                    }
                }
            },
            function (e, url, errMsg) {
                $('#currentCallNum').html("系统异常");
                $('#currentCallNum').attr("class", "redblink");
                //软电话按钮隐藏
                $("#soft-phone-accept-call-in-btn").hide();
                $("#soft-phone-reject-call-in-btn").hide();
                $("#soft-phone-hold-call-in-btn").hide();
                //失败处理
                //$.messager.alert('异常', '120接警分机实时电话状态异常，异常信息：' + errMsg);
            });

        //获取调度分机的电话状态,只对软电话有效, 当后台配置分机是软电话的时候才显示软电话调度键盘。
        if (_pSeat.callOutExt.isSoftphone == "1") {
            getExtPhoneStatusInfo(_pSeat.callOut,
                function (res) {
                    //成功处理
                    // console.log("获取调度的分机的实时电话状态" + JSON.stringify(res));
                    if (res.extStatus == '0') {
                        //$('#currentCallNum').html("检查120调度线路！！！");
                        //$('#currentCallNum').attr("class", "redblink");
                        $("#soft-keyboard-calling-msg").html("检查120调度线路！！！");
                        $('#soft-keyboard-calling-msg').attr("class", "redblink");
                        $("#soft-keyboard-calling-msg").css('display', 'block');
                        $("#soft-phone-call-phone").css('display', 'none');
                        $("#delPhone").css('display', 'none');
                        return null;
                    } else {
                    }
                    if (res.noDisturb == "on") {
                        $("#soft-keyboard-calling-msg").html("限制呼入");
                        $('#soft-keyboard-calling-msg').attr("class", "redblink");
                        $("#soft-keyboard-calling-msg").css('display', 'block');
                        $("#soft-phone-call-phone").css('display', 'none');
                        $("#delPhone").css('display', 'none');
                    } else {
                        //免打扰是否开启
                        if (res.extStatus == "IDLE" || res.extStatus == "ONLINE") { //空闲
                            //空闲状态，不做任何的处理
                            softPhoneResetkeyword();
                        } else { //忙
                            switch (res.callStatus) {
                                case "ANSWER"://接听调度电话中-接听中
                                    if (res.phone) { //填写电话号码
                                        $("#soft-keyboard-calling-msg").html("调度电话接听中：" + res.phone);
                                        $('#soft-keyboard-calling-msg').attr("class", "greenblink");
                                        $("#soft-keyboard-calling-msg").css('display', 'block');

                                        //$("#soft-phone-hold-phone").css('display', 'block');
                                        $("#soft-phone-hangup-phone").css('display', 'block');

                                        //隐藏其他按钮
                                        $("#soft-phone-call-phone").css('display', 'none');
                                        $("#soft-phone-del-phone").css('display', 'none');
                                        $("#soft-keyboard-input").css('display', 'none');
                                        $("#delPhone").css('display', 'none');
                                        $("#soft-phone-dialcall-phone").css('display', 'none');
                                        $("#soft-phone-reject-phone").css('display', 'none');
                                        //$("#soft-phone-unhold-phone").css('display', 'none');
                                    }
                                    break;
                                case "ANSWERED"://呼叫调度电话中-拨打接通接听中
                                    $("#soft-keyboard-calling-msg").html("调度电话拨打中：" + $("#soft-keyboard-input").val());
                                    $('#soft-keyboard-calling-msg').attr("class", "greenblink");
                                    $("#soft-keyboard-calling-msg").css('display', 'block');

                                    //$("#soft-phone-hold-phone").css('display', 'block');
                                    $("#soft-phone-hangup-phone").css('display', 'block');

                                    //隐藏其他按钮
                                    $("#soft-phone-call-phone").css('display', 'none');
                                    $("#soft-phone-del-phone").css('display', 'none');
                                    $("#soft-keyboard-input").css('display', 'none');
                                    $("#delPhone").css('display', 'none');
                                    $("#soft-phone-dialcall-phone").css('display', 'none');
                                    $("#soft-phone-reject-phone").css('display', 'none');
                                    //$("#soft-phone-unhold-phone").css('display', 'none');
                                    break;
                                case "RING"://来电响铃
                                    var _Tab_Selected = $('#left-list').tabs('getSelected');
                                    //var _Tab_Selected_Index = $('#left-list').tabs('getTabIndex', _Tab_Selected);
                                    $('#left-list').tabs('update', {
                                        tab: _Tab_Selected,
                                        options: {
                                            selected: false
                                        }
                                    });
                                    var _Tab = $('#left-list').tabs('getTab', 2);
                                    $('#left-list').tabs('update', {
                                        tab: _Tab,
                                        options: {
                                            selected: true
                                        }
                                    });
                                    //这一步是用于刷新tabs插件，不加的话，tabs不会刷新，不显示最新选中面板。    
                                    $('#left-list').tabs({
                                        plain: 0
                                    })

                                    //调用软电话响铃获取callId来接听电话
                                    var softPhoneAcceptCallId = gProxy.getLastCallInIdSoftPhone();
                                    //console.log("当前报警软电话的呼入callID：" + softPhoneAcceptCallId);
                                    $('#soft-phone-accept-call-id').val(softPhoneAcceptCallId);

                                    $("#soft-keyboard-calling-msg").html("调度来电：" + res.phone);
                                    $('#soft-keyboard-calling-msg').attr("class", "greenblink");
                                    $("#soft-keyboard-calling-msg").css('display', 'block');
                                    //接听按钮显示
                                    $("#soft-phone-dialcall-phone").css('display', 'block');
                                    //拒接按钮显示
                                    $("#soft-phone-reject-phone").css('display', 'block');

                                    //隐藏其他按钮
                                    $("#soft-phone-call-phone").css('display', 'none');
                                    $("#soft-phone-hold-phone").css('display', 'none');
                                    $("#soft-phone-unhold-phone").css('display', 'none');
                                    $("#soft-phone-del-phone").css('display', 'none');
                                    $("#soft-keyboard-input").css('display', 'none');
                                    $("#delPhone").css('display', 'none');
                                    $("#soft-phone-hangup-phone").css('display', 'none');

                                    //响铃
                                    gProxy.ringSoftPhone(true, "ring_call_in_softphone.wav");
                                    break;
                                default:
                                    //软电话按钮隐藏
                                    $("#soft-keyboard-calling-msg").html("拨号中：" + $("#soft-keyboard-input").val());
                                    $('#soft-keyboard-calling-msg').attr("class", "greenblink");
                                    $("#soft-keyboard-calling-msg").css('display', 'block');
                                    $("#soft-phone-hangup-phone").css('display', 'block');

                                    $("#soft-phone-hold-phone").css('display', 'none');
                                    $("#soft-phone-unhold-phone").css('display', 'none');
                                    $("#soft-phone-dialcall-phone").css('display', 'none');
                                    $("#soft-phone-reject-phone").css('display', 'none');
                                    $("#soft-keyboard-input").css('display', 'none');
                                    $("#delPhone").css('display', 'none');
                                    $("#soft-phone-del-phone").css('display', 'none');
                                    $("#soft-phone-call-phone").css('display', 'none');
                                    break;
                            }
                        }
                    }
                },
                function (e, url, errMsg) {
                    //失败处理
                    //$.messager.alert('异常', '120调度分机实时电话状态异常，异常信息：' + errMsg);
                });
        }

        //显示消息提示框，消息类型msgType：1 分站拒绝出车消息、2 重大事件消息
        getMsgUnconfirmed({ "receiveId": _pSeat.id, "msgType": [1, 2] },
            function (res) {
                let isSliderelayMsgShow = res.filter(item => {
                    return item.msgType == 1
                })
                //console.log('分站拒绝出车消息', isSliderelayMsgShow);
                if (Array.isArray(isSliderelayMsgShow) && isSliderelayMsgShow[0]) {
                    for (var j = 0; j < isSliderelayMsgShow.length; j++) {
                        sliderelayMsgShow(isSliderelayMsgShow[j]);
                        break;//只显示一个右下角界面
                    }
                } else {
                    if (messagerWindow) {
                        messagerWindow.window('close', true);
                        messagerWindow = null
                        playRefuseToDispatchSound(false);
                    }
                }

                let isGreatEventShow = res.filter(item => {
                    return item.msgType == 2
                })
                //console.log('重大事件消息', isGreatEventShow);
                if (isGreatEventShow.length > 0 && isGreatEventShow[0]) {
                    for (var j = 0; j < isGreatEventShow.length; j++) {
                        greatEventID = isGreatEventShow[j].msgContent
                        confirmedMsgId = isGreatEventShow[j].id
                        greatEvent(isGreatEventShow[j].msgContentOne, greatEventID)
                        break;//只显示一个右下角界面
                    }
                } else {
                    greatEventWindow = null
                    // if(!greatEventWindow){
                    //     greatEventWindow.window('close', true);
                    //     greatEventWindow = null
                    // }else{
                    //     greatEventWindow = null
                    // }
                }
            },
            function (e, url, errMsg) {
                //失败不做处理
                //$.messager.alert('异常', '座席心跳，异常信息：' + errMsg);
            }
        );

        //通话异常记录查询
        errorMsg()
        window.taskCallStatusInterval = setTimeout(taskCallStatusIntervalTimeout, 1500);
    }
}

/**
 * 车头平板和座席端的视频按钮
 * @param {any} value
 * @param {any} row
 * @param {any} index
 */
function videoCarFormatter(value, row, index) {
    if (row.seatId == _pSeat.id) {
        // 车载视频邀请座席优先级第一，当没有车载视频邀请通话，但是该车辆接到任务并发车时，可以展示座席端邀请车辆视频的图标
        if (row.h5AVInvite == '1') {
            return `<img id="videoCarId${index}" onmouseover="videoCarId(${index})" style="cursor: pointer;width:20px;height:20px" src="./style/img/hasVideo.gif"/>`;
        } else if (row.statusStr == '发车') {
            return `<img id="videoCarId${index}" onmouseover="videoCarId(${index})" style="cursor: pointer;width:20px;height:20px" src="./style/img/yaoqingIcon.png"/>`;
        } else {
            return `<img style="cursor: pointer;" src="./style/img/notVideo.png"/>`;
        }
    } else {
        return `<img style="cursor: pointer;" src="./style/img/notVideo.png"/>`;
    }
}
function videoCarId(index) {
    layui.use(['layer'], function () {
        var layer = layui.layer;
        // 创建提示
        layer.tips("<span style='color:black'>" + '车载视频通话' + "</span>", `#videoCarId${index}`, {
            tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
            time: 2000, // 提示持续时间，单位为毫秒
        });
    });
}
/** 车辆类型字典数据 */
var _carTypeDictionary = [];

/** 登录成功后执行 */
function loginSuccess() {
    //getUserInfo()一定要放在最前面，保证token被赋值
    try {
        //C/S代码
        _user = evalJson(gProxy.getUserInfo());
        try {
            _pSeat = evalJson(gProxy.getSeatInfo()); //将座席信息存储到全局变量
        } catch (e) {
            _pSeat = evalJson(bsProxy.getSeatInfo());
        }

        _token = _user.Token;
    } catch (e) {
        //B/S已经在登录后把数据放到全局变量里面了
    }

    // 获取车辆类型字典数据
    loadCarTypeDictionary();

    $('#substationsList').hide();
    $('#vehicleList').hide();
    $('#sendTaskDialogOpen').hide();
    $('#changeCarForProcessDialogOpen').hide();
    $('#changeMobileProcessMobileBtn').hide();

    //获取配置
    querySysConfSync('ASSIGN_TASK_MODE',
        function (data) {
            if (data) {
                assignTaskMode = data
                if (isAssignTaskModeStation()) {
                    //座席派任务模式：2分站
                    $('#vehicleList').hide();
                    $('#substationsList').show();
                    $('#sendTaskDialogOpen').hide();
                    $('#changeCarForProcessDialogOpen').hide();
                    $('#changeMobileProcessMobileBtn').hide();
                } else {
                    //座席派任务模式：1车辆
                    $('#substationsList').hide();
                    $('#vehicleList').show();
                    $('#sendTaskDialogOpen').show();
                    $('#changeCarForProcessDialogOpen').show();
                    $('#changeMobileProcessMobileBtn').show();
                }
            }
        });

    //获取系统配置,例如任务列表字体大小
    querySysConfSync("seat_font", function (res) {
        //后台返回任务列表字体大小
        seatFontSize = JSON.parse(res).fontSize ? JSON.parse(res).fontSize : 15
    }, function (e, url, errMsg) {
        $.messager.alert('提示', '获取seat_font配置失败，异常信息：' + errMsg);
    })

    //软电话按钮隐藏
    $("#soft-phone-accept-call-in-btn").hide();
    $("#soft-phone-reject-call-in-btn").hide();
    $("#soft-phone-hold-call-in-btn").hide()
    $("#xiaoxi_badge").hide()
    $('#offline-mode').hide();

    //调度电话键盘-如果配置了软电话，那么才显示软电话的功能
    /*if (_pSeat.callOutExt.isSoftphone == "1") {
        $('#ruandianhua').show()
        //显示tab
        $('#left-list').tabs('getTab', "调度话机").panel('options').tab.show();//显示tab表头
    } else {
        $('#ruandianhua').hide()
        //隐藏tab
        $('#left-list').tabs('getTab', "调度话机").panel('options').tab.hide();//显示tab表头
    }*/
    $('#ruandianhua').hide()
    //隐藏tab
    $('#left-list').tabs('getTab', "调度话机").panel('options').tab.hide();//显示tab表头

    //接警分机号
    if (_pSeat.callInExt.isSoftphone == "1") {
        $("#seat-call-in-line-name").html("接警分机(软)：" + _pSeat.callIn);
    } else {
        $("#seat-call-in-line-name").html("接警分机：" + _pSeat.callIn);
    }

    //调度分机号
    if (_pSeat.callOutExt.isSoftphone == "1") {
        $("#seat-call-out-line-name").html("调度分机(软)：" + _pSeat.callOut);
    } else {
        $("#seat-call-out-line-name").html("调度分机：" + _pSeat.callOut);
    }

    //座席上线,并且分机开启接收
    upSeat(_pSeat.id,
        function (res) { //成功才发送心跳
            taskHeartbeatIntervalTimeout();
        },
        function (e, url, errMsg) {
            $.messager.alert('异常', '座席未上线，120电话号码有可能接听不到，请检查本座席分机状态，请重新登录！异常信息：' + errMsg);
        });

    //给副屏传递 token
    try {
        gProxy.loginSuccess(_token);
    } catch (e) {
        //告诉副屏可以解除挂起状态        
        bsProxy.loginSuccess(_token);
    }

    $('#dispatcher-name').text(_user.DisplayName);
    $('#dispatcher-code').text(_user.Username);
    if (_user.ScheduleTimeConfig) {
        $('#cur-schedule-name').text(_user.ScheduleTimeConfig.ScheduleName);
        //$('#cur-schedule-time').text(_user.ScheduleTimeConfig.StartTime + " - " + _user.ScheduleTimeConfig.EndTime);
    }
    $('#loginPanel').hide();
    showProcess(false);

    // 应用主屏配置
    setTimeout(function() {
        applyMainScreenConfigFromStorage();
        // 确保座席与分站状态数据在页面加载时就初始化，不需要等待tab被选中
        setTimeout(function() {
            initSeatStationStatusData();
        }, 200);
    }, 500);

    clickManagement()

    $('.inToday[id!="noAuto"]').click(); //设置默认为查询一天的记录
    //加载事件监控列表
    $('#dg-event-list').datagrid({
        loadMsg: '数据加载，请稍等.....',
        data: refreshEventlist(), //这一步是加载ajax查询的数据，非常重要
        onLoadSuccess: function () {
            //console.log("dg-event-list数据加载成功！！");
        },
        rowStyler: function (value, row, index) {
            //console.log("==========设置事件列表背景色 rowStyler")
            return getEventBackgroundColor(row);
        },
        onClickCell: function (index, field, value) {
            if (field == 'openVideoPage' && eventList[index].h5AVInvite == '1') {
                //打开与指导短信h5进行视频的web界面
                querySysConf('pphi_vedio_web_url',
                    function (data) {
                        //data值例子：https://*************:18085/dev-3d/#/Telemedicine
                        let url = data + '?answerUserName=' + _pSeat.seatId + '&answerUserId=' + _pSeat.id + "&taskId=" + eventList[index].eventId;
                        try {
                            gProxy.openBrowser(url);
                        } catch (e) {
                            window.open(url, '_blank');
                        }
                    },
                    function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
                    });

            }
        },
        onClickRow: function (rowIndex, rowData) {//鼠标单击事件
            if (rowData.eventStatus == '1' || rowData.eventStatus == '7' || rowData.eventStatus == '6') {
                $('#cancelEventDialog').linkbutton("enable"); //撤销事件按钮
                $('#cancelMobileProcessDialog').linkbutton("disable"); //撤销调度按钮
                $('#changeCarForProcessDialogOpen').linkbutton("disable"); //改派车辆按钮
                $('#recoverMobileProcessBtn').linkbutton("disable"); //恢复接诊按钮
                $('#changeMobileProcessMobileBtn').linkbutton("disable"); //纠正出车按钮
                //$('#assistStationDispatchBtn').linkbutton("disable"); //协助派车
            } else {
                $('#cancelEventDialog').linkbutton("disable");
                if (rowData.mobileStatus == '3') { //取消接诊状态任务
                    $('#cancelMobileProcessDialog').linkbutton("disable"); //撤销调度按钮
                    $('#changeCarForProcessDialogOpen').linkbutton("disable"); //改派车辆按钮
                    $('#changeMobileProcessMobileBtn').linkbutton("disable"); //纠正出车按钮
                    $('#recoverMobileProcessBtn').linkbutton("enable"); //恢复接诊按钮
                } else {
                    $('#cancelMobileProcessDialog').linkbutton("enable"); //撤销调度按钮
                    $('#changeCarForProcessDialogOpen').linkbutton("enable"); //改派车辆按钮
                    $('#changeMobileProcessMobileBtn').linkbutton("enable"); //纠正出车按钮
                    $('#recoverMobileProcessBtn').linkbutton("disable"); //恢复接诊按钮
                }

                /*if (rowData.mobileStatus == '5') {
                    $('#assistStationDispatchBtn').linkbutton("enable"); //协助派车
                } else {
                    $('#assistStationDispatchBtn').linkbutton("disable"); //协助派车
                }*/
                
            }

            refreshMobilesOrStations();//刷新车辆列表或分站列表

            var trs = $(this).prev().find('div.datagrid-body').find('tr');
            if (colorIndex !== rowIndex) {
                //当前点击的行和上次选中的行不一样
                if (colorIndex !== null && backgroundColor && trs[colorIndex]) {
                    //把上次选中的行的背景色还原
                    trs[colorIndex].style.backgroundColor = JSON.parse(JSON.stringify(backgroundColor))
                    trs[rowIndex].style.fontSize = seatFontSize;
                    trs[colorIndex].style.color = '#333'
                }
                //记录当前点击行原有的背景色
                backgroundColor = JSON.parse(JSON.stringify(trs[rowIndex].style.backgroundColor))
                colorIndex = rowIndex
                //设置选中背景色
                trs[rowIndex].style.backgroundColor = '#0074d8'
                trs[rowIndex].style.fontSize = seatFontSize;
                trs[rowIndex].style.color = '#ffffff'
            } else {
                //设置选中背景色
                trs[rowIndex].style.backgroundColor = '#0074d8'
                trs[rowIndex].style.fontSize = seatFontSize;
                trs[rowIndex].style.color = '#ffffff'
            }
        },
        onDblClickRow: function (rowIndex) { //鼠标双击事件
            //打开地图上事件所在位置
            var row = $('#dg-event-list').datagrid('getSelected');
            //console.log('打开事件row', row);
            try {
                gProxy.setPlaceBylonAndLat(row.eventId); //地图显示事件位置
            } catch (e) {
                bsProxy.setPlaceBylonAndLat(row.eventId); //地图显示事件位置
            }
        },
    });

    //事件监控中，全部选中，那么未调度与已调度等勾选框不选中
    $('#event-status-all').on('click', function () {
        if ($('#event-status-all').is(':checked')) {
            $("#event-status-unscheduled").prop("checked", true);
            $("#event-status-scheduled").prop("checked", true);
            $("#event-status-appointment").prop("checked", true);
            $("#event-status-waiting").prop("checked", true);
        } else {
            $("#event-status-unscheduled").prop("checked", false);
            $("#event-status-scheduled").prop("checked", false);
            $("#event-status-appointment").prop("checked", false);
            $("#event-status-waiting").prop("checked", false);
        }
    });
    $('#event-status-unscheduled').on('click', function () {
        eventSelectChecked();
    });
    $('#event-status-scheduled').on('click', function () {
        eventSelectChecked();
    });
    $('#event-status-appointment').on('click', function () {
        eventSelectChecked();
    });
    $('#event-status-waiting').on('click', function () {
        eventSelectChecked(); 
    });

    //加载座席与分站状态列表
    $('#dg-seat-station-status-list').datagrid({
        onClickRow: function (rowIndex, rowData) {
            $(this).datagrid('unselectRow', rowIndex);
        }
    });

    //加载座席与分站状态列表
    $('#dl-msg-list').datalist({
        onClickRow: function (rowIndex, rowData) {
            $(this).datagrid('unselectRow', rowIndex);
        }
    });

    //解除挂起副屏幕
    try {
        gProxy.unHangUpSecondScreen();
    } catch (e) {
        // B/S模式解除挂起副屏幕
        bsProxy.unHangUpSecondScreen();
    }

    //加载常用医院
    initHospital()

    //出车医生输入自动搜索
    $('#dispatchDoctorDri').combobox({
        prompt: '输入医生工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车护士输入自动搜索
    $('#dispatchNurseDri').combobox({
        prompt: '输入护士工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车司机输入自动搜索
    $('#dispatchDriverDri').combobox({
        prompt: '输入司机工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车护工输入自动搜索
    $('#dispatchWorkerDri').combobox({
        prompt: '输入护工工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });

    //获得出车班次
    $('#dispatchClassesDri').combobox({
        prompt: '请选择出车班次',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        },
        onSelect: function (record) {
            //找到当前的班次的出车医生护士等自动填写进去
            dispatchClassesSelect = dispatchClassesList.find((item) => item.id === record.id);
            var dispatchDoctorDatas = $('#dispatchDoctorDri').combobox('getData');
            for (var i = 0; i < dispatchDoctorDatas.length; i++) {
                if (dispatchDoctorDatas[i].id == dispatchClassesSelect.dispatchDoctorId) {
                    $('#dispatchDoctorDri').combobox('select', dispatchClassesSelect.dispatchDoctorId);
                    break;
                }
            }

            var dispatchNurseDatas = $('#dispatchNurseDri').combobox('getData');
            for (var i = 0; i < dispatchNurseDatas.length; i++) {
                if (dispatchNurseDatas[i].id == dispatchClassesSelect.dispatchNurseId) {
                    $('#dispatchNurseDri').combobox('select', dispatchClassesSelect.dispatchNurseId);
                    break;
                }
            }

            var dispatchWorkerDatas = $('#dispatchWorkerDri').combobox('getData');
            for (var i = 0; i < dispatchWorkerDatas.length; i++) {
                if (dispatchWorkerDatas[i].id == dispatchClassesSelect.dispatchWorkerId) {
                    $('#dispatchWorkerDri').combobox('select', dispatchClassesSelect.dispatchWorkerId);
                    break;
                }
            }

            var dispatchDriverDatas = $('#dispatchDriverDri').combobox('getData');
            for (var i = 0; i < dispatchDriverDatas.length; i++) {
                if (dispatchDriverDatas[i].id == dispatchClassesSelect.dispatchDriverId) {
                    $('#dispatchDriverDri').combobox('select', dispatchClassesSelect.dispatchDriverId);
                    break;
                }
            }

        },
        onChange: function (newValue, oldValue) {
        }

    });

    //协助派车选择车辆
    $('#checkCar').combobox({
        prompt: '输入车牌号',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'carName',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });

    if (isAssignTaskModeCar()) {
        //加载车辆监控列表
        $('#dg-mobiels-list').datagrid({
            loadMsg: '数据加载，请稍等.....',
            data: [],
            autoRowHeight: false,
            onLoadSuccess: function () {
                //加载成功后，checkbox不可以手动勾选
            },
            onDblClickRow: function (rowIndex) { //鼠标双击事件
            },
            onClickCell: function (index, field, value) {
                if (field == 'openCarVideoPage' && (mobileStatusList[index].h5AVInvite == '1' || mobileStatusList[index].statusStr == '发车')) {
                    let params = {
                        taskId: mobileStatusList[index].currentProcessId,
                        platformType: 0,
                        seatId: _pSeat.id,
                    }
                    createRoom(params, function (data) {
                        //打开与车载pad进行视频的web界面
                        querySysConf('car_vedio_web_url', function (data) {
                                // 本地调试地址   isCreatRoom用于判断是否创建/加入房间,这里是创建房间
                                // let data = 'http://localhost:8888/dev-3d/#/Consultation'
                                //data值例子：https://*************:18085/dev-3d/#/Consultation
                                let url = data + '?token=' + encodeURIComponent(_token) + '&seatId=' + _pSeat.id + "&taskId=" + mobileStatusList[index].currentProcessId + "&isCreatRoom=creatRoom"
                                try {
                                    gProxy.openBrowser(url);
                                } catch (e) {
                                    window.open(url, '_blank');
                                }
                            },
                            function (e, url, errMsg) {
                                //失败才做处理
                                $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
                            });
                    }, function (e, url, errMsg) {
                        //失败才做处理
                        $.messager.alert('异常', '发起与车头平台视频请求，异常信息：' + errMsg);
                    });
                }
            },
            onClickRow: function (rowIndex, rowData) { //鼠标单击事件 
                if (rowData.hCondition == "0") { //车况正常
                    if (rowData.status == '0') { //站内待命
                        $('#openMobileConditionWindowBtn').linkbutton("enable"); //修改车况按钮
                        $('#openUpdateAwaitMobileDialog').linkbutton("disable");//设为待命按钮
                        $('#updateForceTaskSyncBtn').linkbutton("disable");//强制同步按钮
                    } else {
                        $('#openMobileConditionWindowBtn').linkbutton("disable"); //修改车况按钮
                        $('#updateForceTaskSyncBtn').linkbutton("enable");//强制同步按钮
                        if (rowData.status == '5') {
                            $('#openUpdateAwaitMobileDialog').linkbutton("enable");//设为待命按钮
                        } else {
                            $('#openUpdateAwaitMobileDialog').linkbutton("enable");//设为待命按钮
                        }
                    }
                } else { //车况非正常
                    if(rowData.hCondition == '3'){
                        $('#openMobileConditionWindowBtn').linkbutton("enable"); //修改车况按钮
                    }else{
                        $('#openMobileConditionWindowBtn').linkbutton("disable"); //修改车况按钮
                    }
                    $('#openUpdateAwaitMobileDialog').linkbutton("disable");//设为待命按钮
                    $('#updateForceTaskSyncBtn').linkbutton("disable");//强制同步按钮

                }
            }
        });

        //车辆监控中，全部选中，那么待命/任务/暂停/未值班等勾选框不选中
        $('#mobile-status-all').on('click', function () {
            if ($('#mobile-status-all').is(':checked')) {
                $("#mobile-status-await-orders").prop("checked", true);
                $("#mobile-status-running-task").prop("checked", true);
                $("#mobile-status-pause").prop("checked", true);
                $("#mobile-status-not-on-duty").prop("checked", true);
            } else {
                $("#mobile-status-await-orders").prop("checked", false);
                $("#mobile-status-running-task").prop("checked", false);
                $("#mobile-status-pause").prop("checked", false);
                $("#mobile-status-not-on-duty").prop("checked", false);
            }
        });
        $('#mobile-status-await-orders').on('click', function () {
            mobileSelectChecked();
        });
        $('#mobile-status-running-task').on('click', function () {
            mobileSelectChecked();
        });
        $('#mobile-status-pause').on('click', function () {
            mobileSelectChecked();
        });
        $('#mobile-status-not-on-duty').on('click', function () {
            mobileSelectChecked();
        });

        //查询所有分站区域
        getAllStationRegionList(
            function (data) {
                //车辆列表分站区域下拉框
                data.map(r => {
                    r.sname = r.regionName
                    return r
                })
                data.unshift({ id: '', sname: '', regionName: '全部区域' })
                $('#stationRegionId').combobox({
                    prompt: '输入分站区域查询',
                    editable: true,
                    hasDownArrow: true,
                    valueField: 'id',
                    textField: 'regionName',
                    data: data,
                    filter: function (q, row) {
                        var opts = $(this).combobox('options');
                        return row[opts.textField].indexOf(q) > -1;
                    },
                    onSelect: function (params) {
                        getAllStationListByParams({"stationRegionId": params.id });
                    }
                });
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '查询分站区域信息错误，异常信息：' + errMsg);
            }
        );

        //初始查询所有分站
        getAllStationListByParams({});

    } else if (isAssignTaskModeStation()) {
        // 查询出所有分站区域
        getAllStationRegionList(
            function (data) {
                _stationRegionList = data || []
                // 遍历节点
                //设置分站显示区域
                renderStation(_stationRegionList)
                $('#search-regionName').textbox('textbox').bind('input', function (e) {
                    let filterlist = JSON.parse(JSON.stringify(_stationRegionList)).filter(r => r.regionName.indexOf(e.target.value) !== -1)
                    if (!filterlist || !filterlist[0]) {
                        //字母过滤
                        filterlist = JSON.parse(JSON.stringify(_stationRegionList)).filter(r => upperfirst(r.regionName, { upperfirst: true }).indexOf(e.target.value) !== -1)
                    }
                    renderStation(filterlist)
                })
            },
            function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '查询分站区域信息错误，异常信息：' + errMsg);
            }
        );
    }

    refreshMobilesOrStations();//刷新车辆列表或分站列表
    syncMobilesToSecondScreen();//同步车辆数据给第二屏幕

    refreshSelectSeatAndStation() //获取座席和分站信息

    //初始化左侧面板水平拖拽分隔条
    initLeftPanelDragSeparator();

    //视频座席相关处理；
    //开启座席配置需要的相关资源与页面显示。
    openVideoSeat();

    //定时任务统一放在这里
    _taskMobileInterval = true; //车辆定时任务开关
    _taskMsgInterval = true; //消息定时任务开关
    _h5AVInviteInterval = true; //指导视频H5音视频请求定时任务开关
    _noticeListInterval = true; //通知定时任务开关
    _carVideoInterval = true; //车载视频任务开关
    _servertimeInterval = true; //网络延时任务开关
    _taskCallStatusInterval = true; //分机状态定时任务开关
    _taskHeartbeatInterval = true; //心跳定时任务开关
    _refreshEventListInterval = true; //自动刷新事件列表开关

    _uploadAddInterval = true; //指导视频H5上报地址定时任务开关
	
	//获取接警的分机的实时电话状态
    taskCallStatusIntervalTimeout();
	//自动刷新事件列表
    refreshEventListIntervalTimeout()
    //设置车辆信息刷新计时器，定时刷新座席和分站状态
	taskMobileIntervalTimeout();
    //设置定时刷新消息通知
    getNoticeAlertTimeout();
    //设置定时刷新消息
    taskMsgIntervalTimeout();

    //获取待反馈区域联动信息弹窗通知
    getWaitingFeedbackListTimeout();
}



/** 根据条件查询所有分站 */
function getAllStationListByParams(params) {
    if (!params) {
        params = {};
    }
    // 获得分站班次
    getAllStationList(params,
        function (data) {
            //车辆列表分站下拉框
            data.map(r => {
                r.sname = r.stationName
                return r
            })
            data.unshift({ sname: '', stationName: '全部分站' })
            $('#stationName').combobox({
                prompt: '输入分站名称查询',
                editable: true,
                hasDownArrow: true,
                valueField: 'sname',
                textField: 'stationName',
                data: data,
                filter: function (q, row) {
                    var opts = $(this).combobox('options');
                    return row[opts.textField].indexOf(q) > -1;
                },
                onSelect: function (params) {
                },
            });
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '查询分站信息错误，异常信息：' + errMsg);
        }
    );
};

function getEventBackgroundColor(row) {
    //事件列表颜色说明：
    //①白底黑字：新调度事件分站未受理；分站完成调度任务回院。
    //②绿底黑字：执行调度任务未完成。
    //③黄底黑字：分站救护车还未出发，患者来电取消任务，分站在平板上取消派车。
    //④蓝底黑字：分站救护车已经出发，患者来电取消任务，分站在平板上取消派车，空车回院。
    //⑤重大事件：白底黑字加粗倾斜表示重大事件。
    //0-普通事件 1-重大事件
    switch (row.eventType) {
        case '1':
            return `background-color:#ffffffff;font-weight: 900;font-style: italic;font-size: ${seatFontSize}`;//白底；字体加粗；倾斜
        case '0':
            switch (row.eventStatus) {
                case '1':
                    return `background-color:#ffffffff;font-size: ${seatFontSize}`;//白底
                case '7':
                    return `background-color:#ffffffff;font-size: ${seatFontSize}`;//橙黄
                case '6':
                    return `background-color:#ffffffff;font-size: ${seatFontSize}`;//橙黄
                case "2":
                    switch (row.mobileStatus) {
                        case '5':
                            return `background-color:#ffffffff;font-size: ${seatFontSize}`;//白底
                        case "6":
                            return `background-color:#aded8ca1;font-size: ${seatFontSize}`;//绿色
                        case "1":
                            return `background-color:#aded8ca1;font-size: ${seatFontSize}`;//绿色
                        case "2":
                            return `background-color:#aded8ca1;font-size: ${seatFontSize}`;//绿色
                        case "3":
                            return `background-color:#93d1ff;font-size: ${seatFontSize}`;//蓝色
                        case "8":
                            return `background-color:#aded8ca1;font-size: ${seatFontSize}`;//绿色
                        case "4":
                            return `background-color:#aded8ca1;font-size: ${seatFontSize}`;//绿色
                        case "7":
                            return `background-color:#ffec99;font-size: ${seatFontSize}`;//橙黄
                    }
            }
    }
    return '';
}

/** 设置事件列表背景颜色 */
function setListColor(rowIndex) {
    //设置选中背景色
    var trs = $('#dg-event-list').prev().find('div.datagrid-body').find('tr');
    trs[rowIndex].style.backgroundColor = '#0074d8'
    trs[rowIndex].style.fontSize = seatFontSize;
    trs[rowIndex].style.color = '#ffffff'
}
/**
 * 手持终端联系电话
 * @param value
 * @param row
 * @param index
 */
function taskPhoneFormatter(value, row, index) {
    if (row.taskPhone) {
        return `<span style="color: #0AA0FB; cursor: pointer;" onclick="callingPhone2('${row.taskPhone}','${row.taskPhone}')" title="拨打">${row.taskPhone}</span>`;
    }
    return '';
}

/**
 * 车辆联系电话
 * @param value
 * @param row
 * @param index
 */
function carContactFormatter(value, row, index) {
    if (!value) {
        return '';
    }
    if (/\d/.test(value)) {
        return formatPhoneNumber(value);
    }
    return value;
}

function stationIsLoginIconFormatter(value, row, index) {
    switch (row.isLogin) {
        case "0":
            return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
        case "1":
            return '<img src="./style/img/yiyuan_zaixian_ic.png"/>';
        default:
            return '<img src="./style/img/yiyuan_lixian_ic.png"/>';
    }
}
function stationIsLoginFormatter(value, row, index) {
    return getStationIsLoginStr(value);
}
function vehicleStatusFormatter(value, row, index) {
    // 拼接多个字段的值，并格式化显示
    var statusCount = `总数: ${row.countByNum}, `
        + `未值班: ${row.notOnDutyByNum}, `
        + `暂停: ${row.pauseByNum}, `
        + `任务: ${row.taskByNum}`;
    return statusCount;
}
/**
 * 分站电话
 * @param value
 * @param row
 * @param index
 */
function stationContactFormatter(value, row, index) {
    if (!value) {
        return '';
    }
    if (/\d/.test(value)) {
        return formatPhoneNumber(value);
    }
    return value;
}
/**
 * 分站备注
 * @param value
 * @param row
 * @param index
 */
function stationRemarkFormatter(value, row, index) {
    if (!value) {
        return '';
    }
    if (/\d/.test(value)) {
        return formatPhoneNumber(value);
    }
    return value;
}

// 格式化文本中的电话
function formatPhoneNumber(text) {
    // 定义正则表达式来匹配固定电话号码的三种格式
    // (xxxx)xxxxxxxx 或 xxxx-xxxxxxxx 或 xxxxxxxxxxx
    const phoneRegex = /\((\d{4})\)(\d{7,8})|(\d{4})-(\d{7,8})|(\d{11})/g;

    // 使用回调函数替换匹配到的电话号码，并返回处理后的HTML字符串
    const formatText = text.replace(phoneRegex, (match) => {
        const phoneNumberOrigin = match;
        const phoneNumber = match.trim().replace(/[\(\)-]/g, ''); // 去除括号和连字符
        return `<span style="color: #0AA0FB;cursor: pointer;" onclick="callingPhone2('${phoneNumberOrigin}','${phoneNumber}')" title="拨打">${match}</span>`;
    });
    return formatText; // 返回处理后的HTML字符串，供后续插入到DOM中
}

//勾选是否有空闲车辆分站
$('#chkCarStandBy').on('change', function () {
    loadStationList()
});
//勾选分站在线
$('#chkStationIsLogin').on('change', function () {
    loadStationList()
});

//需求变更-->座席端下方列表（分站）
function loadStationList() {
    var params = {
        eventLat: '',
        eventLng: '',
        id: '',
        stationName: '',
        standBy : '',
    }
    //计算距离获取的经纬度
    var eventRows = $('#dg-event-list').datagrid('getSelections');
    var num = eventRows.length;
    if (num == 1) {
        if (eventRows[0].lat != null && eventRows[0].lng != null) {
            params.eventLat = eventRows[0].lat
            params.eventLng = eventRows[0].lng
        }
    }
    //是否已经选中区域
    if (_selectedRegion) {
        params.stationRegionId = _selectedRegion.id
    }
    
    //是否勾选有空闲车辆分站
    var isChecked = $('#chkCarStandBy').prop('checked');
    if (isChecked) {
        params.standBy = '1'
    } else {
        params.standBy = ''
    }

    //勾选分站在线
    var isCheckedStationIsLogin = $('#chkStationIsLogin').prop('checked');
    if (isCheckedStationIsLogin) {
        params.isLogin = '1'
    } else {
        params.isLogin = ''
    }
    
    getStationInfo(params,
        function (res) {
            if (res) {
                $('#station-list').datagrid('loadData', res); // 使用 loadData 方法加载数据
            } else {
                $.messager.alert("分站列表获取数据失败");
            }
            //主屏车辆列表/分站列表首次加载成功
            if (!isMobilesOrStationsFirstLoaded) {
                isMobilesOrStationsFirstLoaded = true;
                //console.log("=====isMobilesOrStationsFirstLoaded set true", isMobilesOrStationsFirstLoaded)
            }
        }, function (e, url, errMsg) {
            // showProcess(false);
            // //失败才做处理
            $.messager.alert('提示', '分站列表获取数据失败，异常信息：' + errMsg);
        })
}

//分站列表操作
function stationOperationFormatter(value, row, index) {
    let node = '';
    if (row) {
        if (row.standByNum > 0) {
            node += `
                    <a href="javascript:void(0);"
                       onclick="sendTaskBtn('${ row.id}');" 
                       style="color: #39c; width: 90px; font-size: 12px; margin-right: 10px; top: 0; padding: 3px 8px;">
                       派发调遣
                    </a>
                `;
        }
    }
    return node;
}

/**
 * 设置分站区域显示方法
 * @param {any} data
 */
function renderStation(data) {
    // 清除当前区域列表
    $(".station-letters").remove();

    // 如果数据有值，先添加"全部"选项
    if (data.length > 0) {
        $('#station-region').append('<li id="" class="selected station-letters">全部</li>');
    }

    // 渲染其他区域
    for (var i = 0; i < data.length; i++) {
        var d = data[i]; // 获取区域数据
        $('#station-region').append('<li id="{0}" class="station-letters">{1}</li>'.formatStr(d.id, d.regionName));
    }

    // 给每个li元素绑定点击事件
    $('.station-letters').on('click', function (e) {
        edcui_selectLi(e);
        var regionId = $(this).attr('id'); // 获取当前li的id
        _selectedRegionId = regionId;
        _selectedRegion = {
            id: regionId,
            name: $(this).text() // 获取当前li的文本值
        };
        loadStationList(); // 调用接口并更新表格
    });
}

// 刷新分站列表
$('#refresh-button').on('click', function () {
    loadStationList(); // 调用接口更新表格
});


/** 查看顶部消息铃铛按钮的详细信息 */
function noticeDetail() {
    // 直接打开消息列表页
    openTab("通知单列表", "noticeList.html");
}

/** 按钮禁用状态切换后点击无效问题覆盖方法 */
function clickManagement() {
    /**
     * linkbutton方法扩展
     * @param {Object} jq
     */
    $.extend($.fn.linkbutton.methods, {
        /**
         * 激活选项（覆盖重写）
         * @param {Object} jq
         */
        enable: function (jq) {
            return jq.each(function () {
                var state = $.data(this, 'linkbutton');
                if ($(this).hasClass('l-btn-disabled')) {
                    var itemData = state._eventsStore;
                    //恢复超链接
                    if (itemData.href) {
                        let { id } = state.options
                        if (id && window[id] && window[id].constructor == Function) {
                            $(this).attr("href", 'javascript:' + id + '()')
                        } else {
                            $(this).attr("href", itemData.href);
                        }
                    }
                    //回复点击事件
                    if (itemData.onclicks) {
                        for (var j = 0; j < itemData.onclicks.length; j++) {
                            $(this).bind('click', itemData.onclicks[j]);
                        }
                    }
                    //设置target为null，清空存储的事件处理程序
                    itemData.target = null;
                    itemData.onclicks = [];
                    $(this).removeClass('l-btn-disabled');
                }
            });
        },
        /**
         * 禁用选项（覆盖重写）
         * @param {Object} jq
         */
        disable: function (jq) {
            return jq.each(function () {
                var state = $.data(this, 'linkbutton');
                if (!state._eventsStore)
                    state._eventsStore = {};
                if (!$(this).hasClass('l-btn-disabled')) {
                    var eventsStore = {};
                    eventsStore.target = this;
                    eventsStore.onclicks = [];
                    //处理超链接
                    var strHref = $(this).attr("href");
                    if (strHref) {
                        eventsStore.href = strHref;
                        $(this).attr("href", "javascript:void(0)");
                    }
                    //处理直接耦合绑定到onclick属性上的事件
                    var onclickStr = $(this).attr("onclick");
                    if (onclickStr && onclickStr != "") {
                        eventsStore.onclicks[eventsStore.onclicks.length] = new Function(onclickStr);
                        $(this).attr("onclick", "");
                    }
                    //处理使用jquery绑定的事件
                    var eventDatas = $(this).data("events") || $._data(this, 'events');
                    if (eventDatas["click"]) {
                        var eventData = eventDatas["click"];
                        for (var i = 0; i < eventData.length; i++) {
                            if (eventData[i].namespace != "menu") {
                                eventsStore.onclicks[eventsStore.onclicks.length] = eventData[i]["handler"];
                                $(this).unbind('click', eventData[i]["handler"]);
                                i--;
                            }
                        }
                    }
                    state._eventsStore = eventsStore;
                    $(this).addClass('l-btn-disabled');
                }
            });
        }
    });
    return;
    $("#openUpdateAwaitMobileDialog").bind("click", function () {
        if (!$(this).linkbutton('options').disabled) {
            openUpdateAwaitMobileDialog()
            return;
        };
        return;
    })
    $("#updateForceTaskSyncBtn").bind("click", function () {
        if (!$(this).linkbutton('options').disabled) {
            updateForceTaskSyncBtn()
            return;
        };
        return;
    })
    $("#openMobileConditionWindowBtn").bind("click", function () {
        if (!$(this).linkbutton('options').disabled) {
            openMobileConditionWindowBtn()
            return;
        };
        return;
    })
}
function changePwdSuccess() {
    $.messager.alert('修改密码', '密码修改成功！');
    showLogin();//showLogin有调用 checkActiveShiftSeat()
}

//确认派车//协助派车(函数重写)
function confirmReceiveSubmit() {
    const mobileProcessId = $('#mobileProcessId').val();

    // 根据 assistType 和 assignTaskMode 判断 carId 的选择逻辑
    const carId = (assistType == 1 && isAssignTaskModeStation()) ? $('#checkCar').combobox('getValue') : $('#dispatchCarId').val();

    const eventId = $('#dispatchEventId').val();

    // 辅助函数用于从下拉选择框中获取详细信息
    function getDispatchDetails(selector) {
        const id = $(selector).combobox('getValue');
        const data = $(selector).combobox('getData');
        let name = "", num = "", phone = "";

        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) {
                name = data[i].xm || "";
                num = data[i].username || "";
                phone = data[i].phone || "";
                break;
            }
        }

        return { id, name, num, phone }; // 返回所有必要的字段
    }

    // 获取各类出车人员的信息
    const dispatchDoctor = getDispatchDetails('#dispatchDoctorDri');
    const dispatchNurse = getDispatchDetails('#dispatchNurseDri');
    const dispatchWorker = getDispatchDetails('#dispatchWorkerDri');
    const dispatchDriver = getDispatchDetails('#dispatchDriverDri');

    // 验证数据完整性
    const personnelToVerify = [
        { ...dispatchDoctor, msg: '出车医生' },
        { ...dispatchNurse, msg: '出车护士' },
        { ...dispatchDriver, msg: '出车司机' },
        { ...dispatchWorker, msg: '出车护工' }
    ];

    const msgArr = personnelToVerify.filter(person => 
        !(person.name && person.id && person.num) && (person.name || person.id || person.num)
    ).map(person => person.msg);

    if (msgArr.length > 0) {
        return $.messager.alert('温馨提示', `${msgArr.join(',')}数据不完整，请在下拉列表选择，不要手动输入数据`, 'error');
    }

    showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');

    // 获取座席的细节
    const seatDetails = {
        seatId: _pSeat.id,
        seatCode: _pSeat.seatId,
        seatUserId: _pSeat.userId,
        seatUserName: _pSeat.userName,
        seatUser: _pSeat.user
    };

    // 通用接口参数
    const commonParams = {
        dispatchDoctorId: dispatchDoctor.id,
        dispatchDoctorNum: dispatchDoctor.num,
        dispatchDoctor: dispatchDoctor.name,
        dispatchDoctorPhone: dispatchDoctor.phone,

        dispatchNurseId: dispatchNurse.id,
        dispatchNurseNum: dispatchNurse.num,
        dispatchNurse: dispatchNurse.name,
        dispatchNursePhone: dispatchNurse.phone,

        dispatchDriverId: dispatchDriver.id,
        dispatchDriverNum: dispatchDriver.num,
        dispatchDriver: dispatchDriver.name,
        dispatchDriverPhone: dispatchDriver.phone,

        dispatchWorkerId: dispatchWorker.id,
        dispatchWorkerNum: dispatchWorker.num,
        dispatchWorker: dispatchWorker.name,
        dispatchWorkerPhone: dispatchWorker.phone,

        carId
    };

    const handleResponse = (message) => ({
        success: () => {
            showProcess(false);
            refreshEventlist();
            refreshMobilesOrStations();//刷新车辆列表或分站列表
            $('#confirm-receive-editor').window('close', true);
            $.messager.alert('提示', message, 'info');
        },
        error: (e, url, errMsg) => {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        }
    });

    // 判断派车类型并执行相应API请求
    if (assistType == 1) {//1 协助分站派车
        const assistParams = {
            mobileProcessId: eventId,
            ...commonParams
        };
        assistStionSendCar(assistParams, handleResponse('派车成功！').success, handleResponse().error);
    } else if (assistType == 2) {//2 座席派车（座席端直接派车给车辆，不经过分站确认）
        const params = {
            id: '',
            eventId,
            toStatus: '6',
            ...seatDetails,
            ...commonParams
        };
        addMobileProcess(params, handleResponse('派车(增派)车辆成功！').success, handleResponse().error);
    }
}

var isClick = false; //防止多次点击登录按钮
function doLogin() {
    if (!isClick) {
        isClick = true;
        showProcess(true, "正在登录...");
        var isSavePsw = "0";
        if ($('#is-save-psw').is(':checked')) {
            isSavePsw = "1";
        }
        try {
            //使用C/S 的C#的登录
            login($('#login-un').val(), $('#login-pwd').val(), isSavePsw, $('#login-seat').val(), $("#login-schedule-time-config-id").val(),
                function (res) {
                    if (res.success == true) {
                        loginSuccess();
                    } else {
                        showProcess(false);
                        $.messager.alert('登录失败', res.msg);
                    }
                });
        } catch (e) {
            // B/S模式用户登录执行
            //记住密码
            //如果是记住密码，那么保存当前登录的用户和密码
            if (isSavePsw == "1") {
                //将密码保存到localStorage中
                window.localStorage.setItem('_lastLoginUser', "{\"Username\":\"" + $('#login-un').val() + "\",\"SeatId\":\"" + $('#login-seat').val() + "\",\"Password\":\"" + $('#login-pwd').val() + "\",\"SavePsw\": true,\"DisplayName\": null}");
            } else {
                //清空localStorage里面的 saveUserCache 字段
                window.localStorage.removeItem('_lastLoginUser');
            }

            let params = {
                un: encryptByDES($('#login-un').val()),
                pwd: encryptByDES($('#login-pwd').val()),
                savePsw: isSavePsw,//记住密码
                seatId: $('#login-seat').val(),//座席ID，登录的时候web端选择座席记录的seatId
                scheduleTimeConfigId: $("#login-schedule-time-config-id").val(),//班次id
                stationId: ""
            }
            loginIn(params, function (res) {
                _user = {
                    AccType: res.accType,
                    Contact: res.contact,
                    DisplayName: res.displayName,
                    LoginPlace: res.loginPlace,
                    Station: null,
                    Token: res.token,
                    Id: res.userId,
                    Username: res.username,
                    ScheduleTimeConfig: null
                };
                if (res.scheduleTimeConfig != null) {
                    _user.ScheduleTimeConfig = {
                        Id: res.scheduleTimeConfig.id,
                        ScheduleName: res.scheduleTimeConfig.scheduleName,
                        //StartTime: res.scheduleTimeConfig.startTime,
                        //EndTime: res.scheduleTimeConfig.endTime,
                        NextId: res.scheduleTimeConfig.nextId,
                        NextScheduleName: res.scheduleTimeConfig.nextScheduleName,
                        //NextStartTime: res.scheduleTimeConfig.nextStartTime,
                        //NextEndTime: res.scheduleTimeConfig.nextEndTime
                    };
                }
                _token = _user.Token;
                //存储到localStorage，方便其他页面进行调用
                window.localStorage.setItem("_token", _user.Token);
                //存储到localStorage，方便其他页面进行调用
                window.localStorage.setItem("_user", JSON.stringify(_user));
                //校验是否能够登录到座席端
                if (!(_user.AccType == "ROLE_ADMIN" || _user.AccType == "AMBULANCE_DISPATCHER" || _user.AccType == "STATION_LEADER" || _user.AccType == "SEAT_DIRECTOR")) {
                    showProcess(false);
                    $.messager.alert('提示', '登录失败：' + '该账号没有使用120调度座席端权限，请联系管理员开通权限。');
                    return null;
                }

                let params = {
                    id: $('#login-seat').val()//座席ID，登录的时候web端选择座席记录的seatId
                }
                //座席上线
                upSeat($('#login-seat').val(), function (upSeatData) {
                    //座席上线成功
                    //获取座席信息，将座席数据传送给html界面中
                    selectSeatInfo(params, function (seatData) {
                        _pSeat = seatData;
                        //存储到localStorage，方便其他页面进行调用
                        window.localStorage.setItem("_pSeat", JSON.stringify(seatData));
                        $('#seat-num').html(_pSeat.seatId);
                        //座席上线
                        // loginSuccess();
                        loadSysConfigAndProceed()
                        

                    }, function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('提示', '登录失败：' + errMsg);
                    })
                }, function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('提示', '座席上线失败：' + errMsg);
                })
            }, function (e, url, errMsg) {
                showProcess(false);
                //失败才做处理
                $.messager.alert('提示', '登录失败：' + errMsg);
            })
        }

        isClick = false
        return false;
    }
}

// 获取系统地图key配置
function loadSysConfigAndProceed() {
    querySysConf('map_config',
        function (res) {
            try {
                const config = JSON.parse(res);
                // 存到 localStorage，键名自定义为 sysConfigMap
                window.localStorage.setItem('sysConfigMap', JSON.stringify(config));
                loadFenceListAndProceed() // 获取围栏列表
            } catch (e) {
                console.error('解析系统配置失败', e);
                $.messager.alert('错误', '系统配置格式异常');
            }
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('错误', '获取系统配置失败：' + errMsg, 'error');
        }
    );
}

// 获取围栏列表
function loadFenceListAndProceed() {
    getFenceList({},
        function (res) {
            try {
                // 存储围栏列表到 localStorage
                window.localStorage.setItem('fenceList', JSON.stringify(res));
                // console.log('围栏列表获取成功', res);
                //登录成功
                loginSuccess();
            } catch (e) {
                console.error('解析围栏列表失败', e);
                // 即使围栏列表获取失败也继续登录
                //loginSuccess();
            }
        },
        function (e, url, errMsg) {
            console.error('获取围栏列表失败：' + errMsg);
            // 即使围栏列表获取失败也继续登录
            //loginSuccess();
        }
    );
}

//开启座席配置需要的相关资源与页面显示。
function openVideoSeat() {
    GetSeatConfigInfoById({ id: _pSeat.id }, function (res) {
        // 处理返回数据，控制元素的显示和隐藏
        if (res) {
            _isVideoSeat = res.isVideoSeat;
            // 1. 根据isVideoSeat控制videoCallInShow的显示或隐藏
            if (_isVideoSeat === "1") {
                taskVideoStatusIntervalTimeout();
                $("#videoCallInShow").show();
            } else {
                $("#videoCallInShow").hide();
            }
            
            // 2. 根据callIn控制phoneCallInShow的显示或隐藏
            if (res.callIn && res.callIn !== "") {
                $("#phoneCallInShow").show();
            } else {
                $("#phoneCallInShow").hide();
            }
            
            // 3. 根据callIn和isVideoSeat控制callInStatusLine的显示或隐藏
            if (res.callIn && res.callIn !== "" && _isVideoSeat === "1") {
                $("#callInStatusLine").show();
            } else {
                $("#callInStatusLine").hide();
            }
        }
    }, function (e, url, errMsg) {
        // 获取座席配置信息失败
    });
}

function doChangePwd() {
    var newPwd1 = $('#cp-newPwd1').val();
    var newPwd2 = $('#cp-newPwd2').val();
    if (newPwd1 != newPwd2) {
        $.messager.alert('修改密码', '新密码两次输入不同！', 'alert');
    } else {
        var res = gProxy.changePwd($('#cp-un').val(), $('#cp-pwd').val(), newPwd1);
        if (res == true) {
            changePwdSuccess();
        } else {
            $.messager.alert('修改密码', res);
        }
    }
    return false;
}

function doHang() {
    if (gProxy.isPhoneFree() == false) {
        //挂机
        $.messager.confirm('无效挂机',
            '确定要标记此次呼叫为无效呼叫并挂机吗？',
            function (s) {
                if (s == true) {
                    gProxy.hang();
                }
            });
    } else {
        $.messager.alert('话机空闲', '当前未在通话中。');
    }
}

function easyUiDateTimeSet() {
    $('.easyui-datetimebox').each(function () {
        var e = $(this);
        e.datetimebox('setValue', e.val());
    });
}

function getConditionFromCode(code) {
    if (code == '1') {
        return '<span style="color:red;">维护中</span>';
    } else if (code == '2') {
        return '<span style="color:red;">驻场中</span>';
    } else if (code == '3') {
        return '<span style="color:red;">未值班</span>';
    } else {
        return '';
    }
}

/** 地图选中后回显派车界面的地址一栏,地图屏幕回显 */
function echoMapSelected(address, lng, lat) {
    /*$('#event-lat').val(lat);
    $('#event-lng').val(lng);
    //console.log("副屏幕选择事件地址，主屏幕联动，调度所选择区域:" + $("#dis-region").find(".selected").attr('id'));
    //车辆距离重新计算,看用户是否点击了区域
    if ($("#dis-region").find(".selected")) {
        var id = $("#dis-region").find(".selected").attr('id');
        var carList;
        if (id == 'region_all') {
            carList = _mobilesWithDistance.where(function (d) {
                return d.status == "0" || d.status == "3";
            });
        } else {
            carList = WithDistance.where(function (d) {
                return d.stationId == id && (d.status == "0" || d.status == "3");
            });
        }
    } else {
     
    }*/

    //获得选中的tab的iframe的dom节点
    var selectIframe = $("#main-tabs").find("iframe")[$(".tabs-header ul li").index($('.tabs-selected')) - 1];
    if (selectIframe != null) {
        //调用iframe中的函数
        if (window.frames[selectIframe.name] && window.frames[selectIframe.name].echoMapSelected) {
            window.frames[selectIframe.name].echoMapSelected(address, lng, lat);
        }
    }

}

function G(id) {
    return document.getElementById(id);
}

var _menuPhone = {
    "sta": "",
    "car": ""
};

function menuHandler(item) {
    var name = item.name;
    var number = '';
    if (name == 'call_station') {
        //呼叫分站
        number = _menuPhone.sta;
    } else if (name == 'call_car') {
        //呼叫车辆
        number = _menuPhone.car;
    }
    $.messager.confirm('',
        '拨打号码 ' + number + ' 吗？',
        function (res) {
            if (res) {
                gProxy.call(number);
            }
        });
}

/*function reportBigEvent() {
    if (_currentEvent != null) {
        $.messager.confirm('',
            '确定要将此事件作为重大事件上报吗？\n请谨慎操作。',
            function (r) {
                if (r) {
                    reportEvent(_currentEvent.id,
                        function (res) {
                            if (res.success) {
                                $.messager.alert('', '上报成功。');
                            } else {
                                $.messager.alert('', '上报失败，发生错误。', 'error');
                            }
                        });
                }
            });
    } else {
        $.messager.alert('', '你需要先进行调度或同步事件才能上报。', 'warn');
    }
}*/

/**
 * 在iFrame中打开一个新tab
 * @param title
 */
function openTab(title, href, iframeName) {
    $('#main-tabs').tabs({
        onClose: closeTab
    });

    var e = $('#main-tabs').tabs('exists', title);
    if (e) {
        $("#main-tabs").tabs('select', title);
        var tab = $("#main-tabs").tabs('getSelected');
        $('#main-tabs').tabs('update', {
            tab: tab,
            options: {
                id: iframeName,
                title: title,
                content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                closable: true,
                selected: true
            }
        });
    } else {
        $('#main-tabs').tabs('add', {
            id: iframeName,
            title: title,
            content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
            iconCls: '',
            closable: true
        });
    }

}
/** 关闭tab */
function closeTab(title, index) {
    var e = $('#main-tabs').tabs('exists', title);
    if (e) {
        $("#main-tabs", parent.document).tabs('close', title);
    }

    var tabs = $('#main-tabs').tabs("tabs")
    //判断是否打开了创建新事件的tab页
    let foundCreatingEventTab = false;
    for (var i = 0; i < tabs.length; i++) {
        let aTitle = $('#main-tabs').tabs('getTab', i).panel('options').title;
        if (aTitle.indexOf('创建新事件') != -1) {
            foundCreatingEventTab = true;
            break;
        }
    }
    if (foundCreatingEventTab) {
        isCreatingEvent = true
    } else {
        isCreatingEvent = false
    }
}
/** 打开事件详情 */
function openEventInfoTab() {
    //获得当前选中的行
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-event-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条事件进行查看!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条事件进行查看!', 'error');
        return;
    }

    //$.messager.alert('提示', '选中的行，事件ID：' + rows[0].eventId + "，任务ID：" + rows[0].processId , 'info');

    var title = "事件详情" + "(" + rows[0].eventId + ")"
    openTab(title, "eventInfo.html?eventId=" + rows[0].eventId, rows[0].eventId);
}

/** 打开公告信息的页面 */
function openNoticeMessage() {

}
/** 打开创建新事件的tab */
function openCreateNewEventTab() {
    isCreatingEvent = true
    var title = "事件受理(手工制单)"
    openTab(title, "createEvents.html?callUid=&phone=&callInTimes=", 'createEventsIframe');
}

/** 打开创建新事件的tab(通过电话) */
function openCreateNewEventTabByPhone(phone, callUid, callInTimes, isPhone) {
    isCreatingEvent = true
    var title = "事件受理-电话(" + callUid + ")"
    openTab(title, "createEvents.html?isVideo=0&callUid=" + callUid + "&phone=" + phone + "&callInTimes=" + callInTimes + "&isPhone=" + isPhone, 'createEventsIframe' + callUid);
}

function keepCall(){
    holdCall(_pSeat.callIn, 
        function (res) {
            // $("#keepCallBtn").hide();
            // $("#recoveryCallBtn").show();
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', '呼叫保持错误：' + errMsg, 'error');
        }
    )
}

function recoveryCall(){
    unholdCall(_pSeat.callIn,
        function (res) {
            // $("#keepCallBtn").show();
            // $("#recoveryCallBtn").hide();
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', '呼叫接回错误：' + errMsg, 'error');
        }
    )
}

/** 打开创建新事件的tab(通过区域联动) */
function openCreateNewEventTabByRegionLinkage(regionLinkageId,externalBizId) {
    isCreatingEvent = true
    var title = "事件受理(区域联动)"
    openTab(title, "createEvents.html?externalBizId="+externalBizId+"&regionLinkageId=" + regionLinkageId, 'createEventsIframe' + regionLinkageId);
}

/**
 * 打开创建新事件的tab(通过视频呼救)
 * @param {*} phone //视频呼救的号码
 * @param {*} callUid //视频呼救的id 
 * @param {*} callInTimes 呼入事件
 * @param {*} isPhone 是否显示相同号码上次的呼入记录
 * 
 * 以下是视频呼救的特殊参数
 * @param {*} address 现场地址
 * @param {*} lng 经度，保留6位小数，>0东经 <0西经
 * @param {*} lat 纬度，保留6位小数，>0北纬 <0南纬
 * @param {*} nickName 用户昵称
 * @param {*} avatarUrl 用户头像URL
 * @param {*} gender 性别 0-未知 1-男性 2-女性
 */
function openCreateNewEventTabByVideo(phone, callUid, callInTimes, isPhone, address, lng, lat, nickName, avatarUrl, gender) {
    isCreatingEvent = true
    var title = "事件受理-视频呼救(" + callUid + ")"
    openTab(title, "createEvents.html?callUid=" + callUid + 
        "&phone=" + phone + 
        "&callInTimes=" + callInTimes + 
        "&isPhone=" + isPhone + 
        "&address=" + encodeURIComponent(address) + 
        "&lng=" + lng + 
        "&lat=" + lat + 
        "&nickName=" + encodeURIComponent(nickName) + 
        "&avatarUrl=" + encodeURIComponent(avatarUrl) + 
        "&gender=" + gender + 
        "&isVideo=1",
        'createEventsIframe' + callUid);
}

/** 挂起调度 */
function hangUpBtn() {
    showProcess(true, "正在挂起座席...");
    $("#hangUpDialogPsw").passwordbox('setValue', '');
    var params = { "seatId": _pSeat.id, "hangOrRenew": "hang" }
    seatHangUp(params,
        function (res) {
            showProcess(false);
            $('#hangUpDialog').window('open');
            try {
                gProxy.hangUpSecondScreen();
            } catch (e) {
                bsProxy.hangUpSecondScreen();
            }
            window.localStorage.setItem('hangId', res);
        },
        function (e, url, errMsg) {
            showProcess(false);
            //失败才做处理
            $.messager.alert('提示', '挂起失败，异常信息：' + errMsg);
        });

}

/** 解除挂起调度 */
function unHangUpBtn() {
    showProcess(true, "正在解除挂起座席...");
    var password = $("#hangUpDialogPsw").val();
    if (password == null || password == "") {
        $.messager.alert('提示', '请输入密码', "info");
        showProcess(false);
        return false;
    }

    var params = { "password": password, "username": _user.Username }
    checkPassword(params,
        function (res) {
            const hangId = window.localStorage.getItem('hangId');
            params = { "seatId": _pSeat.id, "hangOrRenew": "renew", "hangId": hangId }
            seatHangUp(params,
                function (res) {
                    showProcess(false);
                    $('#hangUpDialog').window('close', true);
                    //解除挂起副屏幕
                    try {
                        gProxy.unHangUpSecondScreen();
                    } catch (e) {
                        // B/S模式解除挂起副屏幕
                        bsProxy.unHangUpSecondScreen();
                    }
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('提示', '解除挂起失败，异常信息：' + errMsg);
                });
        },
        function (e, url, errMsg) {
            showProcess(false);
            //失败才做处理
            $.messager.alert('提示', '解除挂起验证用户信息失败，' + errMsg);
        });
}

function getNetworkDelayTimeout() {
    clearTimeout(window.servertimeInterval);
    if (_servertimeInterval) {
        window.servertimeInterval = null;
        getNetwork(function (data) {
            $('#network-delay').html("延时：" + data + 'ms');
        })
        window.servertimeInterval = setTimeout(getNetworkDelayTimeout, 10000);
    }
};
/** 打开车辆状况界面 */
function openMobileConditionWindowBtn() {
    //获得当前选中的行
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-mobiels-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择车辆进行查看!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条车辆记录,只能选择一条车辆记录进行车况修改!', 'error');
        return;
    }
    if (rows[0].status != '0') {
        $.messager.alert('提示', '当前车辆正在运行中，不能修改车辆状态，只能在待命时才能修改车辆状态。', 'error');
        return false;
    }
    $('#updateMobileConditionDialog').window('open');
    $('#updateConditionMobileId').val(rows[0].carId);
    checkedCondition(rows[0].hCondition);
}

/** 强制同步车辆信息到急救平台 */
function updateForceTaskSyncBtn() {
    //获得当前选中的行
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-mobiels-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择车辆进行强制实时同步车辆状态!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条车辆记录,只能选择一条车辆记录进行强制实时同步车辆状态!', 'error');
        return;
    }
    if (rows[0].status == '0') {
        $.messager.alert('提示', '当前车辆待命状态，不用强制同步车辆状态。', 'error');
        return false;
    }

    showProcess(true, '温馨提示', '正在强制同步急救平台车辆状态，请稍后...');

    var params = {
        "carId": rows[0].carId,
    }
    updateForceTaskSync(params,
        function (res) {
            showProcess(false);
            $.messager.alert('成功', '强制同步急救平台车辆状态！');
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "强制同步急救平台车辆状态失败：" + errMsg, 'error');
        });
}

/** 强制待命，弹窗显示 */
function openUpdateAwaitMobileDialog() {
    //获得当前选中的行
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-mobiels-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择车辆进行车辆待命操作!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条车辆记录,只能选择一条车辆记录进行车辆待命操作!', 'error');
        return;
    }
    if (rows[0].status == '0') {
        $.messager.alert('提示', '当前车辆待命状态，不用进行车辆待命操作。', 'error');
        return false;
    }
    $('#updateAwaitMobileDialog').window('open', true);

    $('#updateAwaitMobileProcessId').val(rows[0].currentProcessId);
    $('#updateAwaitMobileProcessIdToStatus').val(rows[0].status);
    $('#updateAwaitMobileProcessIdEventId').val(rows[0].currentEventId);
    $('#updateAwaitMobileProcessIdCarId').val(rows[0].carId);

    //获取时间填写到设为待命对话框中来
    var params = {
        "processId": rows[0].currentProcessId,
    };
    getMobileOperationRecordList(params,
        function (data) {
            var nowInTimes = new Date().formatString("yyyy-MM-dd hh:mm:ss");
            $('#send-car-times').datetimebox('setValue', nowInTimes);
            $('#goto-car-times').datetimebox('setValue', nowInTimes);
            $('#arrive-car-times').datetimebox('setValue', "");
            $('#cancel-car-times').datetimebox('setValue', "");
            $('#leave-car-times').datetimebox('setValue', "");
            $('#back-car-times').datetimebox('setValue', nowInTimes);

            $('#send-car-times').attr("data-options-id", "");
            $('#send-car-times').attr("data-options-mobileProcessId", "");
            $('#send-car-times').attr("data-options-times", "");
            $('#send-car-times').attr("data-options-toStatus", "");
            $('#send-car-times').attr("data-options-remark", "");

            $('#goto-car-times').attr("data-options-id", "");
            $('#goto-car-times').attr("data-options-mobileProcessId", "");
            $('#goto-car-times').attr("data-options-times", "");
            $('#goto-car-times').attr("data-options-toStatus", "");
            $('#goto-car-times').attr("data-options-remark", "");

            $('#cancel-car-times').attr("data-options-id", "");
            $('#cancel-car-times').attr("data-options-mobileProcessId", "");
            $('#cancel-car-times').attr("data-options-times", "");
            $('#cancel-car-times').attr("data-options-toStatus", "");
            $('#cancel-car-times').attr("data-options-remark", "");

            $('#back-car-times').attr("data-options-id", "");
            $('#back-car-times').attr("data-options-mobileProcessId", "");
            $('#back-car-times').attr("data-options-times", "");
            $('#back-car-times').attr("data-options-toStatus", "");
            $('#back-car-times').attr("data-options-remark", "");

            //获取正在进行的事件的列表
            var isCancleCar = false;
            for (var i = 0; i < data.length; i++) {
                //-1：取消调度、1-发车、2-抵达、3-出诊取消、4-出诊完成(回院)、5-调度接收、6-分站派车、7-分站确认、8-离开现场；最终状态-1或4
                switch (data[i].toStatus) {
                    case "6"://派车时间
                        $('#send-car-times').attr("data-options-id", data[i].id);
                        $('#send-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#send-car-times').attr("data-options-times", data[i].times);
                        $('#send-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#send-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#send-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "1"://确认出车，发车时间
                        $('#goto-car-times').attr("data-options-id", data[i].id);
                        $('#goto-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#goto-car-times').attr("data-options-times", data[i].times);
                        $('#goto-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#goto-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#goto-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "2"://抵达时间
                        $('#arrive-car-times').attr("data-options-id", data[i].id);
                        $('#arrive-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#arrive-car-times').attr("data-options-times", data[i].times);
                        $('#arrive-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#arrive-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#arrive-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "3"://取消任务时间
                        $('#cancel-car-times').attr("data-options-id", data[i].id);
                        $('#cancel-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#cancel-car-times').attr("data-options-times", data[i].times);
                        $('#cancel-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#cancel-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#cancel-car-times').datetimebox('setValue', data[i].times);
                        isCancleCar = true;
                        break;
                    case "8"://离开现场时间
                        $('#leave-car-times').attr("data-options-id", data[i].id);
                        $('#leave-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#leave-car-times').attr("data-options-times", data[i].times);
                        $('#leave-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#leave-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#leave-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "4"://完成接诊回到医院时间
                        $('#back-car-times').attr("data-options-id", data[i].id);
                        $('#back-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#back-car-times').attr("data-options-times", data[i].times);
                        $('#back-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#back-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#back-car-times').datetimebox('setValue', data[i].times);
                        break;
                }
            }
            if (isCancleCar) {
                $('#cancel-car-times-div').show();

            } else {
                $('#cancel-car-times-div').hide();
                if ($('#arrive-car-times').datetimebox('getValue') == "") {
                    $('#arrive-car-times').datetimebox('setValue', nowInTimes);
                }
                if ($('#leave-car-times').datetimebox('getValue') == "") {
                    $('#leave-car-times').datetimebox('setValue', nowInTimes);
                }
            }

            //如果救护车是待派车状态，那么显示取消接诊时间，并且取消接诊时间是空的，如果
            //填写了取消接诊时间那么就是取消接诊，此时按取消接诊逻辑进行处理
            if (rows[0].status == '5') {//待派车状态
                $('#cancel-car-times-div').show();
                $('#cancel-car-times').datetimebox('setValue', "");
            }

            //将当前的任务ID存储到页面上
            $('#updateAwaitMobileProcessId').val(rows[0].currentProcessId);
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取派车记录错误，异常信息：' + errMsg);
        });
}

/** 关闭完成任务弹窗 */
function closeUpdateAwaitMobileDialog() {
    $('#updateAwaitMobileDialog').window('close', true);
}

/** 强制待命，同步调用急救平台的接口 */
function updateAwaitMobileBtn() {
    showProcess(true, '温馨提示', '正在强制进行车辆待命操作，请稍后...');

    var mobileProcessItemsList = [];
    if ($('#send-car-times').datetimebox('getValue') != "") {
        var itemSendCarTimes = {
            "id": $('#send-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#send-car-times').datetimebox('getValue'),
            "toStatus": "6",
            "remark": ""
        };
        mobileProcessItemsList.push(itemSendCarTimes);
    }
    if ($('#goto-car-times').datetimebox('getValue') != "") {
        var itemGotoCarTimes = {
            "id": $('#goto-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#goto-car-times').datetimebox('getValue'),
            "toStatus": "1",
            "remark": ""
        };
        mobileProcessItemsList.push(itemGotoCarTimes);
    }
    if ($('#arrive-car-times').datetimebox('getValue') != "") {
        var itemArriveCarTimes = {
            "id": $('#arrive-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#arrive-car-times').datetimebox('getValue'),
            "toStatus": "2",
            "remark": ""
        };
        mobileProcessItemsList.push(itemArriveCarTimes);
    }
    if ($('#cancel-car-times').datetimebox('getValue') != "") {
        var itemCancelCarTimes = {
            "id": $('#cancel-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#cancel-car-times').datetimebox('getValue'),
            "toStatus": "3",
            "remark": ""
        };
        mobileProcessItemsList.push(itemCancelCarTimes);
    }
    if ($('#leave-car-times').datetimebox('getValue') != "") {
        var itemLeaveCarTimes = {
            "id": $('#leave-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#leave-car-times').datetimebox('getValue'),
            "toStatus": "8",
            "remark": ""
        };
        mobileProcessItemsList.push(itemLeaveCarTimes);
    }
    if ($('#back-car-times').datetimebox('getValue') != "") {
        var itemBackCarTimes = {
            "id": $('#back-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#back-car-times').datetimebox('getValue'),
            "toStatus": "4",
            "remark": ""
        };
        mobileProcessItemsList.push(itemBackCarTimes);
    }
    var params = {
        "processId": $('#updateAwaitMobileProcessId').val(),
        "mobileProcessItemsList": mobileProcessItemsList,
    }
    //状态5后端处理了，这里直接调用完成任务接口
    updateAwaitMobile(params,
        function (res) {
            $('#updateAwaitMobileDialog').window('close', true);
            showProcess(false);
            $.messager.alert('成功', '强制车辆待命操作完成！');
        }, function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "强制车辆待命操作失败：" + errMsg, 'error');
        });
}

/** 查看改派车辆的选中状态 */
function checkedCondition(hCondition) {
    switch (hCondition) {
        case "0":
            $('#conditionZc').checkbox({
                checked: true,
            });
            $('#conditionWh').checkbox({
                checked: false,
            });
            $('#conditionZd').checkbox({
                checked: false,
            });
            $('#conditionWzb').checkbox({
                checked: false,
            });
            $("#mobileCondition").val("0");
            break;
        case "1":
            $('#conditionWh').checkbox({
                checked: true,
            });
            $('#conditionZc').checkbox({
                checked: false,
            });
            $('#conditionZd').checkbox({
                checked: false,
            });
            $('#conditionWzb').checkbox({
                checked: false,
            });
            $("#mobileCondition").val("1");
            break;
        case "2":
            $('#conditionZd').checkbox({
                checked: true,
            });
            $('#conditionWh').checkbox({
                checked: false,
            });
            $('#conditionZc').checkbox({
                checked: false,
            });
            $('#conditionWzb').checkbox({
                checked: false,
            });
            $("#mobileCondition").val("2");
            break;
        case "3":
            $('#conditionWzb').checkbox({
                checked: true,
            });
            $('#conditionWh').checkbox({
                checked: false,
            });
            $('#conditionZc').checkbox({
                checked: false,
            });
            $('#conditionZd').checkbox({
                checked: false,
            });
            $("#mobileCondition").val("3");
            break;
    }
}

/** 关闭车辆状况 */
function closeMobileConditionWindowBtn() {
    $('#updateMobileConditionDialog').window('close', true);
}

/** 修改车辆状况 */
function openMobileConditionBtn(mobileId, hCondition) {
    //车辆当前状况，0：正常，1：维护中，2：驻场中，3：未值班
    showProcess(true, '温馨提示', '正在进行撤销调度任务操作，请稍后...');

    var params = { "mobileId": mobileId, "hCondition": hCondition }
    updateMobileCondition(params,
        function (res) {
            showProcess(false);
            $.messager.alert('成功', '车辆状况修改成功');
            $('#updateMobileConditionDialog').window('close', true);

            if (isMobilesOrStationsFirstLoaded) {
                refreshMobilesOrStations();//刷新车辆列表或分站列表
                syncMobilesToSecondScreen();//同步车辆数据给第二屏幕
            }

        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "车辆状况修改失败：" + errMsg, 'error');
        });
}

/**
 * 重置全局变量，防止注销再次登录时拿到错误的值
 */
function resetGlobalParams() {
    isEventListFirstLoaded = false;
    isMobilesOrStationsFirstLoaded = false;
}

/** 关闭定时器 */
function closeInterval() {
    _taskMobileInterval = false; //车辆定时任务开关
    _taskMsgInterval = false; //消息定时任务开关
    _h5AVInviteInterval = false //指导视频H5音视频请求定时任务开关
    _noticeListInterval = false //通知定时任务开关
    _carVideoInterval = false; //车载视频任务开关
    _servertimeInterval = false; //网络延时任务开关
    _taskCallStatusInterval = false; //分机状态定时任务开关
    _taskHeartbeatInterval = false; //心跳定时任务开关
    _refreshEventListInterval = false; //自动刷新事件列表开关
    _waitingFeedbackListInterval = false; //待反馈联动信息定时任务开关

    _uploadAddInterval = false //指导视频H5上报地址定时任务开关
    
    // 清除待反馈联动信息定时器
    clearTimeout(window.waitingFeedbackListInterval);
}

/** 开启所有定时器 */
function openAllInterval() {
    _taskMobileInterval = true; //车辆定时任务开关
    _taskMsgInterval = true; //消息定时任务开关
    _h5AVInviteInterval = true; //指导视频H5音视频请求定时任务开关
    _noticeListInterval = true; //通知定时任务开关
    _carVideoInterval = true; //车载视频任务开关
    _servertimeInterval = true; //网络延时任务开关
    _taskCallStatusInterval = true; //分机状态定时任务开关
    _taskHeartbeatInterval = true; //心跳定时任务开关
    _refreshEventListInterval = true; //自动刷新事件列表开关
    _waitingFeedbackListInterval = true; //待反馈联动信息定时任务开关

    _uploadAddInterval = true; //指导视频H5上报地址定时任务开关

    taskMobileIntervalTimeout();
    taskMsgIntervalTimeout();
    updateVideoLineTimeout();
    getNoticeAlertTimeout();
    updateCarVideoLineTimeout();
    getNetworkDelayTimeout();
    taskCallStatusIntervalTimeout();
    taskHeartbeatIntervalTimeout();
    refreshEventListIntervalTimeout();
    getWaitingFeedbackListTimeout();
}

/** 打开浏览器，显示地图和视频 */
function openMobileVideoAndMap() {
    try {
        gProxy.openVideoAndMapUrl();
    } catch (e) {
        //使用
        window.open(bsProxy.getBaseUrl() + "edc_svrside_service/api/seat/seatToMap?token=" + encodeURIComponent(_token) + "&&baseUrl=" + bsProxy.getBaseUrl());
    }
}

/** 显示版本信息 */
function showVersionInfo() {
    //显示版本信息
    var versionInfo = "未获取到版本信息";
    try {
        gProxy.getExeVersionInfo();
    } catch (e) {
        bsProxy.getExeVersionInfo();
    }
    $('#versionInfo').html(versionInfo);
    $.messager.alert('版本信息', '当前版本：' + versionInfo, 'info');
}

/** 电话呼入开启和关闭 */
function callInOm(th) {
    var ele = $(th).children(".move");
    var noDisturb = "";
    var noDisturbStr = "";
    if (ele.attr("data-state") == "on") {
        //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
        noDisturb = "on";
        noDisturbStr = "关闭";
    } else if (ele.attr("data-state") == "off") {
        noDisturb = "off";
        noDisturbStr = "开启";
    }

    $.messager.confirm("提示", "是否" + noDisturbStr + "120呼入电话？", function (r) {
        if (r) {
            //打开和关闭OM
            var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
            showProcess(true, "正在" + noDisturbStr + "120座席呼入...");
            extAssign(params,
                function (res) {
                    showProcess(false);
                    //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                    if (ele.attr("data-state") == "on") {
                        //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                        ele.animate({ left: "0" }, 300, function () {
                            ele.attr("data-state", "off");
                        });
                        $(th).removeClass("on").addClass("off");
                    } else if (ele.attr("data-state") == "off") {
                        ele.animate({ left: '25px' }, 300, function () {
                            $(this).attr("data-state", "on");
                        });
                        $(th).removeClass("off").addClass("on");
                    }
                    $.messager.alert('提示', noDisturbStr + '成功');

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('提示', noDisturbStr + '失败，异常信息：' + errMsg);
                });
        }
    });
}

/** 退出系统 */
function quitSystem() {
    if (isCreateNewEventWinIsOpen()) {
        $.messager.alert('提示', '正在创建事件，不容许退出系统！');
        return false;
    }
    showProcess(true, "正在退出120座席系统...");
    if (_pSeat == null) {
        try {
            gProxy.closeSystemMain();
        } catch (e) {
            //B/S模式不存在退出系统
        }
    } else {
        //座席下线
        downSeat(_pSeat.id,
            function (res) {
                showProcess(true, "座席下线成功,正在退出系统...");
                try {
                    gProxy.closeSystemMain();
                } catch (e) {
                    //B/S模式不存在退出系统
                }
            },
            function (e, url, errMsg) {
                showProcess(false);
                $.messager.confirm('退出系统',
                    '座席下线失败（' + errMsg + '），120电话未限制呼入，是否强行退出？',
                    function (s) {
                        if (s == true) {
                            showProcess(true, "正在退出系统...");
                            try {
                                gProxy.closeSystemMain();
                            } catch (e) {
                                //B/S模式不存在退出系统
                            }
                        }
                    });

            });
    }
}
/** 换班 */
function changeShiftSeat() {
    //接警分机开启免打扰
    var ele = $('#callInStatus').children(".move");
    var noDisturb = "on";
    var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
    extAssign(params,
        function (res) {
            showProcess(false);
            //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
            if (ele.attr("data-state") == "on") {
                //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                ele.animate({ left: "0" }, 300, function () {
                    ele.attr("data-state", "off");
                });
                $('#callInStatus').removeClass("on").addClass("off");
            } else if (ele.attr("data-state") == "off") {
                ele.animate({ left: '25px' }, 300, function () {
                    $('#callInStatus').attr("data-state", "on");
                });
                $('#callInStatus').removeClass("off").addClass("on");
            }
        },
    );

    $('#change-shifts-win').window('open'); //打开交接班界面
    _successorData = null;
    //接班人下拉自动搜索
    $('#successor').combobox({
        prompt: '输入接班人工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'userId',
        textField: 'displayName',
        data: _successorList,
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        },
        onSelect: function (params) {
            _successorData = {};
            _successorData.successorId = params.userId;
            _successorData.successorUserName = params.username;
            _successorData.successorName = params.displayName;
        }
    });
    _successorData = {};
    _successorData.successorUserName = _user.Username

    //交班基础信息初始化
    $('#shift-seat').text(_pSeat.seatId);
    $('#shift-offGoingUser').text(_pSeat.user);
    $('#shift-offGoingUserName').text(_pSeat.userName)
    $('#shift-curScheduleName').text(_user.ScheduleTimeConfig.ScheduleName);

    $('#shift-offGoingScheduleName').text(_user.ScheduleTimeConfig.ScheduleName)
    $('#shift-offGoingScheduleName-time').text(_user.ScheduleTimeConfig.ScheduleName)
    $('#shift-successorScheduleName').text(_user.ScheduleTimeConfig.NextScheduleName)

    /*
    var curDate = new Date();
    if(_user.ScheduleTimeConfig.EndTime == "00:00"){
        var fromTime = new Date(curDate.getTime() + 24 * 60 * 60 * 1000);
        $('#shift-offGoing-time').text(fromTime.formatString('yyyy-MM-dd') +' '+ _user.ScheduleTimeConfig.EndTime)
    }else{
        $('#shift-offGoing-time').text(curDate.formatString('yyyy-MM-dd') +' '+ _user.ScheduleTimeConfig.EndTime)
    }
    $('#shift-daylight-saving-time').text(_user.ScheduleTimeConfig.StartTime+' - '+_user.ScheduleTimeConfig.EndTime)
    */
    //console.log("初始化时，用户数据", JSON.stringify(_user));

    var params = { "offGoingId": _user.Id, "scheduleTimeConfigId": _user.ScheduleTimeConfig.Id, "seatId": _pSeat.id }
    $('#change-start-times').datetimebox({
        onChange: function (newValue, oldValue) {
            // 在这里可以添加你的逻辑代码
            if ($('#change-start-times').val() && $('#change-end-times').val()) {
                let startTime = $('#change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间

                let endTime = $('#change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间

                params.startTime = startTime
                params.endTime = endTime

                //交接班记录内容初始化
                //获取交接时统计数据

                getShiftSeatStatisticalData4Turnover(params,
                    function (res) {
                        $('#callIn').val(res.callIn);
                        $('#accept').val(res.accept);
                        $('#dispatch').val(res.dispatch);
                        $('#vaildDispatch').val(res.vaildDispatch);
                        $('#emptyDispatch').val(res.emptyDispatch);
                        $('#cancelDispatch').val(res.cancelDispatch);
                        $('#patientNum').val(res.patientNum);
                        $('#pick').val(res.pick);

                        $('#publicEventNum').val(res.publicEventNum);
                        $('#woundedNum').val(res.woundedNum);
                        $('#deadNum').val(res.deadNum);
                    },
                    function (e, url, errMsg) {
                        $.messager.alert('提示', '获取交接班数据异常：' + errMsg);
                    }
                );
            }

        }
    });

    $('#change-end-times').datetimebox({
        onChange: function (newValue, oldValue) {
            // 在这里可以添加你的逻辑代码
            if ($('#change-start-times').val() && $('#change-end-times').val()) {
                let startTime = $('#change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间
                let endTime = $('#change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间

                params.startTime = startTime
                params.endTime = endTime

                //交接班记录内容初始化
                //获取交接时统计数据

                getShiftSeatStatisticalData4Turnover(params,
                    function (res) {
                        $('#callIn').val(res.callIn);
                        $('#accept').val(res.accept);
                        $('#dispatch').val(res.dispatch);
                        $('#vaildDispatch').val(res.vaildDispatch);
                        $('#emptyDispatch').val(res.emptyDispatch);
                        $('#cancelDispatch').val(res.cancelDispatch);
                        $('#patientNum').val(res.patientNum);
                        $('#pick').val(res.pick);

                        $('#publicEventNum').val(res.publicEventNum);
                        $('#woundedNum').val(res.woundedNum);
                        $('#deadNum').val(res.deadNum);
                    },
                    function (e, url, errMsg) {
                        $.messager.alert('提示', '获取交接班数据异常：' + errMsg);
                    }
                );
            }

        }
    });

    // $('#change-start-times').datetimebox('setValue', callInTimes);


    //弹窗关闭监控
    // $('#change-shifts-win').window({
    //     onClose: function () {
    //         //接警分机上线
    //         var ele = $('#callInStatus').children(".move");
    //         var noDisturb = "off";
    //         var params = {
    //             "lineId": _pSeat.lineIdIn,
    //             "ext": _pSeat.callIn,
    //             "noDisturb": noDisturb
    //         }
    //         extAssign(params,
    //             function (res) {
    //                 //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
    //                 if (ele.attr("data-state") == "on") {
    //                     //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
    //                     ele.animate({left: "0"}, 300, function () {
    //                         ele.attr("data-state", "off");
    //                     });
    //                     $('#callInStatus').removeClass("on").addClass("off");
    //                 } else if (ele.attr("data-state") == "off") {
    //                     ele.animate({left: '25px'}, 300, function () {
    //                         $('#callInStatus').attr("data-state", "on");
    //                     });
    //                     $('#callInStatus').removeClass("off").addClass("on");
    //                 }
    //             },
    //         );
    //     }
    // });
    $('#password').val('');
}
/** 交接班更新 */
function doSuccessor() {
    if (_successorData == null || _successorData.successorId == null || _successorData.successorId == "") {
        //$('#successor').focus()
        $.messager.alert('温馨提示', '请选择接班人员', 'info');
        return null;
    }
    /**交接班记录 */
    _successorData.seatId = _pSeat.id
    _successorData.offGoingId = _user.Id
    /**接班人信息 */
    _successorData.userId = _successorData.successorId
    _successorData.username = _successorData.successorUserName
    _successorData.displayName = _successorData.successorName
    _successorData.password = $('#password').val()
    if ($('#change-start-times').val() == null || $('#change-start-times').val() === '') {
        $.messager.alert('温馨提示', '请输入交班时间', 'info');
        return;
    }
    if ($('#change-end-times').val() == null || $('#change-end-times').val() === '') {
        $.messager.alert('温馨提示', '请输入交班时间', 'info');
        return;
    }
    if (_successorData.password == null || _successorData.password == "") {
        //$('#password').focus()
        $.messager.alert('温馨提示', '请输入密码', 'info');
        return null;
    }
    if ($('#callIn').val() == null || $('#callIn').val() === '') {
        $.messager.alert('温馨提示', '请输入呼入次数', 'info');
        return;
    }
    if ($('#accept').val() == null || $('#accept').val() === '') {
        $.messager.alert('温馨提示', '请输入受理次数', 'info');
        return;
    }
    if ($('#dispatch').val() == null || $('#dispatch').val() === '') {
        $.messager.alert('温馨提示', '请输入派车辆数', 'info');
        return;
    }
    if ($('#vaildDispatch').val() == null || $('#vaildDispatch').val() === '') {
        $.messager.alert('温馨提示', '请输入有效出车辆数', 'info');
        return;
    }
    if ($('#emptyDispatch').val() == null || $('#emptyDispatch').val() === '') {
        $.messager.alert('温馨提示', '请输入空车辆数', 'info');
        return;
    }
    if ($('#cancelDispatch').val() == null || $('#cancelDispatch').val() === '') {
        $.messager.alert('温馨提示', '请输入取消派车辆数', 'info');
        return;
    }
    if ($('#patientNum').val() == null || $('#patientNum').val() === '') {
        $.messager.alert('温馨提示', '请输入患者人数', 'info');
        return;
    }
    if ($('#pick').val() == null || $('#pick').val() === '') {
        $.messager.alert('温馨提示', '请输入摘机率', 'info');
        return;
    }
    if ($('#publicEventNum').val() == null || $('#publicEventNum').val() === '') {
        $.messager.alert('温馨提示', '请输入突发公共事件次数', 'info');
        return;
    }
    if ($('#woundedNum').val() == null || $('#woundedNum').val() === '') {
        $.messager.alert('温馨提示', '请输入突发公共事件伤员人数', 'info');
        return;
    }
    if ($('#deadNum').val() == null || $('#deadNum').val() === '') {
        $.messager.alert('温馨提示', '请输入突发公共事件死亡人数', 'info');
        return;
    }

    /**交接班记录内容 */
    _successorData.callIn = $('#callIn').val()//呼入
    _successorData.accept = $('#accept').val()//受理（次）
    _successorData.dispatch = $('#dispatch').val()//派车（辆）
    _successorData.vaildDispatch = $('#vaildDispatch').val()//有效出车（辆）
    _successorData.emptyDispatch = $('#emptyDispatch').val()//空车（辆）
    _successorData.cancelDispatch = $('#cancelDispatch').val()//取消派车（辆）
    _successorData.patientNum = $('#patientNum').val()//患者人数（人）
    _successorData.pick = $('#pick').val()//摘机率（%）
    _successorData.publicEventNum = $('#publicEventNum').val()//突发公共事件次数
    _successorData.woundedNum = $('#woundedNum').val()//突发公共事件伤（人）
    _successorData.deadNum = $('#deadNum').val()//突发公共事件次数亡（人）

    _successorData.offGoingScheduleStartTime = $('#change-start-times').val().replace(/[\s:-]/g, "") + '00'//交接开始时间
    _successorData.offGoingScheduleEndTime = $('#change-end-times').val().replace(/[\s:-]/g, "") + '00'//交接结束时间
    _successorData.offGoingTime = $('#shift-handover-times').val().replace(/[\s:-]/g, "") + '00'  //交班时间

    showProcess(true, '温馨提示', '正在进行交接班，请稍后...');
    updateShiftSeatTurnover(_successorData,
        function (res) {
            $('#change-shifts-win').window('close', true);
            showProcess(false);
            //座席下线
            downSeat(_pSeat.id,
                function (res) {
                    showProcess(false);
                    $('#login-un').val('');
                    $('#login-pwd').val('');
                    $('#loginlogin-schedule-time-config-id').val('');
                    _token = null;
                    showLogin();//showLogin有调用 checkActiveShiftSeat()
                    try {
                        gProxy.hangUpSecondScreen();
                    } catch (e) {
                        bsProxy.hangUpSecondScreen();
                    }
                    closeInterval(); //清理所有的定时器
                    resetGlobalParams();//重置全局变量
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '座席下线失败，异常信息：' + errMsg);
                    showProcess(false);
                    $('#login-un').val('');
                    $('#login-pwd').val('');
                    $('#loginlogin-schedule-time-config-id').val('');
                    _token = null;
                    showLogin();//showLogin有调用 checkActiveShiftSeat()
                    try {
                        gProxy.hangUpSecondScreen();
                    } catch (e) {
                        bsProxy.hangUpSecondScreen();
                    }
                    closeInterval(); //清理所有的定时器
                    resetGlobalParams();//重置全局变量
                });
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '交接班失败，异常信息：' + errMsg);
            //$('#change-shifts-win').window('close', true);
            showProcess(false);
        }
    )
    //接警分机上线
    var ele = $('#callInStatus').children(".move");
    var noDisturb = "off";
    var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
    extAssign(params,
        function (res) {
            //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
            if (ele.attr("data-state") == "on") {
                //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                ele.animate({ left: "0" }, 300, function () {
                    ele.attr("data-state", "off");
                });
                $('#callInStatus').removeClass("on").addClass("off");
            } else if (ele.attr("data-state") == "off") {
                ele.animate({ left: '25px' }, 300, function () {
                    $('#callInStatus').attr("data-state", "on");
                });
                $('#callInStatus').removeClass("off").addClass("on");
            }
        },
    );
    return;

}
/** 取消交接班 */
function cancleSuccessor() {
    //交接班更新弹窗关闭
    $('#change-shifts-win').window('close');
    //接警分机上线
    var ele = $('#callInStatus').children(".move");
    var noDisturb = "off";
    var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
    extAssign(params,
        function (res) {
            //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
            if (ele.attr("data-state") == "on") {
                //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                ele.animate({ left: "0" }, 300, function () {
                    ele.attr("data-state", "off");
                });
                $('#callInStatus').removeClass("on").addClass("off");
            } else if (ele.attr("data-state") == "off") {
                ele.animate({ left: '25px' }, 300, function () {
                    $('#callInStatus').attr("data-state", "on");
                });
                $('#callInStatus').removeClass("off").addClass("on");
            }
        },
    );
}
/** 交接班弹窗右上角关闭事件 */
$('#change-shifts-win').window({
    onBeforeClose: function () {
        // 这个回调会在窗口关闭前触发，可以在此处阻止窗口关闭
        //接警分机上线
        var ele = $('#callInStatus').children(".move");
        var noDisturb = "off";
        var params = { "lineId": _pSeat.lineIdIn, "ext": _pSeat.callIn, "noDisturb": noDisturb }
        extAssign(params,
            function (res) {
                //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                if (ele.attr("data-state") == "on") {
                    //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                    ele.animate({ left: "0" }, 300, function () {
                        ele.attr("data-state", "off");
                    });
                    $('#callInStatus').removeClass("on").addClass("off");
                } else if (ele.attr("data-state") == "off") {
                    ele.animate({ left: '25px' }, 300, function () {
                        $('#callInStatus').attr("data-state", "on");
                    });
                    $('#callInStatus').removeClass("off").addClass("on");
                }
            },
        );
        return true; // 返回 true 表示允许关闭，返回 false 则阻止关闭
    },
});

/** 输入只能是0或正整数 */
function validateInputPositiveOrZero(input) {
    const value = input.value;
    const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
    if (!regex.test(value)) {
        input.value = ""; // 清空输入框
    }
}

/** 计算摘机率 */
function inputCount(input) {
    validateInputPositiveOrZero(input)
    if ($('#callIn').val() == '0' || $('#callIn').val() == '' || $('#accept').val() == '0' || $('#accept').val() == '') {
        $('#pick').val('0.00')
    }
    if ($('#callIn').val() > 0 && $('#accept').val() > 0) {
        var pick = $('#accept').val() / $('#callIn').val() * 100;
        $('#pick').val(pick.toFixed(2));
    }
}

/** 注销登录 */
function LogOut() {
    $.messager.confirm('注销登录',
        '确定要注销吗？',
        function (s) {
            if (s == true) {
                showProcess(true, '温馨提示', '正在进行注销，请稍后...');

                getLastLoginUser()

                //座席下线
                downSeat(_pSeat.id,
                    function (res) {
                        //注销
                        try {
                            //C#版本
                            gProxy.logout();
                            //console.log("注销成功" + res)
                            showProcess(false);
                            $('#login-un').val('');
                            $('#login-pwd').val('');
                            $('#loginlogin-schedule-time-config-id').val('');
                            _token = null;
                            showLogin();//showLogin有调用 checkActiveShiftSeat()
                            $.messager.alert('注销成功', res, 'info');
                            try {
                                gProxy.hangUpSecondScreen();
                            } catch (e) {
                                bsProxy.hangUpSecondScreen();
                            }
                            closeInterval(); //清理所有的定时器
                            resetGlobalParams();//重置全局变量
                        } catch (e) {
                            //网页注销
                            logout(bsProxy.getUserToken(),
                                function (res) {
                                    showProcess(false);
                                    $('#login-un').val('');
                                    $('#login-pwd').val('');
                                    $('#loginlogin-schedule-time-config-id').val('');
                                    _token = null;
                                    showLogin();//showLogin有调用 checkActiveShiftSeat()
                                    $.messager.alert('提示', "注销成功！", 'info');
                                    bsProxy.hangUpSecondScreen();
                                    closeInterval(); //清理所有的定时器
                                    resetGlobalParams();//重置全局变量
                                },
                                function (e, url, errMsg) {
                                    $.messager.alert('提示', '用户注销失败，异常信息：' + errMsg);

                                });
                        }
                    },
                    function (e, url, errMsg) {
                        //注销
                        try {
                            gProxy.logout();

                            showProcess(false);
                            $.messager.alert('提示', '座席下线失败，异常信息：' + errMsg);
                            showProcess(false);
                            $('#login-un').val('');
                            $('#login-pwd').val('');
                            $('#loginlogin-schedule-time-config-id').val('');
                            _token = null;
                            showLogin();//showLogin有调用 checkActiveShiftSeat()
                            try {
                                gProxy.hangUpSecondScreen();
                            } catch (e) {
                                bsProxy.hangUpSecondScreen();
                            }
                            closeInterval(); //清理所有的定时器
                            resetGlobalParams();//重置全局变量
                        } catch (e) {
                            //网页注销
                            logout(bsProxy.getUserToken(),
                                function (res) {
                                    $.messager.alert('提示', '座席下线失败，异常信息：' + errMsg);
                                    showProcess(false);
                                    $('#login-un').val('');
                                    $('#login-pwd').val('');
                                    $('#loginlogin-schedule-time-config-id').val('');
                                    _token = null;
                                    showLogin();//showLogin有调用 checkActiveShiftSeat()
                                    try {
                                        gProxy.hangUpSecondScreen();
                                    } catch (e) {
                                        bsProxy.hangUpSecondScreen();
                                    }
                                    closeInterval(); //清理所有的定时器
                                    resetGlobalParams();//重置全局变量
                                },
                                function (e, url, errMsg) {
                                    $.messager.alert('提示', '用户注销失败，异常信息：' + errMsg);
                                });
                        }
                    });

            }
        });
}
/** 是否容许修改事件呼叫类型 */
function isUpdateEventCallType(eventId) {
    getIsUpdateEventCallType(eventId,
        function (res) {
            if (res) {
                $('#evd-call-type').removeAttr("disabled");
            } else {
                $('#evd-call-type').attr("disabled", true);
            }
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', '获取是否容许修改事件呼叫类型错误，异常信息：' + errMsg);

        });
}
/** 获得全局变量的车辆信息（带距离） */
function getMobilesWithDistance() {
    return _mobilesWithDistance;
}

/** 获取全局变量座席 */
function getSeatInfo() {
    return _pSeat;
}

var messagerWindow = null;//消息弹窗
/**
 * 页面加载时左下角弹出提示审批框操作
 */
function sliderelayMsgShow(content) {
    //如果messagerWindow是null说明已经处理完了。那么再显示一个右下角弹窗
    if (messagerWindow != null) {
        return false;
    }
    playRefuseToDispatchSound(true);
    let msgId = content.id;
    switch (content.msgType) {
        case "1":
            var msgText = "分站[" + content.sendName + "]<br/>拒绝派车！原因：" + content.msgContentOne + '<br/>事件内容：' + content.majorCall.replace('_', ' ') + '<br/>地址：' + content.address;
            messagerWindow = $.messager.show({
                id: msgId,
                title: '<div style="display:flex;justify-content: space-between"><div style="display:flex;align-items: center;"><img style="width:20px;height:20px;margin-right:5px" src="style/img/che_weipaiche_ic.png"/>分站拒绝出车消息提醒</div><div onclick="read(\'{0}\')" style="font-size:16px;cursor: pointer;margin-right:8px">×</div></div>',
                msg: `<div style="height:100%;width:100%;text-align: center;display: flex;flex-direction: column;justify-content: space-between;align-items: center;">
                    <div class="msgblink" style="margin-top:0px;font-size:1.1rem;text-align:left">${msgText}</div>
                    <div style="margin-top: 5px;">
                        <a href="#" class="easyui-linkbutton" onclick="cancelMobileProcessMsgDialog(\'{0}\',\'{2}\')" style="font-size:12px;heigth:20px">撤销调度</a>
                        <a href="#" class="easyui-linkbutton" onclick="refuseProcessCancel(\'{0}\',\'{1}\')" style="font-size:12px;heigth:20px">驳回拒绝派车</a>
                    </div>
                </div>`.formatStr(msgId, content.msgContent, content.msgContentOne),
                timeout: 0,
                showType: 'slide',
                closable: false,//去掉关闭按钮，不容许关闭
                width: '400px',
                height: '240px',
                style: {
                    right: '',
                    top: '',
                    left: '0px',
                    bottom: '0px'
                }
            });
            break;
        default:
            $.messager.alert('提示', '消息提醒未处理', 'error');
    }

}
function closeDialog() {
    messagerWindow.window("close");
    messagerWindow = null;
}

var dialWindow = null;//拨测异常弹窗
/**
 * 页面加载时左下角弹出提示智能拨测异常提醒
 */
function dialTest() {
    //如果dialWindow是null说明已经处理完了
    if (dialWindow != null) {
        return false;
    }
    playRefuseToDispatchSound(true);
    dialWindow = $.messager.show({
        title: '<div style="display:flex;justify-content: space-between"><div style="display:flex;align-items: center;color:red"><img style="width:20px;height:20px;margin-right:5px" src="style/img/zuoxi_duankai_ic.png"/>智能拨测</div></div>',
        msg: `<div style="height:100%;width:100%;text-align: center;display: flex;flex-direction: column;justify-content: space-between;align-items: center;">
            <div class="msgblink" style="margin-top:0px;font-size:1.1rem;text-align:left">警告：智能拨测发现通信异常！</div>
        </div>`,
        timeout: 0,
        showType: 'slide',
        closable: false,//是否显示关闭按钮
        width: '400px',
        height: '240px',
        style: {
            right: '',
            top: '',
            left: '0px',
            bottom: '0px'
        },
        onClose: function () {
            // 在消息窗口关闭时执行的回调函数
        }
    });
}

// 全局变量，记录已提醒的联动ID，防止重复弹窗
var _waitingFeedbackRemindIds = [];
// 记录最后一次关闭区域联动提醒弹窗的时间戳
var _lastLinkageDialogCloseTime = 0;
var _linkageDialogIsOpen = false; // 跟踪弹窗是否打开
// 记录上次提醒的数据签名，用于判断数据是否变化
var _lastNotificationDataSignature = "";
// 记录上次提醒的时间
var _lastNotificationTime = 0;

/** 定时获取待反馈联动信息并弹窗提醒 */
function getWaitingFeedbackListTimeout() {
    clearTimeout(window.waitingFeedbackListInterval);
    window.waitingFeedbackListInterval = null;

    getWaitingFeedbackEventRegionLinkageList({}, function (data) {
        
        if (Array.isArray(data) && data.length > 0) {
            // 按sendRegionName分组统计数量
            var regionMap = {};
            var newItemsCount = 0;
            
            data.forEach(function(item) {
                if (!_waitingFeedbackRemindIds.includes(item.id)) {
                    regionMap[item.sendRegionName] = (regionMap[item.sendRegionName] || 0) + 1;
                    _waitingFeedbackRemindIds.push(item.id);
                    newItemsCount++;
                }
            });
            
            // 检查是否在静默期内（关闭弹窗后的60秒内不再弹窗）
            var currentTime = Date.now();
            var silentPeriod = 60000; // 60秒 = 60000毫秒
            var timeSinceLastClose = currentTime - _lastLinkageDialogCloseTime;
            
            if (currentTime - _lastLinkageDialogCloseTime < silentPeriod) {
                // 在静默期内，不显示弹窗
                return;
            }
            
            // 计算当前数据的签名，用于判断数据是否变化
            var currentDataSignature = JSON.stringify(data.map(function(item) {
                return item.id + "-" + item.feedbackStatus;
            }).sort());
            
            // 检查是否需要显示提醒
            var shouldShowNotification = false;
            
            // 满足以下条件之一时显示提醒：
            // 1. 超过静默期且有数据需要处理(不管数据是否变化)且当前没有弹窗显示
            // 2. 尚未显示过提醒
            var isDialogShowing = $('.linkage-dialog').length > 0 || _linkageDialogIsOpen;
            
            // 如果已经有弹窗显示，直接返回，不再处理
            if (isDialogShowing) {
                return;
            }
            
            if ((timeSinceLastClose >= silentPeriod && data.length > 0) || 
                _lastNotificationTime === 0) {
                
                shouldShowNotification = true;
                _lastNotificationDataSignature = currentDataSignature;
                _lastNotificationTime = currentTime;
                
                // 重新填充regionMap，只包含未处理的项目
                _waitingFeedbackRemindIds = [];
                regionMap = {};
                data.forEach(function(item) {
                    regionMap[item.sendRegionName] = (regionMap[item.sendRegionName] || 0) + 1;
                    _waitingFeedbackRemindIds.push(item.id);
                });
                
                // 如果没有需要提醒的项目，不显示提醒
                if (Object.keys(regionMap).length === 0) {
                    shouldShowNotification = false;
                }
            } else {
                return;
            }
            
            // 弹窗提醒
            if (!shouldShowNotification) {
                return;
            }
            
            // 再次检查是否已有弹窗显示
            if ($('.linkage-dialog').length > 0 || _linkageDialogIsOpen) {
                return;
            }
            
            // 全局标记，确保只显示一个弹窗
            _linkageDialogIsOpen = true;
            
            for (var region in regionMap) {
                var msgWin = $.messager.show({
                    title: '<div style="display:flex;align-items:center;justify-content:space-between;width:100%"><div style="display:flex;align-items:center"><img style="width:20px;height:20px;margin-right:5px" src="style/img/qiangcha.png"/>区域联动提醒</div></div>',
                    msg: `<div style="font-size:1.1rem;text-align:left;"><span style="color:red">${region}</span>发送了<span style="color:red">${regionMap[region]}</span>条联动信息，请前往区域联动界面及时处理联动事件。</div>
                          <div style="margin-top:10px;display:flex;justify-content:center">
                            <a href="javascript:void(0)" class="easyui-linkbutton linkage-close-btn" style="width:120px">关闭</a>
                          </div>`,
                    timeout: 0,
                    showType: 'slide',
                    closable: false,
                    width: '400px',
                    height: '160px',
                    style: {
                        right: '',
                        top: '',
                        left: '0px',
                        bottom: '0px'
                    },
                    onShow: function() {
                        // 给弹窗添加特定类名
                        $(this).closest('.messager-window').addClass('linkage-dialog');
                    }
                });
                
                // 初始化按钮并绑定事件
                setTimeout(function() {
                    // 关闭按钮
                    $('.linkage-close-btn').linkbutton({
                        iconCls: 'icon-cancel'
                    }).unbind('click').bind('click', function() {
                        // 记录关闭时间戳
                        _lastLinkageDialogCloseTime = Date.now();
                        // 重置弹窗打开标志
                        _linkageDialogIsOpen = false;
                        $(this).closest('.messager-body').window('close');
                    });
                }, 100);
            }
        }
    }, function (e, url, errMsg) {
        // 可选：异常不弹窗
    });

    // 10秒后再次执行
    window.waitingFeedbackListInterval = setTimeout(getWaitingFeedbackListTimeout, 10000);
}


/** 常用医院弹窗 */
function initHospital() {
    getAllContacts(function (data) {
        _contactList = data;
        _sentencesHospital = data.filter(r => r.groupName.indexOf('常用医院') !== -1)[0]
        if (_sentencesHospital) {
            renderList(_sentencesHospital.phoneBooks ? _sentencesHospital.phoneBooks : [])
        }
        $('#window-search-name').textbox('textbox').bind('input', function (e) {
            if (_sentencesHospital != null && Array.isArray(_sentencesHospital.phoneBooks)) {
                let list = JSON.parse(JSON.stringify(_sentencesHospital.phoneBooks))
                //过滤数据
                let filterlist = list.filter(r => r.contactor.indexOf(e.target.value) !== -1)
                if (!filterlist || !filterlist[0]) {
                    //字母过滤
                    filterlist = list.filter(r => upperfirst(r.contactor, { upperfirst: true }).indexOf(e.target.value) !== -1)
                }
                //右边列表数据渲染
                renderList(filterlist || [])
            }
        })
    });
}
/** 常用医院列表数据渲染 */
function renderList(list) {
    $('#window-cont-list-body').empty();
    for (var j = 0; j < list.length; j++) {
        $('#window-cont-list-body').append('<tr><td style="width: 30%;">{0}</td>\
              <td style="width: 70%; color: #0AA0FB; cursor: pointer;"  align="left"> &ensp;<a href="javascript:callingPhone2(\'{1}\', \'{1}\');">{1}</a></td></tr>'
            .formatStr(
                list[j].contactor,
                list[j].number
            ));
    }
}
/** 通话异常记录查询 */
var errorMsgStatus = true
var messagerIndex = null
function errorMsg() {
    this.queryErrorMsg({ extList: [_pSeat.callOut, _pSeat.callIn] }, (data) => {
        if (data && Array.isArray(data) && data[0] && errorMsgStatus) {
            switch (data[0].msgType) {
                case "1":
                    errorMsgStatus = false
                    messagerIndex = $.messager.show({
                        title: '<div style="display:flex;justify-content: space-between"><div style="display:flex;align-items: center;"><img style="width:20px;height:20px" src="style/img/qiangcha.png"/>通话异常信息</div></div>',
                        msg: `<div style="height:100%;width:100%;text-align: center;display: flex;flex-direction: column;justify-content: space-between;align-items: center;">
                                <div class="msgblink" style="margin-top:0px;font-size:1.1rem;text-align:left;font:bold"> ${data[0].content}</div>
                                <div style="margin-top: 5px;">
                                    <a href="#" class="easyui-linkbutton" onclick="read(\'{0}\')" style="font-size:12px;heigth:20px">设为已读</a>
                                </div>
                            </div>`.formatStr(data[0].id),
                        timeout: 0,
                        showType: 'slide',
                        closable: false,//去掉关闭按钮，不容许关闭
                        width: '400px',
                        height: '200px',
                        style: {
                            right: '',
                            top: '',
                            left: '0px',
                            bottom: '0px'
                        },
                        onClose: function () {
                            // 在消息窗口关闭时执行的回调函数
                        }

                    });
                    break;
                case "2":
                    errorMsgStatus = false
                    messagerIndex = $.messager.show({
                        title: '<div style="display:flex;justify-content: space-between"><div style="display:flex;align-items: center;"><img style="width:20px;height:20px" src="style/img/qiangcha.png"/>录音功能告警</div></div>',
                        msg: `<div style="height:100%;width:100%;text-align: center;display: flex;flex-direction: column;justify-content: space-between;align-items: center;">
                                    <div class="msgblink" style="margin-top:0px;font-size:1.1rem;text-align:left;font:bold"> ${data[0].content}</div>
                                    <div style="margin-top: 5px;">
                                        <a href="#" class="easyui-linkbutton" onclick="read(\'{0}\')" style="font-size:12px;heigth:20px">设为已读</a>
                                    </div>
                                </div>`.formatStr(data[0].id),
                        timeout: 0,
                        showType: 'slide',
                        closable: false,//去掉关闭按钮，不容许关闭
                        width: '400px',
                        height: '200px',
                        style: {
                            right: '',
                            top: '',
                            left: '0px',
                            bottom: '0px'
                        },
                        onClose: function () {
                            // 在消息窗口关闭时执行的回调函数
                        }

                    });
                    break;
            }
        }
    },
        (e, url, errMsg) => {
            $.messager.alert('提示', '获取是否有通话异常记录失败：' + errMsg);
        })
}
function read(id) {
    $.messager.confirm('提示', '是否确定设置', function (r) {
        if (r) {
            readErrorMsg({ "id": id }, (res) => {
                errorMsgStatus = true
                messagerIndex.window("close");
            },
                (e, url, errMsg) => {
                    $.messager.alert('提示', '异常信息：' + errMsg);
                })
        }
    });

}

//============================软电话机处理逻辑 start============================
/** 软电话-拨打电话 */
/*function dialCallSoftPhone(nLineNo, phoneNumber) {
    gProxy.dialCallSoftPhone(nLineNo, phoneNumber);
}*/
/** 软电话-接听与挂断呼入电话 */
function acceptAndHangupSoftPhone(nLineNo) {
    //处理接警线路
    if (nLineNo == "1") {
        //接听
        if ($("#soft-phone-accept-call-in-btn").linkbutton("options").text == "接听") {
            gProxy.acceptCallInSoftPhone($("#soft-phone-accept-call-id").val(), "1");
            //接听了电话后，接听按钮变成挂断按钮，同时隐藏拒绝按钮
            $("#soft-phone-accept-call-in-btn").show();
            $("#soft-phone-accept-call-in-btn").linkbutton({
                iconCls: 'icon-softphone-Hangup'
            });
            $("#soft-phone-accept-call-in-btn").css("color", "red");
            $("#soft-phone-accept-call-in-btn").linkbutton({ text: '挂机' });


            $("#soft-phone-reject-call-in-btn").hide();

            $("#soft-phone-hold-call-in-btn").show();
            $("#soft-phone-hold-call-in-btn").linkbutton({ text: '保持' });
            return null;
        }
        //挂断
        if ($("#soft-phone-accept-call-in-btn").linkbutton("options").text == "挂机") {
            gProxy.hangupSoftPhone(nLineNo);//接通直接挂机

            $("#soft-phone-accept-call-in-btn").linkbutton({ text: '接听' });
            $("#soft-phone-accept-call-in-btn").linkbutton({
                iconCls: 'icon-softphone-dialcall'
            });
            $("#soft-phone-accept-call-in-btn").css("color", "green");

            $("#soft-phone-hold-call-in-btn").linkbutton({ text: '保持' });
            $("#soft-phone-hold-call-in-btn").linkbutton({
                iconCls: 'icon-softphone-hold'
            });
            $("#soft-phone-hold-call-in-btn").css("color", "black");

            //软电话按钮隐藏
            $("#soft-phone-accept-call-in-btn").hide();
            $("#soft-phone-reject-call-in-btn").hide();
            $("#soft-phone-hold-call-in-btn").hide();
            return null;
        }

    }
    //处理调度线路
    if (nLineNo == "2") {
        //先等待1秒，关闭声音后再接通电话，否则电脑这边播放的声音会传到电话那头
        gProxy.ringSoftPhone(false, "ring_call_in_softphone.wav");

        setTimeout(function () {
            gProxy.acceptCallInSoftPhone($("#soft-phone-accept-call-id").val(), "2");
            $("#soft-phone-hold-phone").css('display', 'block');
            $("#soft-phone-unhold-phone").css('display', 'none');
        }, 1000)

    }
}

/** 软电话-未接通直接挂机-拒接 */
function rejectSoftPhone(nLineNo) {
    //处理接警线路
    if (nLineNo == "1") {
        gProxy.rejectSoftPhone($("#soft-phone-accept-call-id").val(), nLineNo);
        $("#soft-phone-accept-call-in-btn").linkbutton({ text: '接听' });
        $("#soft-phone-accept-call-in-btn").linkbutton({
            iconCls: 'icon-softphone-dialcall'
        });
        $("#soft-phone-hold-call-in-btn").linkbutton({ text: '保持' });
        $("#soft-phone-hold-call-in-btn").linkbutton({
            iconCls: 'icon-softphone-hold'
        });
        //软电话按钮隐藏
        $("#soft-phone-accept-call-in-btn").hide();
        $("#soft-phone-reject-call-in-btn").hide();
        $("#soft-phone-hold-call-in-btn").hide();
    }
    //处理调度线路
    if (nLineNo == "2") {
        gProxy.rejectSoftPhone($("#soft-phone-accept-call-id").val(), nLineNo);
    }
}
/** 软电话-保持通话（有一个问题，就是保持到恢复目前只能一次，软电话是否需要不使用保持恢复功能，使用OM交换机的这个功能是否更加的好，需要研究） */
function holdAndUnholdSoftPhone(nLineNo) {
    //处理接警线路
    if (nLineNo == "1") {
        //保持通话
        if ($("#soft-phone-hold-call-in-btn").linkbutton("options").text == "保持") {
            gProxy.holdSoftPhone(nLineNo);
            //接听了电话后，点击保存按钮保持改电话
            $("#soft-phone-hold-call-in-btn").linkbutton({ text: '恢复' });
            $("#soft-phone-hold-call-in-btn").linkbutton({
                iconCls: 'icon-softphone-unhold'
            });
            return null;
        }
        //恢复通话
        if ($("#soft-phone-hold-call-in-btn").linkbutton("options").text == "恢复") {
            gProxy.unHoldSoftPhone(nLineNo);
            //解除保存通话，恢复通话
            $("#soft-phone-hold-call-in-btn").linkbutton({ text: '保持' });
            $("#soft-phone-hold-call-in-btn").linkbutton({
                iconCls: 'icon-softphone-hold'
            });
            return null;
        }
    }
    //处理出警线路
    if (nLineNo == "2") {
        //保持通话按钮显示
        if ($("soft-phone-hold-phone").css("display") != "none") {
            gProxy.holdSoftPhone(nLineNo);
            $("#soft-phone-hold-phone").css('display', 'none');
            $("#soft-phone-unhold-phone").css('display', 'block');
            return null;
        }
        //恢复通话按钮显示
        if ($("soft-phone-unhold-phone").css("display") != "none") {
            gProxy.unHoldSoftPhone(nLineNo);
            $("#soft-phone-hold-phone").css('display', 'block');
            $("#soft-phone-unhold-phone").css('display', 'none');
            return null;
        }
    }
}

/** 软电话-恢复软键盘最原始的状态 */
function softPhoneResetkeyword() {
    //挂断按钮显示
    $("#soft-keyboard-input").css('display', 'block');
    $("#delPhone").css('display', 'block');
    $("#soft-phone-del-phone").css('display', 'block');
    $("#soft-phone-call-phone").css('display', 'block');
    $("#soft-phone-call-phone").css('display', 'block');

    //隐藏其他按钮
    $("#soft-phone-hold-phone").css('display', 'none');
    $("#soft-phone-unhold-phone").css('display', 'none');
    $("#soft-phone-dialcall-phone").css('display', 'none');
    $("#soft-phone-reject-phone").css('display', 'none');
    $("#soft-phone-hangup-phone").css('display', 'none');
    $("#soft-keyboard-calling-msg").css('display', 'none');
}

/** 软电话-使用软键盘拨打电话 */
function softPhoneCallPhoneBykeyword() {
    if ($("#soft-keyboard-input").val() == null || $("#soft-keyboard-input").val() == '') {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }

    //使用软键盘拨打电话
    gProxyW.dialCallSoftPhone("2", $("#soft-keyboard-input").val());

    //挂断按钮显示
    $("#soft-phone-hangup-phone").css('display', 'block');
    $("#soft-keyboard-calling-msg").html("正在呼叫：" + $("#soft-keyboard-input").val());
    $('#soft-keyboard-calling-msg').attr("class", "greenblink");
    $("#soft-keyboard-calling-msg").css('display', 'block');

    //隐藏其他按钮
    $("#soft-phone-call-phone").css('display', 'none');
    $("#soft-phone-hold-phone").css('display', 'none');
    $("#soft-phone-unhold-phone").css('display', 'none');
    $("#soft-phone-dialcall-phone").css('display', 'none');
    $("#soft-phone-del-phone").css('display', 'none');
    $("#soft-phone-reject-phone").css('display', 'none');
    $("#soft-keyboard-input").css('display', 'none');
    $("#delPhone").css('display', 'none');
}
/** 软电话-使用软键盘挂断电话 */
function softPhoneHangupPhoneBykeyword(nLineNo) {
    //使用软键盘挂断电话
    gProxy.hangupSoftPhone(nLineNo);
    softPhoneResetkeyword();
}
//============================软电话机处理逻辑 end============================

function windowOpenUrl(url) {
    window.open(url);
}

/*重大事件上报*/
function greatEvent(content) {
    //如果greatEventWindow是null说明已经处理完了
    if (greatEventWindow != null) {
        return false;
    }
    // gProxy.playRefuseToDispatchSound(true);
    greatEventWindow = $.messager.show({
        title: '<div style="display:flex;justify-content: space-between"><div style="display:flex;align-items: center;color:red"><img style="width:20px;height:20px;margin-right:5px" src="style/img/zuoxi_duankai_ic.png"/>重大事件</div></div>',
        msg: `<div style="height:100%;width:100%;text-align: center;display: flex;flex-direction: column;justify-content: space-between;align-items: center;">
            <div class="msgblink" style="margin-top:0px;font-size:1.1rem;text-align:left">${content}</div>
            <div><a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:checkGreatEvent();" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">查看事件</a></div>
        </div>`,
        timeout: 0,
        showType: 'slide',
        closable: false,//是否显示关闭按钮
        width: '400px',
        height: '240px',
        style: {
            right: '',
            top: '',
            left: '0px',
            bottom: '0px'
        },
        onClose: function () {
            // 在消息窗口关闭时执行的回调函数
        }
    });
}

function checkGreatEvent() {
    confirmedMsg({ id: confirmedMsgId }, function (res) {
        greatEventWindow.window("close", true);
        greatEventWindow = null;
        let eventTitle = "事件详情" + "(" + greatEventID + ")"
        openTab(eventTitle, "eventInfo.html?eventId=" + greatEventID, greatEventID);
    }, function (e, url, errMsg) {
        //失败不做处理
        $.messager.alert('异常', '打开事件失败，异常信息：' + errMsg);
    })

}

/** 浏览器最小化，或者浏览器切换到其他标签的时候执行,暂时未想到好办法 */
document.addEventListener('visibilitychange', function () {
    if (document.hidden) {
        // 主页面隐藏
        //当页面切换或隐藏时触发的代码,可以用来清除定时器，暂时不做任何处理
    } else {
        // 主页面唤醒
        //当这个页面重新被唤醒的时候触发的代码,可以用来重新触发定时器，页面唤醒的时候先关闭所有的定时器，然后再启动所有的定时器
        //closeInterval(); //清理所有的定时器
        //if (_token) {
        //    openAllInterval();//开启所有定时器
        //}
    }
})


/** 定时获取本座席视频线路的情况 */
var _currentVideoInCall = null;//当前视频呼入的记录

function taskVideoStatusIntervalTimeout() {
    clearTimeout(window.taskVideoStatusInterval);
    if (_taskVideoStatusInterval) {
        window.taskVideoStatusInterval = null;

        //获取接警的分机的电话状态
        getCurrentSeatVideoStatusBySeatId({seatId: _pSeat.id },
            function (res) {
                $("#isVideoHarass").hide();

                if (res.noDisturb == "on") {
                    //免打扰处理
                    $('#currentVideoCallNum').html("限制视频呼救呼入");
                    $('#currentVideoCallNum').attr("class", "redblink");
                    var ele = $("#videoCallInStatus").children(".move");
                    ele.animate({ left: "0" }, 300, function () {
                        ele.attr("data-state", "off");
                    });
                    $("#videoCallInStatus").removeClass("on").removeClass("none").addClass("off");

                    $("#video-accept-btn").hide();//显示接听按钮
                    $("#video-transfer-btn").hide();//显示转移按钮
                    $("#video-complete-btn").hide();//显示完成按钮

                    $("#videoCallInStatusTxt").show();
                    $("#videoCallInStatus").show();

                    _currentVideoInCall = null;

                } else {
                    var ele = $("#videoCallInStatus").children(".move");
                    $("#videoCallInStatus").removeClass("none").addClass("on");
                    ele.animate({ left: '25px' }, 300, function () {
                        $(this).attr("data-state", "on");
                    });
                    $("#videoCallInStatus").removeClass("off").addClass("on");

                    //免打扰是否开启
                    if (res.extStatus == "IDLE" || res.extStatus == "ONLINE") { //空闲
                        $('#currentVideoCallNum').html("空闲");
                        $('#currentVideoCallNum').attr("class", "blueblink");


                        $("#video-accept-btn").hide();//显示接听按钮
                        $("#video-transfer-btn").hide();//显示转移按钮
                        $("#video-complete-btn").hide();//显示完成按钮

                        $("#videoCallInStatusTxt").show();
                        $("#videoCallInStatus").show();

                        _currentVideoInCall = null;
                    } else { //忙
                        switch (res.callStatus) {
                            case "ANSWERED":
                            case "ANSWER":
                                _currentVideoInCall = res.videoCall;
                                $('#currentVideoCallNum').html("接听:" + "视频呼救中");
                                $('#currentVideoCallNum').attr("class", "greenblink");

                                $("#video-accept-btn").hide();//显示接听按钮
                                $("#video-transfer-btn").hide();//显示转移按钮
                                $("#video-complete-btn").show();//显示完成按钮
                    
                                $("#videoCallInStatusTxt").hide();
                                $("#videoCallInStatus").hide();

                                // if(res.isHarass == '1'){
                                //     $("#isHarass").show()
                                //     $("#isHarass").html("骚扰");
                                //     $('#isHarass').attr("class", "redblink");
                                // }
                                
                                //设置当前的全局电话号码
                                //_currentInCall = null;
                                //_currentInCall = res;
                                //显示创建事件的弹窗
                                // let domeStr = document.getElementById('createEventsIframe' + res.videoCall.callTime)
                                // if (!domeStr) {
                                //     openCreateNewEventTabByPhone("", res.videoCall.id, res.videoCall.callTime, 1);
                                // }
                                
                                break;
                            case "RING":
                                $('#currentVideoCallNum').html("响铃:" + "视频呼入");
                                $('#currentVideoCallNum').attr("class", "greenblink");

                                $("#video-accept-btn").show();//显示接听按钮
                                $("#video-transfer-btn").show();//显示转移按钮
                                $("#video-complete-btn").hide();//显示完成按钮
                    
                                $("#videoCallInStatusTxt").hide();
                                $("#videoCallInStatus").hide();

                                _currentVideoInCall = res.videoCall;

                                //播放视频呼救铃声
                                gProxy.ringSoftPhone(true, "ring_call_in_softphone.wav");
                                // if(res.isHarass == '1'){
                                //     $("#isHarass").show()
                                //     $("#isHarass").html("骚扰");
                                //     $('#isHarass').attr("class", "redblink");
                                // }
                                // _currentInCall = null;
                                // _currentInCall = res;
                                //需要判断是否开启了软电话的功能
                                break;
                            default:
                                //软电话按钮隐藏
                                // $("#soft-phone-accept-call-in-btn").hide();
                                // $("#soft-phone-reject-call-in-btn").hide();
                                // $("#soft-phone-hold-call-in-btn").hide();

                                // if (res.isVisitor == '0') { //如果是非来电，那么就不处理
                                //     return null;
                                // }
                                $("#video-accept-btn").hide();//显示接听按钮
                                $("#video-transfer-btn").hide();//显示转移按钮
                                $("#video-complete-btn").hide();//显示完成按钮
                    
                                $("#videoCallInStatusTxt").show();
                                $("#videoCallInStatus").show();

                                $('#currentVideoCallNum').html("线路忙...");
                                $('#currentVideoCallNum').attr("class", "redblink");
                                _currentInCall = null;
                                break;
                        }
                    }
                }
            },
            function (e, url, errMsg) {
                $('#currentCallNum').html("系统异常");
                $('#currentCallNum').attr("class", "redblink");
                //软电话按钮隐藏
                // $("#soft-phone-accept-call-in-btn").hide();
                // $("#soft-phone-reject-call-in-btn").hide();
                // $("#soft-phone-hold-call-in-btn").hide();
                //失败处理
                //$.messager.alert('异常', '120接警分机实时电话状态异常，异常信息：' + errMsg);
            });

        window.taskVideoStatusInterval = setTimeout(taskVideoStatusIntervalTimeout, 3000)
    }


}



/** 接听视频呼入 */
function acceptVideoCall() {
    showProcess(true, "正在接听视频呼救...");
    updateCallCurrentStatus({callId:  _currentVideoInCall.id, callStatus: "ANSWER",seatId: _pSeat.id},
        function (res) {
            $("#video-accept-btn").hide();//显示接听按钮
            $("#video-transfer-btn").hide();//显示转移按钮
            $("#video-complete-btn").show();//显示完成按钮

            $("#videoCallInStatusTxt").hide();
            $("#videoCallInStatus").hide();

            // 视频呼入接听返回数据
            openCreateNewEventTabByVideo(res.phoneNumber, res.id, res.callTime, 1, 
                res.address, 
                res.lng,
                res.lat,
                res.nickName,
                res.avatarUrl,
                res.gender);
            showProcess(false, "正在接听视频呼救...");
            // 接听视频呼入成功
            //打开视频呼入对话页面
            try {
                //发送刷新的车辆数据给第二屏幕
                gProxy.openVideoCallingPage(_currentVideoInCall.id,"视频呼入对话"); //打开视频呼入对话执行
            } catch (e) {
                // B/S模式打开视频呼入对话执行
                bsProxy.openVideoCallingPage(_currentVideoInCall.id,"视频呼入对话"); //BS模式打开视频呼入对话执行
            }
        },
        function (e, url, errMsg) {
            showProcess(false, "正在接听视频呼救...");
            $.messager.alert('提示', '接听视频呼入失败：' + errMsg, 'error');
        }
    );
}


/** 挂断视频呼救视频 */
function hangupVideoCall() {
    $.messager.confirm("提示", "是否完成此次视频呼救？", function (r) {
        if (r) {
            showProcess(true, "正在挂断视频呼救...");
            updateCallCurrentStatus({callId: _currentVideoInCall.id, callStatus: "BYE",seatId: _pSeat.id},
                function (res) {
                    $("#video-accept-btn").hide();//显示接听按钮
                    $("#video-transfer-btn").hide();//显示转移按钮
                    $("#video-complete-btn").hide();//显示完成按钮
        
                    $("#videoCallInStatusTxt").show();
                    $("#videoCallInStatus").show();
                    // 挂断视频呼救视频成功
                    showProcess(false, "正在挂断视频呼救...");
                },
                function (e, url, errMsg) {
                    showProcess(false, "正在挂断视频呼救...");
                    $.messager.alert('提示', '挂断视频呼救失败：' + errMsg, 'error');
                }
            );
        }
    });


}

/** 转移视频呼入 */
function transferVideoCall(toSeatId) {
    try {
        transferVideoByCallId({callId: _currentVideoInCall.id, toSeatId: toSeatId},
            function (res) {
                // 转移视频呼入成功
            },
            function (e, url, errMsg) {
                $.messager.alert('提示', '转移视频呼入失败：' + errMsg, 'error');
            }
        );

    } catch (e) {
        $.messager.alert('提示', '转移视频呼入失败：' + e.message, 'error');
    }
}

/** 本座席限制视频呼入 开启和关闭 */
function videoCallLineNoDisturb(th) {
    var ele = $(th).children(".move");
    var noDisturb = "";
    var noDisturbStr = "";
    if (ele.attr("data-state") == "on") {
        //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
        noDisturb = "on";
        noDisturbStr = "关闭";
    } else if (ele.attr("data-state") == "off") {
        noDisturb = "off";
        noDisturbStr = "开启";
    }

    $.messager.confirm("提示", "是否" + noDisturbStr + "本座席的视频呼救呼入？", function (r) {
        if (r) {
            //打开和关闭OM
            var params = {
                "noDisturb": noDisturb,
                "seatId": _pSeat.id
            }
            showProcess(true, "正在" + noDisturbStr + "本座席的视频呼救呼入...");

            setVideoCallLineNoDisturb(params,
                function (res) {
                    showProcess(false);
                    //根据this对象获取到节点move的css，然后对css进行操作，实现按钮滑动开关的效果
                    if (ele.attr("data-state") == "on") {
                        //获取到按钮的名称和开关状态，进行后台操作；设置关的状态=0，开=1
                        ele.animate({ left: "0" }, 300, function () {
                            ele.attr("data-state", "off");
                        });
                        $(th).removeClass("on").addClass("off");
                    } else if (ele.attr("data-state") == "off") {
                        ele.animate({ left: '25px' }, 300, function () {
                            $(this).attr("data-state", "on");
                        });
                        $(th).removeClass("off").addClass("on");
                    }
                    $.messager.alert('提示', noDisturbStr + '成功');
                },
                function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('提示', noDisturbStr + '失败，异常信息：' + errMsg);
                }
            );
        }
    });
}

// ========== 右键菜单相关函数 ==========

/**
 * 事件列表右键菜单处理函数
 * @param {Event} e - 右键点击事件
 * @param {number} rowIndex - 行索引
 * @param {Object} rowData - 行数据
 */
function onEventRowContextMenu(e, rowIndex, rowData) {
    e.preventDefault();
    
    // 将当前选中的行数据保存到全局变量
    window._currentEventContextRow = rowData;
    window._currentEventContextRowIndex = rowIndex;
    
    // 根据事件状态和锁定状态动态调整菜单项
    var menu = $('#event-context-menu');
    
    // 检查是否可以设置待派（只有落单和预约专题才能设置待派）
    var canSetWaiting = (rowData.eventStatus == '1' || rowData.eventStatus == '7'); // 1=落单, 7=预约
    
    // 简化菜单状态控制，避免findItem可能的问题
    try {
        if (!canSetWaiting) {
            menu.menu('disableItem', menu.find('div:contains("设置待派")'));
        } else {
            menu.menu('enableItem', menu.find('div:contains("设置待派")'));
        }
        
        // 检查事件是否已锁定，动态显示锁定/解锁菜单
        if (rowData.isLock && rowData.isLock == '1') {
            menu.menu('disableItem', menu.find('div:contains("锁定事件")'));
            menu.menu('enableItem', menu.find('div:contains("解锁事件")'));
        } else {
            menu.menu('enableItem', menu.find('div:contains("锁定事件")'));
            menu.menu('disableItem', menu.find('div:contains("解锁事件")'));
        }
    } catch (err) {
        // 菜单状态控制出错
    }
    
    // 显示右键菜单
    menu.menu('show', {
        left: e.pageX,
        top: e.pageY
    });
}

/**
 * 设置事件为待派状态
 */
function setEventToWaitingStatus() {
    var rowData = window._currentEventContextRow;
    if (!rowData) {
        $.messager.alert('提示', '请先选择要设置的事件', 'warning');
        return;
    }
    
    // 检查是否可以设置待派
    if (rowData.eventStatus != '1' && rowData.eventStatus != '7') {
        $.messager.alert('提示', '只有落单和预约事件才能设置为待派状态', 'warning');
        return;
    }
    
    $.messager.confirm('确认', '是否将事件"' + rowData.eventName + '"设置为待派状态？', function(r) {
        if (r) {
            // 调用接口设置事件为待派状态
            updateEventPending({
                eventId: rowData.eventId
            }, function(data) {
                $.messager.alert('提示', '设置待派状态成功', 'info');
                refreshEventlist(); // 刷新事件列表
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '设置待派状态失败: ' + errMsg, 'error');
            });
        }
    });
}

/**
 * 锁定事件
 */
function lockEvent() {
    var rowData = window._currentEventContextRow;
    if (!rowData) {
        $.messager.alert('提示', '请先选择要锁定的事件', 'warning');
        return;
    }
    
    // 检查事件是否已锁定
    var isLocked = rowData.isLocked == '1' || rowData.locked == true;
    if (isLocked) {
        $.messager.alert('提示', '该事件已经被锁定', 'warning');
        return;
    }
    
    $.messager.confirm('确认', '是否锁定事件"' + rowData.eventName + '"？', function(r) {
        if (r) {
            updateEventLock({
                eventId: rowData.eventId,
                isLock: '1',
                lockSeatId: _pSeat.id,
                lockSeatCode: _pSeat.seatId
            }, function(data) {
                $.messager.alert('提示', '锁定事件成功', 'info');
                refreshEventlist(); // 刷新事件列表
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '锁定事件失败: ' + errMsg, 'error');
            });
        }
    });
}

/**
 * 解锁事件
 */
function unlockEvent() {
    var rowData = window._currentEventContextRow;
    if (!rowData) {
        $.messager.alert('提示', '请先选择要解锁的事件', 'warning');
        return;
    }
    
    // 检查事件是否已锁定
    if ( rowData.isLock && rowData.isLock != '1' ) {
        $.messager.alert('提示', '该事件未被锁定', 'warning');
        return;
    }

    if(!(_pSeat && _pSeat.directorPermission && _pSeat.directorPermission == '1' )){//主任座席可以解锁所有事件
        if(!canOperateEvent(rowData.isLock,rowData.lockSeatUserId,rowData.lockSeatId)){
            $.messager.alert('提示', '事件被 ' + (rowData.lockSeatCode || '') + ' (' + (rowData.lockSeatUserName || '') + ') 锁定，您可以通知锁定事件的坐席或主任座席解锁！', 'error');
            return;
        }
    }else{
        //主任座席可以解锁所有事件
    }
    
    $.messager.confirm('确认', '是否解锁事件"' + rowData.eventName + '"？', function(r) {
        if (r) {
            updateEventLock({
                eventId: rowData.eventId,
                isLock: '0',
                lockSeatId: _pSeat.id,
                lockSeatCode: _pSeat.seatId
            }, function(data) {
                $.messager.alert('提示', '解锁事件成功', 'info');
                refreshEventlist(); // 刷新事件列表
            }, function(e, url, errMsg) {
                $.messager.alert('错误', '解锁事件失败: ' + errMsg, 'error');
            });
        }
    });
}

// ========== 车辆类型相关函数 ==========

/**
 * 加载车辆类型字典数据
 */
function loadCarTypeDictionary() {
    queryAllDic(["car_type"], function(data) {
        // 修复：queryAllDic返回的是数组格式，需要过滤出car_type类型的数据
        if (data && Array.isArray(data)) {
            _carTypeDictionary = data.filter(function(item) {
                return item.typeCode === 'car_type';
            });
        } else if (data && data.car_type) {
            // 兼容对象格式
            _carTypeDictionary = data.car_type;
        } else {
            _carTypeDictionary = [];
        }
    }, function(error) {
        _carTypeDictionary = [];
    });
}

/**
 * 根据车辆类型代码获取显示名称
 * @param {string} carTypeCode 车辆类型代码
 * @returns {string} 车辆类型显示名称
 */
function getCarTypeDisplayName(carTypeCode) {
    if (!carTypeCode || carTypeCode === '') {
        return '';
    }
    
    // 在字典中查找对应的显示名称
    if (_carTypeDictionary && _carTypeDictionary.length > 0) {
        var matchedType = _carTypeDictionary.find(function(dict) {
            return dict.codeName === carTypeCode;
        });
        
        if (matchedType) {
            return matchedType.codeVale;
        }
    }
    
    // 如果字典中没找到，返回原始代码
    return carTypeCode;
}

/**
 * 车辆类型格式化函数
 * @param {string} value 车辆类型代码
 * @param {object} row 行数据
 * @param {number} index 行索引
 * @returns {string} 格式化后的车辆类型显示名称
 */
function carTypeFormatter(value, row, index) {
    // 尝试从不同字段获取车辆类型代码
    var carTypeCode = value || row.carType || row.vehicleType || row.mobileType || row.type || row.typeCode || row.carTypeCode || row.vehicleTypeCode || row.mobileTypeCode;
    
    if (!carTypeCode) {
        return '';
    }
    
    // 获取车辆类型显示名称
    return getCarTypeDisplayName(carTypeCode);
}

/**
 * 距离格式化函数
 * @param {string} value 距离值
 * @param {object} row 行数据
 * @param {number} index 行索引
 * @returns {string} 格式化后的距离显示
 */
function distanceFormatter(value, row, index) {
    if (!value || value === '' || value === '0' || value === 0) {
        return '';
    }
    
    // 16px黑色加粗显示
    return '<span style="font-size: 16px; font-weight: bold; color: #000000;">' + value + '</span>';
}

// ========== 左侧面板水平拖拽分隔条功能 ==========

/**
 * 初始化水平拖拽分隔条功能
 */
function initHorizontalDragSeparator() {
    const separator = document.getElementById('horizontalDragSeparator');
    const messageArea = document.getElementById('messageArea');
    const tabsArea = document.getElementById('tabsArea');
    const leftPanel = document.getElementById('leftPanel');
    
    if (!separator || !messageArea || !tabsArea || !leftPanel) {
        // console.log('水平拖拽分隔条元素未找到');
        return;
    }

    let isDragging = false;
    let startY = 0;
    let startMessageHeight = 0;
    let startTabsHeight = 0;
    let resizeTimer = null;

    // 鼠标按下事件
    separator.addEventListener('mousedown', function(e) {
        isDragging = true;
        startY = e.clientY;
        
        // 获取当前高度
        const messageRect = messageArea.getBoundingClientRect();
        const tabsRect = tabsArea.getBoundingClientRect();
        startMessageHeight = messageRect.height;
        startTabsHeight = tabsRect.height;
        
        // 防止文本选择
        document.body.style.userSelect = 'none';
        document.body.style.cursor = 'ns-resize';
        
        e.preventDefault();
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;
        
        const deltaY = e.clientY - startY;
        const leftPanelRect = leftPanel.getBoundingClientRect();
        const callMessageArea = document.getElementById('callMessageArea');
        const callMessageRect = callMessageArea.getBoundingClientRect();
        
        // 计算可用高度（左侧面板总高度 - 呼叫与消息区域高度 - 分隔条高度）
        const availableHeight = leftPanelRect.height - callMessageRect.height - 3;
        
        // 计算新的高度
        let newMessageHeight = startMessageHeight + deltaY;
        let newTabsHeight = startTabsHeight - deltaY;
        
        // 限制最小高度
        const minHeight = 100;
        if (newMessageHeight < minHeight) {
            newMessageHeight = minHeight;
            newTabsHeight = availableHeight - minHeight;
        } else if (newTabsHeight < minHeight) {
            newTabsHeight = minHeight;
            newMessageHeight = availableHeight - minHeight;
        }
        
        // 应用新的高度
        messageArea.style.flex = 'none';
        messageArea.style.height = newMessageHeight + 'px';
        tabsArea.style.flex = 'none';
        tabsArea.style.height = newTabsHeight + 'px';
        
        // 使用节流方式通知EasyUI组件重新计算大小
        if (resizeTimer) {
            clearTimeout(resizeTimer);
        }
        resizeTimer = setTimeout(function() {
            try {
                $('#dl-msg-list').datagrid('resize');
                $('#missedCalls').datagrid('resize');
                $('#dg-seat-station-status-list').datagrid('resize');
                $('#left-list').tabs('resize');
            } catch (e) {
                // 忽略可能的错误
            }
        }, 16); // 约60fps的更新频率
        
        e.preventDefault();
    });

    // 鼠标松开事件
    document.addEventListener('mouseup', function(e) {
        if (isDragging) {
            isDragging = false;
            document.body.style.userSelect = '';
            document.body.style.cursor = '';
            
            // 拖拽结束后最终调整一次组件大小
            setTimeout(function() {
                try {
                    $('#dl-msg-list').datagrid('resize');
                    $('#missedCalls').datagrid('resize');
                    $('#dg-seat-station-status-list').datagrid('resize');
                    $('#left-list').tabs('resize');
                } catch (e) {
                    // 忽略可能的错误
                }
            }, 50);
        }
    });

    // 鼠标悬停效果
    separator.addEventListener('mouseenter', function() {
        separator.style.backgroundColor = '#87ceeb';
    });

    separator.addEventListener('mouseleave', function() {
        if (!isDragging) {
            separator.style.backgroundColor = '#cce7ff';
        }
    });

    // 窗口大小变化时重新调整组件大小
    window.addEventListener('resize', function() {
        setTimeout(function() {
            try {
                $('#dl-msg-list').datagrid('resize');
                $('#missedCalls').datagrid('resize');
                $('#dg-seat-station-status-list').datagrid('resize');
                $('#left-list').tabs('resize');
            } catch (e) {
                // 忽略可能的错误
            }
        }, 100);
    });
    
    // console.log('水平拖拽分隔条初始化完成');
    
    // 初始化时设置默认高度比例（消息提醒:未接来电 = 1:2）
    setTimeout(function() {
        const leftPanelRect = leftPanel.getBoundingClientRect();
        const callMessageArea = document.getElementById('callMessageArea');
        const callMessageRect = callMessageArea.getBoundingClientRect();
        
        // 计算可用高度
        const availableHeight = leftPanelRect.height - callMessageRect.height - 3;
        const messageHeight = Math.floor(availableHeight * 0.3); // 30%给消息提醒
        const tabsHeight = availableHeight - messageHeight; // 70%给未接来电
        
        // 设置初始高度
        messageArea.style.flex = 'none';
        messageArea.style.height = messageHeight + 'px';
        tabsArea.style.flex = 'none';
        tabsArea.style.height = tabsHeight + 'px';
        
        // 调整组件大小
        try {
            $('#dl-msg-list').datagrid('resize');
            $('#missedCalls').datagrid('resize');
            $('#dg-seat-station-status-list').datagrid('resize');
            $('#left-list').tabs('resize');
        } catch (e) {
            // 忽略可能的错误
        }
    }, 100);
}

/**
 * 在登录成功后调用初始化函数
 */
function initLeftPanelDragSeparator() {
    // 延迟执行以确保DOM元素已完全加载
    setTimeout(function() {
        initHorizontalDragSeparator();
    }, 500);
}
