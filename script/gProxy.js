function gBsProxy(target) {
    console.log('gBsProxy构造函数被调用，参数:', target);
    console.log('gBsProxy定义时间:', new Date().toISOString());
    // 构造函数逻辑
    this.target = target;
}

/** 获取token */
gBsProxy.prototype.getUserToken = function () {
    return window.localStorage.getItem("_token");
};

/** 获取用户信息 */
gBsProxy.prototype.getSeatInfo = function () {
    return window.localStorage.getItem("_pSeat");
};

/** 获取本地用户信息 */
gBsProxy.prototype.getUserInfo = function () {
    return window.localStorage.getItem("_user");
};

gBsProxy.prototype.getBaseUrl = function () {
    //console.log('gBsProxy.prototype.getBaseUrl() called');
    return window.location.protocol + "//" + window.location.host+"/";
};

gBsProxy.prototype.getExeVersionInfo = function () {
    //console.log('gBsProxy.prototype.getExeVersionInfo() called');
    return "B/S *******";
};

/** 从cookie中读取最后一次登录的信息，代码先写死 */
gBsProxy.prototype.getLastLoginUser = function () {
    let _lastLoginUser = window.localStorage.getItem('_lastLoginUser');
    if (_lastLoginUser) {
        return _lastLoginUser;
    } else {
        return "{\"Username\":\"\",\"Password\":\"\",\"SavePsw\":false,\"DisplayName\":null,\"SeatId\":\"\"}";
    }
};

/** 登录成功后，把token发送给副屏幕的监听 */
gBsProxy.prototype.loginSuccess = function (token) {
    window.localStorage.setItem('loginSuccess', "{\"token\":\"" + token + "\",times:\"" + timestampToYmdHmsS(Date.now())+"\"}");
};

/** 解除挂起副屏幕 */
gBsProxy.prototype.unHangUpSecondScreen = function () {
    window.localStorage.setItem('unHangUpSecondScreen', "{\"data\":\"" + timestampToYmdHmsS(Date.now())+"\"}");
};


/** 挂起副屏幕 */
gBsProxy.prototype.hangUpSecondScreen = function () {
    window.localStorage.setItem('hangUpSecondScreen', "{\"data\":\"" + timestampToYmdHmsS(Date.now())+"\"}");
};

/** 同步车辆数据给到副屏幕 */
gBsProxy.prototype.syncMobiles = function (mobileData) {
    let data = {
        data: mobileData,
        times: timestampToYmdHmsS(Date.now())
    }
    window.localStorage.setItem('syncMobiles', JSON.stringify(data));
};

/** 获取副屏幕地图当前的位置经纬度 */
gBsProxy.prototype.getMapCurrentLocLngLat = function (mobileData) {
    return window.localStorage.getItem("_MapCurrentLocLngLat");
};

/** 通过经纬度设置选中车辆在地图上的定位 */
gBsProxy.prototype.setPlaceBylonAndLat = function (eventId) {
    window.localStorage.setItem('setPlaceBylonAndLat', "{\"data\":\"" + eventId + "\",times:\"" + timestampToYmdHmsS(Date.now()) + "\"}");
};

/** 发送地址到副屏的输入框中 */
gBsProxy.prototype.sendToMap = function (address, longitude, latitude) {
    window.localStorage.setItem('sendToMap', "{\"address\":\"" + address + "\",\"longitude\":" + longitude + ",\"latitude\":" + latitude + ",\"times\":\"" + timestampToYmdHmsS(Date.now())+"\"}");
};

/** 设置副屏当前是否派车状态，派车状态 */
gBsProxy.prototype.sendToNewEventStatus = function (status) {
    window.localStorage.setItem('sendToNewEventStatus', "{\"status\":\"" + status + "\",\"times\":\"" + timestampToYmdHmsS(Date.now()) + "\"}");
};

/** 打开视频呼入对话页面（多屏幕传递） */
gBsProxy.prototype.openVideoCallingPage = function (callVideoId, tabsShow) {
    var data = {
        callVideoId: callVideoId || "",
        tabsShow: tabsShow || "",
        times: timestampToYmdHmsS(Date.now())
    };
    window.localStorage.setItem('openVideoCallingPage', JSON.stringify(data));
};

/** 播放报警的声音 */
gBsProxy.prototype.playAlert = function (openIs) {
    if (openIs) {
        //监听循环播放
        var audioaAlert = document.getElementById('audioaAlert');
        audioaAlert.play();
    } else {
        var audioaAlert = document.getElementById('audioaAlert');
        audioaAlert.pause();
        audioaAlert.load();
    }
};

/** 播放拒绝派遣的声音 */
gBsProxy.prototype.playRefuseToDispatchSound = function (openIs) {
    if (openIs) {
        //播放拒绝派遣音频
        var audioRefuseDispatch = document.getElementById('audioRefuseDispatch');
        if (audioRefuseDispatch) {
            audioRefuseDispatch.currentTime = 0; // 重置播放位置
            var playPromise = audioRefuseDispatch.play();
            if (playPromise !== undefined) {
                playPromise.catch(function(error) {
                    console.error('播放拒绝派遣音频失败:', error);
                });
            }
        } else {
            console.warn('未找到audioRefuseDispatch音频元素，使用alert音频替代');
            // fallback 到 alert 音频
            var audioaAlert = document.getElementById('audioaAlert');
            if (audioaAlert) {
                audioaAlert.currentTime = 0;
                var fallbackPromise = audioaAlert.play();
                if (fallbackPromise !== undefined) {
                    fallbackPromise.catch(function(error) {
                        console.error('播放替代音频失败:', error);
                    });
                }
            }
        }
    } else {
        //停止播放拒绝派遣音频
        var audioRefuseDispatch = document.getElementById('audioRefuseDispatch');
        if (audioRefuseDispatch) {
            audioRefuseDispatch.pause();
            audioRefuseDispatch.currentTime = 0; // 重置播放位置
        }
        // 同时停止可能的替代音频
        var audioaAlert = document.getElementById('audioaAlert');
        if (audioaAlert) {
            audioaAlert.pause();
            audioaAlert.currentTime = 0;
        }
    }
};

/** 打印HTML内容（B/S模式下的打印功能） */
gBsProxy.prototype.print = function (htmlContent) {
    // B/S模式下使用浏览器的打印功能
    try {
        // 创建一个新的窗口用于打印
        var printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (printWindow) {
            printWindow.document.write(htmlContent);
            printWindow.document.close();
            
            // 等待内容加载完成后打印
            printWindow.onload = function() {
                printWindow.print();
                // 打印完成后关闭窗口
                setTimeout(function() {
                    printWindow.close();
                }, 1000);
            };
        } else {
            // 如果无法打开新窗口，使用当前窗口打印
            console.warn('无法打开打印窗口，尝试在当前窗口打印');
            this.printInCurrentWindow(htmlContent);
        }
    } catch (error) {
        console.error('打印失败:', error);
        // 降级方案：使用当前窗口打印
        this.printInCurrentWindow(htmlContent);
    }
};

/** 在当前窗口中打印（备用方案） */
gBsProxy.prototype.printInCurrentWindow = function (htmlContent) {
    try {
        // 保存当前页面内容
        var originalContent = document.body.innerHTML;
        var originalTitle = document.title;
        
        // 替换为打印内容
        document.body.innerHTML = htmlContent;
        document.title = "120急救单打印";
        
        // 执行打印
        window.print();
        
        // 恢复原始内容
        setTimeout(function() {
            document.body.innerHTML = originalContent;
            document.title = originalTitle;
            // 重新绑定事件等
            location.reload();
        }, 1000);
    } catch (error) {
        console.error('当前窗口打印失败:', error);
        alert('打印功能暂时不可用，请手动复制内容进行打印');
    }
};

function timestampToYmdHmsS(timestamp) {
    const date = new Date(timestamp);
    const year = date.getUTCFullYear();
    const month = ('0' + (date.getUTCMonth() + 1)).slice(-2); // 月份是从0开始的
    const day = ('0' + date.getUTCDate()).slice(-2);
    const hours = ('0' + date.getUTCHours()).slice(-2);
    const minutes = ('0' + date.getUTCMinutes()).slice(-2);
    const seconds = ('0' + date.getUTCSeconds()).slice(-2);
    const milliseconds = ('00' + date.getUTCMilliseconds()).slice(-3); // 毫秒可能需要补足两位
    return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
}

// 使用示例
//var proxy = new gBsProxy(document.body);
//proxy.method1(); // 输出: Method 1 called
//proxy.method2(); // 输出: Method 2 called
