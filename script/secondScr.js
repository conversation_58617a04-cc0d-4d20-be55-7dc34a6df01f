/** 电话簿离线列表 */
var _contactList = null;
/** 是否处于离线模式 */
var _offline = false;
/** 是否派车状态,主屏幕是否正在派车状态，1：是；0：否 */
var newEventStatus = "0";
/** 当前位置 */
var _currentLoc = null;
/** 存放选中的派车地点的坐标 */
var _selCarId = null;
/** 存放之前的地图上的车辆的marker，画车辆的时候，先删除掉旧的救护车 */
var carMarkers = [];
/** 最后选中的车辆的marker */
var carLastSelectMarker = null;
var map;
var tokens = null;
/** 驾车路线 */
var driving = null;
/** 派车地点，事件地址 */
var sendCarMarker = null;
/** 是否已经初始化 */
var isInitStations = false;
/** 点击左侧菜单后拿到的右边列表数据 */
var menuObj = {}
// 10-7 版本新增
/**  存储该车，用于视口跟随 */
var nowCar = null;
/**  是否视口跟随车辆？ 默认不跟随 */
var isFollow = false;
/**  是否是地图点击的车辆还是 右侧菜单栏点击的车辆 */
var isMapClick = false;
var showGuiJi = false;
var staLists = null;
/**  座席信息 */
var _pSeat = null;
var infoWindow = null
/**  配置的分站勾选信息 */
var fenzhanChecked = true
/**  配置的车牌勾选信息 */
var chepaiChecked = true
/**  配置的速度勾选信息 */
var suduChecked = true
/**  配置的车辆状态勾选信息 */
var statusChecked = true
/** 配置的弹窗是否打开用于监听 */
var configDialog = false
var tableHeight = 0
var _stations = [];//所有分站信息

// 判断是否显示 所属分站 车牌号 速度 状态等
$(document).ready(function () {
    // 监听配置弹窗的事件
    $('#config-dialog').dialog({
            onOpen: function() {
            configDialog = true
            $('#configs').css('display', 'none');
            $('#configsChoose').css('display', 'block');
        },
        onClose: function() {
            configDialog = false
            $('#configs').css('display', 'block');
            $('#configsChoose').css('display', 'none');
        },
    });
    $('#config-dialog').window('close');
    $('#show_guiji').css('display', 'block');
    $('#hide_guiji').css('display', 'none');
    $('#configs').css('display', 'block');
    $('#configsChoose').css('display', 'none');
    //无操作
    $('#isFollow').click(function (e) {
        isFollow = true
        $('#isFollow').css('display', 'none');
        $('#noFollow').css('display', 'block');
    });
    $('#noFollow').click(function (e) {
        isFollow = false
        $('#isFollow').css('display', 'block');
        $('#noFollow').css('display', 'none');
    });
    // 点击配置按钮触发打开弹窗
    $('#configs').click(function (e) {
        $('#config-dialog').window('open');
    });
    $('#configsChoose').click(function (e) {
        $('#config-dialog').window('close');
    });
    $('#show_guiji').click(function (e) {
        showGuiJi = true
        $('#hide_guiji').css('display', 'block');
        $('#show_guiji').css('display', 'none');
    });
    $('#hide_guiji').click(function (e) {
        showGuiJi = false
        $('#show_guiji').css('display', 'block');
        $('#hide_guiji').css('display', 'none');
        var allOverlay = map.getOverlays();
        for (var i = 0; i < allOverlay.length; i++){
            // if(allOverlay[i].toString().indexOf("Marker") > 0){//删除标志点
                // map.removeOverlay(allOverlay[i]);
            // }
            if(allOverlay[i].toString().indexOf("Polyline") > 0){//删除折线
                map.removeOverlay(allOverlay[i]);
            }
        }
    });
});
// 点击显示配置分站的easyui处理
$('#fenzhan-checkbox').checkbox({
    onChange: function(checked) {
        fenzhanChecked = checked
        map.closeInfoWindow() // 关闭 信息弹窗
        reRangeAndShowStations(staLists) // 重新请求 配置信息
        sendCarMarker.openInfoWindow(infoWindow); // 再打开信息弹窗就更新页面内容啦
    },
});
// 点击显示配置车牌号的easyui处理
$('#chepai-checkbox').checkbox({
    onChange: function(checked) {
        chepaiChecked = checked
        map.closeInfoWindow() // 关闭 信息弹窗
        reRangeAndShowStations(staLists) // 重新请求 配置信息
        sendCarMarker.openInfoWindow(infoWindow); // 再打开信息弹窗就更新页面内容啦
    },
}); 
// 点击显示配置车辆速度的easyui处理
$('#sudu-checkbox').checkbox({
    onChange: function(checked) {
        suduChecked = checked
        map.closeInfoWindow() // 关闭 信息弹窗
        reRangeAndShowStations(staLists) // 重新请求 配置信息
        sendCarMarker.openInfoWindow(infoWindow); // 再打开信息弹窗就更新页面内容啦
    },
}); 
// 点击显示配置车辆速度的easyui处理
$('#status-checkbox').checkbox({
    onChange: function(checked) {
        statusChecked = checked
        map.closeInfoWindow() // 关闭 信息弹窗
        reRangeAndShowStations(staLists) // 重新请求 配置信息
        sendCarMarker.openInfoWindow(infoWindow); // 再打开信息弹窗就更新页面内容啦
    },
});
/** 设置车辆信息刷新计时器,定时刷新座席和分站状态 */
function drawMobileIntervalTimeout() {
    clearTimeout(window.drawMobileInterval);
    window.drawMobileInterval = null;
    if (_token != "" && _mobilesWithDistance != null) {
        sortAndShowStations(_mobilesWithDistance);
        if (nowCar && isFollow) {
            // 判断点击的车量是哪个，然后根据车辆定位刷新地图时 ，就以他为中心, 实现视口跟随
            if (!isMapClick) { // 这里判断了 是右侧点击的还是地图上点击的，两个地方取值方式不太一样
                var id = $(nowCar.target).attr('data');
                var car = _mobilesWithDistance.first(function (m) { return m.id == id; });
                map.setCenter(new BMap.Point(car.lng, car.lat));
            } else {
                map.setCenter(new BMap.Point(nowCar.target.Ov.lng, nowCar.target.Ov.lat));
            }
        }
        //画一下车辆状态下的路线图
        if (showGuiJi && nowCar) {
            // 这是是配置地图出发路线的参数，纯配置
            var driving = new BMap.DrivingRoute(map, {
                renderOptions: { map: map, autoViewport: false },
            });
            driving.setSearchCompleteCallback(function () {
                var plan = driving.getResults().getPlan(0)
                for (var i = 0; i < plan.getNumRoutes(); i++) {
                    var pts = plan.getRoute(i).getPath() // 重点在这   这个地方是关于修改颜色的
                    var polyline = new BMap.Polyline(pts, {
                        strokeColor: "#03C27A",
                        strokeWeight: 2,
                        strokeOpacity: 1,                                                            
                    })
                    map.addOverlay(polyline)
                }
            })
        
            // 这是是配置地图返回路线的参数，纯配置
            var driving_back = new BMap.DrivingRoute(map, {
                renderOptions: { map: map, autoViewport: false },
            });
            driving_back.setSearchCompleteCallback(function () {
                var plan_back = driving_back.getResults().getPlan(0)
                for (var i = 0; i < plan_back.getNumRoutes(); i++) {
                    var pts_back = plan_back.getRoute(i).getPath() // 重点在这   这个地方是关于修改颜色的
                    var polyline_back = new BMap.Polyline(pts_back, {
                        strokeColor: "#4986FC", 
                        strokeWeight: 2,
                        strokeOpacity: 1,
                    })
                    map.addOverlay(polyline_back)
                }
            })
            //右侧的侧边栏点击的车辆和地图上点击的车辆信息都会存在nowCar里，只有点击过了车辆信息才会显示它的轨迹
            let carLineShow = null
            if (!isMapClick) { // 这里判断了 是右侧点击的还是地图上点击的，两个地方取值方式不太一样
                var id = $(nowCar.target).attr('data');
                var car = _mobilesWithDistance.first(function (m) { return m.id == id; });
                carLineShow = car
            } else {
                var car = _mobilesWithDistance.first(function (m) { return m.id == nowCar.currentTarget.id; });
                carLineShow = car
            }
            if (carLineShow && carLineShow.stationLongitude && carLineShow.stationLatitude && carLineShow.destinationLng && carLineShow.destinationLag) {
                if (carLineShow.status == '2' || carLineShow.status == '5' || carLineShow.status == '6') {
                    // 创建起点和终点坐标
                    var startPoint = new BMap.Point(Number(carLineShow.stationLongitude), Number(carLineShow.stationLatitude)); // 起点坐标
                    var endPoint = new BMap.Point(Number(carLineShow.destinationLng), Number(carLineShow.destinationLag)); // 终点坐标
                    // 创建驾车路线搜索实例
                    // 执行路线搜索
                    driving.search(startPoint, endPoint);
                    // 创建返程的路线
                    setTimeout(() => {
                        var startPoint_back = new BMap.Point(Number(carLineShow.destinationLng), Number(carLineShow.destinationLag)); // 起点坐标
                        var endPoint_back = new BMap.Point(Number(carLineShow.stationLongitude), Number(carLineShow.stationLatitude)); // 终点坐标
                        // 执行路线搜索
                        driving_back.search(startPoint_back, endPoint_back);
                    },200)
                }
            }
        }
    }
    window.drawMobileInterval = setTimeout(drawMobileIntervalTimeout, 5000)
}
/** c#登录成功立即调用 */
function onLoginSuccess(token) {
    tokens = token
    _token = token;
    _pSeat = null;
    try {
        _pSeat = evalJson(gProxy.getSeatInfo()); //将座席信息存储到全局变量
    } catch (e) {
        _pSeat = evalJson(bsProxy.getSeatInfo());
    }
   
    if (_pSeat.directorPermission && _pSeat.directorPermission == '1') {
        var isTabExists = $('#second-screen-tabs').tabs('exists', '主任座席');
        if (!isTabExists) {
            setTimeout(() => {
                $('#second-screen-tabs').tabs('add', {
                    title:'主任座席',
                    content:`<iframe name="seatsList"  id="seatsListIframe" scrolling="auto" src="seatsList.html" frameborder="0" style="width:100%;height:100%;"></iframe>`,
                    iconCls: 'icon-zhuren',
                });
                $('#second-screen-tabs').tabs('select', 0);
            },1000)
        }
    }
    if (!isInitStations) {
        initStations();
        //调用并刷新事件列表的iframe
        //获取iframe
        var iframe1 = document.getElementById("eventListIframe");
        var iwindow1 = iframe1.contentWindow;
        //调用iframe的方法
        iwindow1.getDgEventListDatas();

        //获取iframe
        var iframe2 = document.getElementById("callListIframe");
        var iwindow2 = iframe2.contentWindow;
        //调用iframe的方法
        iwindow2.getDgCallListDatas();

        //获取iframe
        var iframe3 = document.getElementById("shiftRecordIframe");
        var iwindow3 = iframe3.contentWindow;
        //调用iframe的方法
        iwindow3.selectShiftSeatContentListDatas();

        //获取iframe
        var iframe4 = document.getElementById("largerEventIframe");
        var iwindow4 = iframe4.contentWindow;
        //调用iframe的方法
        iwindow4.getLargerEventList();

        //获取记事本iframe
        var iframe5 = document.getElementById("notebookIframe");
        var iwindow5 = iframe5.contentWindow;
        //调用iframe的方法
        iwindow5.getLargerEventList();


        //已经初始化过地图，不用再次初始化地图，否则地图上面某些信息会丢失
        isInitStations = true;
    }
}
function initStations() {
    getRegionStations(function (data) {
        _stations = data.selectMany(function (s) { return s.stations; });
        //设置车辆信息
        initMap();
        setAutoComplete();
        drawStations();//画站点
        setTimeout(function () {
            sortAndShowStations(_mobilesWithDistance);
        }, 1000);
        drawMobileIntervalTimeout();//开始画地图上面的车
    });
    getAllContacts(function (data) {
        _contactList = data;
        //try {
        //    gProxy.updateContacts(JSON.stringify(data));
        //} catch (e) {
        //    //b/s模式无法将联系人信息报错
        //}
        //渲染左侧医院菜单 
        renderMenu(_contactList)
        $('#search-hospital').textbox('textbox').bind('input',function(e){
            let list = JSON.parse(JSON.stringify(_contactList))
            let filterlist = list.filter(r=>r.groupName.indexOf(e.target.value) !== -1)
            if(!filterlist || !filterlist[0]){
                filterlist = list.filter(r=>upperfirst(r.groupName,{upperfirst: true}).indexOf(e.target.value) !== -1)
            }
            //根据查询过滤掉的数据渲染左侧医院菜单
            renderMenu(filterlist)
        })
        $('#search-name').textbox('textbox').bind('input',function(e){
            if(menuObj != null && Array.isArray(menuObj.phoneBooks)){
                let list = JSON.parse(JSON.stringify(menuObj.phoneBooks))
                //过滤数据
                let filterlist = list.filter(r=>r.contactor.indexOf(e.target.value) !== -1)
                if(!filterlist || !filterlist[0]){
                    //字母过滤
                    filterlist = list.filter(r=>upperfirst(r.contactor,{upperfirst: true}).indexOf(e.target.value) !== -1)
                }
                //右边列表数据渲染
                renderObj(filterlist || [])
            }
        })      
    });
    $('#map-search-box').keydown(function (e) {
        if (e.keyCode == 13) {
            manualSearch();
        }
    });
}
/** 渲染左侧医院菜单 */
function renderMenu(list){
    //删除指定子元素
    $('#cont-group').find('li').remove()
    for (var i = 0; i < list.length; i++) {
        var group = list[i];
        $('#cont-group').append(' <li data="{0}">{1}</li>'.formatStr(
            group.id,
            group.groupName
        ));
    }
    $('#cont-group li').mouseup(function (e) {
        var groupId = $(e.target).attr('data');
        menuObj = list.first(function (f) { return f.id == groupId; });
        //右边列表数据渲染
        if(menuObj != null){
            renderObj(menuObj.phoneBooks || [])
        }
    });
}
/** 右边列表数据渲染 */
function renderObj(list){
    $('#cont-list-body').empty();
    for (var j = 0; j < list.length; j++) {
        $('#cont-list-body').append('<tr><td style="width: 20%;">{0}</td>\
              <td style="width: 80%;"  align="left"> &ensp;<a href="javascript:callingPhone(\'{1}\');">{1}</a></td></tr>'
            .formatStr(
                list[j].contactor,
                list[j].number
            ));
    }
}
function sortAndShowStations(staListParam) {
    if (!staListParam) {
        return;
    } 
    var temp = [].slice.call(staListParam);
    for (var i = 0; i < temp.length; i++) {
        var data = temp[i];
        data.distance = MAX_DISTANCE;
        if (_currentLoc != null) {
            //算距离
            caculateRouteDistance(data,
                function (dist, staData) {
                    staData.distance = dist;
                });
        }
    }
    //重新排序并展示
    staLists = temp
    reRangeAndShowStations(staLists);
}

function reRangeAndShowStations(staListParam) {
    tableHeight = $('#map-station').scrollTop()
    //清理车辆的的marker，目前修改成不清理，每次刷新的时候直接判断是否存在，如果有，那么直接移动点，而不是先删除再添加
    // console.log("地图上的marker：" + map.getOverlays());
    // console.log("地图上的车辆的carMarkers：" + carMarkers);
    //for (var i = 0; i < carMarkers.length; i++) {
    //    map.removeOverlay(carMarkers[i]);
    //}
    //carMarkers = [];//清空之前存储的carMarkers

    var disStation = $('#map-station');
    disStation.empty();

    if (staListParam.length > 0 && staListParam[0].distance < 9999999) {
        staListParam.sort(function (left, right) {
            if (left.distance < 9999999) {
                //按照距离排序算法
                return left.distance - right.distance;
            }
        });
    }

    for (var j = 0; j < staListParam.length; j++) {
        var data = staListParam[j];
        var offline = data.lng == 0 && data.lat == 0;
        var color = "black";
        var statusStr = "";

        if (data.hCondition == 0) {
            if (data.status != 0) {
                color = "red";
            }
            statusStr = data.statusStr;
        } else {
            color = "red";
            switch (data.hCondition) {
                case "1":
                    statusStr = "维修";
                    break;
                case "2":
                    statusStr = "驻场";
                    break;
				case "3":
                    statusStr = "未值班";
                    break;
            }
        }

        if (data.distance < MAX_DISTANCE) {
            disStation.append('<li style="color:{3};" data="{2}" status="{4}">{0}·{5}·{6}({1})</li>'.formatStr(
                data.stationName,
                offline?'<span style="color:#888888; font-size:0.8em;">离线</span>':(data.distance / 1000).toFixed(1)+"km",
                data.id,
                color,
                data.status,
                data.carName,
                statusStr)
            );
        } else {
            disStation.append('<li style="color:{2};" data="{1}" status="{3}">{0}·{4}·{5}</li>'.formatStr(
                data.stationName,
                data.id,
                color,
                data.status,
                data.carName,
                statusStr)
            );
        }
        drawMobile(data);
        $('#map-station').scrollTop(tableHeight)
    }
    //绑定事件
    $('#map-station li').click(function (e) {
        isMapClick = false // 右侧点击的而非地图点击的车辆信息
        // 点击车辆时存一下
        nowCar = e;
        if (newEventStatus == "1") {
            //不写轨迹。如果正在派车，那就什么都不做。点击救护车没有任何反应即可
            if (driving != null) {
                driving.clearResults();//清理轨迹
            }
            if (sendCarMarker == null) return;
            //非派车状态，那么找到地图上面的车，并将地图中心点放到车辆上。
            edcui_selectLi(e);
            //自动重定位地图
            var id = $(e.target).attr('data');
            //计算需要多长时间抵达，路线
            var startMarker = null;
            //寻找marker，通过ID
            for (var i = 0; i < carMarkers.length; i++) {
                if (carMarkers[i].id == id) {//找到地图上的车，然后显示label
                    startMarker = carMarkers[i];
                    break;
                }
            }
            var start = startMarker;
            var end = sendCarMarker;
            driving.search(start, end);    //显示一条公交线路
        } else {
            //非派车状态，那么找到地图上面的车，并将地图中心点放到车辆上。
            edcui_selectLi(e);
            //自动重定位地图
            var id = $(e.target).attr('data');
            _selCarId = id;
            carLastSelectMarker = id;//存储最后选中的车辆id
            var car = _mobilesWithDistance.first(function (m) { return m.id == id; });

            //寻找marker，通过ID
            for (var i = 0; i < carMarkers.length; i++) {
                if (carMarkers[i].id == id) {//找到地图上的车，然后显示label
                    //console.log("寻找marker，通过ID:" + carMarkers[i].id);
                    carMarkers[i].setTop(true);//设置显示在最上面，防止多图层显示在相同位置时 无法知道时哪个图层
                    if (carMarkers[i].getLabel()) {
                        carMarkers[i].getLabel().show();
                    }
                    sendCarMarker = carMarkers[i]
                    carMarkers[i].openInfoWindow(carMarkers[i].infoWindow);//打开infoWindow
                    infoWindow = carMarkers[i].infoWindow
                    break;
                }
            }
            //map.panTo(new BMap.Point(car.lng, car.lat));  // 初始化地图,设置中心点坐标和地图级别
            //跳转到那个中心点
            map.setCenter(new BMap.Point(car.lng, car.lat));
        }
    });

    $('#map-station li').dblclick(function (e) {
        edcui_selectLi(e);
        if ($(e.target).attr('status') == "0") {
            var id = $(e.target).attr('data');
            $.messager.confirm('',
                '要将此车派给当前事件吗？',
                function (res) {
                    if (res) {
                        try {
                            gProxy.autoSelectStation(id);
                        } catch (e) {
                            window.opener.autoSelectStation(id);
                        }
                  }
                });
        }
    });

}

function G(id) {
    return document.getElementById(id);
}

function initMap() {
    //百度地图
    map = new BMap.Map("the-map", {
        enableMapClick: false,
        minZoom: 10,
        maxZoom: 18
    });    // 创建Map实例,关闭底图可点功能
    var top_left_control = new BMap.ScaleControl({ anchor: BMAP_ANCHOR_TOP_LEFT });// 左上角，添加比例尺
    var top_left_navigation = new BMap.NavigationControl();  //左上角，添加默认缩放平移控件
    //添加地图类型控件
    map.addControl(new BMap.MapTypeControl({
        mapTypes: [BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP]
    }));
    map.addControl(top_left_control);
    map.addControl(top_left_navigation);
    querySysConf('map_city',
        function (data) {
            map.setCurrentCity(data); // 设置地图显示的城市 此项是必须设置的
            map.centerAndZoom(data);  // 初始化地图,设置中心点坐标和地图级别
            map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
            //行政区域边界绘制
            var bdary = new BMap.Boundary();
            bdary.get(data, function (rs) {       //获取行政区域
                //这里是用户自己的函数。 
                var count = rs.boundaries.length; //行政区域的点有多少个
                for (var i = 0; i < count; i++) {
                    var ply = new BMap.Polygon(rs.boundaries[i], { strokeWeight: 2, strokeColor: "#ff0000", fillOpacity: 0.01 }); //建立多边形覆盖物
                    map.addOverlay(ply);  //添加覆盖物
                }  
            }); 
            setTimeout(function () {
                map.setZoom(11);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
            }, 1500);  //2秒后放大
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
        });
}
var ac = null;//自动完成对象
function setAutoComplete() {
    ac = new BMap.Autocomplete(    //建立一个自动完成的对象
        {
            "input": "map-search-box",
            "location": map
        });

    var myValue;
    ac.addEventListener("onhighlight", function (e) {  //鼠标放在下拉列表上的事件
        //console.log("鼠标放在下拉列表上的事件");
        var str = "";
        var _value = e.fromitem.value;
        var value = "";
        if (e.fromitem.index > -1) {
            value = _value.province + _value.city + _value.district + _value.street + _value.business;
        }
        str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

        value = "";
        if (e.toitem.index > -1) {
            _value = e.toitem.value;
            value = _value.province + _value.city + _value.district + _value.street + _value.business;
        }
        str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
        G("searchResultPanel").innerHTML = str;
    });

    ac.addEventListener("onconfirm", function (e) {    //鼠标点击下拉列表后的事件
        //console.log("鼠标点击地址的下拉事件...");
        var _value = e.item.value;
        myValue = _value.province + _value.city + _value.district + _value.street + _value.business;
        G("searchResultPanel").innerHTML = "onconfirm<br />index = " + e.item.index + "<br />myValue = " + myValue;

        setPlace(myValue);
    });

    //如果删除了搜索文本中的内容，那么取消掉点
    $('#map-search-box').keyup(function (e) {
        //console.log("键盘按下点击地址的下拉事件...");
        var _value = $("#map-search-box").val();
        if (_value == "") {
            //移除掉派车的点
            if (sendCarMarker != null) {
                map.removeOverlay(sendCarMarker);
                sendCarMarker = null;
            }

        }
    });
}
function setTextMarker(){
    sortAndShowStations(_mobilesWithDistance);
    //自动选中
    if (_selCarId != null) {
        $('#map-station li[data=' + _selCarId + ']').mouseup();
    } else {
        if (_currentLoc != null) {
            map.setCenter(_currentLoc);
        }
    }
    if (_currentLoc != null) {
        if (driving != null) {
            driving.clearResults();//清理轨迹
        }
        //添加派车地点
        if (sendCarMarker != null) {

        } else { 
            //添加派车地点
            sendCarMarker = new BMap.Marker(_currentLoc);
            //sendCarMarker.setAnimation(BMAP_ANIMATION_BOUNCE);
            //绘制图标
            map.addOverlay(sendCarMarker); //添加标注
            setTimeout(function () {
                map.setZoom(16);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
            }, 1000);  //2秒后放大
        }
        var opts = {
            width: 250,     //信息窗口宽度  
            height: 100,     //信息窗口高度  
            title: "事件"  //信息窗口标题  
        }
        var text = "位置：" + $("#map-search-box").val() + "<br/>" +
            "纬度: " + _currentLoc.lat + ", " + "经度：" + _currentLoc.lng;

        var infoWindow = new BMap.InfoWindow(text, opts);  //创建信息窗口对象  
        sendCarMarker.openInfoWindow(infoWindow);

        setTimeout(function () {
            map.setZoom(17);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
        }, 500);  //2秒后放大
        //移动派车地点
        sendCarMarker.setPosition(_currentLoc);
        //跳转到那个中心点
        map.setCenter(_currentLoc);
        //如果创建窗口打开才发送给主屏幕
        try {
            if (!gProxy.getIsCreateNewEventWinIsOpen()) {
                gProxy.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
            }
        } catch (e) {
            //调用主窗口的函数，判断是否打开了新建页面
            if (!window.opener.isCreateNewEventWinIsOpen()) {
                window.opener.echoMapSelected($("#map-search-box").val(), _currentLoc.lng, _currentLoc.lat);
            }
            
        }

        //*******************************************可托拽的标注
        //marker的enableDragging和disableDragging方法可用来开启和关闭标注的拖拽功能。
        sendCarMarker.enableDragging();
        //监听标注的dragend事件来捕获拖拽后标注的最新位置
        sendCarMarker.addEventListener("dragend", function (e) {
            if (sendCarMarker != null) {
                var gc = new BMap.Geocoder();//地址解析类 
                gc.getLocation(e.point, function (rs) {
                    _currentLoc = e.point;
                    showLocationInfo(e.point, rs);
                });
            }
        });
        
    }
}
function setPlace(value) {
    //map.clearOverlays();    //清除地图上所有覆盖物
    function myFun() {
        var ppr = local.getResults().getPoi(0);    //获取第一个智能搜索的结果
        if (ppr == undefined) {
            _currentLoc = null;
        } else {
            var pp = ppr.point;
            _currentLoc = pp;
        }
        setTextMarker()
    }

    var local = new BMap.LocalSearch(map, { //智能搜索
        onSearchComplete: myFun
    });
    local.search(value);
}

/**
 * 通过事件ID，显示事件在地图上面的位置
 * @param {any} eventId
 */
function setPlaceBylonAndLat(eventId) {
    
    getEventById(eventId,
        function (res) {
            //成功不做任何处理
            if (driving != null) {
                driving.clearResults();//清理轨迹
            }
            if (!(res.lat != null && res.lng != null)) {
                $.messager.alert('提示', '事件无经纬度，无法在地图上显示。');
                if (sendCarMarker != null) {
                    map.removeOverlay(sendCarMarker);
                    sendCarMarker = null;
                }
                return null;
            }
            _currentLoc = new BMap.Point( parseFloat(res.lng),parseFloat(res.lat));
            //添加派车地点
            if (sendCarMarker != null) {

            } else {
                //添加派车地点
                sendCarMarker = new BMap.Marker(_currentLoc);
                //sendCarMarker.setAnimation(BMAP_ANIMATION_BOUNCE);
                //绘制图标
                map.addOverlay(sendCarMarker); //添加标注
                //setTimeout(function () {
                //    map.setZoom(17);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
                //}, 500);  //2秒后放大
            }

            var opts = {
                width: 250,     //信息窗口宽度  
                height: 100,     //信息窗口高度  
                title: "事件"  //信息窗口标题  
            }

            var text = "事件: " + res.majorCall.replace("_", " ") + "<br/>" +
                "位置：" + res.address + "<br/>" +
                "纬度: " + res.lat + ", " + "经度：" + res.lng;

            var infoWindow = new BMap.InfoWindow(text, opts);  //创建信息窗口对象  
            sendCarMarker.openInfoWindow(infoWindow);

            //setTimeout(function () {
            //    map.setZoom(16);    // setZoom 方法，负责设置级别，只有停留几秒，才能看到效果
            //}, 2000);  //2秒后放大
            //移动派车地点
            sendCarMarker.setPosition(_currentLoc);
            //跳转到那个中心点
            map.setCenter(_currentLoc);
            //计算距离
            sortAndShowStations(_mobilesWithDistance);
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获取事件id错误，无法定位：' + errMsg);
        });
}

//*****************************信息窗口
/** 显示地址信息窗口 */
function showLocationInfo(pt, rs) {
    if (driving != null) {
        driving.clearResults();//清理轨迹
    }
    sortAndShowStations(_mobilesWithDistance);
    var opts = {
        width: 250,     //信息窗口宽度  
        height: 100,     //信息窗口高度  
        title: "当前位置"  //信息窗口标题  
    }
    var addComp = rs.addressComponents;
    var text = "当前位置：" + addComp.province + ", " + addComp.city + ", " + addComp.district + ", " + addComp.street + ", " + addComp.streetNumber + "<br/>" +
        "纬度: " + pt.lat + ", " + "经度：" + pt.lng;

    var infoWindow = new BMap.InfoWindow(text, opts);  //创建信息窗口对象  
    sendCarMarker.openInfoWindow(infoWindow);
    ac.setInputValue(addComp.district + addComp.street + addComp.streetNumber);
    //console.log("副屏幕车辆信息：" + JSON.stringify(_mobilesWithDistance));
    try {
        gProxy.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    } catch (e) {
        window.opener.echoMapSelected(addComp.district + addComp.street + addComp.streetNumber, pt.lng, pt.lat);
    }
   
} 

/** 将分站图标放到地图上面去 */
function drawStations() {
    //绘制所有分站图标
    //console.info('绘制所有分站图标');
    for (var i = 0; i < _stations.length; i++) {
        var sta = _stations[i];
        var pt = new BMap.Point(sta.stationLongitude, sta.stationLatitude);
        var myIcon = new BMap.Icon("style/img/hospital.png", new BMap.Size(24, 24));
        var marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
        map.addOverlay(marker);// 将标注添加到地图中
        var label = new BMap.Label('<span style="color:green; font-size:1.2em;">{0}</span>'.formatStr(sta.stationName), { offset: new BMap.Size(0, 24) });
        label.setStyle({
            color: "#008000",
            border: "none",
            fontSize: "12px",
            height: "20px",
            lineHeight: "20px",
            fontFamily: "微软雅黑",
            background: "rgba(0,0,0,0)"
        });
        marker.setLabel(label);
    }
}

/** 将事故地点图标放到地图上面去 */
function drawEvent() {
    //绘制所有事件的图标
    
}

/** 画车辆 */
function drawMobile(car) {
    if (car.lng == 0 && car.lat == 0) return;//没有正确gps数据的车不绘制，后端会直接去医院的坐标绘制

    var oldMarker = null;
    //寻找marker，通过ID
    for (var i = 0; i < carMarkers.length; i++) {
        if (carMarkers[i].id == car.id) {//找到地图上的车，然后显示label
            oldMarker = carMarkers[i];
            break;
        }
    }

    var marker = null;
    if (oldMarker != null) {
        //直接使用旧的marker
        marker = oldMarker;
        var myIcon = null;
        if (car.hCondition == "0") {
            if (car.status == "0") {
                //未出车超过1公里，那么车辆变成黄色
                var distance = getDistance(car.lat, car.lng, car.stationLatitude, car.stationLongitude);
                // console.log("离医院距离：" + distance);
                if (distance > 1000) {
                    myIcon = new BMap.Icon("style/img/ambul_yellow.png", new BMap.Size(48, 48));
                } else {
                    //待命图标
                    myIcon = new BMap.Icon("style/img/ambul.png", new BMap.Size(48, 48));
                }
            } else {
                //非待命图标
                myIcon = new BMap.Icon("style/img/ambul_red.png", new BMap.Size(48, 48));

            }
        } else {//车辆非正常状态
            myIcon = new BMap.Icon("style/img/ambul_blue.png", new BMap.Size(48, 48));
        }
      //  map.centerAndZoom(new BMapGL.Point(car.lng, car.lat), 11);
      //   var p1 = new BMapGL.Point(car.lat, car.lat);
      //   var p2 = new BMapGL.Point(oldMarker.point.lat,oldMarker.point.lng);

      //   var driving = new BMapGL.DrivingRoute(map, {renderOptions:{map: map, autoViewport: true}});
      //   driving.search(p1, p2);
      // 状态2 已抵达
        var pt = new BMap.Point(car.lng, car.lat);
        map.removeOverlay(marker.getIcon()); //删除之前的Icon
        map.removeOverlay(marker.getPosition()); //删除之前的Position
        marker.setIcon(myIcon);
        marker.setPosition(pt);
        //跳转到那个中心点
        //map.setCenter(pt);
    } else { 
        //绘制车辆图标
        var pt = new BMap.Point(car.lng, car.lat);
        if (car.hCondition == "0") {
            if (car.status == "0") {
                //未出车超过1公里，那么车辆变成黄色
                var distance = getDistance(car.lat, car.lng, car.stationLatitude, car.stationLongitude);
                if (distance > 1000) {
                    myIcon = new BMap.Icon("style/img/ambul_yellow.png", new BMap.Size(48, 48));
                } else {
                    //待命图标
                    myIcon = new BMap.Icon("style/img/ambul.png", new BMap.Size(48, 48));
                }
            } else {
                //非待命图标
                myIcon = new BMap.Icon("style/img/ambul_red.png", new BMap.Size(48, 48));
            }
        } else {//车辆非正常状态
            myIcon = new BMap.Icon("style/img/ambul_blue.png", new BMap.Size(48, 48));
        }
        //创建新的marker
        marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
        carMarkers.push(marker); //将car的marker放到数组中
        map.addOverlay(marker);// 将标注添加到地图中，需要先删除掉之前绘制的车辆图标。不然车辆图标会一致画，导致卡死
    }
    marker.id = car.id;//markers的id；就是救护车的id
    marker.setRotation(car.direct);//角度

    //var label = new BMap.Label('<div onclick="autoSelectCar(\'{2}\')" style="color:red; font-size:1em; cursor:pointer;">{1}<br/>{0}</div>'.formatStr(car.carName, car.stationName, car.id), { offset: new BMap.Size(0, 40) });
    //map.removeOverlay(marker.getLabel()); //删除之前的label
   // marker.setLabel(label);
    ////添加windowInfo 
    ////车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-接诊完成、5-调度接收、6、已派车
    var carStatus = "";
    if (car.hCondition != "0") {
        switch (car.hCondition) {
            case "1":
                carStatus = "维修";
                break;
            case "2":
                carStatus = "驻场";
				break;
            case "3":
                carStatus = "未值班";
				break;
        }
    } else {
        carStatus = car.statusStr;
    }
    var fenZhan = `
        <font size='2' color='#0066FF'>分站：</font>
        <font size='2'>${car.stationName}</font>
        <hr style='color:#0066FF'>
    `
    var chePai = `
        <font size='2' color='#0066FF'>车牌：</font>
        <font size='2'>${car.plateNum}(${car.carName})</font>
        <hr style='color:#0066FF'>
    `
    var suDu = `
        <font size='2' color='#0066FF'>速度：</font>
        ${(Math.round(car.speed * 100) / 100)} 
        <font size='2'>km/h</font>
        <hr style='color:#0066FF'>
    `
    var status = `
        <font size='2' color='#0066FF'>状态：</font>
        <font size='2'>${carStatus}</font>
        <hr style='color:#0066FF'>
        <font size='2'>${car.acc == '0'?'熄火':car.acc == '1'?'点火':''}</font>
    `
    var text = ""
    if (fenzhanChecked) {
        text+=fenZhan
    }
    if (chepaiChecked) {
        text+=chePai
    }
    if (suduChecked) {
        text+=suDu
    }
    if (statusChecked) {
        text+=status
    }
    infoWindow = new BMap.InfoWindow(text,{enableCloseOnclick:true});
    marker.infoWindow = infoWindow;

    marker.addEventListener("click", function (e) {
        isMapClick = true // 地图点击的车辆信息
        nowCar = e
        sendCarMarker = this
        this.openInfoWindow(e.target.infoWindow)
    })
   
}

function autoSelectCar(carId) {
    $('#map-station [data="{0}"]'.formatStr(carId)).mouseup();
}

function caculateRouteDistance(carData, callback) {
    var distance = MAX_DISTANCE;
    if (_currentLoc != null) {
        // console.log("经纬度计算距离：" + carData.id +";"+ carData.lat + ";" + carData.lng + ";" + _currentLoc.lat + ";" + _currentLoc.lng);
        distance = getDistance(carData.lat, carData.lng, _currentLoc.lat, _currentLoc.lng);
    }
    callback(distance, carData);
}

function manualSearch() {
    setPlace($('#map-search-box').val());
    _selCarId = null;
}

/**
 * 设置地图地址（有经纬度时使用经纬度，没坐标时使用地址）
 * @param {any} address 地址
 * @param {any} longitude 经度，有可能为空
 * @param {any} latitude 纬度，有可能为空
 */
function setAddress(address, longitude, latitude) {
    ac.setInputValue(address);
    if (longitude && latitude) {
        _currentLoc = new BMap.Point(parseFloat(longitude),parseFloat(latitude));
        setTextMarker()
    } else {
        setPlace(address);
    }   
    _selCarId = null;
}

function syncMobiles(data) {
    //debugger;
    _mobilesWithDistance =JSON.parse(data);
}
//设置当前是否派车状态，派车状态
function sendToNewEventStatus(status) {
    newEventStatus = status;
    if (driving != null) {
        driving.clearResults();//清理轨迹
    }
    //移除掉派车的点
    if (sendCarMarker != null) {
        map.removeOverlay(sendCarMarker);
        sendCarMarker = null;
    }
}

//挂起调度
function hangUpBtn() {
    $('#hangUpDialog').window('open');
}

//解除挂起调度
function unHangUpBtn() {
    $('#hangUpDialog').window('close', true);
}

/*******************************主副屏通讯开始*********************************************************/
//Bs主屏幕与副屏幕通信，通过storage来监听
window.addEventListener('storage', event => {
    //解除挂起副屏幕
    if (event.key === 'unHangUpSecondScreen') {
       
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            unHangUpBtn();
        }
    }

    //挂起副屏幕
    if (event.key === 'hangUpSecondScreen') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            hangUpBtn();
        }
    }

    //登录成功后调用
    if (event.key === 'loginSuccess') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            onLoginSuccess(eventData.token);
        }
    }

    //同步车辆数据
    if (event.key === 'syncMobiles') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            syncMobiles(eventData.data);
        }
    }

    //通过经纬度设置选中车辆在地图上的定位
    if (event.key === 'setPlaceBylonAndLat') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            setPlaceBylonAndLat(eventData.data);
        }
    }

    //发送地址到副屏的输入框中
    if (event.key === 'sendToMap') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            setAddress(eventData.address, eventData.longitude, eventData.latitude);
        }
    }

    //设置副屏状态是否为派车状态
    if (event.key === 'sendToNewEventStatus') {
        let eventData = evalJson(event.newValue);
        if (eventData != null) {
            sendToNewEventStatus(eventData.status);
        }
    }
    
})
/*******************************主副屏通讯结束*********************************************************/
