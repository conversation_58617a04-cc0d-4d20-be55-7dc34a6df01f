/* *****************************************************************
 * Ajax行为集中脚本，所有Ajax请求函数都放在这个脚本下
 * 兼容性：该脚本将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器），理论上跨域限制已经默认解除
 * -----------------------------------------------------------------
 * Creator: 陈蒋耀
 * Created: 2019/5/27
 * -----------------------------------------------------------------
 * Modification:
 * Modifier:
 * Modified:
 * *****************************************************************/

var _baseUrl = null;//EDC接口服务器基地址
var _token = "";//加密后的Token
var _mobiles;//所有车辆信息
var _mobilesWithDistance;//所有车辆信息（带距离）

var _constantErrorTimes = 0;//服务器连续出错次数

var MAX_DISTANCE = 99999999;//距离初始值

document.body.onselectstart = function () {
    return false;
}

/**
 * EDC平台通用Ajax请求调用函数，自动填充Token
 * @param {string} url - 请求地址.(只需要输入相对地址，不需要/开头，基地址自动读取配置文件)
 * @param {object} data - 请求内容.
 * @param {function} callback - 请求完成后的回调函数.
 */
var gProxyW = null;
var proxyNum = 0;
function ProxyObj(parent){
    if(parent.gProxy){
        return parent.gProxy
    }
    if(proxyNum<5){
       proxyNum++
       return ProxyObj(parent.parent)
    }
    return {}
}

var bsProxyW = null;
var bsProxyNum = 0;
function BsProxyObj(parent) {
    if (parent.bsProxy) {
        return parent.bsProxy
    }
    if (bsProxyNum < 5) {
        bsProxyNum++
        return BsProxyObj(parent.parent)
    }
    return {}
}

$.ajaxSetup({
    beforeSend: function(jqXHR, settings) {
      var index = settings.url.indexOf("&_=");
      if (index != -1) {
        settings.url = settings.url.substring(0, index);
      }
    },
});

function edcGet(url, data, callback, errorCallback,config) {
    //_baseUrl要以/结尾，url不能以/开头
    if (url.startsWith('/')) {
        url = url.substring(1);
    }
    url = _baseUrl + (_baseUrl.endWith('/') ? '' : '/') + url;

    let sign = encryptByDES(md5(JSON.stringify(data)));
    //修改获取token直接网页登录获取，不从C#中读取
    if (!_token) {
        try {
            _token = gProxyW.getUserToken();
        } catch (e) {
            _token = window.localStorage.getItem("_token");
            console.log("B/S模式执行,不需要执行gProxyW.getUserToken()");
        }
    }
    
    let headers ={}
    headers = {
        "Content-Type": "application/json",
        "token": _token,
        "sign": sign,
    }
    $.ajax({
        url,
        type: 'get',
        dataType: 'json',
        data,
        timeout: 20000,
        cache: false,
        headers:headers,
        success: function (res) {
            _constantErrorTimes = 0;//归零错误次数
            //Token过期，错误码5026，直接关闭程序
            if (res.code == '5026') {
                try {
                   gProxyW.shutdown();
                } catch (e) {

                }
            } else {
                callback(res);
            }
        },
        error: function (e) {
            //_constantErrorTimes++;//增加错误次数

            if (errorCallback != "undefined" && errorCallback != null) {
                switch (e.status) {
                    case 404:
                        e.responseText = "404,错误，请求无效";
                        break;
                    case 504:
                        e.responseText = "504,错误，请求超时TimeOut";
                        break;
                    case 502:
                        e.responseText = "502,错误，网络异常";
                        break;
                }
                if (e.responseText != null) {
                    if (e.responseText.indexOf("5026") > -1) {
                        e.responseText = "您的用户未登陆或在其他地方登录！请求数据data=" + data + ";header_token=" + _token + ";header_sign=" + sign;
                    }
                }

                errorCallback(e, url, data);
            }
        },
        complete: function (XHR, TS) {
            XHR = null
        }
    });
}

/**
 * 异步post json
 * @param {any} url
 * @param {any} data
 * @param {any} callback
 * @param {any} errorCallback
 * @param {any} config
 */
function edcRequest(url, data, callback, errorCallback,config) {
    if(!gProxyW){
        gProxyW = ProxyObj(window);
    }
    if (!bsProxyW) {
        bsProxyW = BsProxyObj(window);
    }
    data.accessTime = (new Date()).formatString('yyyy-MM-dd hh:mm:ss');
    //_baseUrl要以/结尾，url不能以/开头
    if (url.startsWith('/')) {
        url = url.substring(1);
    }
    url = _baseUrl + (_baseUrl.endWith('/') ? '' : '/') + url;
    data = JSON.stringify(data);

    //console.log("C/S MD5加密中文：" + gProxyW.md5("中国话，你好吗？"));
    //console.log("B/S MD5加密中文1：" + hex_md5("中国话，你好吗？"));
    //console.log("B/S MD5加密中文2：" + md5("中国话，你好吗？"));

    //console.log("C/S MD5加密中文：" + gProxyW.md5("aaaaaabbbbbb"));
    //console.log("B/S MD5加密中文1：" + hex_md5("aaaaaabbbbbb"));
    //console.log("B/S MD5加密中文2：" + md5("aaaaaabbbbbb"));

    //console.log("C/S DES加密中文：" + gProxyW.desEncrypt(gProxyW.md5("中国话，你好吗？")));
    //console.log("B/S DES加密中文：" + encryptByDES(md5("中国话，你好吗？")));
    //将加密放到js代码中，取消C/S中的加密计算
    //let sign = gProxyW.desEncrypt(gProxyW.md5(data));
    let sign = encryptByDES(md5(data));

    //修改获取token直接网页登录获取，不从C#中读取
    if (!_token) {
        try {
            _token = gProxyW.getUserToken();
        } catch (e) {
            try {            
                _token = bsProxyW.getUserToken();
                console.log("B/S模式执行,不需要执行gProxyW.getUserToken()");
            } catch (e) {
                _token = window.localStorage.getItem("_token");
            }
        }
    }
    
    let headers ={}
    headers = {
        "Content-Type": "application/json",
        "token": _token,
        "sign": sign,
    }
    $.ajax({
        url,
        type: 'post',
        dataType: 'json',
        data,
        timeout: 10000,
        cache: false,
        headers:headers,
        success: function (res) {
            _constantErrorTimes = 0;//归零错误次数
            //Token过期，错误码5026，直接关闭程序
            if (res.code == '5026') {
                try {
                    gProxyW.shutdown();
                } catch (e) {}
            } else {
                callback(res);
            }
        },
        error: function (e) {
            //_constantErrorTimes++;//增加错误次数

            if (errorCallback != "undefined" && errorCallback != null) {
                switch (e.status) {
                    case 404:
                        e.responseText = "404,错误，请求无效";
                        break;
                    case 504:
                        e.responseText = "504,错误，请求超时TimeOut";
                        break;
                    case 502:
                        e.responseText = "502,错误，网络异常";
                        break;
                }
                if (e.responseText != null) {
                    if (e.responseText.indexOf("5026") > -1) {
                        e.responseText = "您的用户未登陆或在其他地方登录！请求数据data=" + data + ";header_token=" + _token + ";header_sign=" + sign;
                    }
                }

                errorCallback(e, url, data);
            }
        },
        complete: function (XHR, TS) {
            XHR = null
        }
    });
}

/**
 * 同步post json
 * @param {any} url
 * @param {any} data
 * @param {any} callback
 * @param {any} errorCallback
 * @param {any} config
 */
function edcRequestSync(url, data, callback, errorCallback, config) {
    if (!gProxyW) {
        gProxyW = ProxyObj(window);
    }
    if (!bsProxyW) {
        bsProxyW = BsProxyObj(window);
    }
    data.accessTime = (new Date()).formatString('yyyy-MM-dd hh:mm:ss');
    //_baseUrl要以/结尾，url不能以/开头
    if (url.startsWith('/')) {
        url = url.substring(1);
    }
    url = _baseUrl + (_baseUrl.endWith('/') ? '' : '/') + url;
    data = JSON.stringify(data);

    //console.log("C/S MD5加密中文：" + gProxyW.md5("中国话，你好吗？"));
    //console.log("B/S MD5加密中文1：" + hex_md5("中国话，你好吗？"));
    //console.log("B/S MD5加密中文2：" + md5("中国话，你好吗？"));

    //console.log("C/S MD5加密中文：" + gProxyW.md5("aaaaaabbbbbb"));
    //console.log("B/S MD5加密中文1：" + hex_md5("aaaaaabbbbbb"));
    //console.log("B/S MD5加密中文2：" + md5("aaaaaabbbbbb"));

    //console.log("C/S DES加密中文：" + gProxyW.desEncrypt(gProxyW.md5("中国话，你好吗？")));
    //console.log("B/S DES加密中文：" + encryptByDES(md5("中国话，你好吗？")));
    //将加密放到js代码中，取消C/S中的加密计算
    //let sign = gProxyW.desEncrypt(gProxyW.md5(data));
    let sign = encryptByDES(md5(data));

    //修改获取token直接网页登录获取，不从C#中读取
    if (!_token) {
        try {
            _token = gProxyW.getUserToken();
        } catch (e) {
            _token = bsProxyW.getUserToken();
            console.log("B/S模式执行,不需要执行gProxyW.getUserToken()");
        }
    }

    let headers = {}
    headers = {
        "Content-Type": "application/json",
        "token": _token,
        "sign": sign,
    }
    $.ajax({
        url,
        type: 'post',
        dataType: 'json',
        data,
        timeout: 10000,
        cache: false,
        headers: headers,
        async: false,
        success: function (res) {
            _constantErrorTimes = 0;//归零错误次数
            //Token过期，错误码5026，直接关闭程序
            if (res.code == '5026') {
                try {
                    gProxyW.shutdown();
                } catch (e) { }
            } else {
                callback(res);
            }
        },
        error: function (e) {
            //_constantErrorTimes++;//增加错误次数

            if (errorCallback != "undefined" && errorCallback != null) {
                switch (e.status) {
                    case 404:
                        e.responseText = "404,错误，请求无效";
                        break;
                    case 504:
                        e.responseText = "504,错误，请求超时TimeOut";
                        break;
                    case 502:
                        e.responseText = "502,错误，网络异常";
                        break;
                }
                if (e.responseText != null) {
                    if (e.responseText.indexOf("5026") > -1) {
                        e.responseText = "您的用户未登陆或在其他地方登录！请求数据data=" + data + ";header_token=" + _token + ";header_sign=" + sign;
                    }
                }

                errorCallback(e, url, data);
            }
        },
        complete: function (XHR, TS) {
            XHR = null
        }
    });
}

/** 模拟ajax请求 */
/*function simulateRequest(fakeRes, callback) {
    setTimeout(function () {
        callback(fakeRes);
    }, 500);
}*/

/** 用户登录 */
function loginIn(params, callback, errorCallback) {
    bsProxy.playAlert(false);
    edcRequest('edc_svrside_service/api/login/login',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 用户注销（传入token） */
function logout(token, callback, errorCallback) {
    bsProxy.playAlert(false);
    edcRequest('edc_svrside_service/api/login/login_out',
        {"token":token},
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}


/** 获取座席信息 */
function selectSeatInfo(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/selectSeatInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}



/** 获取座席配置信息通过座席ID 用的微服务的接口, 微服务的接口返回的是data数据不是content，这个要注意*/
function GetSeatConfigInfoById(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/pseat/getSeatById',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

//通过座席ID获得座席配置信息 */
function getPhoneList(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/phone_book_group/getConfigPhoneBookList',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 判断是否发送过 指导视频短信 */
function countSendH5Message(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/external/countSendH5Message',params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
              errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if(errorCallback){
                errorCallback(e, url, e.responseText);
            }
        });
}
/** 发送指导视频短信 */
function sendH5Message(params, callback, errorCallback) {
 
    edcRequest('edc_svrside_service/api/external/sendH5Message',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if(errorCallback){
                errorCallback(e, url, e.responseText);
            }
        });
}
/**
 * 从dicts中获取指定类型typeCode的字典子列表（不会调用后台接口）
 * @param {any} typeCode 类型
 * @param {any} dicts 字典列表
 */
function getDicList(typeCode, dicts) {
    console.log("getDicList被调用:", typeCode, "字典总数:", dicts ? dicts.length : 0);
    
    if (!dicts || dicts.length === 0) {
        console.warn("字典数据为空, 无法获取字典:", typeCode);
        return [];
    }
    
    var list = dicts.where(function (d) { return d.typeCode == typeCode; });
    console.log("typeCode为" + typeCode + "的字典数:", list.length);
    
    if (typeCode === 'push_outer_type') {
        console.log("接收中心字典详情:");
        if (list && list.length > 0) {
            for (var i = 0; i < list.length; i++) {
                console.log("字典项" + i + ":", list[i]);
                if (list[i].val) {
                    console.log("字典值数量:", list[i].val.length);
                    console.log("字典第一项:", list[i].val[0]);
                }
            }
        } else {
            console.warn("未找到接收中心字典数据!");
        }
    }
    
    return list;
}
/**
 * 从dicts中获取指定类型typeCode的某个key字典的值（不会调用后台接口）
 * @param {any} typeCode 类型
 * @param {any} key 具体字典的编码
 * @param {any} dicts 字典列表
 */
function getDicValue(typeCode, key, dicts) {
    var list = getDicList(typeCode, dicts);
    var ret = list.first(function (d) { return d.codeName == key; });
    if (ret && ret.codeVale) {
        return ret.codeVale;
    }
    return '';
}
/**
 * 获取指定类型的字典，一般会配合 getDicList 和 getDicValue 方法一起使用
 * @param {any} typeIds 字典类型数组
 * @param {any} callback
 * @param {any} errorCallback
 */
function queryAllDic(typeIds, callback, errorCallback) {
    console.log("调用字典接口, 请求字典类型:", typeIds);
    edcRequest('edc_svrside_service/api/dictionary/dictionary_vale',
        { "type": typeIds },
        function (res) {
            console.log("字典接口返回结果:", res);
            if (res.success) {
                console.log("字典数据获取成功, 数据长度:", res.content.length);
                
                // 检查push_outer_type字典数据
                if (typeIds.includes("push_outer_type")) {
                    const pushOuterTypeDict = res.content.find(dict => dict.typeCode === "push_outer_type");
                    if (pushOuterTypeDict) {
                        console.log("push_outer_type字典数据:", pushOuterTypeDict);
                    } else {
                        console.warn("未找到push_outer_type字典数据!");
                    }
                }
                
                callback(res.content);
            } else {
                console.error("字典接口调用失败:", res.msg);
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            console.error("字典接口调用异常:", errMsg);
            if(errorCallback){
                errorCallback(e, url, e.responseText);
            }
        });
}

/**
 * 根据任务ID查询任务
 * @param {any} processId 任务ID
 * @param {any} callback
 * @param {any} errorCallback
 */
function getMobileProcessById(processId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMobileProcessById',
        { "processId": processId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/**
 * 通过id查询车辆信息当前车辆任务数
 * @param {Object} data 查询参数，需包含id
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getMobileVoById(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobile/getMobileVoById',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}


function getMobileProcessChangeMobileList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMobileProcessChangeMobileList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

function getMobileOperationRecordList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMobileOperationRecordList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获得用户 */
function getAllSeatUser(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/userInfo/seatUser',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取交接班记录 */
function getActiveShiftSeat(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/getActiveShiftSeat',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 添加交接班记录 */
function insertShiftRecord(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/insertShiftRecord',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席台交接班：当前用户交班给接班人 */
function updateShiftSeatTurnover(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/updateShiftSeatTurnover',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

function updateForceTaskSync(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateForceTaskSync',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 异步获取系统配置
 * @param {any} id
 * @param {any} callback
 * @param {any} errorCallback
 */
function querySysConf(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/sys_conf/sys_conf_info',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 同步获取系统配置
 * @param {any} id
 * @param {any} callback
 * @param {any} errorCallback
 */
function querySysConfSync(id, callback, errorCallback) {
    edcRequestSync('edc_svrside_service/api/sys_conf/sys_conf_info',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) {
                    errorCallback(null, null, res.msg);
                }
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 获取诊断字典 */
function getPatientDiagnosis(callback, errorCallback) {
    edcRequest('edc_svrside_service/api/sys_conf/list',
        { "confName": "诊断选项" },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/*function getEmergencyPlatformToken(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/external/getToken ',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/

/** 获取急救的token */
/*function getEmergencyPlatformTokenDefault(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/external/getTokenDefault',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/

/**
 * 根据id获取急救平台地址
 * @param {*} id 系统配置属性id
 * @param {*} callback 
 */
function getFirstaidPlatformUrl(id, callback) {
    edcRequest('edc_svrside_service/api/external/getFirstaidPlatformUrl',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}

/** 查询统计转院人数列表 */
function getTransshipmentList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/transshipment/getTransshipmentList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 查询是否可以修改事件呼叫类型 */
function getIsUpdateEventCallType(eventId, callback, errorCallback) {
  edcRequest('edc_svrside_service/api/event/getIsUpdateEventCallType',
      { "id": eventId },
      function (res) {

          if (res.success) {
              callback(res.content);
          } else {
              errorCallback(null, null, res.msg);
          }
      },
      function (e, url, errMsg) {
          errorCallback(e, url, e.responseText);
      });

}


/** 获取枚举的所有的呼叫类型，用来判断是否创建事件的呼叫类型 */
function getCallTypeShouldCreateEventList(callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/callTypeShouldCreateEventList',
        {},
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg); 
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 座席上线 */
function upSeat(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/upSeat',
        { "id": id },
        function (res) {

            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 座席上线/挂起  hangOrRenew：挂起或者恢复（挂起：hang 恢复：renew）；seatId：座席ID */
function seatHangUp(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/seatHangUp',
        params,
        function (res) {

            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 座席挂起解密 */
function checkPassword(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/checkPassword',
        params,
        function (res) {

            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 分站下线 */
function stationLoginOut(stationId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/stationLoginOut',
        { "stationId": stationId },
        function (res) {

            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 分站上线 */
function stationLogin(stationId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/station/stationLogin',
        { "stationId": stationId },
        function (res) {

            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 分站心跳，10秒内 */
function stationHeart(stationId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/station/stationHeart',
        { "stationId": stationId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 获取事件列表。通过事件状态 */
function getEventPages(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getEventPages',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取首次事件列表 */
function getFirstEventPages(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getFirstEventPages',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 根据事件id查询受理集合 */
function selectEventAcceptanceList(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/event/selectEventAcceptanceList',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 获取座席交接班记录内容分页 */
function selectShiftSeatList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/selectShiftSeatList',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席交接班记录内容更新 */
function shiftContentUpdate(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftContent/updateById',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席交接班记录内容查询 */
function getShiftSeatDetailById(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/getShiftSeatDetailById',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取交接时统计数据 */
function getShiftSeatStatisticalData4Turnover(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/shiftseat/getShiftSeatStatisticalData4Turnover',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取黑名单列表 */
function getBlacklistPages(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/blacklist/selectBlacklist',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 解除黑名单 */
function relieveunBlacklist(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/blacklist/unBlacklist',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
function insertBlacklist(params, callback, errorCallback){
    edcRequest('edc_svrside_service/api/blacklist/insertBlacklist',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取事件列表。通过事件状态 */
function getEventProcessList(eventStatus, seatId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getEventProcessList',
        { "eventStatus": eventStatus, "seatId": seatId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 修改设置事件挂起待派状态 */
function updateEventPending(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateEventPending',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg); 
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 修改事件锁定状态 */
function updateEventLock(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateEventLock',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 检查当前用户是否可以操作事件
 * @param {string} isLock 事件是否锁定
 * @param {string} lockSeatUserId 锁定事件的座席用户id
 * @param {string} lockSeatId 锁定事件的座席id
 * @returns {boolean} true-可以操作，false-不可以操作
 */
function canOperateEvent(isLock,lockSeatUserId,lockSeatId) {
    // 如果事件没有锁定，直接返回true
    if (!isLock || isLock != '1') {
        return true;
    }
    
    // 检查当前座席信息是否存在
    if (!_pSeat) {
        console.error('当前座席信息不存在');
        return false;
    }
    
    // 如果是本人锁定的（lockSeatUserId == 当前用户ID）
    if (lockSeatUserId && lockSeatUserId == _pSeat.userId) {
        return true;
    }
    
    // 如果是同座席锁定的（lockSeatId == 当前座席ID）
    if (lockSeatId && lockSeatId == _pSeat.id) {
        return true;
    }
    
    // 其他情况不允许编辑
    return false;
}

/** 关联通话记录到事件 */
function bindCallAndEvent(callId, eventId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/bindCallAndEvent',
        { "id": callId, "eventId": eventId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 取消通话关联事件 */
function unbindCallAndEvent(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/unbindCallAndEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席心跳更新 */
function updateHeartbeat(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/updateHeartbeat',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席下线 */
function downSeat(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/downSeat',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.msg);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/** 查询120分机的当前状态 */
function getExtPhoneStatusInfo(ext, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/ext/selectExtStatus',
        { "ext": ext },//分机号
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 拨打电话-有话机的情况下拨打
 * @param {any} ext 呼出分机号
 * @param {any} phoneNumber 电话号码
 * @param {any} callback 成功回调
 * @param {any} errorCallback 失败回调
 */
function callPhone(ext, phoneNumber, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/clickCall',
        { "ext": ext, "outerPhone": phoneNumber },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
    
}

/**
 * 拨打电话-有话机的情况下拨打
 * @param {any} dto 请求参数
 */
function callPhone2(dto, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/clickCall', dto,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 拨打电话-有话机的情况下拨打，顺便把通话记录关联事件
 * @param {any} ext 呼出分机号
 * @param {any} phoneNumber 电话号码
 * @param {any} eventId 事件ID
 * @param {any} type 类型，表明这个通话是急救电话还是联系电话等
 * @param {any} callback 成功回调
 * @param {any} errorCallback 失败回调
 */
function callPhoneWithEvent(ext, phoneNumber, eventId, type, callback, errorCallback) {
    if (!eventId || eventId === 'null' || eventId === 'undefined') {
        eventId = '';
    }
    if (!type || type === 'null' || type === 'undefined') {
        type = '';
    }

    /*console.log("拨打电话参数WithEvent：", { "ext": ext, "outerPhone": phoneNumber, "eventId": eventId })
    showProcess(false);*/

    edcRequest('edc_svrside_service/api/call/clickCall',
        { "ext": ext, "outerPhone": phoneNumber, "eventId": eventId, "type": type },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });

}

/**
 * 拨打电话（通用方法）
 * @param {any} phoneNumber 电话号码
 */
function callingPhone(phoneNumber) {

    if (phoneNumber == null || phoneNumber == '') {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }

    $.messager.confirm("提示", "是否拨打电话" + phoneNumber + "？", function (r) {
        if (r) {
            if (!gProxyW) {
                gProxyW = ProxyObj(window);
            }
            if (!bsProxyW) {
                bsProxyW = BsProxyObj(window);
            }

            var pSeat = null;
            try {
                pSeat = evalJson(gProxyW.getSeatInfo());
            } catch (e) {
                pSeat = evalJson(bsProxyW.getSeatInfo());
            }
            if (pSeat == null || pSeat == '') {
                $.messager.alert('提示', '未配置呼出分机号，请先配置！');
                return null;
            };

            showProcess(true, '温馨提示', '正在拨打电话...');

            //接入软电话拨打处理
            /*if (pSeat.callOutExt.isSoftphone == "1") {
                //进行拨打软电话
                gProxyW.dialCallSoftPhone("2", phoneNumber);
                showProcess(false);
            } else {
                callPhone(pSeat.callOut, phoneNumber,
                    function (res) {
                        showProcess(false);
                        //正在拨打电话
                        $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                    });
            }*/

            callPhone(pSeat.callOut, phoneNumber,
                function (res) {
                    showProcess(false);
                    //正在拨打电话
                    $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                });

        }
    });
}

/**
 * 拨打电话（通用方法）
 * @param {any} phoneNumberOrigin 电话号码，带符号的。用于提示语展示
 * @param {any} phoneNumber 电话号码
 */
function callingPhone2(phoneNumberOrigin, phoneNumber) {
    if (!phoneNumberOrigin || !phoneNumber) {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }

    $.messager.confirm("提示", "是否拨打电话" + phoneNumberOrigin + "？", function (r) {
        if (r) {
            if (!gProxyW) {
                gProxyW = ProxyObj(window);
            }
            if (!bsProxyW) {
                bsProxyW = BsProxyObj(window);
            }

            var pSeat = null;
            try {
                pSeat = evalJson(gProxyW.getSeatInfo());
            } catch (e) {
                pSeat = evalJson(bsProxyW.getSeatInfo());
            }
            if (pSeat == null || pSeat == '') {
                $.messager.alert('提示', '未配置呼出分机号，请先配置！');
                return null;
            };

            showProcess(true, '温馨提示', '正在拨打电话...');

            //接入软电话拨打处理
            /*if (pSeat.callOutExt.isSoftphone == "1") {
                //进行拨打软电话
                gProxyW.dialCallSoftPhone("2", phoneNumber);
                showProcess(false);
            } else {
                callPhone(pSeat.callOut, phoneNumber,
                    function (res) {
                        showProcess(false);
                        //正在拨打电话
                        $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                    });
            }*/

            callPhone(pSeat.callOut, phoneNumber,
                function (res) {
                    showProcess(false);
                    //正在拨打电话
                    $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                });

        }
    });
}

/**
 * 拨打电话（通用方法），顺便把通话记录关联事件
 * @param {any} phoneNumber 电话号码
 * @param {any} eventId 事件ID
 * @param {any} type 类型，表明这个通话是急救电话还是联系电话等
 */
function callingPhoneWithEvent(phoneNumber, eventId, type) {
    if (phoneNumber == null || phoneNumber == '') {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }

    $.messager.confirm("提示", "是否拨打电话" + phoneNumber + "？", function (r) {
        if (r) {
            if (!gProxyW) {
                gProxyW = ProxyObj(window);
            }
            if (!bsProxyW) {
                bsProxyW = BsProxyObj(window);
            }

            var pSeat = null;
            try {
                pSeat = evalJson(gProxyW.getSeatInfo());
            } catch (e) {
                pSeat = evalJson(bsProxyW.getSeatInfo());
            }
            if (pSeat == null || pSeat == '') {
                $.messager.alert('提示', '未配置呼出分机号，请先配置！');
                return null;
            };

            showProcess(true, '温馨提示', '正在拨打电话...');

            //接入软电话拨打处理
            /*if (pSeat.callOutExt.isSoftphone == "1") {
                //进行拨打软电话
                gProxyW.dialCallSoftPhone("2", phoneNumber);
                showProcess(false);
            } else {
                callPhoneWithEvent(pSeat.callOut, phoneNumber, eventId,
                    function (res) {
                        showProcess(false);
                        //正在拨打电话
                        $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                    });
            }*/

            callPhoneWithEvent(pSeat.callOut, phoneNumber, eventId, type,
                function (res) {
                    showProcess(false);
                    //正在拨打电话
                    // $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                });

        }
    });
}

/**
 * 拨打电话（通用方法），顺便把通话记录关联事件
 * @param {any} phoneNumberOrigin 电话号码，带符号的。用于提示语展示
 * @param {any} phoneNumber 电话号码
 * @param {any} eventId 事件ID
 * @param {any} type 类型，表明这个通话是急救电话还是联系电话等
 */
function callingPhoneWithEvent2(phoneNumberOrigin, phoneNumber, eventId, type) {
    if (!phoneNumberOrigin || !phoneNumber) {
        $.messager.alert('提示', '电话号码不能为空！', 'info');
        return null;
    }

    $.messager.confirm("提示", "是否拨打电话" + phoneNumberOrigin + "？", function (r) {
        if (r) {
            if (!gProxyW) {
                gProxyW = ProxyObj(window);
            }
            if (!bsProxyW) {
                bsProxyW = BsProxyObj(window);
            }

            var pSeat = null;
            try {
                pSeat = evalJson(gProxyW.getSeatInfo());
            } catch (e) {
                pSeat = evalJson(bsProxyW.getSeatInfo());
            }
            if (pSeat == null || pSeat == '') {
                $.messager.alert('提示', '未配置呼出分机号，请先配置！');
                return null;
            };

            showProcess(true, '温馨提示', '正在拨打电话...');

            //接入软电话拨打处理
            /*if (pSeat.callOutExt.isSoftphone == "1") {
                //进行拨打软电话
                gProxyW.dialCallSoftPhone("2", phoneNumber);
                showProcess(false);
            } else {
                callPhoneWithEvent(pSeat.callOut, phoneNumber, eventId,
                    function (res) {
                        showProcess(false);
                        //正在拨打电话
                        $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                    },
                    function (e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                    });
            }*/

            callPhoneWithEvent(pSeat.callOut, phoneNumber, eventId, type,
                function (res) {
                    showProcess(false);
                    //正在拨打电话
                    // $.messager.alert('提示', '拨打电话成功，听到铃声请拿起呼出电话机。', 'info');

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', '拨打电话错误：' + errMsg, 'error');
                });

        }
    });
}

/**
 * 将已完成与已撤销的任务恢复到当前事件列表中，可以再次进行调度
 * @param {any} eventId 事件ID
 * @param {any} callback
 * @param {any} errorCallback
 */
function updateRecoverEvent(eventId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateRecoverEvent',
        { "eventId": eventId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 按区域获取所有站点的车辆信息 */
function getRegionStations(callback, errorCallback, rescueCenterList = []) {
    edcRequest('edc_svrside_service/api/station/region_station_list',
        { rescueCenterList },
        function (res) {
            if (res.success) {
                var data = res.content;
                callback(data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 分机免打扰开启和关闭 */
function extAssign(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/ext/extAssign',
        params,
        function (res) {
            if (res.success) {
                var data = res.content;
                callback(data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取所有站点区域列表 */
function getAllStationRegionList(callback, errorCallback) {
    edcRequest('edc_svrside_service/api/station_region/getAllStationRegionList',
        {},
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取所有站点列表 */
function getAllStationList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/station/getAllStationList', 
        params, 
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取单个分站信息 */
function getStationInfoById(stationId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/station/get_station_info',
        { "id": stationId },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 实时更新车辆列表，会把车辆列表数据放到全局变量 _mobiles */
function refreshMobiles(stationId, callback) {
    edcRequest('edc_svrside_service/api/mobile/list',
        { 'stationId': stationId },
        function (res) {
            if (res.success) {
                var data = res.content;

                /*不转换成百度坐标
                //转换坐标系
                for (var i = 0; i < data.length; i++) {
                    //TODO：模拟gps数据，部署前删除
                    //data[i].lng = Math.random() * 0.1 + 114.4;
                    //data[i].lat = Math.random() * 0.05 + 23.0;
                    //data[i].direct = Math.random() * 6.28;
                    //如果坐标值不正常，则忽略
                    if (data[i].lng == 0 && data[i].lat == 0) {
                    }
                    else {
                        //转换为百度地图坐标
                        var newPoint = convertCoord(data[i].lng, data[i].lat);
                        data[i].lng = newPoint.lng;
                        data[i].lat = newPoint.lat;
                    }
                }
                */
                _mobiles = data;
                callback(data);
            }
        });
}

/**
 * 通过车辆的状态获取车辆列表信息，不会算与事件地点的距离
 * 车辆状态：0:全部、1：待命、2: 任务、3：暂停
 */
function getMobilesStatusList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/getMobilesStatusList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
};

/**
 * 通过车辆的状态获取车辆列表信息，会算与事件地点的距离
 * 车辆状态：0:全部、1：待命、2: 任务、3：暂停
 */
function getMobilesStatusListWithDistance(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/getMobilesStatusListWithDistance',
        params,
        function (res) {
            if (res.success) {
                _mobilesWithDistance = res.content;
                callback(res.content);
            } else {
                if (errorCallback) {
                    errorCallback(null, null, res.msg);
                }
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
};

/**
 * 请求车载视频的进入房间的患者信息
 */
function getProcessIdByCanVideo(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/getProcessIdByCanVideo',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 指导视频H5请求
 */
function h5AVInvite(eventStatus,seatId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/h5AVInvite',
        { "eventStatus": eventStatus,"seatId": seatId},
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**
 * 获取现场照片
 */
function getPhoto(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getEventFile',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**
 * 查询分站车辆树
 */
function getMobileList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/getMobileList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**
 * 获取消息列表
*/
function getNoticelists(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/notice/getNoticeList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
function updateNoticeUserRead(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/notice/updateNoticeUserRead',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 修改车辆状况，车辆当前状况，0：正常，1：维护中，2：驻场中，3：未值班 */
function updateMobileCondition(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/updateMobileCondition',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获得某个任务中的当前车辆运行记录 */
function getMobileProcessItemsByProcessId(processId, callback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMobileProcessItemsByProcessId',
        { "processId": processId },
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}
/** 获取上报的地址 */
function getUploadAddress(params, callback, errorCallback) {
  if (params && params.eventId) {
    edcRequest('edc_svrside_service/api/event/selectLatestAddressesReport',
      params,
      function (res) {
          if (res.success) {
              callback(res.content);
          } else {
              errorCallback(null, null, res.msg);
          }
      },
      function (e, url, errMsg) {
          errorCallback(e, url, e.responseText);
      });
   }
}

/** 同步地址 */
function updateEventAddress(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateEventAddress',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
function getNoticeDetail(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/notice/getNoticeUserById',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 获取出车任务列表 */
function getMobileProcessByEventId(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMobileProcessByEventId',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 分站端接收任务，状态从5改为7 */
function acceptProcess(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/acceptProcess',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 获取分站出车任务记录分页查询 */
function getStationMobileProcessPage(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getStationMobileProcessPage',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取事件信息 */
function getEventDetailById(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getEventDetailById',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 打印单查询 */
function getInfoForPrint(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getInfoForPrint ',params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 处理呼叫处理结果和类型 */
function updateHandleStatusAndType(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/updateHandleStatusAndType',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取事件信息通过事件ID */
function getEventById(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getEventById',
        { "id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/*function importantEvent(id, callback, errorCallback){
    edcRequest('edc_svrside_service/api/sms/sendSmsByEventId',
        {"id": id },
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/
/** 发送短信 */
/*function sendsMessages(data, callback, errorCallback){
    edcRequest('edc_svrside_service/api/sms/sendSms',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/

/*function getMobileProcess(eventId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/list',
        { "eventId": eventId },
        function (res) {
            if (res.success) {
                var data = res.content;
                callback(data);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/

/** 添加调度任务 */
function addMobileProcess(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/add',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获取重大事件基础信息 */
function getImportantEventInfo(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/getImportantEventInfo',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 伤员流向更新接口 */
function eventWoundedTransferUpdateById(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventWoundedTransfer/updateById',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 重大事件专用更新接口 */
function largeUpdateById(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventReportItem/updateById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 重大事件删除接口 */
function largeDeleteById(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventReportItem/deleteById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 查询重大事件是否上报过 */
function isReported(data, callback, errorCallback) {
    edcGet('edc-svrside/emergency-management/eventInfo/isReported',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 查询重大事件得 类型和等级得单独接口 */
function getByEventCode(data, callback, errorCallback) {
    edcGet('edc-svrside/emergency-management/eventInfo/getByEventCode',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 查询重大事件的上报列表 */
function eventReportItem(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventReportItem/list',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 副屏查询列表 */
function selectPage(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventInfo/selectPage',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 副屏查询详情 */
function getDetailById(data, callback, errorCallback) {
    edcGet('edc-svrside/emergency-management/eventInfo/getDetailById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 查询重大事件的伤员流向列表 */
function eventWoundedTransfer(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventWoundedTransfer/list',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 重大事件数据的上报接口 */
function eventInfoReport(data, callback, errorCallback) {
    edcRequest('edc-svrside/emergency-management/eventInfo/report',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 获取打开ADLS系统的链接 */
function getAdlsUrl(data, callback, errorCallback) {
    edcRequest('/edc-svrside/thirdparty/adls/getAdlsUrl',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 创建新事件 */
function createNewEventData(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/addEvent',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 更新事件 */
function updateEventData(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateEvent',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 获得接收端未确认的消息 */
function getMsgUnconfirmed(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/msg/getMsgUnconfirmed',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 分站拒绝派车接口 */
function refuseSendCar(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/refuseSendCar',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 座席驳回拒绝派车/分站撤销拒绝派车
 * @param {any} params msgId、processId
 * @param {any} callback
 * @param {any} errorCallback
 */
function rejectRefuseSendCar(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/rejectRefuseSendCar',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 确认接收到消息 */
function confirmedMsg(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/msg/confirmedMsg',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 撤销事件
 * @param {any} eventId 撤销事件ID
 * @param {any} callback
 * @param {any} errorCallback
 */
function updateCancelEvent(eventId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/updateCancelEvent',
        eventId,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 改派车辆 */
function updateReassignment(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateReassignment',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 撤销调度任务
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function updateCancelMobileProcess(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateCancelMobileProcess',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 取消派车任务
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function cancelSendCar(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/cancelSendCar',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 修改并同步急救平台的出车人员相关信息
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function updateMobileProcessDispatchPeople(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateMobileProcessDispatchPeople',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 恢复撤销调度任务
 * @param {any} processId 恢复调度任务的ID
 * @param {any} callback
 * @param {any} errorCallback
 */
function recoverMobileProcess(processId, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateRecoverMobileProcess',
        processId,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 强制待命，同步调用急救平台的接口
 * @param {any} processId 任务ID
 * @param {any} callback
 * @param {any} errorCallback
 */
function updateAwaitMobile(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/updateFinishEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 改派任务车辆-更换任务车辆(纠正出车)
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function changeMobileProcessMobile(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/changeMobileProcessMobile',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 座席和分站状态
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function selectSeatAndStation(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/selectSeatAndStation',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取消息
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
function getMsgInfo(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile_process/getMsgInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**
 * 已经发车的可以远程邀请视频通话
 */
function createRoom(params, callback, errorCallback) {
  edcRequest('edc_svrside_service/api/av/room/createRoom/' + params.taskId,
      params,
      function (res) {
          if (res.success) {
              callback(res.content);
          } else {
              errorCallback(null, null, res.msg);
          }
      },
      function (e, url, errMsg) {
          errorCallback(e, url, e.responseText);
      });
}

/**
 * 获取音视频远程车载
 * @param {any} params
 * @param {any} callback
 * @param {any} errorCallback
 */
/*function getCarVideoList(id, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/av/room/seatOnlineRoom/' + id, {},
      function (res) {
          if (res.success) {
              callback(res.content);
          } else {
              errorCallback(null, null, res.msg);
          }
      },
      function (e, url, errMsg) {
          errorCallback(e, url, e.responseText);
      });
}*/

/** 通话记录带分页，current，size */
function getCallPageList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/callList',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 通过电话号码获取上一次呼入出任务的信息 */
function getLastCallInfoByNumber(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/getLastCallInfoByNumber',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) {
                    errorCallback(null, null, res.msg);
                }
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}


/** 新电话接入查询是否已被标记骚扰电话 */
function getHarassInfoByNumber(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/getHarassInfoByNumber',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 未接和正在接听的通话记录 */
function callAnswerList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/call/callAnswerList',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/*
function alertEventToStation(eventId, stationId, carId, callback) {
    edcRequest('edc_svrside_service/api/station/event_alerts',
        {"eventId": eventId, "stationId": stationId, "carId": carId },
        callback);
}*/

function createCallRecordData(data, callback) {
    edcRequest('edc_svrside_service/api/call/add', data, callback);
}

function updateCallRecordData(data, callback) {
    edcRequest('edc_svrside_service/api/call/update', data, callback);
}

function getAllContacts(callback) {
    edcRequest('edc_svrside_service/api/phone_book_group/list',
        {},
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}
function getNetwork(callback) {
    let sendTime = new Date().getTime()
    let backTime = null
    edcRequest('edc_svrside_service/api/network/servertime',
        {},
        function (res) {
          if (res) {
            backTime = new Date().getTime()
            let delay = backTime - sendTime
            callback(delay);
          }
        });
}

function getStationById(stationId, callback) {
    edcRequest('edc_svrside_service/api/station/query',
        { "id": stationId },
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}

/*function getEventByCarId(carId, callback) {
    edcRequest('edc_svrside_service/api/event/event_by_car_id',
        {"carId": carId },
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}*/

/*function reportEvent(eventId, callback) {
    edcRequest('edc_svrside_service/api/event/report_major_accident',
        { "event": eventId },
        callback);
}*/

function getAssetsByStation(stationId, callback) {
    edcRequest('edc_svrside_service/api/asset/asset_by_station_id',
        { "stationId": stationId },
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}

/*function uploadOfflineData(data, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/offline_data_supplement', data, callback, errorCallback);
}*/

/** 获得出车班次 */
function getDispatchClassesByStation(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/external/getDispatchClassesByStation',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
function getStreetRoot(callback){
    edcRequest('edc_svrside_service/api/street/getStreetRoot',
        {},
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}
function getStreetChildrenList(params,callback) {
    edcRequest('edc_svrside_service/api/street/getStreetChildrenList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            }
        });
}

/** 获取医院相关职能人员，1001:医生；1002：护士；1003：司机；1004：护工 */
function getPeopleByStation(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/external/getPeopleByStationCode',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 电话创建事件后查询地址经纬度 */
function queryLocation(params,callback, errorCallback) {
    edcRequest('edc_svrside_service/api/location/getLocation',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 通话异常记录查询 */
function queryErrorMsg(params,callback, errorCallback) {
    edcRequest('edc_svrside_service/api/callErrorMsg/callListByExtList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 通话异常记录设为已读 */
function readErrorMsg(params,callback, errorCallback) {
    edcRequest('edc_svrside_service/api/callErrorMsg/read',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  座席列表 */
function getSeatsList(params,callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/getList',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  座席列表，包含统计。给主任坐席使用 */
function getSeatsListWithStatistics(params,callback, errorCallback) {
    edcRequest('edc_svrside_service/api/seat/getListWithStatistics',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  获得班次列表 */
function getScheduleTimeConfigList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/scheduleTimeConfig/list',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  监听分机 */
function monitor(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/monitor',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  强插分机 */
function bargeIn(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/bargeIn',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  会议 */
function conference(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/conference',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 结束会议 */
function closeTheConference(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/closeTheConference',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**  每秒判断一下 是否本座席在会议中 */
function isInMeetingNow(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/extInConference',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 交接班查询分页数据  */
function getScheduleShiftSummary(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/scheduleShiftSummary/selectPage',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 分站出车任务统计  */
function stationDispatchStatistics(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/statistics/stationDispatchStatistics',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 排队电话  */
function getCallQueueList(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/extGroupQueue/selectPage',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/** 获取所有菜单  */
/*function getAllList(callback, errorCallback) {
    edcRequest('edc_svrside_register/permission/getAllList',
        {},
        function (res) {
            if (res.success) {
                console.info("获取所有菜单接口返回",res.data);
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}*/

function convertCoord(lng, lat) {
    var temp = coordtransform.wgs84togcj02(lng, lat);
    var res = coordtransform.gcj02tobd09(temp[0], temp[1]);
    return { "lng": res[0], "lat": res[1] };
}

/** 进行经纬度转换为距离的计算 */
function rad(d) {
    return d * Math.PI / 180.0;//经纬度转换成三角函数中度分表形式。
}
/** 计算距离（单位米），参数分别为第一点的纬度，经度；第二点的纬度，经度 */
function getDistance(lat1, lng1, lat2, lng2) {

    var radLat1 = rad(lat1);
    var radLat2 = rad(lat2);
    var a = radLat1 - radLat2;
    var b = rad(lng1) - rad(lng2);
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
        Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378.137;// EARTH_RADIUS;
    s = Math.round(s * 10000) / 10; //输出为米
    //s=s.toFixed(4);
    return s;
}

/** easyUI进度框，防止重复提交 */
function showProcess(isShow, title, msg) {
    if (!isShow) {
        $.messager.progress('close');
        return;
    }
    var win = $.messager.progress({
        title: title,
        msg: msg
    });
}

/**
 * 计算倒计时
 * @param {any} year
 * @param {any} month
 * @param {any} day
 * @param {any} hour
 * @param {any} minute
 * @param {any} second
 */
function leftTimer(year, month, day, hour, minute, second) {
    let time = ((new Date()) - (new Date(year, month - 1, day, hour, minute, second)))/1000; //计算剩余的毫秒数
    let days = parseInt(time / 60 / 60 / 24, 10); //计算剩余的天数
    let hours = parseInt(time / 60 / 60 % 24, 10); //计算剩余的小时
    let minutes = parseInt(time / 60 % 60, 10);//计算剩余的分钟
    let seconds = parseInt(time % 60, 10);//计算剩余的秒数
    days = checkTime(days);
    hours = checkTime(hours);
    minutes = checkTime(minutes);
    seconds = checkTime(seconds);
    if (days != "00") {
        return days + "天" + hours + "小时" + minutes + "分" + seconds + "秒";
    }
    if (hours != "00") {
        return hours + "小时" + minutes + "分" + seconds + "秒";
    }

    return minutes + "分" + seconds + "秒";
}

/** 将0-9的数字前面加上0，例1变为01 */
function checkTime(i) { //将0-9的数字前面加上0，例1变为01
    if (i < 10) {
        i = "0" + i;
    }
    return i;
}

/**
 * linkbutton方法扩展
 * @param {Object} jq
 */
$.extend($.fn.linkbutton.methods, {
    /**
     * 激活选项（覆盖重写）
     * @param {Object} jq
     */
    enable: function (jq) {
        return jq.each(function () {
            var state = $.data(this, 'linkbutton');
            if ($(this).hasClass('l-btn-disabled')) {
                var itemData = state._eventsStore;
                //恢复超链接
                if (itemData.href) {
                    $(this).attr("href", itemData.href);
                }
                //回复点击事件
                if (itemData.onclicks) {
                    for (var j = 0; j < itemData.onclicks.length; j++) {
                        $(this).bind('click', itemData.onclicks[j]);
                    }
                }
                //设置target为null，清空存储的事件处理程序
                itemData.target = null;
                itemData.onclicks = [];
                $(this).removeClass('l-btn-disabled');
            }
        });
    },
    /**
     * 禁用选项（覆盖重写）
     * @param {Object} jq
     */
    disable: function (jq) {
        return jq.each(function () {
            var state = $.data(this, 'linkbutton');
            if (!state._eventsStore)
                state._eventsStore = {};
            if (!$(this).hasClass('l-btn-disabled')) {
                var eventsStore = {};
                eventsStore.target = this;
                eventsStore.onclicks = [];
                //处理超链接
                var strHref = $(this).attr("href");
                if (strHref) {
                    eventsStore.href = strHref;
                    $(this).attr("href", "javascript:void(0)");
                }
                //处理直接耦合绑定到onclick属性上的事件
                var onclickStr = $(this).attr("onclick");
                if (onclickStr && onclickStr != "") {
                    eventsStore.onclicks[eventsStore.onclicks.length] = new Function(onclickStr);
                    $(this).attr("onclick", "");
                }
                //处理使用jquery绑定的事件
                var eventDatas = $(this).data("events") || $._data(this, 'events');
                if (eventDatas["click"]) {
                    var eventData = eventDatas["click"];
                    for (var i = 0; i < eventData.length; i++) {
                        if (eventData[i].namespace != "menu") {
                            eventsStore.onclicks[eventsStore.onclicks.length] = eventData[i]["handler"];
                            $(this).unbind('click', eventData[i]["handler"]);
                            i--;
                        }
                    }
                }
                state._eventsStore = eventsStore;
                $(this).addClass('l-btn-disabled');
            }
        });
    }
});

/** 让window居中 */
var easyuiPanelOnOpen = function (left, top) {
    var iframeWidth = $(this).parent().parent().width();
    var iframeHeight = $(this).parent().parent().height();

    var windowWidth = $(this).parent().width();
    var windowHeight = $(this).parent().height();

    var setWidth = (iframeWidth - windowWidth) / 2;
    var setHeight = (iframeHeight - windowHeight) / 2;
    $(this).parent().css({/* 修正面板位置 */
        left: setWidth,
        top: setHeight
    });

    if (iframeHeight < windowHeight) {
        $(this).parent().css({/* 修正面板位置 */
            left: setWidth,
            top: 0
        });
    }
    $(".window-shadow").hide();
};
$.fn.window.defaults.onOpen = easyuiPanelOnOpen;

var easyuiPanelOnResize = function (left, top) {
    var iframeWidth = $(this).parent().parent().width();
    var iframeHeight = $(this).parent().parent().height();

    var windowWidth = $(this).parent().width();
    var windowHeight = $(this).parent().height();

    var setWidth = (iframeWidth - windowWidth) / 2;
    var setHeight = (iframeHeight - windowHeight) / 2;
    $(this).parent().css({/* 修正面板位置 */
        left: setWidth - 6,
        top: setHeight - 6
    });

    if (iframeHeight < windowHeight) {
        $(this).parent().css({/* 修正面板位置 */
            left: setWidth,
            top: 0
        });
    }
    $(".window-shadow").hide();
    //$(".window-mask").hide().width(1).height(3000).show();
};
$.fn.window.defaults.onResize = easyuiPanelOnResize;

/** DES加密 CBC模式加密  utf8 to base64 */
function encryptByDES(message) {
    let key = "EDC1AWSL";
    let ckey = CryptoJS.enc.Utf8.parse(key)
    let encrypted = CryptoJS.DES.encrypt(message, ckey, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: CryptoJS.enc.Utf8.parse(key)
    });
    //return encrypted.toString(); //此方式返回base64格式密文
    return encrypted.ciphertext.toString().toUpperCase(); // 此方式返回hex格式的密文
}

/**  JS DES CBC模式 解密 */
function decryptByDES(message) {
    let key = "EDC1AWSL";
    let ckey = CryptoJS.enc.Utf8.parse(key);
    let ciphertext = CryptoJS.enc.Hex.parse(message);
    let srcs = CryptoJS.enc.Base64.stringify(ciphertext);
    let decrypt = CryptoJS.DES.decrypt(srcs, ckey, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: CryptoJS.enc.Utf8.parse(key)
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
}

function getStrFromBytes(arr) {
    var r = "";
    for (var i = 0; i < arr.length; i++) {
        r += String.fromCharCode(arr[i]);
    }
    return r;
}


//根据电话号码查询个人信息 */
function personInfoByNumber(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/phone_book/getByNumber',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

//获取通信录 */
function getPhoneList(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/phone_book_group/getConfigPhoneBookList',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}



//需求变更-->获取座席端下方分站列表 */
function getStationInfo(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/station/getStationInfo',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) {
                    errorCallback(null, null, res.msg);
                }
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 分站确认出车 */
function confirmDispatchByStation(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/mobile_process/confirmDispatchByStation',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 座席端协助分站派车 */
function assistStionSendCar(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/mobile_process/confirmDispatchBySeat',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}
/**
 *  新增记事本
 */
function addNotebookApi(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/notebook/seatAdd',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/** 获取座席线路情况 用的微服务的接口, 微服务的接口返回的是data数据不是content，这个要注意*/
function getCurrentSeatVideoStatusBySeatId(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videoCallLine/getCurrentSeatVideoStatusBySeatId',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 设置座席视频线路免打扰 */
function setVideoCallLineNoDisturb(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videoCallLine/setNoDisturb',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}


/** 接听、挂断结束视频呼救视频 */
function updateCallCurrentStatus(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/updateCallCurrentStatus',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 座席视频呼救转移到其他座席 */
function transferVideoByCallId(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/transferVideoByCallId',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}


/** 视频呼救记录分页列表 */
function getVideoCallPageList(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/selectPage',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 更新视频通话记录 */
function updateVideoCallById(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/updateById',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 绑定视频呼救与事件 */
function bindVideoCallAndEvent(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/bindVideoCallAndEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 根据事件ID获取电话和视频通话记录 */
function getPhoneAndVideoCallListByEventId(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/getPhoneAndVideoCallListByEventId',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 解绑视频呼救与事件 */
function unbindVideoCallAndEvent(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/unbindVideoCallAndEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}


/** 获取全部会话消息 */
function getNewSessionMessagesByUserId(params, callback, errorCallback) {
    edcRequest('/edc-svrside/thirdparty/chats/message/getNewSessionMessagesByUserId',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 发送消息 */
function sendMessageToApi(params, callback, errorCallback) {
    edcRequest('/edc-svrside/thirdparty/chats/message/send',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 撤回消息 */
function recallMessage(params, callback, errorCallback) {
    edcRequest('/edc-svrside/thirdparty/chats/message/recall',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 获取用户在指定会话中的未读消息列表 */
function getUnreadMessagesBySessionId(params, callback, errorCallback) {
    edcRequest('/edc-svrside/thirdparty/chats/message/unreadBySessionId',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}

/** 根据视频呼救ID获取视频呼救详情 */
function getVideoCallById(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/videocall/getVideoCallById',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) {
                errorCallback(e, url, e.responseText);
            }
        });
}



//============================以下是常见的时间操作============================
/**
 * 获取格式为yyyyMMddhhmmss的时间字符串
 */
function getTimeNotModifier() {
    return (new Date()).formatString('yyyyMMddhhmmss');
}
/**
 * 获取格式为yyyy-MM-dd hh:mm:ss的时间字符串
 */
function getTimeModifier() {
    return (new Date()).formatString('yyyy-MM-dd hh:mm:ss');
}
/**
 * 获取格式为指定格式 pattern 的时间字符串
 * @param {any} date 日期对象，不传默认为当前时间
 * @param {any} pattern 格式，不传默认为 yyyy-MM-dd hh:mm:ss
 */
function getTimeStr(date, pattern) {
    if (!date) {
        date = new Date()
    }
    if (!pattern) {
        pattern = 'yyyy-MM-dd hh:mm:ss'
    }
    return date.formatString(pattern);
}
/**
 * 获取年月日，中文的，例如 2024年5月1日
 */
function getCnYMD() {
    var today = new Date();
    var year = today.getFullYear();
    var month = today.getMonth() + 1;
    var date = today.getDate();

    var curDate = year + "年" + month + "月" + date + "日";
    return curDate;
}

/**
 * 分页查询记事本
 */
function notebookSelectPage(params, callback, errorCallback){
    edcRequest('/edc-svrside/cds/notebook/seatSelectPage',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 修改记事本
 */
function updateNotebook(params, callback, errorCallback){
    edcRequest('/edc-svrside/cds/notebook/seatUpdate',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 *  删除记事本
 */
function delNotebookApi(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/notebook/seatDelete',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 通过车辆ID获取车辆信息
 * @param {string} id 车辆ID
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getVehicleById(id, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobile/selectById',
        {id: id},
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取今日舆情监控数据
 */
function getPublicOpinionMonitorStatistics(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/statistics/getPublicOpinionMonitorStatistics',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取GPS历史数据列表
 * @param {object} params 查询参数
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getGpsHistoryList(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/mobile/getHistoryInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取围栏列表
 * @param {object} params 查询参数
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getFenceList(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/fenceConfig/selectAllList',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取超出围栏范围的违规车辆列表
 * @param {object} params 查询参数
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getOutFenceVehicles(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/fenceViolationRecord/getOutFenceVehicles',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取围栏违规记录分页列表
 * @param {object} params 查询参数
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getFenceViolationRecordPage(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/fenceViolationRecord/selectPage',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 按车辆统计超出电子围栏次数和时长
 * @param {object} params 查询参数
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getViolationStatisticsByVehicle(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/fenceViolationRecord/getViolationStatisticsByVehicle',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/**
 * 获取系统地图配置
 */
function getMapSysConfig() {
    const str = window.localStorage.getItem('sysConfigMap') || '{}';
    try {
        return JSON.parse(str);
    } catch {
        return {};
    }
}

/**
 * 短信回访
 */
function msgVisit(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/eventVisit/sendSms',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 无效回访任务
 */
function invalidVisit(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/eventVisit/invalid',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 解除回访任务
 */
function delInvalidVisit(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/eventVisit/delInvalid',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取回访单
 */
function getVisitForm(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/visitForm/getEnable',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取回访记录
 */
function getVisitRecord(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/visitRecord/getData',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 电话回访
 */
function callVisit(params, callback, errorCallback) {
    edcRequest('edc-svrside/cds/eventVisit/callPhone',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 解除关联首次事件
 * @param {Object} params - 传参对象，需包含eventId等
 * @param {Function} callback - 成功回调
 * @param {Function} errorCallback - 失败回调
 */
function unbindFirstEvent(params, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/event/unbindFirstEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 事件关联首次事件
 * @param {Object} params - 传参对象，需包含eventId、firstEventId等
 * @param {Function} callback - 成功回调
 * @param {Function} errorCallback - 失败回调
 */
function bindFirstEvent(params, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/event/bindFirstEvent',
        params,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}



/**
 * 获取围栏列表配置
 */
function getFenceListConfig() {
    const str = window.localStorage.getItem('fenceList') || '[]';
    try {
        return JSON.parse(str);
    } catch {
        return [];
    }
}

/**
 * 查询事件回访分页列表
 */
function selectEventVisitPage(data, callback, errorCallback) {
    edcRequest('edc-svrside/cds/eventVisit/selectPage',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}


/**
 * 查询车辆报停原因列表
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function selectMobileStopReasonList(callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopReason/selectReasonList',
        {},
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}
/**
 * 查询车辆报停记录分页列表
 * @param {Object} data 查询参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function selectMobileStopRecordsPage(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/selectPage',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 车辆报停接口
 * @param {Object} data 报停参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function stopVehicle(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/stopVehicle',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 车辆报停恢复接口
 * @param {Object} data 恢复参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function recoverVehicleStop(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/recover',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询单个车辆报停记录
 * @param {Object} data 查询参数，需包含id
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getVehicleStopRecordById(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/selectById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 修改单个车辆报停记录
 * @param {Object} data 修改参数，需包含id及要更新的字段
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function updateVehicleStopRecordById(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/updateById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 通过id获取分站信息
 * @param {Object} data 查询参数，需包含id
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getStationByIdV2(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/station/selectById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 报停车辆关联通话记录
 * @param {Object} data 关联参数，需包含id和audioFileId
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function linkAudioFileToStopRecord(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/linkAudioFile',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 通过通话id获取通话记录
 * @param {Object} data 查询参数，需包含id
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getCallById(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/call/selectById',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 通过通话id查询报停记录
 * @param {Object} data 查询参数，需包含audioFileId
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getStopRecordByAudioFileId(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/mobileStopRecords/selectByAudioFileId',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 通过资源code获取基础资源数据信息
 * @param {Object} data 查询参数，需包含typeCode
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getBasicResourceInfoByTypeCode(data, callback, errorCallback) {
    edcRequest('/edc-svrside/emergency-management/basicResourceInfo/listByTypeCode',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 急救中心联动推送事件处理
 * @param {Object} data 推送参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function pushEventToRegionLinkage(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/push/event',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 获取急救分中心联动推送与接收的数据分页列表
 * @param {Object} data 查询参数，包含分页信息
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getEventRegionLinkageList(data, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/selectPage',
        data,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 通过外部事件id查询区域联动信息
 * @param {string} externalBizId 外部事件ID
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getEventRegionLinkageByExternalBizId(externalBizId, callback, errorCallback) {
    console.log('调用getEventRegionLinkageByExternalBizId函数，参数externalBizId:', externalBizId);
    edcRequest('/edc-svrside/cds/eventRegionLinkage/selectByExternalBizId',
        {
            externalBizId: externalBizId
        },
        function (res) {
            console.log('getEventRegionLinkageByExternalBizId接口返回结果:', res);
            if (res.success) {
                console.log('区域联动信息查询成功，数据:', res.data);
                if (res.data) {
                    console.log('区域联动接收中心编码:', res.data.receiveRegionCode);
                } else {
                    console.log('区域联动信息返回为空');
                }
                callback(res.data);
            } else {
                console.error('区域联动信息查询失败，错误信息:', res.msg);
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            console.error('区域联动信息查询出现异常:', errMsg, e);
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 根据id查询区域联动的信息
 * @param {string} id 区域联动ID
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getEventRegionLinkageDetailById(id, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/selectById',
        {
            id: id
        },
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}


/**
 * 通过id查询外部事件数据
 * @param {string} id 事件ID
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getEventRegionLinkageById(id, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/selectById',
        {
            id: id
        },
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 更新区域联动反馈状态
 * @param {Object} params 请求参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function updateEventRegionLinkageFeedbackStatus(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/updateFeedbackStatus',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 获取待反馈的区域联动列表
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getWaitingFeedbackEventRegionLinkageList(params,callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/getWaitingFeedbackList',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询所有分中心通讯状态
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getAllCommunicationStatus(callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getAllCommunicationStatus',
        {},
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询市县分中心事件列表
 * @param {Object} params 查询参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getCityLinkageEventList(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getEventList',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询市县各分中心车辆定位信息及状态
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getVehicleLocations(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getVehicleLocations',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询分中心分站信息
 * @param {Object} params 查询参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getCityLinkageStationInfo(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getStationInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 查询资源信息接口
 * @param {Object} params  typeCode 类型编码 一定要传 (ZYLX_09:疾控中心, ZYLX_10:血站)
 * @param {function} callback 成功回调函数
 * @param {function} errorCallback 错误回调函数
 */
function getResourceInfoByTypeCode(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getResourceInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        });
}

/**
 * 查询分中心医院信息
 * @param {Object} params 查询参数
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getResourceHospitalInfo(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventCityLinkage/getResourceHospitalInfo',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}

/**
 * 获取分中心配置信息
 * @param {Object} params centerCode 获取分中心配置通过code,不传就是全部分中心配置信息，可以逗号分隔获取1个或者多个分中心配置
 * @param {Function} callback 成功回调函数
 * @param {Function} errorCallback 错误回调函数
 */
function getRegionConfigByCode(params, callback, errorCallback) {
    edcRequest('/edc-svrside/cds/eventRegionLinkage/getRegionConfigByCode',
        params,
        function (res) {
            if (res.success) {
                callback(res.data);
            } else {
                if (errorCallback) errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            if (errorCallback) errorCallback(e, url, e.responseText);
        }
    );
}


/**
 * 获取当前地图事件位置
 */
function getCurrentMapEventLocation() {
    const str = window.localStorage.getItem('currentMapEventLocation') || '{}';
    try {
        return JSON.parse(str);
    } catch {
        return {};
    }
}

/**
 * 获取受理页面配置
 * @returns {Object} 受理页面配置对象
 */
function getAcceptPageConfig() {
    const str = window.localStorage.getItem('acceptPageConfig') || '{}';
    try {
        return JSON.parse(str);
    } catch {
        return {};
    }
}

/**
 * 获取事件状态文字描述
 * @param {string|number} statusCode 事件状态代码
 * @returns {string} 事件状态文字描述
 */
function getEventStatusText(statusCode) {
    const statusMap = {
        '7': '预约-未调度',
        '1': '落单-未调度', 
        '6': '挂起-未调度',
        '2': '已调度',
        '3': '已完成',
        '4': '已撤销'
    };
    
    return statusMap[statusCode] || '未知状态';
}

/** 呼叫保持 */
function holdCall(ext, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/hold',
        { "ext": ext },//分机号
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/** 呼叫接回 */
function unholdCall(ext, callback, errorCallback) {
    edcRequest('edc_svrside_service/api/om/unhold',
        { "ext": ext },//分机号
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 座席端分页查询公告通知
 */
function selectSeatNoticePage(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/selectSeatPage',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 分站端分页查询公告通知
 */
function selectStationNoticePage(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/selectStationPage',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 获取座席端通知单未读数量
 */
function seatUnreceivedNum(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/seatUnreceivedNum',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 删除通知单
 */
function deleteNotice(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/delete',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 修改通知单
 */
function updateNotice(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/update',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 新增通知单
 */
function addNotice(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/add',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

/**
 * 读通知单
 */
function receiveNotice(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/receive',
        data,
        function (res) {
            if (res.success) {
                callback(res);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

function getNoticeById(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/notice/getById',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}

// 查询所有车辆
function listMobile(data, callback, errorCallback) {
    edcRequest('/edc_svrside_service/api/mobile/list',
        data,
        function (res) {
            if (res.success) {
                callback(res.content);
            } else {
                errorCallback(null, null, res.msg);
            }
        },
        function (e, url, errMsg) {
            errorCallback(e, url, e.responseText);
        });
}