var _stations = [];//所有分站信息

$(document).ready(function () {
    
});

function onLoginSuccess(token) {
    _token = token;
    getRegionStations(function (data) {
        _stations = data.selectMany(function (s) { return s.stations; });
        //设置车辆信息
        initMap();
        sortAndShowStations(_mobilesWithDistance);
    });
}

var _currentLoc = null;
function sortAndShowStations(staList) {
    var temp = [].slice.call(staList);
    for (var i = 0; i < temp.length; i++) {
        var data = temp[i];
        data.distance = MAX_DISTANCE;
        if (_currentLoc != null) {
            //算距离
            caculateRouteDistance(data,
                function (dist, staData) {
                    staData.distance = dist;
                    ////重新排序并展示
                    //reRangeAndShowStations(temp);
                });
        }
    }
    reRangeAndShowStations(temp);
}

function reRangeAndShowStations(staList) {
    //map.clearOverlays();    //清除地图上所有覆盖物
    drawStations();
    var disStation = $('#map-station');
    disStation.empty();
    staList.sort(function (left, right) {
        //按照距离排序算法
        return left.distance - right.distance;
    });
    for (var j = 0; j < staList.length; j++) {
        var data = staList[j];
        var color = "black";
        if (data.status != 0) {
            color = "red";
        }
        if (data.distance < MAX_DISTANCE) {
            disStation.append('<li style="color:{3};" data="{2}" status="{4}">{0}·{5}({1}km)</li>'.formatStr(
                data.stationName,
                (data.distance / 1000).toFixed(1),
                data.id,
                color,
                data.status,
                data.carName));
        } else {
            disStation.append('<li style="color:{2};" data="{1}" status="{3}">{0}·{4}</li>'.formatStr(
                data.stationName,
                data.id,
                color,
                data.status,
                data.carName));
        }
        drawMobile(data);
    }
}

function G(id) {
    return document.getElementById(id);
}

var map;

function initMap() {
    //百度地图
    map = new BMap.Map("the-map");    // 创建Map实例
    map.centerAndZoom(new BMap.Point(114.42586, 23.093224), 13);  // 初始化地图,设置中心点坐标和地图级别
    var top_left_control = new BMap.ScaleControl({ anchor: BMAP_ANCHOR_TOP_LEFT });// 左上角，添加比例尺
    var top_left_navigation = new BMap.NavigationControl();  //左上角，添加默认缩放平移控件
    //添加地图类型控件
    map.addControl(new BMap.MapTypeControl({
        mapTypes: [
            BMAP_NORMAL_MAP,
            BMAP_HYBRID_MAP
        ]
    }));
    map.addControl(top_left_control);
    map.addControl(top_left_navigation);
    querySysConf('map_city',
        function(data) {
            map.setCurrentCity(data); // 设置地图显示的城市 此项是必须设置的
            map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
        });
}

function setPlace(value) {
    //map.clearOverlays();    //清除地图上所有覆盖物
    function myFun() {
        var ppr = local.getResults().getPoi(0);    //获取第一个智能搜索的结果
        if (ppr == undefined) {
            _currentLoc = null;
        } else {
            var pp = ppr.point;
            _currentLoc = pp;
        }
        //map.centerAndZoom(_currentLoc, 18);
        sortAndShowStations(_mobilesWithDistance);
    }
    var local = new BMap.LocalSearch(map, { //智能搜索
        onSearchComplete: myFun
    });
    local.search(value);
}

function drawStations() {
    //绘制所有分站图标
    console.info('draw station');
    for (var i = 0; i < _stations.length; i++) {
        var sta = _stations[i];
        var pt = new BMap.Point(sta.stationLongitude, sta.stationLatitude);
        var myIcon = new BMap.Icon("style/img/hospital.png", new BMap.Size(64, 64));
        var marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
        map.addOverlay(marker);// 将标注添加到地图中
        var label = new BMap.Label('<span style="color:green; font-size:1.2em;">{0}</span>'.formatStr(sta.stationName), { offset: new BMap.Size(0, 60) });
        marker.setLabel(label);
    }
    //绘制定点坐标
    if (_currentLoc != null) {
        map.addOverlay(new BMap.Marker(_currentLoc)); //添加标注
    }
}

function drawMobile(car) {
    //绘制车辆图标
        var pt = new BMap.Point(car.lng, car.lat);
        var myIcon = new BMap.Icon("style/img/ambul.png", new BMap.Size(48, 48));
        var marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
        map.addOverlay(marker);// 将标注添加到地图中
        var label = new BMap.Label('<div style="color:red; font-size:1em; cursor:pointer;">{1}<br/>{0}({2}km)</div>'.formatStr(car.carName, car.stationName, car.distance>=MAX_DISTANCE?'0':(car.distance/1000).toFixed(1)), { offset: new BMap.Size(0, 40) });
        marker.setLabel(label);
}

function caculateRouteDistance(carData, callback) {
    var distance = MAX_DISTANCE;
    if (_currentLoc != null) {
        distance = getDistance(carData.lat, carData.lng, _currentLoc.lat, _currentLoc.lng);
    }
    callback(distance, carData);
}

function setAddress(address) {
    $('#map-search-box').val(address);
    setPlace(address);
}

function syncMobiles(data) {
    _mobilesWithDistance = evalJson(data);
    sortAndShowStations(_mobilesWithDistance);
}

function promoteCamera(pos, device, channel, text) {
    var div = $('#camera' + pos);
    $('#camera' + pos + '>span').html(text);
}




/**
  * 海康威视摄像头代码
*/
var oWebControl = null;// 摄像头插件对象
var pubKey = '';