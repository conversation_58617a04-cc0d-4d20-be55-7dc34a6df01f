var linq = { EqualityComparer: function (t, r) { return t === r || t.valueOf() === r.valueOf() }, SortComparer: function (t, r) { return t === r ? 0 : null === t ? -1 : null === r ? 1 : "string" == typeof t ? t.toString().localeCompare(r.toString()) : t.valueOf() - r.valueOf() }, Predicate: function () { return !0 }, Selector: function (t) { return t } }; !function () { "use strict"; var t = t, o = o || t; Array.prototype.select = Array.prototype.map || function (t, r) { r = r || o; for (var n = [], i = this.length, e = 0; e < i; e++)n.push(Selector.call(r, this[e], e, this)); return n } }(), function () { "use strict"; Array.prototype.selectMany = function (n, i) { return i = i || function (t, r) { return r }, this.aggregate(function (t, r) { return t.concat(n(r).select(function (t) { return i(r, t) })) }, []) } }(), function () { "use strict"; Array.prototype.take = function (t) { return this.slice(0, t) } }(), function () { "use strict"; Array.prototype.skip = Array.prototype.slice }(), function () { "use strict"; Array.prototype.first = function (t, r) { var n = this.length; if (!t) return n ? this[0] : null == r ? null : r; for (var i = 0; i < n; i++)if (t(this[i], i, this)) return this[i]; return null == r ? null : r } }(), function () { "use strict"; Array.prototype.last = function (t, r) { var n = this.length; if (!t) return n ? this[n - 1] : null == r ? null : r; for (; 0 < n--;)if (t(this[n], n, this)) return this[n]; return null == r ? null : r } }(), function () { "use strict"; Array.prototype.union = function (t) { return this.concat(t).distinct() } }(), function () { "use strict"; Array.prototype.intersect = function (r, n) { return n = n || linq.EqualityComparer, this.distinct(n).where(function (t) { return r.contains(t, n) }) } }(), function () { "use strict"; Array.prototype.except = function (t, r) { t instanceof Array || (t = [t]), r = r || linq.EqualityComparer; for (var n = this.length, i = [], e = 0; e < n; e++) { for (var o = t.length, u = !1; 0 < o--;)if (!0 === r(this[e], t[o])) { u = !0; break } u || i.push(this[e]) } return i } }(), function () { "use strict"; Array.prototype.distinct = function (t) { for (var r = [], n = this.length, i = 0; i < n; i++)r.contains(this[i], t) || r.push(this[i]); return r } }(), function () { "use strict"; Array.prototype.zip = function (n, i) { return this.take(Math.min(this.length, n.length)).select(function (t, r) { return i(t, n[r]) }) } }(), function () { "use strict"; Array.prototype.indexOf = Array.prototype.indexOf || function (t, r) { for (var n = this.length, i = Math.max(Math.min(r, n), 0) || 0; i < n; i++)if (this[i] === t) return i; return -1 } }(), function () { "use strict"; Array.prototype.lastIndexOf = Array.prototype.lastIndexOf || function (t, r) { for (var n = Math.max(Math.min(r || this.length, this.length), 0); 0 < n--;)if (this[n] === t) return n; return -1 } }(), function () { "use strict"; Array.prototype.remove = function (t) { var r = this.indexOf(t); -1 != r && this.splice(r, 1) } }(), function () { "use strict"; Array.prototype.removeAll = function (t) { for (var r, n = 0; null != (r = this.first(t));)n++ , this.remove(r); return n } }(), function () { "use strict"; Array.prototype.orderBy = function (n, i) { i = i || linq.SortComparer; var t = this.slice(0), o = function (t, r) { return i(n(t), n(r)) }; return t.thenBy = function (i, e) { return e = e || linq.SortComparer, t.orderBy(linq.Selector, function (t, r) { var n = o(t, r); return 0 === n ? e(i(t), i(r)) : n }) }, t.thenByDescending = function (i, e) { return e = e || linq.SortComparer, t.orderBy(linq.Selector, function (t, r) { var n = o(t, r); return 0 === n ? -e(i(t), i(r)) : n }) }, t.sort(o) } }(), function () { "use strict"; Array.prototype.orderByDescending = function (t, n) { return n = n || linq.SortComparer, this.orderBy(linq.Selector, function (t, r) { return -n(t, r) }) } }(), function () { "use strict"; Array.prototype.innerJoin = function (t, n, i, e, o) { o = o || linq.EqualityComparer; var u = []; return this.forEach(function (r) { t.where(function (t) { return o(n(r), i(t)) }).forEach(function (t) { u.push(e(r, t)) }) }), u } }(), function () { "use strict"; Array.prototype.groupJoin = function (n, i, e, r, o) { return o = o || linq.EqualityComparer, this.select(function (t) { var r = i(t); return { outer: t, inner: n.where(function (t) { return o(r, e(t)) }), key: r } }).select(function (t) { return t.inner.key = t.key, r(t.outer, t.inner) }) } }(), function () { "use strict"; Array.prototype.groupBy = function (t, r) { var n = [], i = this.length; r = r || linq.EqualityComparer, t = t || linq.Selector; for (var e = 0; e < i; e++) { var o = t(this[e]), u = n.first(function (t) { return r(t.key, o) }); u || ((u = []).key = o, n.push(u)), u.push(this[e]) } return n } }(), function () { "use strict"; Array.prototype.toDictionary = function (t, r) { for (var n = {}, i = this.length; 0 < i--;) { var e = t(this[i]); null != e && "" !== e && (n[e] = r(this[i])) } return n } }(), function () { "use strict"; Array.prototype.aggregate = Array.prototype.reduce || function (t, r) { var n = this.slice(0), i = this.length; null == r && (r = n.shift()); for (var e = 0; e < i; e++)r = t(r, n[e], e, this); return r } }(), function () { "use strict"; Array.prototype.min = function (t) { t = t || linq.Selector; for (var r = this.length, n = t(this[0]); 0 < r--;)t(this[r]) < n && (n = t(this[r])); return n } }(), function () { "use strict"; Array.prototype.max = function (t) { t = t || linq.Selector; for (var r = this.length, n = t(this[0]); 0 < r--;)t(this[r]) > n && (n = t(this[r])); return n } }(), function () { "use strict"; Array.prototype.sum = function (t) { t = t || linq.Selector; for (var r = this.length, n = 0; 0 < r--;)n += t(this[r]); return n } }(), function () { "use strict"; var t = t, o = o || t; Array.prototype.where = Array.prototype.filter || function (t, r) { r = r || o; for (var n = [], i = this.length, e = 0; e < i; e++)!0 === Predicate.call(r, this[e], e, this) && n.push(this[e]); return n } }(), function () { "use strict"; var t = t, n = n || t; Array.prototype.any = function (t, r) { return r = r || n, t = t || linq.Predicate, (this.some || function (t, r) { var n = this.length; if (!t) return 0 < n; for (; 0 < n--;)if (!0 === t.call(r, this[n], n, this)) return !0; return !1 }).apply(this, [t, r]) } }(), function () { "use strict"; var t = t, n = n || t; Array.prototype.all = function (t, r) { return r = r || n, t = t || linq.Predicate, (this.every || function (t, r) { return this.length == this.where(t, r).length }).apply(this, [t, r]) } }(), function () { "use strict"; Array.prototype.takeWhile = function (t) { t = t || linq.Predicate; for (var r = this.length, n = [], i = 0; i < r && !0 === t(this[i], i); i++)n.push(this[i]); return n } }(), function () { "use strict"; Array.prototype.skipWhile = function (t) { t = t || linq.Predicate; var r = this.length, n = 0; for (n = 0; n < r && !1 !== t(this[n], n); n++); return this.skip(n) } }(), function () { "use strict"; Array.prototype.contains = function (t, r) { r = r || linq.EqualityComparer; for (var n = this.length; 0 < n--;)if (!0 === r(this[n], t)) return !0; return !1 } }(), function () { "use strict"; var t = t, e = e || t; Array.prototype.forEach = Array.prototype.forEach || function (t, r) { r = r || e; for (var n = this.length, i = 0; i < n; i++)t.call(r, this[i], i, this) } }(), function () { "use strict"; Array.prototype.defaultIfEmpty = function (t) { return 0 === this.length ? [null === t ? null : t] : this } }(), function () { "use strict"; Array.range = function (t, r) { for (var n = []; 0 < r--;)n.push(t++); return n } }();