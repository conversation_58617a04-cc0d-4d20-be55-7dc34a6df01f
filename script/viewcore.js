var loaded_McWinformMVC = false;

$(function() {
    loaded_McWinformMVC = true;
});

function getLoaded() {
    return loaded_McWinformMVC;
}

function bindEvent(selector, eventName, funcStr) {
    if (eventName == 'href') {
        $(selector).attr('href', 'javascript:' + funcStr + ';');
    } else {
        $(selector).bind(eventName,
            function() {
                eval(funcStr);
            });
    }
}

function setHtml(selector, htmlStr) {
    $(selector).html(htmlStr);
}

function appendHtml(selector, htmlStr) {
    $(selector).append(htmlStr);
}

function getHtml(selector) {
    return $(selector).html();
}

function setVal(selector, valu) {
    $(selector).val(valu);
}

function getVal(selector) {
    return $(selector).val();
}

Date.prototype.formatString = function (t) {
    var date = this;
    if (date == undefined || date == null) return null;
    var o = {
        "M+": date.getMonth() + 1,                 //月份   
        "d+": date.getDate(),                    //日   
        "h+": date.getHours(),                   //小时   
        "m+": date.getMinutes(),                 //分   
        "s+": date.getSeconds(),                 //秒   
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度   
        "S": date.getMilliseconds()             //毫秒   
    };
    if (/(y+)/.test(t)) {
        t = t.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    };
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(t)) {
            t = t.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        };
    }
    return t;
};

String.prototype.startWith = function (str) {
    var reg = new RegExp("^" + str);
    return reg.test(this);
}

String.prototype.endWith = function (str) {
    var reg = new RegExp(str + "$");
    return reg.test(this);
}

function evalJson(str) {
    return eval('(' + str + ')');
}

String.prototype.formatStr = function () {
    if (arguments.length == 0)
        return this;
    var str = this;

    for (var i = 0; i < arguments.length; i++) {
        var re = new RegExp('\\{' + i + '\\}', 'gm');
        str = str.replace(re, arguments[i]);
    }
    return str;
}

String.prototype.hasValue = function() {
    return this != undefined && this != null && this != '';
}

String.prototype.isEmpty = function () {
    return this == undefined || this == null || this == '';
}

function random(min, max) {
    return Math.floor(Math.random() * (max+1-min))+min; 
}