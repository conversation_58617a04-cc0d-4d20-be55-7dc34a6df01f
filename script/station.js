/* *****************************************************************
 * 120EDC全局Javascript脚本
 * 兼容性：该脚本将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
 * -----------------------------------------------------------------
 * Creator: 陈蒋耀
 * Created: 2019/6/10
 * -----------------------------------------------------------------
 * Modification:
 * Modifier:
 * Modified:
 * *****************************************************************/
var _user = null;//用户数据
var _stationId = null;//分站Id
var _stationInfo = null;//分站信息
var _currentLoc = null;//本分站的坐标
var _alertTimer = 0;//已经响铃的次数
var _assets = null;
var _currentProcess = null;//当前任务
var _alertTimerPauseTime = 0;//暂停响铃的倒计时时间
var _taskTimesInterval = true;//时间获取
var _eventSearchInterval = true;//事件查询
var _taskStationHeartInterval = true;//心跳
var _stationSearchEventsAutoRefreshInterval = true;//定时刷新任务开关
var _noticeListInterval = true //通知定时任务开关
//var _curAssignTaskMode = '1'
/*var SendOrder = [//列表按照这个格式置顶排序
    { code: '5', name: 'status5' },
    { code: '7', name: 'status7' },
    { code: '6', name: 'status6' },
    { code: '1', name: 'status1' },
    { code: '2', name: 'status2' },
    { code: '8', name: 'status8' },
    { code: '4', name: 'status4' },
    { code: '3', name: 'status3' },
    { code: '-1', name: 'status-1' },
    { name: 'status' }
]*/
var callTypeList = [] //呼叫类型
//var noBell = []//不响铃数组
var backgroundColor = ''
var colorIndex = null;
var dispatchClassesList = [];//班次列表
var page = 1;
var size = 20;
var _mobilesProcessList = []; //当前分站任务列表
var _dics = new Array();//EDC字典表数组

var _baseUrl = "";
var bsProxy = null;
//读取服务器接口地址
try {
    _baseUrl = gProxy.getBaseUrl();
    console.log("C/S模式执行");
} catch (e) {
    console.log("B/S模式执行");
    bsProxy = new gBsProxy(document.body);
    _baseUrl = bsProxy.getBaseUrl();
    console.log("_baseUrl:" + _baseUrl);
}

$(document).ready(function () {
    turnOffAlert();
    $('#version-number').html("版本号" + getExeVersionInfo());
    $('#eventDetailRegion').hide();


    try{
    //读取接口服务器基地址
    _stationId = gProxy.getStationId();

    //获取最后一次登录的用户和密码
    var lastLoginUser = evalJson(gProxy.getLastLoginUser());
    if (lastLoginUser != null) {
            //回填分站，用户，密码
            if (lastLoginUser.StationId) {
                $("#login-station").val(lastLoginUser.StationId);
            }
        $("#login-un").val(lastLoginUser.Username);

        if (lastLoginUser.SavePsw) {
            $("#is-save-psw").attr("checked", "checked");
            $("#login-pwd").val(lastLoginUser.Password);
        }
        }
    } catch (e) {
        console.log("B/S模式执行，获取分站ID失败");
        // 显示分站选择框
        $('#station-select-container').css('display', 'inline-block');
        $('#station-select-label').css('display', 'inline-block');
        // 加载分站列表
        loadStationList();
    }
    //定时刷新
    stationSearchEventsAutoRefreshIntervalTimeout();
    
});

function setUI() {
    //设置车辆信息刷新计时器
    refreshMobileStatus();
    queryAllDic(["event_source", "call_type", "patient_status", "country", "race"],
        function (data) {
            _dics = data;
            //事件来源
            insertIntoSelect('evd-source', 'event_source', data);
            //呼叫类型
            callTypeList = insertIntoSelect('evd-call-type', 'call_type', data) || [];
            //病情
            //insertIntoSelect('det-condition', 'patient_status', data);
            //国家
            insertIntoSelect('det-country', 'country', data);
            //民族
            insertIntoSelect('det-race', 'race', data);
        });//查询所有字典
}

function insertIntoSelect(id, typeCode, dicts) {
    var ui = $('#' + id);
    var list = getDicList(typeCode, dicts);
    ui.empty();
    for (var i = 0; i < list.length; i++) {
        var d = list[i];
        ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
    }
    if (list != null && list.length > 0) {
        ui.val(list[0].codeName);
    }
    return list
}

/**
 * 刷新车辆状态列表
 */
function refreshMobileStatus() {
    //getAllAssets();//获取摄像头信息
    //获取站点车辆信息，并将车辆信息存储到mobile里面
    refreshMobiles(_stationId, function (data) {
        $('#car-status-body').empty();
        for (var i = 0; i < _mobiles.length; i++) {
            let { hCondition, status, statusStr, carName, plateNum, contact, remark, currentProcessId, id, refuseSendCarMsgId } = _mobiles[i];
            /*if (status == '5') {
                if (statusStr == '拒绝派车' && record.indexOf(currentProcessId) == -1) {
                    record.push(currentProcessId)
                } else {
                    let index = record.indexOf(currentProcessId)
                    if (index !== -1) {
                        let i = noBell.indexOf(record[index])
                        if (i !== -1) {
                            noBell.splice(i, 1)
                            record.splice(index, 1)
                        }
                    }
                }
            }*/
            $('#car-status-body').append(`
                ${createMobileTableTrLabel(hCondition, status, statusStr)}
                    <td style="width: 10%;"><a href="javascript:openMobileConditionWindowBtn(\'{6}\',\'{7}\',\'{8}\');"><i class="fa fa-cogs"></i></a></td>
                    <td style="width: 10%;">{0}</td>
                    <td style="width: 16%;">{1}</td>
                    <td style="width: 23%;">{2}</td>
                    <td style="width: 15%;">{3}</td>
                    <td style="width: 26%;">${filterButtons(hCondition, status, statusStr)}</td>
                </tr >`.formatStr(carName, plateNum, contact, getTextByMobileStatus(status, hCondition, statusStr),
                remark, currentProcessId, id, status, hCondition, refuseSendCarMsgId));
        }
        checkAlert();
    });
}

/** 返回标签 <tr> 。车辆列表是否添加双击事件 */
function createMobileTableTrLabel(hCondition, status, statusStr) {
    let flag = false;
    if (hCondition == '0') {
        if (status == "5" || status == "0") {
            flag = status == "5" && statusStr !== '拒绝派车';
        } else {
            flag = true;
        }
    } else {
        flag = true;
    }
    //if (hCondition == '0' ? ((status == "5" || status == "0") ? (status == "5" && statusStr !== '拒绝派车') : true) : true) {
    if (flag) {
        return `<tr ondblclick="confirmAcceptProcess(\'{5}\', \'{7}\')" id="car-tr-{1}">`
    }
    return `<tr id="car-tr-{1}">`
}

function filterButtons(hCondition, status, statusStr) {
    if (hCondition != '0') {
        return ''
    }

    if (status == "0") {//站内待命
        return ''
    } else if (status == "5") {//受理中（座席已创建调度任务）
        if (statusStr == '拒绝派车') {//如果操作了拒绝出车
            return `<a id="refuseProcessCancelBtn" href = "javascript:refuseProcessCancel(\'{9}\',\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">撤销拒绝派车</a >`
        } else {
            return `
                <a  id="refuseProcessBtn" href = "javascript:refuseProcessReasonWindowShow(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">拒绝派车</a >
                ${filterA(status)}
            `
        }
    } else if (status == "3") {//取消接诊
        return `
            <a  id="finishedProcessBtn" href = "javascript:openUpdateAwaitMobileDialog(\'1\',\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">抵达医院</a >
            ${filterA(status)}
        `
    } else {
        return `
            <a  id="cancelProcessBtn" href="javascript:openUpdateAwaitMobileDialog(\'0\',\'{5}\');" style="width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">空车回院</a>
            ${filterA(status)}
        `
    }
}
/** 车辆列表显示派车和修改按钮 */
function filterA(status) {
    if (status == "5" || status == "7") {//5-受理中（座席已创建调度任务）、7-已接收（分站已接收调度任务）
        return `
            <a id="confirmationCarBtn" href = "javascript:confirmCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">出车</a >
            <a id="cancelDispatchedCarBtn" href = "javascript:cancelDispatchedCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">取消派车</a >
        `
    } else if (status == "3") {//取消接诊
        return '<a id="modificationCarBtn" href = "javascript:modifyCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">修改出车</a >'
    } else if (status == "6") {//确认出车
        return `
        <a  id="finishedProcessBtn" href = "javascript:openUpdateAwaitMobileDialog(\'1\',\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">抵达医院</a >
        <a id="modificationCarBtn" href = "javascript:modifyCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">修改出车</a >
        <a id="cancelDispatchedCarBtn" href = "javascript:cancelDispatchedCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">取消派车</a >
    `
    }
    return `
        <a  id="finishedProcessBtn" href = "javascript:openUpdateAwaitMobileDialog(\'1\',\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px;">抵达医院</a >
        <a id="modificationCarBtn" href = "javascript:modifyCar(\'{5}\')" style = "width: 80px;height:25px;line-height: 25px; font-size: 10px; margin: 5px; color:blue">修改出车</a >
    `
}

/** 获取所有的摄像头 */
function getAllAssets() {
    getAssetsByStation(_stationId,
        function (data) {
            _assets = data;
        });
}

function closeTalkAlert(deviceId) {
    $('.talk-form-' + deviceId).remove();
    playAlert(false);
}

function getNoticeAlert() {
    clearTimeout(window.noticeInterval);
    window.noticeInterval = null;
    let params = {
        receiveType: "2",
        receiverId: _stationId
    }
    seatUnreceivedNum(params, function (data) {
        if (data) {
            let numNotice = data
            if (numNotice > 0) {
                $("#xiaoxi_badge").show()
                $("#xiaoxi_badge").text(numNotice)
            } else {
                $("#xiaoxi_badge").hide()
            }
        }
    }, function (e, url, errMsg) {
        $.messager.alert('异常', '获取通知列表，异常信息：' + errMsg);
    });
    if (_noticeListInterval) {
        window.noticeInterval = setTimeout(getNoticeAlert, 3000);
    }
}

/**
 * 在iFrame中打开一个新tab
 * @param title
 */
function openTab(title, href, iframeName) {
    $('#main-tabs').tabs({
        onClose: closeTab
    });

    var e = $('#main-tabs').tabs('exists', title);
    if (e) {
        $("#main-tabs").tabs('select', title);
        var tab = $("#main-tabs").tabs('getSelected');
        $('#main-tabs').tabs('update', {
            tab: tab,
            options: {
                id: iframeName,
                title: title,
                content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                closable: true,
                selected: true
            }
        });
    } else {
        $('#main-tabs').tabs('add', {
            id: iframeName,
            title: title,
            content: '<iframe id="' + iframeName + '" name="' + iframeName + '" scrolling="auto" src="' + href + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
            iconCls: '',
            closable: true
        });
    }

}
/** 关闭tab */
function closeTab(title, index) {
    var e = $('#main-tabs').tabs('exists', title);
    if (e) {
        $("#main-tabs", parent.document).tabs('close', title);
    }

    var tabs = $('#main-tabs').tabs("tabs")
    //判断是否打开了创建新事件的tab页
    let foundCreatingEventTab = false;
    for (var i = 0; i < tabs.length; i++) {
        let aTitle = $('#main-tabs').tabs('getTab', i).panel('options').title;
        if (aTitle.indexOf('创建新事件') != -1) {
            foundCreatingEventTab = true;
            break;
        }
    }
    if (foundCreatingEventTab) {
        isCreatingEvent = true
    } else {
        isCreatingEvent = false
    }
}
/** 打开事件详情 */
function openEventInfoTab() {
    //获得当前选中的行
    //获得当前选中的事件的列表的当前行，datagrid的行
    var rows = $('#dg-event-list').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条事件进行查看!', 'error');
        return;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条事件进行查看!', 'error');
        return;
    }

    //$.messager.alert('提示', '选中的行，事件ID：' + rows[0].eventId + "，任务ID：" + rows[0].processId , 'info');

    var title = "事件详情" + "(" + rows[0].eventId + ")"
    openTab(title, "eventInfo.html?eventId=" + rows[0].eventId, rows[0].eventId);
}

/** 打开公告信息的页面 */
function openNoticeMessage() {

}

/** 查看顶部消息铃铛按钮的详细信息 */
function noticeDetailStation() {
    console.log("尝试打开消息列表页面");
    try {
        console.log("检查main-tabs元素是否存在:", $('#main-tabs').length > 0);
        console.log("检查EasyUI Tabs是否初始化:", $('#main-tabs').data('tabs') !== undefined);
        openTab("通知单列表", "noticeListStation.html"); 
        console.log("openTab调用成功");
    } catch(e) {
        console.error("打开页面失败:", e);
    }
}

/**
 * 根据车辆状态不同，展示不同的颜色
 * @param {any} mobileStatus
 * @param {any} hCondition
 * @param {any} mobileStatusStr 
 */
function getTextByMobileStatus(mobileStatus, hCondition, mobileStatusStr) {
    if (hCondition == '0') {
        if (mobileStatus == '0') {
            return '<span style="color:green;">待命</span>';
        } else if (mobileStatus == "1" || mobileStatus == "2" || mobileStatus == "3" || mobileStatus == "8"
            || mobileStatus == "4" || mobileStatus == "7" || mobileStatus == "6") {
            return '<span style="color:blue;">' + mobileStatusStr + '</span>';
        } else if (mobileStatus == "5") {
            return '<span style="color:red;">' + mobileStatusStr + '</span>'; // 状态为5时，状态中文有可能是 受理中 或 拒绝派车
        } else {
            return '<span style="color:blue;">' + mobileStatusStr + '</span>';
        }
    } else if (hCondition == '1' || hCondition == '2') {
        return '<span style="color:blue;">' + mobileStatusStr + '</span>';
    } else if (hCondition == '3') {
        return '<span style="color:grey;">' + mobileStatusStr + '</span>';
    } else {
        return '<span style="color:blue;">' + mobileStatusStr + '</span>';
    }
}

function openAdmin() {
    gProxy.openAdmin();
}

function timeClickHandler() {
    var from = $(this).parent().children('input:nth-of-type(1)');
    var to = $(this).parent().children('input:nth-of-type(2)');
    var now = new Date();
    var fromTime = new Date();
    var hours = 24;
    if ($(this).hasClass('inAnHour')) {
        hours = 1;
    }
    fromTime.setHours(fromTime.getHours() - hours);
    from.val(fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
    to.val(null);
    var parentId = $(this).parent().attr('id');
    easyUiDateTimeSet();
    //自动搜索
    if (parentId.startWith('eventSch')) {
        if (parentId == "eventSchCre") {
            //清空修改时间的时间框
            $('#modTimeFrom').val('');
            $('#modTimeEnd').val('');
        } else {
            $('#creTimeFrom').val('');
            $('#creTimeEnd').val('');
        }
        searchEvents(page, size);
    }
}

function easyUiDateTimeSet() {
    $('.easyui-datetimebox').each(function () {
        var e = $(this);
        e.datetimebox('setValue', e.val());
    });
}
/** 搜索按钮方法 */
function searchButton() {
    searchEvents()
}
function searchEvents(pageNum, pageSize) {
    try {
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = size;
        }
    } catch (e) {
        pageNum = 1;
        pageSize = size;
    }
    //$('#dg-event-process-pages').datagrid('loading');//打开进度条：打开等待div
    var fromC = $('#creTimeFrom').val();
    var toC = $('#creTimeEnd').val();
    var fromCDate = fromC == undefined || fromC == "" ? null : new Date(fromC).formatString("yyyy-MM-dd hh:mm:ss");
    var toCDate = toC == undefined || toC == "" ? null : new Date(toC).formatString("yyyy-MM-dd hh:mm:ss");

    //调用接口获取出车任务数据
    var getMobileProcessByEventIdParams = {
        "pageNum": pageNum,
        "pageSize": pageSize,
        //"stationId": _stationId,
        "beginTime": fromCDate,
        "endTime": toCDate
    };
    getStationMobileProcessPage(getMobileProcessByEventIdParams,
        function (data) {
            //$('#dg-event-process-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
            //data.content = sortBy(data)
            _mobilesProcessList = data.content;
            if (data.content && data.content[0]) {
                let wornData = $('#dg-event-process-pages').datagrid('getData').rows || []

                //原来选中的行
                let rowsSelected = $('#dg-event-process-pages').datagrid('getSelections')
                //console.log("==========行数：" + rowsSelected.length)
                let eventIdSelected, processIdSelected
                if (rowsSelected[0]) {
                    //console.log("==========原来选中的行，事件id：" + rowsSelected[0].eventId + "，任务id：" + rowsSelected[0].processId)
                    eventIdSelected = rowsSelected[0].eventId
                    processIdSelected = rowsSelected[0].processId
                }
                $('#dg-event-process-pages').datagrid('clearSelections')
                //console.log("==========清除选中后，行数：" + rowsSelected.length)

                //先删除数据（新查询出来的数据比列表里的少）
                if (data.content.length < wornData.length) {
                    //删除多余的几条，从尾部开始删除
                    for (var i = wornData.length - 1; i >= data.content.length; i--) {
                        //console.log("删除事件任务数据：" + i)
                        setTaskList('deleteRow', i)
                    }
                    //console.log("重新当前事件任务数据：" + wornData.length)
                }

                //insertRow / updateRow / deleteRow操作后，wornData的数据量会实时变化

                for (var i = 0; i < data.content.length; i++) {
                    if (data.content.length == wornData.length) {
                        //console.log("更新事件任务数据：" + i)
                        setTaskList('updateRow', i, data.content[i])
                    } else if (data.content.length > wornData.length) {
                        //console.log("插入事件任务数据：" + i)
                        setTaskList('insertRow', i, data.content[i])
                    }
                }

                trigerProcessListRowStyler(data.content.length)

                //回选操作
                if (eventIdSelected) {
                    //console.log("==========回选行，事件id：" + eventIdSelected + "，任务id：" + processIdSelected)
                    for (let j = 0; j < wornData.length; j++) {
                        if (wornData[j].eventId === eventIdSelected && wornData[j].processId === processIdSelected) {
                            //let index = $('#dg-event-process-pages').datagrid('getRowIndex', wornData[j])
                            //$('#dg-event-process-pages').datagrid('selectRow', index)
                            //console.log("==========设置背景色，行数：" + index)

                            $('#dg-event-process-pages').datagrid('selectRow', j)
                            setListColor(j)
                            colorIndex = j;
                            //console.log("==========设置背景色，行数：" + j)

                        }
                    }
                } else {
                    //console.log("==========无需回选行")
                }

            } else {
                //如果没有返回数据则空清空数据
                $('#dg-event-process-pages').datagrid('loadData', { total: 0, rows: [] });
            }
            setPage(data.total || 0, pageNum)
            //回显任务信息的结果
            $('#dg-event-process-pages').datagrid('loaded');//关闭loding进度条；

            checkAlert();
        }, function (e, url, errMsg) {
            //回显任务信息的结果
            $('#dg-event-process-pages').datagrid('loaded');//关闭loding进度条；
            // $.messager.alert('异常', '异常信息：' + errMsg);
        });
}

function trigerProcessListRowStyler(indexNotExists) {
    //console.log("==========插入空白行，用于触发rowStyler：" + indexNotExists)
    setTaskList('insertRow', indexNotExists, {})
    setTaskList('deleteRow', indexNotExists)
}

/**
 * 备注的气泡框
 * @param value
 * @param row
 * @param index
 */
function centerRemarkFormatter(value, row, index) {
    let node = `
                <div id="centerRemarkText${index}" onmouseout="closeAllTips()" onmouseover="reportContentHover(${index})">${row.centerRemark}</div>
              `
    return node
}

function reportContentHover(index) {
    layui.use(['layer'], function () {
        var layer = layui.layer;
        // 创建提示
        layer.tips("<div style='color:black;'>" + _mobilesProcessList[index].centerRemark + "</div>", `#centerRemarkText${index}`, {
            tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
            time: 0, // 提示持续时间，单位为毫秒
        });
    });
}

function closeAllTips() {
    layui.use(['layer'], function () {
        var layer = layui.layer;
        // 关闭所有的 tips 类型的提示框
        layer.closeAll('tips');
    });
}
/**
 * 事件名称换行展示
 * @param value
 * @param row
 * @param index
 */
function eventNameFormatter(value, row, index) {
    return `<div class="wrap-text" style="max-width: 350px; word-wrap: break-word; white-space: normal;">${value}</div>`;
}
/**
 * 地址换行展示
 * @param value
 * @param row
 * @param index
 */
function addressFormatter(value, row, index) {
    return `<div class="wrap-text" style="max-width: 180px; word-wrap: break-word; white-space: normal;">${value}</div>`;
}
/**
 * 查看任务详情
 * @param value
 * @param row
 * @param index
 */
function viewProcessDetailFormatter(value, row, index) {
    return '<a style="color:#007bff;width:120px" href="javascript:void(0);" onclick="viewProcessDetail(\'' + row.processId + '\')">查看详情</a>';
}
// 查看详情点击函数
function viewProcessDetail(processId) {
    showMobileProcessDetailById(processId);
}
//------------------------------------------------------------
function setPage(total, pageNum) {
    var pg = $("#dg-event-process-pages").datagrid("getPager");
    if (pg) {
        $(pg).pagination({
            total,
            pageSize: size,
            pageNumber: pageNum,
            pageList: [10, 20, 30, 40, 50],
            onBeforeRefresh: function (pageNumber, pageSize) {
            },
            onRefresh: function (pageNumber, pageSize) {
            },
            onChangePageSize: function (pageSize) {
                page = 1
                size = pageSize
                searchEvents(page, pageSize);
            },
            onSelectPage: function (pageNumber, pageSize) {
                page = pageNumber
                size = pageSize
                searchEvents(pageNumber, pageSize);
            }
        });
    }
}
/**
 * 
 * @param {any} type insertRow/updateRow/deleteRow
 * @param {any} i
 * @param {any} row
 */
function setTaskList(type, i, row) {
    if (type !== 'deleteRow') {
        $('#dg-event-process-pages').datagrid(type, {
            index: i,  // 索引从0开始
            row: {
                address: row.address,
                callIn: row.callIn,
                callType: row.callType,
                carId: row.carId,
                carName: row.carName,
                centerRemark: row.centerRemark,
                direct: row.direct,
                dispatchDoctor: row.dispatchDoctor,
                dispatchDoctorId: row.dispatchDoctorId,
                dispatchDoctorPhone: row.dispatchDoctorPhone,
                dispatchDriver: row.dispatchDriver,
                dispatchDriverId: row.dispatchDriverId,
                dispatchDriverPhone: row.dispatchDriverPhone,
                dispatchNurse: row.dispatchNurse,
                dispatchNurseId: row.dispatchNurseId,
                dispatchNursePhone: row.dispatchNursePhone,
                dispatchWorker: row.dispatchWorker,
                dispatchWorkerId: row.dispatchWorkerId,
                dispatchWorkerPhone: row.dispatchWorkerPhone,
                eventContact: row.eventContact,
                eventContacter: row.eventContacter,
                eventCreateTime: row.eventCreateTime,
                eventId: row.eventId,
                eventSrc: row.eventSrc,
                eventType: row.eventType,
                isFinishd: row.isFinishd,
                isFinishdStr: row.isFinishdStr,
                lat: row.lat,
                lng: row.lng,
                majorCall: row.majorCall ? row.majorCall.replace("_", " ") : '',
                eventName: row.address + " + " + (row.patientName ? row.patientName : "无名氏") + " + " + (row.majorCall ? row.majorCall.replace("_", " ") : ""),
                mobileProcessCreateTime: row.mobileProcessCreateTime,
                patientAge: row.patientAge,
                patientAmount: row.patientAmount,
                patientCondition: row.capatientConditionrName,
                patientCountry: row.patientCountry,
                patientGender: row.patientGender,
                patientName: row.patientName,
                patientRace: row.patientRace,
                plateNum: row.plateNum,
                plateNumStatus: row.plateNumStatus,
                printNum: row.printNum,
                processId: row.processId,
                seatCode: row.seatCode,
                seatId: row.seatId,
                seatUserId: row.seatUserId,
                seatUserName: row.seatUserName,
                seatUser: row.seatUser,
                stationCode: row.stationCode,
                stationId: row.stationId,
                stationName: row.stationName,
                toStatus: row.toStatus,
                toStatusStr: row.toStatusStr,
                refuseSendCarMsgId: row.refuseSendCarMsgId
            }
        })
    } else {
        $('#dg-event-process-pages').datagrid(type, i)
    }
}
$('#dg-event-process-pages').datagrid({
    //单击事件   
    onClickRow: function (rowIndex, rowData) {

        if (rowData.toStatus == 5) {
            if (rowData.toStatusStr == "拒绝派车") {
                $('#refuseSendCarBtn').hide();
                $('#abortRefuseSendCarBtn').show();
            } else {
                $('#refuseSendCarBtn').show();
                $('#abortRefuseSendCarBtn').hide();
            }
        } else {
            $('#refuseSendCarBtn').hide();
            $('#abortRefuseSendCarBtn').hide();
        }

        var trs = $(this).prev().find('div.datagrid-body').find('tr');
        if (colorIndex !== rowIndex) {
            //当前点击的行和上次选中的行不一样
            if (colorIndex !== null && backgroundColor && trs[colorIndex]) {
                //把上次选中的行的背景色还原
                trs[colorIndex].style.backgroundColor = JSON.parse(JSON.stringify(backgroundColor))
                trs[colorIndex].style.color = '#333'
            }
            //记录当前点击行原有的背景色
            backgroundColor = JSON.parse(JSON.stringify(trs[rowIndex].style.backgroundColor))
            colorIndex = rowIndex
            //设置选中背景色
            trs[rowIndex].style.backgroundColor = '#0074d8'
            trs[rowIndex].style.color = '#ffffff'
        } else {
            //设置选中背景色
            trs[rowIndex].style.backgroundColor = '#0074d8'
            trs[rowIndex].style.color = '#ffffff'
        }
    },
})
/** 设置事件列表背景颜色 */
function setListColor(rowIndex) {
    //设置选中背景色
    var trs = $('#dg-event-process-pages').prev().find('div.datagrid-body').find('tr');
    trs[rowIndex].style.backgroundColor = '#0074d8'
    trs[rowIndex].style.color = '#ffffff'
}

/**
 * 打开拒绝派车弹窗（给任务列表使用的）
 */
function refuseSendCarButton() {
    var rows = $('#dg-event-process-pages').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条调度任务进行拒绝派车操作!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条调度任务进行拒绝派车操作!', 'error');
        return null;
    }

    let canRefuse = rows[0].toStatus == 5 && rows[0].toStatusStr != "拒绝派车";
    if (!canRefuse) {
        $.messager.alert('提示', '状态错误，不可进行拒绝派车操作!', 'error');
        return null;
    }

    refuseProcessReasonWindowShow(rows[0].processId)
}
/**
 * 撤销拒绝派车（给任务列表使用的）
 */
function abortRefuseSendCar() {
    var rows = $('#dg-event-process-pages').datagrid('getSelections');
    var num = rows.length;
    if (num == 0) {
        $.messager.alert('提示', '请选择一条调度任务进行撤销拒绝派车操作!', 'error');
        return null;
    } else if (num > 1) {
        $.messager.alert('提示', '您选择了多条记录,只能选择一条调度任务进行撤销拒绝派车操作!', 'error');
        return null;
    }

    let canAbort = rows[0].toStatus == 5 && rows[0].toStatusStr == "拒绝派车";
    if (!canAbort) {
        $.messager.alert('提示', '状态错误，不可进行撤销拒绝派车操作!', 'error');
        return null;
    }

    refuseProcessCancel(rows[0].refuseSendCarMsgId, rows[0].processId);
}

/** 列表置顶数据排序 */
//function sortBy(data) {
//    let SendOrderObj = {}
//    for (var j = 0; j < data.content.length; j++) {
//        let toStatus = data.content[j].toStatus
//        let res = SendOrder.filter(r => r.code == toStatus)[0]
//        if (res) {
//            if (SendOrderObj[res.name]) {
//                SendOrderObj[res.name].push(data.content[j])
//            } else {
//                SendOrderObj[res.name] = []
//                SendOrderObj[res.name].push(data.content[j])
//            }
//        } else {
//            if (SendOrderObj['status']) {
//                SendOrderObj['status'].push(data.content[j])
//            } else {
//                SendOrderObj['status'] = []
//                SendOrderObj['status'].push(data.content[j])
//            }
//        }
//    }
//    let arr = []
//    for (var y = 0; y < SendOrder.length; y++) {
//        if (SendOrderObj[SendOrder[y].name] && Array.isArray(SendOrderObj[SendOrder[y].name])) {
//            for (var t = 0; t < SendOrderObj[SendOrder[y].name].length; t++) {
//                arr.push(SendOrderObj[SendOrder[y].name][t])
//            }
//        }
//    }
//    return arr
//};
function rowStyler(rowIndex, rowData) {
    switch (rowData.toStatus) {
        case '5':
            return 'background-color:#ff000026;';
        case "6":
            return 'background-color:#0000ff26;';
        case "1":
            return 'background-color:#0000ff26;';
        case "2":
            return 'background-color:#0000ff26;';
        case "8":
            return 'background-color:#0000ff26;';
        case "3":
            return 'background-color:#00800029;';
        case "4":
            return 'background-color:#00800029;';
        case "-1":
            return 'background-color:#acacad29;';
        case "7":
            return 'background-color:#ff000026;';
        default:
            return '';
    }
}
/*function onEventAlert(eventId, carId) {
    $('#start-toay').click();
    turnOnAlert();
    gProxy.playAlert(true);
    var printConfig = null;
    querySysConf('print_config',
        function (data) {
            printConfig = data;
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取打印配置失败，异常信息：' + errMsg);
        });
    showPrint(eventId, carId, printConfig);
}*/

function showLogin() {
    $('#loginPanel').show();
    $('#loginForm').show();
    $('#changePwdForm').hide();
}

function showChangePwd() {
    $('#loginForm').hide();
    $('#changePwdForm').show();
    $('#cp-un').val('');
    $('#cp-pwd').val('');
    $('#cp-newPwd1').val('');
    $('#cp-newPwd2').val('');
}

/** 时间轮询 */
function taskTimesIntervalTimeout() {
    clearTimeout(window.taskTimesInterval);
    if (_taskTimesInterval) {
        window.taskTimesInterval = null;
        //更新当前时间
        var now = new Date();
        $('#cur-date-time').html(now.formatString('yyyy-MM-dd hh:mm:ss'));
        //暂停响铃倒计时
        if (_alertTimerPauseTime >= 0) {
            _alertTimerPauseTime = _alertTimerPauseTime - 1;
            $('#alert-pause-text').html("暂停响铃" + _alertTimerPauseTime + "秒");
        }
        //一定要1秒一次
        window.taskTimesInterval = setTimeout(taskTimesIntervalTimeout, 1000);
    }
}

/** 事件搜索轮询 */
function eventSearchIntervalTimeout() {
    clearTimeout(window.eventSearchInterval);
    if (_taskTimesInterval) {
        window.eventSearchInterval = null;
        if (_token != "") {
            //刷新车辆列表
            refreshMobileStatus();
        }
        window.eventSearchInterval = setTimeout(eventSearchIntervalTimeout, 5000);

    }
}

/** 分站心跳轮询 */
function taskStationHeartIntervalTimeout() {
    clearTimeout(window.taskStationHeartInterval);
    if (_taskTimesInterval) {
        window.taskStationHeartInterval = null;
        stationHeart(_stationId,
            function (res) {
            },
            function (e, url, errMsg) {
                //$.messager.alert('提示', '分站心跳，异常信息：' + errMsg);
            });

        window.taskStationHeartInterval = setTimeout(taskStationHeartIntervalTimeout, 8000);
    }
}

function loginSuccess() {
    try{
    _user = evalJson(gProxy.getUserInfo());
    _token = _user.Token;
    _stationInfo = evalJson(gProxy.getStationInfo());
    } catch (e) {
        //获取分站信息
        getStationInfoById(_stationId, 
            function(data) {
                _stationInfo = data;
                _stationInfo.StationCode = data.stationCode;//为了兼容C#旧代码
            },
            function(e, url, errMsg) {
                console.error('获取分站信息失败:', errMsg);
            }
        );
    }
    //console.log("登录成功，用户信息：" + JSON.stringify(_user) + ";站点信息：" + JSON.stringify(_stationInfo));
    setUI();
    //获取配置
    /*querySysConfSync('ASSIGN_TASK_MODE',
        function (data) {
            if (data) {
                _curAssignTaskMode = data;
            }
        });*/

    getNoticeAlert();

    //获取分站信息
    getStationById(_stationId,
        function (data) {
            _user.Station = data;
            $('#dispatcher-name').html(_user.DisplayName);
            $('#dispatcher-code').html(_user.Username);
            $('#station-name').html(data.stationName);
            _currentLoc = {
                "lng": data.stationLongitude,
                "lat": data.stationLatitude
            };
            $('#loginPanel').hide();
            showProcess(false);
            $('#start-toay').click();
        });
    
    //出车医生输入自动搜索
    $('#dispatchDoctor').combobox({
        prompt: '输入医生工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车护士输入自动搜索
    $('#dispatchNurse').combobox({
        prompt: '输入护士工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车司机输入自动搜索
    $('#dispatchDriver').combobox({
        prompt: '输入司机工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });
    //出车护工输入自动搜索
    $('#dispatchWorker').combobox({
        prompt: '输入护工工号和姓名',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });

    //获得出车班次
    $('#dispatchClasses').combobox({
        prompt: '请选择出车班次',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        },
        onSelect: function (record) {

            //找到当前的班次的出车医生护士等自动填写进去
            dispatchClassesSelect = dispatchClassesList.find((item) => item.id === record.id);
            var dispatchDoctorDatas = $('#dispatchDoctor').combobox('getData');
            for (var i = 0; i < dispatchDoctorDatas.length; i++) {
                if (dispatchDoctorDatas[i].id == dispatchClassesSelect.dispatchDoctorId) {
                    $('#dispatchDoctor').combobox('select', dispatchClassesSelect.dispatchDoctorId);
                    break;
                }
            }

            var dispatchNurseDatas = $('#dispatchNurse').combobox('getData');
            for (var i = 0; i < dispatchNurseDatas.length; i++) {
                if (dispatchNurseDatas[i].id == dispatchClassesSelect.dispatchNurseId) {
                    $('#dispatchNurse').combobox('select', dispatchClassesSelect.dispatchNurseId);
                    break;
                }
            }

            var dispatchDriverDatas = $('#dispatchDriver').combobox('getData');
            for (var i = 0; i < dispatchDriverDatas.length; i++) {
                if (dispatchDriverDatas[i].id == dispatchClassesSelect.dispatchDriverId) {
                    $('#dispatchDriver').combobox('select', dispatchClassesSelect.dispatchDriverId);
                    break;
                }
            }

            var dispatchWorkerDatas = $('#dispatchWorker').combobox('getData');
            for (var i = 0; i < dispatchWorkerDatas.length; i++) {
                if (dispatchWorkerDatas[i].id == dispatchClassesSelect.dispatchWorkerId) {
                    $('#dispatchWorker').combobox('select', dispatchClassesSelect.dispatchWorkerId);
                    break;
                }
            }

        },
        onChange: function (newValue, oldValue) {
        }

    });

    //出车车辆输入自动搜索
    $('#chooseCar').combobox({
        prompt: '输入车辆编号和车牌',
        editable: true,
        hasDownArrow: true,
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = $(this).combobox('options');
            return row[opts.textField].indexOf(q) > -1;
        }
    });

    //分站心跳
    taskStationHeartIntervalTimeout();
    taskTimesIntervalTimeout();
    eventSearchIntervalTimeout();

    //分站上线
    stationLogin(_stationId,
        function (res) {
        },
        function (e, url, errMsg) {
            $.messager.alert('提示', '分站上线失败，异常信息：' + errMsg);
        });

    //为所有快速时间操作设置处理函数
    $('.inAnHour,.inToday').on("click", timeClickHandler);

    $('#dg-event-process-pages').datagrid({
        loadMsg: '数据加载，请稍等.....',
        //data: searchEvents(), //这一步是加载ajax查询的数据，非常重要
        onDblClickRow: function (rowIndex, rowData) {//鼠标双击事件
            confirmAcceptProcess(rowData.processId, rowData.toStatus)
        }
    });
}

/**
 * 确认接收任务，状态从5改为7
 * @param {any} processId
 * @param {any} toStatus
 */
function confirmAcceptProcess(processId, toStatus) {
    if (toStatus == "5") {
        $.messager.confirm('',
            '确认接收任务？',
            function (r) {
                if (r) {
                    var params = {
                        "id": processId
                    }
                    //分站端接收任务，状态从5改为7
                    acceptProcess(params, function (res) {
                        //刷新任务列表
                        searchEvents()
                    }, function (e, url, errMsg) {
                        $.messager.alert('异常', '异常信息：' + errMsg);
                    })
                }
            });
    }
}

function closeDiagle() {
    //清理数据
    $('#chooseCar').combobox('clear');
    $('#dispatchClasses').combobox('clear');
    $('#dispatchDoctor').combobox('clear');
    $('#dispatchNurse').combobox('clear');
    $('#dispatchDriver').combobox('clear');
    $('#dispatchWorker').combobox('clear');
    $('#eventDetailRegion').hide();
}

/** 更新出车人员班次选择框列表 */
function reloadDispatch(processDetail) {
    // 获得分站班次
    debugger;
    var params = { "substationcode": _stationInfo.StationCode }
    getDispatchClassesByStation(params,
        function (docs) {
            dispatchClassesList = docs;
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonArr.push({ 'id': docs[i].id, 'name': docs[i].scheduleName });
            }
            if (jsonArr.length == 0) {
                //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                $('#dispatchClasses').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchClasses').combobox('loadData', jsonArr);
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得排班信息，异常信息：' + errMsg);
        });

    //获取医生1001
    params = { "substationcode": _stationInfo.StationCode, "page": "1", "hospitalDutyCode": "1001", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            $('#dispatchDoctor').combobox('select', '');
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonArr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (_currentProcess.dispatchDoctorId != null && _currentProcess.dispatchDoctorId != "") {
                //加入当前出车医生护士到对应的框中
                var currentData = {
                    'id': _currentProcess.dispatchDoctorId,
                    'name': _currentProcess.dispatchDoctorNum + "-" + _currentProcess.dispatchDoctor,
                    'phone': _currentProcess.dispatchDoctorPhone,
                    'username': _currentProcess.dispatchDoctorNum,//工号
                    'xm': _currentProcess.dispatchDoctor,
                }
                jsonArr.push(currentData);
            }
            if (jsonArr.length == 0) {
                $('#dispatchDoctor').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchDoctor').combobox('loadData', jsonArr);
                if (_currentProcess.dispatchDoctorId != null && _currentProcess.dispatchDoctorId != "") {
                    $('#dispatchDoctor').combobox('select', _currentProcess.dispatchDoctorId);
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得医生信息，异常信息：' + errMsg);
        });

    //获取护士1002
    params = { "substationcode": _stationInfo.StationCode, "page": "1", "hospitalDutyCode": "1002", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            $('#dispatchNurse').combobox('select', '');
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonArr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (_currentProcess.dispatchNurseId != null && _currentProcess.dispatchNurseId != "") {
                //加入当前出车医生护士到对应的框中
                var currentData = {
                    'id': _currentProcess.dispatchNurseId,
                    'name': _currentProcess.dispatchNurseNum + "-" + _currentProcess.dispatchNurse,
                    'phone': _currentProcess.dispatchNursePhone,
                    'username': _currentProcess.dispatchNurseNum,//工号
                    'xm': _currentProcess.dispatchNurse,
                }
                jsonArr.push(currentData);
                
            }
            if (jsonArr.length == 0) {
                //$('#dispatchDoctor').combobox('clear'); //清除已选中数据
                $('#dispatchNurse').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchNurse').combobox('loadData', jsonArr);
                if (_currentProcess.dispatchNurseId != null && _currentProcess.dispatchNurseId != "") {
                    $('#dispatchNurse').combobox('select', _currentProcess.dispatchNurseId);
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得护士信息，异常信息：' + errMsg);
        });

    //获取司机1003
    params = { "substationcode": _stationInfo.StationCode, "page": "1", "hospitalDutyCode": "1003", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            $('#dispatchDriver').combobox('select', '');
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonArr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (_currentProcess.dispatchDriverId != null && _currentProcess.dispatchDriverId != "") {
                //加入当前出车医生护士到对应的框中
                var currentDoctorData = {
                    'id': _currentProcess.dispatchDriverId,
                    'name': _currentProcess.dispatchDriverNum + "-" + _currentProcess.dispatchDriver,
                    'phone': _currentProcess.dispatchDriverPhone,
                    'username': _currentProcess.dispatchDriverNum,//工号
                    'xm': _currentProcess.dispatchDriver,
                }
                jsonArr.push(currentDoctorData);
            }
            if (jsonArr.length == 0) {
                $('#dispatchDriver').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchDriver').combobox('loadData', jsonArr);
                if (_currentProcess.dispatchDriverId != null && _currentProcess.dispatchDriverId != "") {
                    $('#dispatchDriver').combobox('select', _currentProcess.dispatchDriverId);
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得司机信息，异常信息：' + errMsg);
        });

    //获取工人1004
    params = { "substationcode": _stationInfo.StationCode, "page": "1", "hospitalDutyCode": "1004", "rows": "999" }
    getPeopleByStation(params,
        function (docs) {
            $('#dispatchWorker').combobox('select', '');
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                jsonArr.push({
                    'id': docs[i].id,
                    'name': docs[i].userName + "-" + docs[i].name,
                    'phone': docs[i].legalContactPhone,
                    'username': docs[i].userName,
                    'xm': docs[i].name,
                });
            }
            if (_currentProcess.dispatchWorkerId != null && _currentProcess.dispatchWorkerId != "") {
                //加入当前出车医生护士到对应的框中
                var currentData = {
                    'id': _currentProcess.dispatchWorkerId,
                    'name': _currentProcess.dispatchWorkerNum + "-" + _currentProcess.dispatchWorker,
                    'phone': _currentProcess.dispatchWorkerPhone,
                    'username': _currentProcess.dispatchWorkerNum,//工号
                    'xm': _currentProcess.dispatchWorker,
                }
                jsonArr.push(currentData);
            }
            if (jsonArr.length == 0) {
                $('#dispatchWorker').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                $('#dispatchWorker').combobox('loadData', jsonArr);
                if (_currentProcess.dispatchWorkerId != null && _currentProcess.dispatchWorkerId != "") {
                    $('#dispatchWorker').combobox('select', _currentProcess.dispatchWorkerId);
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得工人信息，异常信息：' + errMsg);
        });

    //获取车辆列表
    refreshMobiles(_stationId,
        function (docs) {
            $('#chooseCar').combobox('select', '');
            var jsonArr = [];
            for (var i = 0; i < docs.length; i++) {
                // 如果车辆状态正常，站内待命，则加入到下拉框中
                if (docs[i].hCondition == '0' && docs[i].status == '0'){
                    jsonArr.push({
                        'id': docs[i].id,
                        'name': docs[i].carName,
                    });
                }
            }
            if (_currentProcess.carId != null && _currentProcess.carName != "") {
                //数据回显
                var currentData = {
                    'id': _currentProcess.carId,
                    'name': _currentProcess.carName,
                }
                jsonArr.push(currentData);
            }
            if (jsonArr.length == 0) {
                $('#chooseCar').combobox('loadData', []);//重新本地加载一个空数据
            } else {
                if (_currentProcess.toStatus != "5" && _currentProcess.toStatus !== '7') {
                    //加入当前车辆到对应的框中
                    var currentData = {
                        'id': _currentProcess.carId,
                        'name': _currentProcess.carName,
                    }
                    if (_currentProcess.carId != null && _currentProcess.carId != "") {
                        jsonArr.push(currentData);
                    }
                }
                $('#chooseCar').combobox('loadData', jsonArr);
                if (_currentProcess.carId) {
                    $('#chooseCar').combobox('select', _currentProcess.carId);
                    $('#chooseCar').combobox('disable');
                } else {
                    $('#chooseCar').combobox('enable'); // 如果没有 carId，确保下拉框可用
                }
            }
        }, function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('提示', '获得车辆信息，异常信息：' + errMsg);
        })

}

function changePwdSuccess() {
    $.messager.alert('修改密码', '密码修改成功！');
    showLogin();
}

/** 登录 */
var isClick = false;//防止多次点击登录按钮
function doLogin() {
    if (!isClick) {
        isClick = true;
        var isSavePsw = "0";
        if ($('#is-save-psw').is(':checked')) {
            isSavePsw = "1";
        }
       
        let un = $('#login-un').val();
        let pwd = $('#login-pwd').val();

        if (!un) {
            $.messager.alert('提示', '账号/工号不能为空');
            isClick = false;
            return
        }
        if (!pwd) {
            $.messager.alert('提示', '密码不能为空');
            isClick = false;
            return
        }

        showProcess(true, "正在登录...");

        try{
        login(un, pwd, isSavePsw,
            function (res) {
                if (res.success == true) {
                    loginSuccess();
                } else {
                    showProcess(false);
                    $.messager.alert('提示', '登录失败', res.msg);
                }
            });
        } catch (e) {
            console.log("B/S模式用户登录执行");
            // 将选中的分站ID保存到全局变量
            // let stationId = $('#login-station').val();
            // if (!stationId) {
            //     $.messager.alert('提示', '请选择分站');
            //     isClick = false;
            //     showProcess(false, "正在登录...");
            //     return false;
            // }
            // _stationId = stationId;
            //记住密码
            //如果是记住密码，那么保存当前登录的用户和密码
            let params = {
                un: encryptByDES($('#login-un').val()),
                pwd: encryptByDES($('#login-pwd').val()),
                savePsw: isSavePsw,//记住密码
                seatId: null,//座席ID，登录的时候web端选择座席记录的seatId
                scheduleTimeConfigId: null,//班次id
                // stationId: stationId//分站ID
            }
            loginIn(params, function (res) {
                if(res.station){
                    _stationId = res.station.id;
                }else{
                    $.messager.alert('提示', '账号密码不属于该分站，请重新选择分站');
                    isClick = false;
                    showProcess(false, "正在登录...");
                    return false;
                }
                _user = {
                    AccType: res.accType,
                    Contact: res.contact,
                    DisplayName: res.displayName,
                    LoginPlace: res.loginPlace,
                    Station: res.station,
                    Token: res.token,
                    Id: res.userId,
                    Username: res.username,
                    ScheduleTimeConfig: null
                };
                if (res.scheduleTimeConfig != null) {
                    _user.ScheduleTimeConfig = {
                        Id: res.scheduleTimeConfig.id,
                        ScheduleName: res.scheduleTimeConfig.scheduleName,
                        //StartTime: res.scheduleTimeConfig.startTime,
                        //EndTime: res.scheduleTimeConfig.endTime,
                        NextId: res.scheduleTimeConfig.nextId,
                        NextScheduleName: res.scheduleTimeConfig.nextScheduleName,
                        //NextStartTime: res.scheduleTimeConfig.nextStartTime,
                        //NextEndTime: res.scheduleTimeConfig.nextEndTime
                    };
                }
                _token = _user.Token;
                //存储到localStorage，方便其他页面进行调用
                window.localStorage.setItem("_token", _user.Token);
                //存储到localStorage，方便其他页面进行调用
                window.localStorage.setItem("_user", JSON.stringify(_user));
                if (isSavePsw == "1") {
                    //将密码保存到localStorage中
                    window.localStorage.setItem('_lastLoginUser', "{\"Username\":\"" + $('#login-un').val() + "\",\"Password\":\"" + $('#login-pwd').val() + "\",\"SavePsw\": true,\"DisplayName\": null}");
                } else {
                    //清空localStorage里面的 saveUserCache 字段
                    window.localStorage.removeItem('_lastLoginUser');
                }
                loginSuccess();
            }, function (e, url, errMsg) {
                showProcess(false);
                //失败才做处理
                $.messager.alert('提示', '登录失败：' + errMsg);
            })
        }
        isClick = false
        return false;
    }
}

function doChangePwd() {
    let un = $('#cp-un').val();
    let pwd = $('#cp-pwd').val();
    let newPwd1 = $('#cp-newPwd1').val();
    let newPwd2 = $('#cp-newPwd2').val();

    if (!un) {
        $.messager.alert('提示', '账号/工号不能为空');
        return false
    }
    if (!pwd) {
        $.messager.alert('提示', '旧密码不能为空');
        return false
    }
    if (!newPwd1) {
        $.messager.alert('提示', '新密码不能为空');
        return false
    }

    if (pwd == newPwd1) {
        $.messager.alert('提示', '新旧密码不能相同');
        return false
    }

    if (!newPwd2) {
        $.messager.alert('提示', '确认密码不能为空');
        return false
    }

    if (newPwd1 != newPwd2) {
        $.messager.alert('修改密码', '新密码两次输入不同！');
        return false;
    }

    var res = gProxy.changePwd(un, pwd, newPwd1);
    if (res == true) {
        changePwdSuccess();
    } else {
        $.messager.alert('修改密码', res);
    }

    return false;
}

function doLogout() {
    $.messager.confirm('注销登录',
        '确定要注销吗？',
        function (s) {
            if (s == true) {
                var res = gProxy.logout();
                if (res == true) {
                    $('#login-un').val('');
                    $('#login-pwd').val('');
                    _token = null;
                    showLogin();
                } else {
                    $.messager.alert('注销登录', res);
                }
            }
        });
}

/** 通过当前任务id获得事件信息，并打开任务详情页面 */
function showMobileProcessDetailById(processId) {
    if (processId != null && processId != "") {
        //获取事件任务信息
        var getMobileProcessByEventIdParams = {
            "eventId": '',
            "beginTime": '',
            "endTime": '',
            "stationId": '',
            "processId": processId,
            "stationCode": ''
        };
        getMobileProcessByEventId(getMobileProcessByEventIdParams, function (data) {
            _currentProcess = data[0];
            /*if (_currentProcess.toStatus == '5') {
                //if (noBell.indexOf(processId) == -1) {
                    //noBell.push(processId)
                //
                var params = {
                    "id": _currentProcess.processId
                }
                //分站端接收任务，状态从5改为7
                acceptProcess(params, function (res) {
                    showMobileProcessDetail(_currentProcess);
                }, function (e, url, errMsg) {
                    $.messager.alert('异常', '异常信息：' + errMsg);
                })
            } else {
                showMobileProcessDetail(_currentProcess);
            }*/
            showMobileProcessDetail(_currentProcess);
        }, function (e, url, errMsg) {
            $.messager.alert('异常', '异常信息：' + errMsg);
        })
    }
}

/** 打开任务详情页面 */
function showMobileProcessDetail(processDetail) {
    _currentProcess = processDetail;
    if (processDetail.toStatus == "5" || processDetail.toStatus == "7") {
        $('#confirmReceiveSubmitBtn').show();
        $('#updateDispatchPeopleBtn').hide();
    } else {
        $('#confirmReceiveSubmitBtn').hide();
        $('#updateDispatchPeopleBtn').show();
    }
    reloadDispatch(processDetail);
    //if (processDetail.toStatus != "5" && processDetail.toStatus != "7") {
    //    $('#confirm-receive-button').hide();
    //    $('#update-process-button').show();
    //} else {
    //    $('#confirm-receive-button').show();
    //    $('#update-process-button').hide();
    //}
    clearAllDetails();

    $('#event-id').val(processDetail.eventId);
    $('#process-id').val(processDetail.processId);
    $('#dispatch-doctor').val(processDetail.dispatchDoctor);
    $('#dispatch-doctorId').val(processDetail.dispatchDoctorId);
    $('#dispatch-doctorPhone').val(processDetail.dispatchDoctorPhone);
    $('#dispatch-driver').val(processDetail.dispatchDriver);
    $('#dispatch-driverId').val(processDetail.dispatchDriverId);
    $('#dispatch-driverPhone').val(processDetail.dispatchDriverPhone);
    $('#dispatch-nurse').val(processDetail.dispatchNurse);
    $('#dispatch-nurseId').val(processDetail.dispatchNurseId);
    $('#dispatch-nursePhone').val(processDetail.dispatchNursePhone);
    $('#dispatch-worker').val(processDetail.dispatchWorker);
    $('#dispatch-workerId').val(processDetail.dispatchWorkerId);
    $('#dispatch-workerPhone').val(processDetail.dispatchWorkerPhone);

    $('#evd-source').val(processDetail.eventSrc);
    $('#evd-call-type').val(processDetail.callType);
    //$('#det-condition').val(processDetail.patientCondition);
    $('#det-gender').val(processDetail.patientGender);
    $('#det-country').val(processDetail.patientCountry);

    $('#offline-mode').hide();

    $('#det-amount').val(processDetail.patientAmount);
    $('#det-address').val(processDetail.address);
    $('#det-major').val(processDetail.majorCall.replace("_", " "));
    $('#det-callin').val(processDetail.callIn);
    $('#det-contact').val(processDetail.eventContact);
    $('#det-contacter').val(processDetail.eventContacter);
    $('#det-120remark').val(processDetail.centerRemark);
    $('#det-name').val(processDetail.patientName);
    $('#det-age').val(processDetail.patientAge);
    $('#det-race').val(processDetail.patientRace);
    if (processDetail.patientIsForeign == 1) {
        $("#det-Foreign").prop("checked", true);
    } else {
        $("#det-Foreign").prop("checked", false);
    }

    $('#dispatch-chooseCar').val(processDetail.carName);
    $('#eventDetailRegion').show();
    showMobileProcessItems(processDetail.processId);
}
function showMobileProcessItems(processId) {
    //显示车辆详细信息
    getMobileProcessItemsByProcessId(processId,
        function (data) {
            for (var j = 0; j < data.length; j++) {
                var d = data[j];
                var cDateStr = new Date(d.times).formatString('yyyy-MM-dd hh:mm:ss');
                var add =
                    `<tr>
                        <td style="width: 30%;">${cDateStr}</td>
                        <td style="width: 20%;">${d.toStatusStr}</td>
                        <td style="width: 50%;">${d.remark == null ? "" : d.remark}</td>
                    </tr>`;
                $('#dis-cars-body').append(add);
            }
        });
}

function getMobileFullInfo(mobileId) {
    var ret = _mobiles.first(function (m) { return m.id == mobileId; });
    return ret;
}

function clearAllDetails() {
    $('#process-id').val('');
    $('#dispatch-doctor').val('');
    $('#dispatch-doctorId').val('');
    $('#dispatch-doctorPhone').val('');
    $('#dispatch-driver').val('');
    $('#dispatch-driverId').val('');
    $('#dispatch-driverPhone').val('');
    $('#dispatch-nurse').val('');
    $('#dispatch-nurseId').val('');
    $('#dispatch-nursePhone').val('');
    $('#dispatch-worker').val('');
    $('#dispatch-workerId').val('');
    $('#dispatch-workerPhone').val('');

    $('#evd-source').prop('selectedIndex', 0);
    $('#evd-call-type').prop('selectedIndex', 0);
    //$('#det-condition').prop('selectedIndex', 0);
    $('#det-gender').prop('selectedIndex', 0);
    $('#det-country').prop('selectedIndex', 0);
    $('#det-race').prop('selectedIndex', 0);
    $("#det-Foreign").prop("checked", false);

    $('#offline-mode').hide();

    $('#det-amount').val(1);
    $('#det-address').val('');
    $('#det-major').val('');
    $('#det-callin').val('');
    $('#det-contact').val('');
    $('#det-contacter').val('');
    $('#det-120remark').val('');
    $('#det-name').val('');
    $('#det-age').val('');
    $('#det-stationRemark').val('');

    $('#region_all').mouseup();
    $('#dis-selectedStation').empty();
    $('#dis-stationStatus').empty();
    $('#dis-mobileStatus').empty();
    $('#dis-cars-body').empty();
    $('#event-calls-body').empty();
}

function addNewMobile() {
    $('#mobile-editor').window('setTitle', '新增车辆');
    $('#mobile-editor').window('open');
}

function editMobile(id) {
    $('#mobile-editor').window('setTitle', '修改车辆');
    $('#mobile-editor').window('open');
    $('#med-submit-btn').val('修改');
}

function deleteMobile(id) {
    $.messager.confirm('删除确认',
        '确定删除这条车辆记录吗？',
        function (res) {
            if (res) {
                //TODO:
            }
        });
}

/** 打印 */
function showPrintPaper() {
    var eventId = _currentProcess.eventId;//事件id
    var carId = _currentProcess.carId;//车辆id
    //打印急救单配置值
    //查询配置：1-打印原来的急救单；2-打印一式两份的急救单
    querySysConf('print_config',
        function (data) {
            var printConfig = data;
            if (printConfig == '1') {
                $('#print-zone-one').show();
                $('#print-zone-two').hide();
            } else if (printConfig == '2') {
                $('#print-zone-two').show();
                $('#print-zone-one').hide();
            }
            $('#printConfig').val(printConfig);
            showPrint(eventId, carId, printConfig);
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取打印配置失败，异常信息：' + errMsg);
        });
}

function formatterCallType(callType) {
    let obj = callTypeList.filter(r => r.codeName == callType)[0]
    if (obj) {
        return obj.codeVale
    }
    return ''
}

/** 打印 */
function showPrint(id, carId, printConfig) {
    if (printConfig == '1') {
        getInfoForPrint({
            eventId: _currentProcess.eventId,
            processId: _currentProcess.processId
        }, function (data) {
            $('#prt-dispatchDoctor').html(data.pMobileProcess.dispatchDoctor);
            $('#prt-dispatchNurse').html(data.pMobileProcess.dispatchNurse);
            $('#prt-dispatchDriver').html(data.pMobileProcess.dispatchDriver);
        }, function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "事件查询失败：" + errMsg, 'error');
        });
        getEventDetailById(id,
            function (data) {
                $('#print-prev').window('open');
                var carInfo = getMobileFullInfo(carId);
                $('#prt-stationName').html("120派车任务单" + " " + _user.Station.stationName);
                $('#prt-createTime').html(data.createTime);
                $('#prt-dispatcher').html(data.createUserName);
                $('#prt-call-type').html(formatterCallType(data.callType));
                $('#prt-id').html(data.id);
                $('#prt-major').html(data.majorCall.replace("_", " "));
                $('#prt-car').html(_user.Station.stationName + '·' + carInfo.carName + ' [' + carInfo.plateNum + ']');
                $('#prt-addr').html(data.address);
                $('#prt-contacter').html(data.contacter);
                //$('#prt-lng').html();
                //$('#prt-lat').html();
                $('#prt-callIn').html(data.callIn);
                $('#prt-contact').html(data.contact);
                $('#prt-name').html(data.patientName);
                $('#prt-gender').html(data.patientGender == '0' ? '男' : data.patientGender == '1' ? '女' : data.patientGender == '2' ? '未知' : '');
                $('#prt-amount').html(data.patientAmount);
                $('#prt-age').html(data.patientAge);
                $('#prt-country').html(getDicValue('country', data.patientCountry, _dics));
                $('#prt-race').html(getDicValue('race', data.patientRace, _dics));
                //$('#prt-condition').html(getDicValue('patient_status', data.patientCondition, _dics));
                $('#prt-remark').html(data.centerRemark);
            }, function (e, url, errMsg) {
                showProcess(false);
                $.messager.alert('提示', "事件查询失败：" + errMsg, 'error');
            });
    } else if (printConfig == '2') {
        getInfoForPrint({
            eventId: _currentProcess.eventId,
            processId: _currentProcess.processId
        }, function (data) {
            $('#print-prev').window('open');
            var carInfo = getMobileFullInfo(carId);
            $('#prt-dispatchDriver2').html(data.pMobileProcess.dispatchDriver);//司机
            $('#prt-dispatchDoctor2').html(data.pMobileProcess.dispatchDoctor);//医生
            $('#prt-dispatchNurse2').html(data.pMobileProcess.dispatchNurse);//护士
            $('#prt-dispatchWorker2').html(data.pMobileProcess.dispatchWorker);//担架工
            $('#prt-receiptTime').html(data.pMobileProcess.createTime);//收单时刻
            $('#prt-flowNo').html(data.pMobileProcess.id);//流水号
            $('#prt-seatUser').html(data.pMobileProcess.seatUser);//派车人员
            $('#prt-eventId').html(data.pEvent.id);//事件编号
            if (data.pMobile) {
                $('#prt-plateNum').html(data.pMobile.plateNum);//车牌号码
            }
            $('#prt-dispatchMiles').html(data.pMobileProcess.dispatchMiles);//里程
            $('#prt-stationName2').html("120派车任务单" + " " + _user.Station.stationName);

            $('#prt-patientName').html(data.pEvent.patientName);//患者姓名
            $('#prt-patientGender').html(data.pEvent.patientGender == '0' ? '男' : data.pEvent.patientGender == '1' ? '女' : data.pEvent.patientGender == '2' ? '未知' : '');//性别
            $('#prt-patientAge').html(data.pEvent.patientAge);//年龄
            $('#prt-dispatchType').html(formatterCallType(data.pEvent.callType));//派车类型

            $('#prt-callIn2').html(data.pEvent.callIn);//呼救电话
            $('#prt-contact2').html(data.pEvent.contact);//联系电话

            $('#prt-majorCall').html(data.pEvent.majorCall);//呼救原因

            $('#prt-address').html(data.pEvent.address);//现场地址

            // $('#prt-destination').html();//送往地址

            $('#prt-remark2').html(data.pEvent.centerRemark);//备注

            //一式两份，深克隆一份表
            var clonedTable = $('#print-zone-two').clone(true); // 复制原始表格
            clonedTable.insertAfter($('#print-zone-two')); // 插入复制的表格在原始表格后面
            clonedTable.addClass("clonedTable"); // 添加一个新的类
            $('.clonedTable').css('margin-top', '60px');

        }, function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "任务查询失败：" + errMsg, 'error');
        });
    }
}

/** 初始化有关一式两份出车单 组件时绑定事件 */
$('#print-prev').window({
    onBeforeClose: function () {
        // 这个回调会在窗口关闭前触发，可以在此处阻止窗口关闭
        $('#prt-dispatchDriver2').html('');//司机
        $('#prt-dispatchDoctor2').html('');//医生
        $('#prt-dispatchNurse2').html('');//护士
        $('#prt-dispatchWorker2').html('');//担架工
        $('#prt-receiptTime').html('');//收单时刻
        $('#prt-flowNo').html('');//流水号
        $('#prt-seatUser').html('');//派车人员
        $('#prt-eventId').html('');//事件编号
        $('#prt-plateNum').html('');//车牌号码
        $('#prt-dispatchMiles').html('');//里程
        $('#prt-stationName2').html('');

        $('#prt-patientName').html('');//患者姓名
        $('#prt-patientGender').html('');//性别
        $('#prt-patientAge').html('');//年龄
        $('#prt-dispatchType').html('');//派车类型

        $('#prt-callIn2').html('');//呼救电话
        $('#prt-contact2').html('');//联系电话

        $('#prt-majorCall').html('');//呼救原因

        $('#prt-address').html('');//现场地址

        $('#prt-destination').html('');//送往地址

        $('#prt-remark2').html('');//备注
        // 获取并删除所有带有 "clonedTable" 类的元素（在这个例子中是克隆的表格）
        $('.clonedTable').remove();
        return true; // 返回 true 表示允许关闭，返回 false 则阻止关闭
    },
    onClose: function () {
        // 这个回调会在窗口关闭后触发
    }
});

/** 打开确认接收弹窗 */
/*function confirmReceiveShow(rowData) {
    //清理下拉选择框
    $('#dispatchClasses').combobox('clear');
    $('#dispatchDoctor').combobox('clear');
    $('#dispatchNurse').combobox('clear');
    $('#dispatchDriver').combobox('clear');
    $('#dispatchWorker').combobox('clear');
    if (rowData.toStatus == "5" || rowData.toStatus == "7") {
        $('#confirmReceiveSubmitBtn').html("确认接收（派车）");
    } else {
        $('#confirmReceiveSubmitBtn').html("修改出车人员");
    }
    //获取相关医生护士信息
    reloadDispatch(rowData);
    //事件ID
    $('#dispatchEventId').val(rowData.eventId);
    //车辆ID
    $('#dispatchCarId').val(rowData.carId);
    //车辆事件ID
    $('#mobileProcessId').val(rowData.processId);
    $('#eventDetailRegion').show();
}*/

/** 确认出车或修改出车人员 */
function confirmReceiveSubmit() {
    var mobileProcessId = $('#mobileProcessId').val();
    var carId = $('#chooseCar').combobox('getValue');
    var eventId = $('#dispatchEventId').val();

    //医生
    var dispatchDoctor = "";
    var dispatchDoctorNum = "";
    var dispatchDoctorPhone = "";
    var dispatchDoctorId = $('#dispatchDoctor').combobox('getValue');
    var dispatchDoctorDatas = $('#dispatchDoctor').combobox('getData');
    for (var i = 0; i < dispatchDoctorDatas.length; i++) {
        if (dispatchDoctorId == dispatchDoctorDatas[i].id) {
            dispatchDoctor = dispatchDoctorDatas[i].xm;
            dispatchDoctorNum = dispatchDoctorDatas[i].username;
            dispatchDoctorPhone = dispatchDoctorDatas[i].phone == null ? "" : dispatchDoctorDatas[i].phone;
            break;
        }
    }

    //护士
    var dispatchNurse = "";
    var dispatchNursePhone = "";
    var dispatchNurseNum = "";
    var dispatchNurseId = $('#dispatchNurse').combobox('getValue');
    var dispatchNurseDatas = $('#dispatchNurse').combobox('getData');
    for (var i = 0; i < dispatchNurseDatas.length; i++) {
        if (dispatchNurseId == dispatchNurseDatas[i].id) {
            dispatchNurse = dispatchNurseDatas[i].xm;
            dispatchNurseNum = dispatchNurseDatas[i].username;
            dispatchNursePhone = dispatchNurseDatas[i].phone == null ? "" : dispatchNurseDatas[i].phone;
            break;
        }
    }

    //护工
    var dispatchWorker = "";
    var dispatchWorkerNum = "";
    var dispatchWorkerPhone = "";
    var dispatchWorkerId = $('#dispatchWorker').combobox('getValue');
    var dispatchWorkerDatas = $('#dispatchWorker').combobox('getData');
    for (var i = 0; i < dispatchWorkerDatas.length; i++) {
        if (dispatchWorkerId == dispatchWorkerDatas[i].id) {
            dispatchWorker = dispatchWorkerDatas[i].xm;
            dispatchWorkerNum = dispatchWorkerDatas[i].username;
            dispatchWorkerPhone = dispatchWorkerDatas[i].phone == null ? "" : dispatchWorkerDatas[i].phone;
            break;
        }
    }

    //司机
    var dispatchDriver = $('#dispatchDriver').combobox('getText');
    var dispatchDriverId = $('#dispatchDriver').combobox('getValue');
    var dispatchDriver = "";
    var dispatchDriverNum = "";
    var dispatchDriverPhone = "";
    var dispatchDriverId = $('#dispatchDriver').combobox('getValue');
    var dispatchDriverDatas = $('#dispatchDriver').combobox('getData');
    for (var i = 0; i < dispatchDriverDatas.length; i++) {
        if (dispatchDriverId == dispatchDriverDatas[i].id) {
            dispatchDriver = dispatchDriverDatas[i].xm;
            dispatchDriverNum = dispatchDriverDatas[i].username;
            dispatchDriverPhone = dispatchDriverDatas[i].phone == null ? "" : dispatchDriverDatas[i].phone;
            break;
        }
    }
    
    let verify = [
        {
            name: dispatchWorker,
            id: dispatchWorkerId,
            // phone:dispatchWorkerPhone,
            num: dispatchWorkerNum,
            msg: '出车护工'
        },
        {
            name: dispatchDoctor,
            id: dispatchDoctorId,
            // phone:dispatchDoctorPhone,
            num: dispatchDoctorNum,
            msg: '出车医生'
        },
        {
            name: dispatchNurse,
            id: dispatchNurseId,
            // phone:dispatchNursePhone,
            num: dispatchNurseNum,
            msg: '出车护士'
        },
        {
            name: dispatchDriver,
            id: dispatchDriverId,
            // phone:dispatchDriverPhone,
            num: dispatchDriverNum,
            msg: '出车司机'
        }
    ]
    let msgArr = []
    verify.forEach(r => {
        let { name, id, num, msg } = r
        if (!(name && id && num) && (name || id || num)) {
            msgArr.push(msg)
        }
    })
    if (msgArr[0]) {
        return $.messager.alert('温馨提示', msgArr.join(',') + "数据不完整，请在下拉列表选择，不要手动输入数据", 'error');
    }
    if (_currentProcess.toStatus == "5" || _currentProcess.toStatus == "7") {
        if (!carId) {
            return $.messager.alert('温馨提示', "请选择车辆", 'error');
        }

        showProcess(true, '温馨提示', '正在处理派车操作，请稍后...');
        //派车操作
        var params = {
            "mobileProcessId": _currentProcess.processId,
            "carId": carId,
            //"toStatus": '6',
            "dispatchDoctorId": dispatchDoctorId,
            "dispatchDoctorNum": dispatchDoctorNum,
            "dispatchDoctor": dispatchDoctor,
            "dispatchDoctorPhone": dispatchDoctorPhone,
            "dispatchNurseId": dispatchNurseId,
            "dispatchNurseNum": dispatchNurseNum,
            "dispatchNurse": dispatchNurse,
            "dispatchNursePhone": dispatchNursePhone,
            "dispatchDriverId": dispatchDriverId,
            "dispatchDriverNum": dispatchDriverNum,
            "dispatchDriver": dispatchDriver,
            "dispatchDriverPhone": dispatchDriverPhone,
            "dispatchWorkerId": dispatchWorkerId,
            "dispatchWorkerNum": dispatchWorkerNum,
            "dispatchWorker": dispatchWorker,
            "dispatchWorkerPhone": dispatchWorkerPhone
        };
        //不论是否同步成功，都进行派车
        confirmDispatchByStation(params,
            function (res) {
                showProcess(false);
                $.messager.alert('成功提示', '确认接收并派车成功');
                refreshMobileStatus();
                showMobileProcessItems(_currentProcess.processId);
                $('#confirm-receive-button').hide();
                $('#dis-cars-body').empty();
                $('#confirm-receive-editor').window('close');
                if (_currentProcess.listStatus) {
                    _currentProcess = null
                }
            }, function (e, url, errMsg) {
                showProcess(false);
                $.messager.alert('提示', "派车失败：" + errMsg, 'error');
            });
    } else {
        //修改出车医生护士等操作
        showProcess(true, '温馨提示', '正在修改出车人员信息，请稍后...');

        var params = {
            "processId": _currentProcess.processId,
            "dispatchDoctorId": dispatchDoctorId,
            "dispatchDoctorNum": dispatchDoctorNum,
            "dispatchDoctor": dispatchDoctor,
            "dispatchDoctorPhone": dispatchDoctorPhone,
            "dispatchNurseId": dispatchNurseId,
            "dispatchNurseNum": dispatchNurseNum,
            "dispatchNurse": dispatchNurse,
            "dispatchNursePhone": dispatchNursePhone,
            "dispatchDriverId": dispatchDriverId,
            "dispatchDriverNum": dispatchDriverNum,
            "dispatchDriver": dispatchDriver,
            "dispatchDriverPhone": dispatchDriverPhone,
            "dispatchWorkerId": dispatchWorkerId,
            "dispatchWorkerNum": dispatchWorkerNum,
            "dispatchWorker": dispatchWorker,
            "dispatchWorkerPhone": dispatchWorkerPhone
        };
        updateMobileProcessDispatchPeople(params,
            function (res) {
                showProcess(false);
                $.messager.alert('成功提示', '修改出车人员信息成功');
                $('#eventDetailRegion').hide();
                if (!_currentProcess.listStatus) {
                    // showMobileProcellDetailByid(_currentProcess.processId);
                } else {
                    _currentProcess = null
                }
                $('#confirm-receive-editor').window('close');
            }, function (e, url, errMsg) {
                showProcess(false);
                $.messager.alert('提示', "修改出车人员信息失败：" + errMsg, 'error');
            });

    }
}

/** 打印急救单 */
function doPrint(printConfig) {
    if (printConfig == '1') {
        var html = '<!DOCTYPE html>\
         <head>\
        <title>120急救单打印</title>\
            <style>\
            #print-zone-one td{\
                padding: 5px;\
            }\
            #print-zone-one td.title {\
                width: 15%;\
                text-align: right;\
            }\
            table {border-collapse: collapse;border-spacing: 0;}\
            table td {height:26px;overflow:hidden;border: 1px solid #000000;}\
            @media print {\
                body { margin: 0; }\
                table { page-break-inside: avoid; }\
            }\
            </style>\
        </head>\
        <body>{0}</body>'.formatStr($('#print-one-copies').prop("outerHTML"));
        printHtml(html);
    } else if (printConfig == '2') {
        var html = '<!DOCTYPE html>\
        <head>\
       <title>120急救单打印</title>\
           <style>\
           #print-zone-two td{\
               padding: 4px;\
           }\
           #print-zone-two td.title {\
               width: 10%;\
               text-align: right;\
           }\
           table {border-collapse: collapse;border-spacing: 0;}\
           table td {height:26px;overflow:hidden;border: 1px solid #000000;}\
           @media print {\
               body { margin: 0; }\
               table { page-break-inside: avoid; }\
               .clonedTable { page-break-before: always; }\
           }\
           </style>\
       </head>\
       <body>{0}</body>'.formatStr($('#print-two-copies').prop("outerHTML"));
        printHtml(html);
    }
}

/**
 * 检查是否要开启响铃
 */
function checkAlert() {
    // ASSIGN_TASK_MODE 值为1时，车辆接收任务响铃   
    const hasMobilesAlert = _mobiles != null && _mobiles.some(m => (m.status === '5' || (m.status === '6' && m.halfwaySend === '1')));
    // ASSIGN_TASK_MODE 值为2时，分站接收任务响铃
    const hasStationAlert = _mobilesProcessList != null && _mobilesProcessList.some(m => m.toStatus === '5' || (m.status === '6' && m.halfwaySend === '1'));

    //有任务要响铃
    if (hasMobilesAlert || hasStationAlert) {
        //暂停响铃倒计时小于等于0，说明暂停响铃结束了，需要开启响铃
        if (_alertTimerPauseTime <= 0) {
            turnOnAlert();
        }
    } else {
        turnOffAlert();
    }
}

/**
 * 开启响铃
 */
function turnOnAlert() {
    $('#alert-on').show();
    $('#alert-off').hide();
    $('#alert-pause').hide();
    $('#alert-main').removeClass("none");
    playAlert(true);
}

/**
 * 关闭响铃
 */
function turnOffAlert() {
    $('#alert-on').hide();
    $('#alert-off').show();
    $('#alert-pause').hide();
    $('#alert-main').addClass("none");
    playAlert(false);
}

/** 暂停响铃 */
function pauseAlert() {
    _alertTimerPauseTime = 30;//30秒后再次响铃
    $('#alert-on').hide();
    $('#alert-off').hide();
    $('#alert-pause').show();
    $('#alert-pause-text').html("暂停响铃" + _alertTimerPauseTime + "秒");
    $('#alert-main').removeClass("none");
    playAlert(false);
}

// function caculateRouteDistance(carData, callback) {
//     var distance = MAX_DISTANCE;
//     if (_currentLoc != null) {
//         distance = getDistance(carData.lat, carData.lng, _currentLoc.lat, _currentLoc.lng);
//     }
//     callback(distance, carData);
// }

/*function reportBigEvent() {
    if (_currentProcess != null) {
        $.messager.confirm('',
            '确定要将此事件作为重大事件上报吗？\n请谨慎操作。',
            function (r) {
                if (r) {
                    reportEvent(_currentProcess.eventId,
                        function (res) {
                            if (res.success) {
                                $.messager.alert('', '上报成功。');
                            } else {
                                $.messager.alert('', '上报失败，发生错误。', 'error');
                            }
                        });
                }
            });
    }
}*/

/** 打开车辆状况界面 */
function openMobileConditionWindowBtn(mobileId, status, hCondition) {
    if (status != '0') {
        $.messager.alert('提示', '当前车辆正在运行中，不能修改车辆状态，只能在待命时才能修改车辆状态。', 'error');
        return false;
    }
    $('#updateMobileConditionDialog').window('open');
    $('#updateConditionMobileId').val(mobileId);
    checkedCondition(hCondition);
}

/** 查看选中状态 */
function checkedCondition(hCondition) {
    const conditions = {
        "0": "conditionZc",
        "1": "conditionWh",
        "2": "conditionZd",
        "3": "conditionWzb"
    };
    for (const key in conditions) {
        $(`#${conditions[key]}`).checkbox({
            checked: key === hCondition
        });
    }
    $("#mobileCondition").val(hCondition);
}

/** 关闭车辆状况 */
function closeMobileConditionWindowBtn() {
    $('#updateMobileConditionDialog').window('close', true);
}

/** 修改车辆状况 */
function openMobileConditionBtn(mobileId, hCondition) {
    //车辆当前状况，0：正常，1：维护中，2：驻场中，3：未值班
    showProcess(true, '温馨提示', '正在进行撤销调度任务操作，请稍后...');

    var params = {
        "mobileId": mobileId,
        "hCondition": hCondition,
    }
    updateMobileCondition(params,
        function (res) {
            showProcess(false);
            $.messager.alert('成功', '车辆状况修改成功');
            $('#updateMobileConditionDialog').window('close', true);
            refreshMobileStatus();
        }, function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "车辆状况修改失败：" + errMsg, 'error');
        });
}

/** 打开车辆调度界面（急救平台） */
function openWebDispatchBtn() {
    getFirstaidPlatformUrl('vehicle_scheduling_url',
        function (data) {
            let url = data.trim();
            try {
                window.parent.gProxy.openBrowser(url);
            } catch (e) {
                window.parent.opener.windowOpenUrl(url);
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取车辆调度界面失败，异常信息：' + errMsg);
        });
}

/** 打开车辆管理页面（急救平台） */
function openWebCarDetailBtn() {
    getFirstaidPlatformUrl('ambulance_url',
        function (data) {
            let url = data.trim();
            try {
                window.parent.gProxy.openBrowser(url);
            } catch (e) {
                window.parent.opener.windowOpenUrl(url);
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取车辆管理页面失败，异常信息：' + errMsg);
        });
}

/** 打开历史调度界面（急救平台） */
function openWebHistoryDispatchBtn() {
    getFirstaidPlatformUrl('history_scheduling_url',
        function (data) {
            let url = data.trim();

            // 尝试使用 gProxy 或 opener 打开浏览器
            try {
                if (window.parent && window.parent.gProxy && typeof window.parent.gProxy.openBrowser === 'function') {
                    // 如果 gProxy 存在且有 openBrowser 方法，使用它
                    window.parent.gProxy.openBrowser(url);
                } else if (window.parent && window.parent.opener && typeof window.parent.opener.windowOpenUrl === 'function') {
                    // 如果 opener 存在且有 windowOpenUrl 方法，使用它
                    window.parent.opener.windowOpenUrl(url);
                } else {
                    // 默认使用 window.open() 打开 URL
                    window.open(url, '_blank');  // 打开新窗口
                }
            } catch (e) {
                // 如果出现错误，使用默认的 window.open() 打开 URL
                window.open(url, '_blank');
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取历史调度页面失败，异常信息：' + errMsg);
        });
}


/** 打开视频按钮（急救平台） */
function openVideoBtn() {
    getFirstaidPlatformUrl('transfer_video_url',
        function (data) {
            let url = data.trim();
            try {
                window.parent.gProxy.openBrowser(url);
            } catch (e) {
                window.parent.opener.windowOpenUrl(url);
            }
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取视频监控页面失败，异常信息：' + errMsg);
        });
}
/** 统计转院人数 */
function openTheNumberOfTransfer() {
    const start = new Date().toJSON().substring(0, 8) + '01 00:00:00';//开始时间
    $('#startCreateTime').datetimebox('setValue', start);
    $('#endCreateTime').datetimebox('setValue', '');
    $('#outHospital').textbox('setValue', '');
    searchInfoButton()
    $('#theNumberOfTransfer').window('open');
}
/** 查询统计转院人数列表 */
function searchInfoButton() {
    $('#theNumberOfTransfer-content').datagrid('loadData', { total: 0, rows: [] });
    getTransshipmentList({
        id: _stationId,
        startCreateTime: $('#startCreateTime').val(),
        endCreateTime: $('#endCreateTime').val(),
        outHospital: $('#outHospital').val()
    }, (content) => {
        if (content && content[0]) {
            let data = groupedArray(content, 3)
            let key = [
                //第一列
                {
                    outHospital: 'outHospital1',
                    transshipment: 'transshipment1'
                },
                //第二列
                {
                    outHospital: 'outHospital2',
                    transshipment: 'transshipment2'
                },
                //第三列
                {
                    outHospital: 'outHospital3',
                    transshipment: 'transshipment3'
                },
            ]
            for (let i = 0; i < data.length; i++) {
                let row = {}
                data[i].forEach((r, i) => {
                    row[key[i].outHospital] = r.outHospital
                    row[key[i].transshipment] = r.transshipmentNum
                })
                $('#theNumberOfTransfer-content').datagrid('insertRow', {
                    index: i, // 索引从0开始
                    row,
                })
            }
        }
    },
        (e, url, errMsg) => {
            $.messager.alert('提示', "获取统计转院人数失败：" + errMsg, 'error');
        })
}
/** 根据数值大小分隔成二维数组 */
function groupedArray(array, num = 2) {
    let index = 0;
    let newArray = [];
    while (index < array.length) {
        newArray.push(array.slice(index, index += num));
    }
    return newArray
}
/** 显示版本信息 */
function showVersionInfo() {
    //显示版本信息
    var versionInfo = getExeVersionInfo();
    $('#versionInfo').html(versionInfo);
    $.messager.alert('版本信息', '当前版本：' + versionInfo, 'info');
}

/** 事件监控表格的状态颜色格 */
function setDgProcessStatusColumnFormatter(value, row, index) {
    switch (row.toStatus) {
        case '5':
            return '<font color=red>' + row.toStatusStr + '</font>';
        case "6":
            return '<font color=blue>' + row.toStatusStr + '</font>';
        case "1":
            return '<font color=blue>' + row.toStatusStr + '</font>';
        case "2":
            return '<font color=blue>' + row.toStatusStr + '</font>';
        case "3":
            return '<font color=green>' + row.toStatusStr + '</font>';
        case "8":
            return '<font color=blue>' + row.toStatusStr + '</font>';
        case "4":
            return '<font color=green>' + row.toStatusStr + '</font>';
        case "-1":
            return '<font style="color: #acacad;">' + row.toStatusStr + '</font>';
        case "7":
            return '<font color=red>' + row.toStatusStr + '</font>';
        case "9":
            return '<font color=green>' + row.toStatusStr + '</font>';
        default:
            return row.toStatusStr;
    }

}

/** 设置车辆信息刷新计时器,定时刷新座席和分站状态 */
function stationSearchEventsAutoRefreshIntervalTimeout() {

    clearTimeout(window.stationSearchEventsAutoRefreshInterval);
    if (_stationSearchEventsAutoRefreshInterval) {
        window.stationSearchEventsAutoRefreshInterval = null;
        if (_token != "") {
            if ($('#autoRefreshList').is(':checked')) {//选中才自动刷新
                searchEvents(page, size);
            }
        }

        window.stationSearchEventsAutoRefreshInterval = setTimeout(stationSearchEventsAutoRefreshIntervalTimeout, 6000);
    }
}

/**
 * 回院弹窗显示
 * @param {any} type 类型 0：取消接诊回院，空车回院；1：正常回院完成任务
 * @param {any} currentProcessId
 */
function openUpdateAwaitMobileDialog(type, currentProcessId) {
    //获得当前选中的行
    $('#updateAwaitMobileDialog').window('open', true);
    //获取时间填写到设为待命对话框中来
    var params = {
        "processId": currentProcessId,
    };
    getMobileOperationRecordList(params,
        function (data) {
            var nowInTimes = new Date().formatString("yyyy-MM-dd hh:mm:ss");
            $('#send-car-times').datetimebox('setValue', nowInTimes);//派车时间
            $('#goto-car-times').datetimebox('setValue', nowInTimes);//出车时间
            $('#cancel-car-times').datetimebox('setValue', "");//取消时间
            $('#arrive-car-times').datetimebox('setValue', "");//抵达时间
            $('#leave-car-times').datetimebox('setValue', "");//离开现场
            $('#back-car-times').datetimebox('setValue', nowInTimes);//回院时间

            //派车时间
            $('#send-car-times').attr("data-options-id", "");
            $('#send-car-times').attr("data-options-mobileProcessId", "");
            $('#send-car-times').attr("data-options-times", "");
            $('#send-car-times').attr("data-options-toStatus", "");
            $('#send-car-times').attr("data-options-remark", "");

            //出车时间
            $('#goto-car-times').attr("data-options-id", "");
            $('#goto-car-times').attr("data-options-mobileProcessId", "");
            $('#goto-car-times').attr("data-options-times", "");
            $('#goto-car-times').attr("data-options-toStatus", "");
            $('#goto-car-times').attr("data-options-remark", "");

            //取消任务时间
            $('#cancel-car-times').attr("data-options-id", "");
            $('#cancel-car-times').attr("data-options-mobileProcessId", "");
            $('#cancel-car-times').attr("data-options-times", "");
            $('#cancel-car-times').attr("data-options-toStatus", "");
            $('#cancel-car-times').attr("data-options-remark", "");

            //完成接诊回到医院时间
            $('#back-car-times').attr("data-options-id", "");
            $('#back-car-times').attr("data-options-mobileProcessId", "");
            $('#back-car-times').attr("data-options-times", "");
            $('#back-car-times').attr("data-options-toStatus", "");
            $('#back-car-times').attr("data-options-remark", "");

            //获取正在进行的事件的列表
            var isCancleCar = false;
            for (var i = 0; i < data.length; i++) {
                //-1：取消调度、1-发车、2-抵达、3-出诊取消、4-出诊完成(回院)、5-调度接收、6-分站派车、7-分站确认、8-离开现场；最终状态-1或4
                switch (data[i].toStatus) {
                    case "6"://派车时间
                        $('#send-car-times').attr("data-options-id", data[i].id);
                        $('#send-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#send-car-times').attr("data-options-times", data[i].times);
                        $('#send-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#send-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#send-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "1"://出车时间
                        $('#goto-car-times').attr("data-options-id", data[i].id);
                        $('#goto-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#goto-car-times').attr("data-options-times", data[i].times);
                        $('#goto-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#goto-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#goto-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "2"://抵达时间
                        $('#arrive-car-times').attr("data-options-id", data[i].id);
                        $('#arrive-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#arrive-car-times').attr("data-options-times", data[i].times);
                        $('#arrive-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#arrive-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#arrive-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "3"://取消任务时间
                        $('#cancel-car-times').attr("data-options-id", data[i].id);
                        $('#cancel-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#cancel-car-times').attr("data-options-times", data[i].times);
                        $('#cancel-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#cancel-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#cancel-car-times').datetimebox('setValue', data[i].times);
                        isCancleCar = true;
                        break;
                    case "8"://离开现场时间
                        $('#leave-car-times').attr("data-options-id", data[i].id);
                        $('#leave-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#leave-car-times').attr("data-options-times", data[i].times);
                        $('#leave-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#leave-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#leave-car-times').datetimebox('setValue', data[i].times);
                        break;
                    case "4"://完成接诊回到医院时间
                        $('#back-car-times').attr("data-options-id", data[i].id);
                        $('#back-car-times').attr("data-options-mobileProcessId", data[i].mobileProcessId);
                        $('#back-car-times').attr("data-options-times", data[i].times);
                        $('#back-car-times').attr("data-options-toStatus", data[i].toStatus);
                        $('#back-car-times').attr("data-options-remark", data[i].remark == null ? "" : data[i].remark);

                        $('#back-car-times').datetimebox('setValue', data[i].times);
                        break;
                }
            }
            if (type == "0") {//如果传入进来是取消接诊的话，那么必须填写取消接诊的时间
                isCancleCar = true;
            }
            if (isCancleCar) {
                //显示取消时间
                $('#cancel-car-times-div').show();
                if ($('#cancel-car-times').datetimebox('getValue') == "") {
                    $('#cancel-car-times').datetimebox('setValue', nowInTimes);
                }
            } else {
                //隐藏取消时间
                $('#cancel-car-times-div').hide();
                //显示抵达时间&离开现场
                if ($('#arrive-car-times').datetimebox('getValue') == "") {
                    $('#arrive-car-times').datetimebox('setValue', nowInTimes);
                }
                if ($('#leave-car-times').datetimebox('getValue') == "") {
                    $('#leave-car-times').datetimebox('setValue', nowInTimes);
                }
            }
            //将当前的任务ID存储到页面上
            $('#updateAwaitMobileProcessId').val(currentProcessId);
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取派车记录错误，异常信息：' + errMsg);
        });
}

/** 关闭完成任务弹窗 */
function closeUpdateAwaitMobileDialog() {
    $('#updateAwaitMobileDialog').window('close', true);
}

/** 强制待命，同步调用急救平台的接口 */
function updateAwaitMobileBtn() {
    showProcess(true, '温馨提示', '正在强制进行车辆待命操作，请稍后...');

    var mobileProcessItemsList = [];
    //派车时间
    if ($('#send-car-times').datetimebox('getValue') != "") {
        var itemSendCarTimes = {
            "id": $('#send-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#send-car-times').datetimebox('getValue'),
            "toStatus": "6",
            "remark": ""
        };
        mobileProcessItemsList.push(itemSendCarTimes);
    }
    //出车时间
    if ($('#goto-car-times').datetimebox('getValue') != "") {
        var itemGotoCarTimes = {
            "id": $('#goto-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#goto-car-times').datetimebox('getValue'),
            "toStatus": "1",
            "remark": ""
        };
        mobileProcessItemsList.push(itemGotoCarTimes);
    }
    //抵达时间
    if ($('#arrive-car-times').datetimebox('getValue') != "") {
        var itemArriveCarTimes = {
            "id": $('#arrive-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#arrive-car-times').datetimebox('getValue'),
            "toStatus": "2",
            "remark": ""
        };
        mobileProcessItemsList.push(itemArriveCarTimes);
    }
    //取消时间
    if ($('#cancel-car-times').datetimebox('getValue') != "") {
        var itemCancelCarTimes = {
            "id": $('#cancel-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#cancel-car-times').datetimebox('getValue'),
            "toStatus": "3",
            "remark": ""
        };
        mobileProcessItemsList.push(itemCancelCarTimes);
    }
    //离开现场
    if ($('#leave-car-times').datetimebox('getValue') != "") {
        var itemLeaveCarTimes = {
            "id": $('#leave-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#leave-car-times').datetimebox('getValue'),
            "toStatus": "8",
            "remark": ""
        };
        mobileProcessItemsList.push(itemLeaveCarTimes);
    }
    //回院时间
    if ($('#back-car-times').datetimebox('getValue') != "") {
        var itemBackCarTimes = {
            "id": $('#back-car-times').attr("data-options-id"),
            "mobileProcessId": $('#updateAwaitMobileProcessId').val(),
            "times": $('#back-car-times').datetimebox('getValue'),
            "toStatus": "4",
            "remark": ""
        };
        mobileProcessItemsList.push(itemBackCarTimes);
    }
    var params = {
        "processId": $('#updateAwaitMobileProcessId').val(),
        "mobileProcessItemsList": mobileProcessItemsList,
    }
    updateAwaitMobile(params,
        function (res) {
            $('#updateAwaitMobileDialog').window('close', true);
            showProcess(false);
            $.messager.alert('成功', '强制车辆待命操作完成！');
        }, function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', "强制车辆待命操作失败：" + errMsg, 'error');
        });
}

/** 拒绝派车打开对话框 */
function refuseProcessReasonWindowShow(processId) {
    queryAllDic(["refuse_send_cat"],
        function (data) {
            var html = '<div style="margin-top: 20px; margin-left: 20px;">';
            for (var i = 0; i < data.length; i++) {
                var value = data[i].codeVale;
                var code = data[i].codeName;
                var id = data[i].uuid;
                html += '<div style="margin-bottom:10px;display:block"><input class="easyui-radiobutton" name="refuseProcessReason" value="' + value + '" ><span style="margin-left:10px;">' + value + '</span></div>'
            }

            html += '<div style="margin-bottom:10px;display:block"><input class="easyui-radiobutton" name="refuseProcessReason" value="-1" data-options="onChange:otherCahrgeMobileReasonSelectBtn"><span style="margin-left:5px;margin-right:10px" >其他</span><input class="easyui-textbox" id="otherRefuseProcessReason" style="height:24px;width:200px"></div>'
            html += '</div>';
            $("#refuseProcessReasonForm").html(html);
            $.parser.parse();

            //将改派车辆的原因显示成选择框
            $('#refuseProcessReasonDialog').window('open');
            $('#refuse-mobile-process-id').val(processId);

        });//查询改派车辆的字典       
}

/** 拒绝派车确认按钮点击 */
function updateRefuseProcessReasonExecBtn() {
    //撤销调度任务，撤销调度任务时候不能撤销事件，如果需要撤销事件，那么需要撤销所有的分站未派车的任务后才能进行撤销事件的操作。
    var reason = "";
    reason = $('input[type="radio"][name="refuseProcessReason"]:checked').val();
    if (typeof reason === 'undefined' || reason == "") {
        $.messager.alert('提示', '请选择拒绝派车原因！', 'error');
        return false;
    }
    if ($('input[type="radio"][name="refuseProcessReason"]:checked').val() == "-1") {
        reason = $("#otherRefuseProcessReason").textbox('getValue');
    }
    showProcess(true, '温馨提示', '正在进行拒绝派车的消息推送操作，请稍后...');
    if (!reason) {
        $.messager.alert('提示', '请输入拒绝派车其他原因！', 'error');
        showProcess(false);
        return false;
    }
    var params = {
        'processId': $('#refuse-mobile-process-id').val(),
        'stationId': _stationId,
        'reason': reason,
    };
    refuseSendCar(params,
        function (res) {
            //成功
            showProcess(false);
            refreshMobileStatus();
            $('#refuseProcessReasonDialog').window('close');
            $.messager.alert('提示', '拒绝派车操作成功！', 'info');

        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        });
}

/** 拒绝派车取消按钮点击 */
function cancleRefuseProcessReasonExecBtn() {
    $('#refuseProcessReasonDialog').window('close');
}

/** 选中了改派车辆类型的其他按钮，那么显示输入框 */
function otherCahrgeMobileReasonSelectBtn() {
    if ($('input[type="radio"][name="refuseProcessReason"]:checked').val() == "-1") {
        $("#otherRefuseProcessReason").next().show();
    } else {
        $("#otherRefuseProcessReason").next().hide();
    }
};

/** 撤销拒绝派车 */
function refuseProcessCancel(msgId, processId) {
    $.messager.confirm("提示", "是否撤销拒绝派车？", function (r) {
        if (r) {
            showProcess(true, '温馨提示', '正在撤销拒绝派车操作，请稍后...');
            var params = {
                "msgId": msgId,
                "processId": processId
            }
            rejectRefuseSendCar(params,
                function (res) {
                    //成功
                    showProcess(false);

                },
                function (e, url, errMsg) {
                    showProcess(false);
                    $.messager.alert('提示', errMsg, 'error');
                });
        }
    });
}
/** 确认派车 */
function confirmCar(processId) {
    //queryDetails(processId)
    showMobileProcessDetailById(processId)
}
/** 修改派车信息 */
function modifyCar(processId) {
    showMobileProcessDetailById(processId)
    // queryDetails(processId)
}
/** 获取事件任务信息 */
/*function queryDetails(processId) {
    if (processId != null && processId != "") {
        //获取事件任务信息
        var getMobileProcessByEventIdParams = {
            "eventId": '',
            "beginTime": '',
            "endTime": '',
            "stationId": '',
            "processId": processId,
            "stationCode": ''
        };
        getMobileProcessByEventId(getMobileProcessByEventIdParams, function (data) {
            _currentProcess = data[0];
            _currentProcess.listStatus = true
            confirmReceiveShow(data[0])
        }, function (e, url, errMsg) {
            $.messager.alert('异常', '异常信息：' + errMsg);
        })
    }
}*/

// 关闭定时器
//function closeInterval() {
//    clearInterval(_taskTimesInterval);
//    clearInterval(_eventSearchInterval);
//    clearInterval(_taskStationHeartInterval);
//}

/** 打开取消派车填写原因界面 */
function cancelDispatchedCar(processId) {
    $('#cancel-process-id').val(processId); // 任务id
    $('#cancel-reason').textbox("setValue","");// 初始化取消原因为空
    $('#cancelDispatchedDialog').window('open');
    console.log('Opening processId:', processId);
    console.log('Opening reason:', $('#cancel-reason').val());
}

/** 关闭取消派车填写原因界面 */
function closecancelDispatchedBtn() {
    $('#cancelDispatchedDialog').window('close', true);
}

/** 取消派车保存按钮，只有在分站未派车的情况下才能取消派车 */
function cancelDispatchedBtn() {
    showProcess(true, '温馨提示', '正在进行取消派车操作，请稍后...');
    var params = {
        'processId': $('#cancel-process-id').val(),
        'reason': $('#cancel-reason').val(),
    };
    cancelSendCar(params,
        function (res) {
            $('#cancelDispatchedDialog').window('close', true);
            //成功
            showProcess(false);
            $.messager.alert('提示', '取消派车成功！', 'info');
        },
        function (e, url, errMsg) {
            showProcess(false);
            $.messager.alert('提示', errMsg, 'error');
        });
}

/**
 * 播放报警音频（兼容C/S和B/S模式）
 * @param {boolean} openIs 是否开启播放，true表示播放，false表示停止
 */
function playAlert(openIs) {
    try {
        gProxy.playAlert(openIs);
    } catch (e) {
        // B/S模式下使用bsProxy
        try {
            if (typeof bsProxy === 'undefined' || !bsProxy) {
                bsProxy = new gBsProxy(document.body);
            }
            bsProxy.playAlert(openIs);
        } catch (bsError) {
            console.error('播放报警音频失败:', bsError);
        }
    }
}

/**
 * 获取版本信息（兼容C/S和B/S模式）
 * @returns {string} 版本信息字符串
 */
function getExeVersionInfo() {
    try {
        return gProxy.getExeVersionInfo();
    } catch (e) {
        // B/S模式下使用bsProxy
        try {
            if (typeof bsProxy === 'undefined' || !bsProxy) {
                bsProxy = new gBsProxy(document.body);
            }
            return bsProxy.getExeVersionInfo();
        } catch (bsError) {
            console.error('获取版本信息失败:', bsError);
            return "B/S 1.0.0.1"; // 默认版本号
        }
    }
}

/**
 * 打印HTML内容（兼容C/S和B/S模式）
 * @param {string} htmlContent 要打印的HTML内容
 */
function printHtml(htmlContent) {
    try {
        // 尝试使用C/S模式的gProxy打印
        gProxy.print(htmlContent);
    } catch (e) {
        // B/S模式下使用bsProxy
        try {
            if (typeof bsProxy === 'undefined' || !bsProxy) {
                bsProxy = new gBsProxy(document.body);
            }
            bsProxy.print(htmlContent);
        } catch (bsError) {
            console.error('打印失败:', bsError);
            // 最后的降级方案：提示用户手动操作
            alert('打印功能暂时不可用，请使用浏览器的打印功能（Ctrl+P）');
        }
    }
}

/**
 * 加载分站列表
 */
function loadStationList() {
    // 调用getStationInfo接口获取分站列表
    getAllStationList({},
        function (data) {
            if (data && data.length > 0) {
                var stationSelect = $('#login-station');
                // 清空现有选项（保留"请选择分站"）
                stationSelect.find('option:not(:first)').remove();
                
                // 添加分站选项
                $.each(data, function(index, station) {
                    var optionText = station.stationName;
                    if (station.stationRegionName) {
                        optionText += ' - ' + station.stationRegionName;
                    }
                    stationSelect.append('<option value="' + station.id + '">' + optionText + '</option>');
                });
                
                // 如果有保存的分站ID，恢复选择
                var lastLoginUserBS = evalJson(bsProxy.getLastLoginUser());
                if (lastLoginUserBS != null) {
                    //回填分站
                    if (lastLoginUserBS.StationId) {
                        stationSelect.val(lastLoginUserBS.StationId);
                    }
                    //回填用户
                    $("#login-un").val(lastLoginUserBS.Username);
                    //回填密码
                    if (lastLoginUserBS.SavePsw) {
                        $("#is-save-psw").attr("checked", "checked");
                        $("#login-pwd").val(lastLoginUserBS.Password);
                    }
                }
            } else {
                console.warn('获取分站列表为空');
            }
        },
        function (e, url, errMsg) {
            console.error('获取分站列表失败:', errMsg);
            // 可以显示一个友好的错误提示
            $.messager.alert('提示', '获取分站列表失败，请刷新页面重试');
        }
    );
}