let mapObj = null
//父窗口数据
var parentData = window.window.HCUI_LAYER_PARAENT_DATA;
let deviceId = parentData.gpsTerminal
let radioList = [
    {
        el:'gpsTerminal',
        value:parentData.gpsTerminal,
        checked:true
    },
    {
        el:'reserveGpsTerminal',
        value:parentData.reserveGpsTerminal,
        checked:false
    }
]
window.onload = () => {
    let i = 0
    while(i<radioList.length){
        radioList[i].el = document.getElementById(radioList[i].el)
        if(radioList[i].value){
            radioList[i].el.value = radioList[i].value
        } else {
            radioList[i].el.parentNode.style.display = 'none'
            //console.log(radioList[i].el.parentNode)
        }
        radioList[i].el.checked = radioList[i].checked
        radioList[i].el.onchange = checkChange
        i++
    }
    mapObj = new Map()
}
function checkChange(){
    let i = 0
    while(i<radioList.length){
        if(radioList[i].el.checked){
            deviceId = radioList[i].el.value
            mapObj.getData()
        }
        i++
    }window.window.
}
const end = new Date();
const start = new Date();
start.setTime(start.getTime() - 1000*60*60);
class SlidingProgressBar {
    constructor() {
        this.init()
    }
    init() {
        this.progress = document.querySelector('.progress-bar')
        this.moveBlock = document.querySelector('.block');
        this.proBar = document.querySelector('.pro-bar');
        this.text = document.querySelector('.text')
        this.flag = false;
        this.startX = null;
        this.moveMax = (this.progress.offsetWidth - 12); // 背景条宽度减去滑块的宽度
        this.moveBlock.onmousedown = (e) => {
            this.text.style.display = 'block'
            this.startX = e.pageX;
            this.moveBlock.style.left ? this.moveBlock.style.left : this.moveBlock.style.left = '0px';
            let startLeft = parseInt(this.moveBlock.style.left);
            document.onmousemove = (e) => {
                let moveX = (e.pageX - this.startX) > 0 ? true : false;
                let moveSection = startLeft + (e.pageX - this.startX);
                // 限定移动范围
                if (moveSection >= 0 && moveSection <= this.moveMax) {
                    let percent = Math.round(((startLeft + (e.pageX - this.startX)) / this.moveMax).toFixed(4) * 9900);
                    percent.toString().length > 5 ? percent = percent.toString().subStr(0, 5) :
                        percent = percent.toString();
                    this.moveBlock.style.left = startLeft + (e.pageX - this.startX) + 'px';
                    this.proBar.style.width = this.moveBlock.style.left;
                    this.text.innerText = 100 + parseInt(percent);
                    if (mapObj) {
                        mapObj.navg.setSpeed(100 + parseInt(percent))
                    }
                }
            };
        };
        // 鼠标松开移除事件
        this.moveBlock.onmouseup = () => {
            document.onmousemove = null;
        };
        document.onclick = () => {
            document.onmousemove = null;
            this.text.style.display = 'none'
        }
    }
}
class Map {
    constructor() {
        this.map = null
        this.pathSimplifier = null
        this.pathSimplifierIns = null
        this.getBase64('./js/ambunance.png', (base64) => {
            this.icon = base64
        })
        this.historyatt = []
        this.lineArr = []
        this.distance = null
        this.speed = null
        this.buttonDiv = document.querySelector('.map-button')
        getDatetime('deviceStartTime');
        getDatetime('deviceEndTime');
        $('#deviceStartTime').val(JSONTime(start))
        $('#deviceEndTime').val(JSONTime(end))
        this.gpsTime = null
        this.buttonList = [{
                name: '开始动画',
                index: 0,
                mapStats: false
            },
            {
                name: '暂停动画',
                index: 1,
                mapStats: true
            },
            {
                name: '继续动画',
                index: 2,
                mapStats: true
            },
            {
                name: '停止动画',
                index: 3,
                mapStats: true
            },
            {
                name: '销毁动画',
                index: 4,
                mapStats: true
            },
        ]
        this.getData()
        this.initMap()

    }
    initButton(plateNum) {
        this.buttonDiv.innerHTML = `${
            this.buttonList.map(r=>{
                return `<button class="button button${r.index}" disabled="true" onclick="setNavg(${r.index})">${r.name}</button>`
            }).join('')
        }
        <div>车牌号：${plateNum || ''}</div>
        <div class="flex-msg">           
            <span> 动画时速：</span>
            <span>
                <div class="progress-bar">
                    <div class="pro-bar"></div>
                    <div class="block">
                        <div class="text">0</div>
                    </div>
                </div>           
            </span>
        </div>
        <div class="flex-msg">
            <span id="gpsTime"> 时间：</span>
            <span id="speed"> 车速：</span></br>
        </div>
        <div class="flex-msg">
            <span id="distance"> 车辆行驶距离：</span>
        </div>     
        `
        this.speed = document.getElementById('speed')
        this.gpsTime = document.getElementById('gpsTime')
        this.distance = document.getElementById('distance')
        this.buttonDiv.style.display = 'block'
        new SlidingProgressBar()
    }
    initMap() {
        //创建地图
        this.map = new AMap.Map('map-track', {
            resizeEnable: true,
            // center: this.lineArr[0],
            zoom: 17
        });
        this.map.addControl(new AMap.Scale());
        this.map.addControl(new AMap.ToolBar());
    }
    getData() {
        this.clearData()
        let deviceStartTime = $('#deviceStartTime').val()
        let deviceEndTime = $('#deviceEndTime').val()
        if (!deviceStartTime || !deviceEndTime) {
          return $.messager.alert('开始时间或结束时间为空！');
        }
        $.ajax({
            url: "/edc_svrside_service/mobile/getHistoryInfo",
            type: "post",
            dataType: "JSON",
            data: JSON.stringify({
                "deviceStartTime": this.forTimes(deviceStartTime),
                "deviceId": deviceId,
                "deviceEndTime": this.forTimes(deviceEndTime)
            }),
            contentType: 'application/json',
            success: (res) => {
                if (res.success) {
                    let {
                        plateNum,
                        gpsHistoryInfoList
                    } = res.content || {}
                    this.plateNum = plateNum
                    if (Array.isArray(gpsHistoryInfoList)) {
                        this.initButton(plateNum)
                        this.lineArr = gpsHistoryInfoList.map((r, ind) => {
                            this.historyatt.push({
                                speed: r.speed || 0,
                                gpsTime: r.createdTime
                            })
                            return [r.gaodeLongitude, r.gaodeLatitude]
                        })
                        this.gpsTime.innerHTML = '时间：' + gpsHistoryInfoList[0].createdTime
                        this.speed.innerHTML = '车速：' + (gpsHistoryInfoList[0].speed || 0) + 'km/h'
                        this.initAMapUI()
                    } else {
                      $.messager.alert('无经纬度信息');
                    }
                } else {
                  $.messager.alert(res.msg);
                }
            },
            error:(XMLHttpRequest, textStatus, errorThrown)=>{
              $.messager.alert(textStatus + ':' + XMLHttpRequest.responseText);
            }
        });
    }
    clearData() {
        this.buttonDiv.style.display = 'none'
        this.lineArr = []
        this.historyatt = []
        if (this.pathSimplifierIns && this.pathSimplifierIns.setData) {
            this.pathSimplifierIns.setData([{
                // name: this.plateNo,
                name: this.plateNum,
                path: []
            }]);
        }
    }
    initAMapUI() {
        AMapUI.load(['ui/misc/PathSimplifier', 'lib/$'], (PathSimplifier, $) => {
            if (!PathSimplifier.supportCanvas) {
                alert('当前环境不支持 Canvas！');
                return;
            }
            this.pathSimplifier = PathSimplifier
            //创建一个巡航轨迹路线

            this.pathSimplifierIns = new PathSimplifier({
                zIndex: 100, //地图层级，
                map: this.map, //所属的地图实例
                //巡航路线轨迹列表
                getPath: (pathData, pathIndex) => {
                    return pathData.path;
                },
                //hover每一个轨迹点，展示内容
                getHoverTitle: (pathData, pathIndex, pointIndex) => {
                    if (pointIndex >= 0) {
                        return pathData.name + '，当前路段车辆速度：' + this.historyatt[pointIndex].speed + 'km/h ，时间：' + this.historyatt[pointIndex].gpsTime
                    }
                },
                //自定义样式，可设置巡航器样式，巡航轨迹样式，巡航轨迹点击、hover等不同状态下的样式，不设置则用默认样式，详情请参考api文档
                renderOptions: {
                    renderAllPointsIfNumberBelow: -1, //绘制路线节点，如不需要可设置为-1
                    keyPointTolerance: -1,
                    pathLineStyle: {
                        lineWidth: 5,
                        strokeStyle: '#28F',
                        borderWidth: 1,
                        borderStyle: '#eeeeee',
                        dirArrowStyle: true
                    },
                    pathLineHoverStyle: {
                        lineWidth: 5,
                        strokeStyle: '#411f25',
                        borderWidth: 1,
                        borderStyle: '#eeeeee',
                    },
                    hoverTitleStyle: {
                        position: 'top', //title的位置，left 或者 top										
                        classNames: ('hoverTitleStyle'),
                        //title所用的dom节点上的classNames，多个用空格分开，可借此调整dom节点的样式									
                        // offset: { [number, number] } //title的dom节点相对于定位点的偏移
                    }
                },
            });
            //设置数据
            this.pathSimplifierIns.setData([{
                // name: this.plateNo,
                name: this.plateNum,
                path: this.lineArr
            }]);
            this.createPathNavigator()
        });
    }
    createPathNavigator() {
        let _this = this

        function onload() {
            _this.pathSimplifierIns.renderLater();
        }

        function onerror(e) {
            alert('图片加载失败！');
        }
        //对第一条线路（即索引 0）创建一个巡航器
        this.navg = this.pathSimplifierIns.createPathNavigator(0, {
            loop: false, //循环播放
            speed: 100, //巡航速度，单位千米/小时
            pathNavigatorStyle: {
                width: 25,
                height: 45,
                //使用图片
                content: this.pathSimplifier.Render.Canvas.getImageContent(this.icon, onload, onerror),
                strokeStyle: null,
                fillStyle: null,
                //经过路径的样式
                pathLinePassedStyle: {
                    lineWidth: 5,
                    strokeStyle: '#f77d38d9',
                    dirArrowStyle: {
                        stepSpace: 15,
                        strokeStyle: '#ffffff'
                    }
                }
            },
        });
        this.buttonSdatas(0, false, null, false, true)
        this.navg.on('move', function (data, position) {
            let idx = position.dataItem.pointIndex //走到了第几个点
            _this.gpsTime.innerHTML = '时间：' + _this.historyatt[idx].gpsTime
            _this.speed.innerHTML = '车速：' + _this.historyatt[idx].speed + 'km/h'
            _this.distance.innerHTML = '车辆行驶距离：' + Math.round(this.getMovedDistance()) + '米'
            if (idx == _this.lineArr.length - 1) {
                _this.buttonSdatas(0, false, null, false, true)
            }
        });
    }
    buttonSdatas(a, b, c, d, e) {
        this.buttonList.map((r, ind) => {
            let button = document.querySelector('.button' + r.index)
            if (ind == a) {
                r.mapStats = b
            } else if (ind == c) {
                r.mapStats = d
            } else {
                r.mapStats = e
            }
            button.disabled = r.mapStats
            return r
        })
    }
    getBase64(url, callback) {
        //通过构造函数来创建的 img 实例，在赋予 src 值后就会立刻下载图片，相比 createElement() 创建 省去了 append()，也就避免了文档冗余和污染
        const Img = new Image()
        let dataURL = '';
        Img.src = url;
        Img.onload = () => { //要先确保图片完整获取到，这是个异步事件
            //创建canvas元素
            const canvas = document.createElement("canvas"),
                width = Img.width, //确保canvas的尺寸和图片一样
                height = Img.height;
            canvas.width = width;
            canvas.height = height;
            canvas.getContext("2d").drawImage(Img, 0, 0, width, height); //将图片绘制到canvas中
            dataURL = canvas.toDataURL('image/jpg'); //转换图片为dataURL
            callback(dataURL)
        };
        return dataURL;
    }
    getTimes(ev) { //处理成 带-的时间格式
        if (ev) {
            let Timestamp = ev.substring(0, 14).split('')
            Timestamp.splice(4, 0, "-")
            Timestamp.splice(7, 0, "-")
            Timestamp.splice(10, 0, " ")
            Timestamp.splice(13, 0, ":")
            Timestamp.length > 16 ? Timestamp.splice(16, 0, ":") : null
            return Timestamp.join("")
        } else {
            return null
        }
    };

    forTimes(ev) { //处理成 不带-的时间格式
        if (ev) {
            let arr = []
            for (let i = 0; i < ev.length; i++) {
                if (ev[i] !== " " && ev[i] !== "-" && ev[i] !== ":") {
                    arr.push(ev[i])
                }
            }
            let str = arr.join("")
            return str.length < 13 ? str + '00' : str
        } else {
            return null
        }
    }
}

function setNavg(ev) {
    if (ev == 0) {
        if (mapObj.navg == null) {
            mapObj.createPathNavigator()
        }
        mapObj.navg.start()
        mapObj.buttonSdatas(0, true, 2, true, false)
    } else if (ev == 1) {
        mapObj.navg.pause()
        mapObj.buttonSdatas(1, true, 3, true, false)
    } else if (ev == 2) {
        mapObj.navg.resume()
        mapObj.buttonSdatas(0, true, 2, true, false)
    } else if (ev == 3) {
        mapObj.navg.stop()
        mapObj.buttonSdatas(0, false, 4, false, true)
    } else if (ev == 4) {
        mapObj.navg.destroy()
        mapObj.navg = null
        mapObj.buttonSdatas(0, false, null, false, true)
    }
}
//搜索按钮
$('#queryListBtn').on('click', function () {
    mapObj.getData()
});

function getDatetime(id) {
    //执行一个laydate实例
    HCUI.datePicker({
        elem: '#' + id,
        type: 'datetime',
    });
}
let sign = '-'
let n = ':'
let u = ' '
function JSONTime(time) { //获取带时间2021-4-9 16:38:06 格式
	let y = time.getFullYear()
	let m = time.getMonth() + 1
	let d = time.getDate()
	let h = time.getHours()
	let mi = time.getMinutes()
	let s = time.getSeconds()
	m = m < 10 ? `0${m}` : m
	d = d < 10 ? `0${d}` : d
	h = h < 10 ? `0${h}` : h
	mi = mi < 10 ? `0${mi}` : mi
	s = s < 10 ? `0${s}` : s
    return `${y}${sign}${m}${sign}${d}${u}${h}${n}${mi}${n}${s}`
}