/* *****************************************************************
 * 120EDC全局Javascript脚本
 * 兼容性：该脚本将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
 * -----------------------------------------------------------------
 * Creator: 陈蒋耀
 * Created: 2019/6/10
 * -----------------------------------------------------------------
 * Modification:
 * Modifier:
 * Modified:
 * *****************************************************************/
var _user = null;//用户数据
var _assets = null;//设备清单
var _stations = [];//所有分站信息

$(document).ready(function () {
    $('#eventDetailRegion').hide();
    //读取接口服务器基地址
    _baseUrl = gProxy.getBaseUrl();
    setInterval(function () {
        var now = new Date();
        $('#cur-date-time').html(now.formatString('yyyy-MM-dd hh:mm:ss'));
    }, 1000);
    setInterval(function () {
        if (_token == null) return;
        refreshMobileStatus();
    }, 10000);
});

function setUI() {
    //设置车辆信息刷新计时器
    setSelection();
    refreshMobileStatus();
    initMap();
    setAutoComplete();
}

function setSelection() {
    getRegionStations(function (data) {
        _stations = data.selectMany(function (s) { return s.stations; });
        $('#station-list').empty();
        $('#station-list').append('<option value="_all">全部分站</option>');
        for (var i = 0; i < _stations.length; i++) {
            var sta = _stations[i];
            $('#station-list').append('<option value="{0}">{1}</option>'.formatStr(sta.id, sta.stationName));
        }
    });
}

function onStationSel() {
    var id = $('#station-list').val();
    refreshAssets(id);
}

function getAllAssets() {
    getAssetsByStation(null,
        function(data) {
            _assets = data;
            refreshAssets($('#station-list').val());
        });
}

var _currentAsset = {"code":"","chan":"","name":""};

function refreshAssets(id) {
    var list = _assets;
    if (id != '_all') {
        list = _assets.where(function(l) { return l.stationId == id; });
    }
    var tb = $('#car-status-body');
    tb.empty();
    for (var i = 0; i < list.length; i++) {
        var ass = list[i];
        tb.append('<tr code="{0}" chan="{1}">\
            <td style="width: 30%;">{2}</td>\
            <td style="width: 30%;">{3}</td>\
            <td style="width: 30%;">{4}</td>\
            <td style="width: 10%;">{5}</td></tr>'.formatStr(
                ass.assetCode, ass.assetChannel, ass.assetName, ass.stationName, ass.carName == undefined || ass.carName.isEmpty()?'':ass.carName, ass.carId == undefined || ass.carId.isEmpty()?'':getTextByMobileStatus(getMobileFullInfo(ass.carId).status)
            ));
    }
    $('#car-status-body tr td').mouseup(function (e) {
        if (3 == e.which) {
            _currentAsset.code = $(e.target).parent().attr('code');
            _currentAsset.chan = $(e.target).parent().attr('chan');
            _currentAsset.name = $(e.target).parent().children("td:eq(0)").html() +
                "[" +
                $(e.target).parent().children("td:eq(1)").html() +
                "·" +
                $(e.target).parent().children("td:eq(2)").html() + "]";
            $('#menu-mobile').menu('show',
                {
                    left: e.clientX,
                    top: e.clientY
                });
        }
    });
}

function refreshMobileStatus() {
    refreshMobiles(null, function (data) {
        sortAndShowStations(_mobiles);
        getAllAssets();
        gProxy.syncMobiles(JSON.stringify(_mobiles));
    });
}

function getTextByMobileStatus(mobileStatus) {
    if (mobileStatus == "1") {
        return '<span style="color:red;">发车</span>';
    }
    else if (mobileStatus == "2") {
        return '<span style="color:red;">抵达</span>';
    }
    else if (mobileStatus == "3") {
        return '<span style="color:red;">接诊取消</span>';
    }
    else if (mobileStatus == "8") {
        return '<span style="color:red;">离开现场</span>';
    }
    else if (mobileStatus == "4") {
        return '<span style="color:red;">接诊完成</span>';
    }
    else if (mobileStatus == "5") {
        return '<span style="color:red;">接到调度</span>';
    }
    return '<span style="color:green;">待命</span>';
}

function showLogin() {
    $('#loginPanel').show();
    $('#loginForm').show();
    $('#changePwdForm').hide();
}

function showChangePwd() {
    $('#loginForm').hide();
    $('#changePwdForm').show();
    $('#cp-un').val('');
    $('#cp-pwd').val('');
    $('#cp-newPwd1').val('');
    $('#cp-newPwd2').val('');
}

function loginSuccess() {
    _user = evalJson(gProxy.getUserInfo());
    _token = _user.Token;
    gProxy.loginSuccess(_token);
    setUI();
    $('#dispatcher-name').html(_user.DisplayName);
    $('#dispatcher-code').html(_user.Username);
    $('#loginPanel').hide();
}

function changePwdSuccess() {
    $.messager.alert('修改密码', '密码修改成功！');
    showLogin();
}

function doLogin() {
    var res = gProxy.login($('#login-un').val(), $('#login-pwd').val());
    if (res == true) {
        loginSuccess();
    } else {
        $.messager.alert('登录', res);
    }
    return false;
}

function doChangePwd() {
    var newPwd1 = $('#cp-newPwd1').val();
    var newPwd2 = $('#cp-newPwd2').val();
    if (newPwd1 != newPwd2) {
        $.messager.alert('修改密码', '新密码两次输入不同！');
    } else {
        var res = gProxy.changePwd($('#cp-un').val(), $('#cp-pwd').val(), newPwd1);
        if (res == true) {
            changePwdSuccess();
        } else {
            $.messager.alert('修改密码', res);
        }
    }
    return false;
}

function doLogout() {
    $.messager.confirm('注销登录',
        '确定要注销吗？',
        function (s) {
            if (s == true) {
                var res = gProxy.logout();
                if (res == true) {
                    $('#login-un').val('');
                    $('#login-pwd').val('');
                    _token = null;
                    showLogin();
                } else {
                    $.messager.alert('注销登录', res);
                }
            }
        });
}

function getMobileFullInfo(mobileId) {
    var ret = _mobiles.first(function (m) { return m.id == mobileId; });
    return ret;
}

function menuHandler(item) {
    var name = item.name;
    if (name == 'promote1') {
        promoteCamera(0);
    } else if (name == 'promote2') {
        promoteCamera(1);
    } else if (name == 'promote3') {
        promoteCamera(2);
    } else if (name == 'promote4') {
        promoteCamera(3);
    } else if (name == 'promote5') {
        promoteCamera(4);
    } else if (name == 'promote6') {
        promoteCamera(5);
    }
}

function promoteCamera(placeId) {
    gProxy.promoteCam(placeId, _currentAsset.code, parseInt(_currentAsset.chan), _currentAsset.name);
}

var _currentLoc = null;
function sortAndShowStations(staList) {
    var temp = [].slice.call(staList);
    for (var i = 0; i < temp.length; i++) {
        var data = temp[i];
        data.distance = MAX_DISTANCE;
        if (_currentLoc != null) {
            //算距离
            caculateRouteDistance(data,
                function (dist, staData) {
                    staData.distance = dist;
                    ////重新排序并展示
                    //reRangeAndShowStations(temp);
                });
        }
    }
    reRangeAndShowStations(temp);
}

function reRangeAndShowStations(staList) {
    //map.clearOverlays(); //清除地图上所有覆盖物
    drawStations();
    for (var j = 0; j < staList.length; j++) {
        var data = staList[j];
        drawMobile(data);
    }
}

function G(id) {
    return document.getElementById(id);
}

var map;

function initMap() {
    //百度地图
    map = new BMap.Map("the-map");    // 创建Map实例
    map.centerAndZoom(new BMap.Point(114.42586, 23.093224), 13);  // 初始化地图,设置中心点坐标和地图级别
    var top_left_control = new BMap.ScaleControl({ anchor: BMAP_ANCHOR_TOP_LEFT });// 左上角，添加比例尺
    var top_left_navigation = new BMap.NavigationControl();  //左上角，添加默认缩放平移控件
    //添加地图类型控件
    map.addControl(new BMap.MapTypeControl({
        mapTypes: [
            BMAP_NORMAL_MAP,
            BMAP_HYBRID_MAP
        ]
    }));
    map.addControl(top_left_control);
    map.addControl(top_left_navigation);
    querySysConf('map_city',
        function (data) {
            map.setCurrentCity(data); // 设置地图显示的城市 此项是必须设置的
            map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
        },
        function (e, url, errMsg) {
            //失败才做处理
            $.messager.alert('异常', '获取城市配置失败，异常信息：' + errMsg);
        });
}

function setAutoComplete() {
    var ac = new BMap.Autocomplete(    //建立一个自动完成的对象
        {
            "input": "map-search-box",
            "location": map
        });

    var myValue;
    ac.addEventListener("onhighlight", function (e) {  //鼠标放在下拉列表上的事件
        var str = "";
        var _value = e.fromitem.value;
        var value = "";
        if (e.fromitem.index > -1) {
            value = _value.province + _value.city + _value.district + _value.street + _value.business;
        }
        str = "FromItem<br />index = " + e.fromitem.index + "<br />value = " + value;

        value = "";
        if (e.toitem.index > -1) {
            _value = e.toitem.value;
            value = _value.province + _value.city + _value.district + _value.street + _value.business;
        }
        str += "<br />ToItem<br />index = " + e.toitem.index + "<br />value = " + value;
        G("searchResultPanel").innerHTML = str;
    });

    ac.addEventListener("onconfirm", function (e) {    //鼠标点击下拉列表后的事件
        var _value = e.item.value;
        myValue = _value.province + _value.city + _value.district + _value.street + _value.business;
        G("searchResultPanel").innerHTML = "onconfirm<br />index = " + e.item.index + "<br />myValue = " + myValue;

        setPlace(myValue);
        try {
            gProxy.sendToMap(myValue);
        } catch (e) {
            bsProxy.sendToMap(myValue);
        }
        
    });
}

function setPlace(value) {
    //map.clearOverlays();    //清除地图上所有覆盖物
    function myFun() {
        var ppr = local.getResults().getPoi(0);    //获取第一个智能搜索的结果
        if (ppr == undefined) {
            _currentLoc = null;
        } else {
            var pp = ppr.point;
            _currentLoc = pp;
        }
        //map.centerAndZoom(_currentLoc, 18);
        sortAndShowStations(_mobiles);
    }
    var local = new BMap.LocalSearch(map, { //智能搜索
        onSearchComplete: myFun
    });
    local.search(value);
}

function drawStations() {
    //绘制所有分站图标
    for (var i = 0; i < _stations.length; i++) {
        var sta = _stations[i];
        var pt = new BMap.Point(sta.stationLongitude, sta.stationLatitude);
        var myIcon = new BMap.Icon("style/img/hospital.png", new BMap.Size(64, 64));
        var marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
        map.addOverlay(marker);// 将标注添加到地图中
        var label = new BMap.Label('<span style="color:green; font-size:1.2em;">{0}</span>'.formatStr(sta.stationName), { offset: new BMap.Size(0, 60) });
        marker.setLabel(label);
    }
    //绘制定点坐标
    if (_currentLoc != null) {
        map.addOverlay(new BMap.Marker(_currentLoc)); //添加标注
    }
}

function drawMobile(car) {
    //绘制车辆图标
    var pt = new BMap.Point(car.lng, car.lat);
    var myIcon = new BMap.Icon("style/img/ambul.png", new BMap.Size(48, 48));
    var marker = new BMap.Marker(pt, { icon: myIcon });  // 创建标注
    map.addOverlay(marker);// 将标注添加到地图中
    var label = new BMap.Label('<div onclick="autoSelectCar(\'{2}\')" style="color:red; font-size:1em; cursor:pointer;">{1}<br/>{0}</div>'.formatStr(car.carName, car.stationName, car.id), { offset: new BMap.Size(0, 40) });
    marker.setLabel(label);
}

function autoSelectCar(carId) {
    var ass = _assets.first(function(a) {
         return a.carId == carId;
    });
    if (ass != null) {
        _currentAsset.code = ass.assetCode;
        _currentAsset.chan = ass.assetChannel;
        _currentAsset.name = ass.assetName +
            "[" +
            ass.stationName +
            "·" +
            ass.carName +
            "]";
        $('#menu-mobile').menu('show',
            {
                left: event.clientX,
                top: event.clientY
            });
    }
}

function manualSearch() {
    setPlace($('#map-search-box').val());
    gProxy.sendToMap($('#map-search-box').val());
}

function caculateRouteDistance(carData, callback) {
    var distance = MAX_DISTANCE;
    if (_currentLoc != null) {
        distance = getDistance(carData.lat, carData.lng, _currentLoc.lat, _currentLoc.lng);
    }
    callback(distance, carData);
}