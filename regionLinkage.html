<!--
/* *****************************************************************
* 120EDC急救指挥中心区域联动界面
* 用于显示和管理区域联动事件的状态和列表
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: AI Assistant
* Created: 2024/12/19
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心区域联动管理</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .l-btn-left l-btn-icon-left {
            margin-top: -1px;
        }
        /* 只针对非TreeGrid的DataGrid应用样式 */
        .datagrid:not(.treegrid) .datagrid-cell, 
        .datagrid:not(.treegrid) .tree-title {
            font-size: 14px !important;
            font-weight: bold !important;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }
        .window-body{
            overflow-y:hidden;
        }
        /*只针对非TreeGrid的DataGrid应用行高设置*/
         .datagrid:not(.treegrid) .datagrid-row {
            height: 28px;
        }
         .datagrid:not(.treegrid) .datagrid-btable tr {
             height: 0px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover,
        .datagrid-row-selected {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        .datagrid-wrap .datagrid-body tr:hover a,
        .datagrid-row-selected a {
            color: #ffffff !important;
        }

        /* 确保选中行的背景色优先级最高 */
        .datagrid-row-selected {
            background: #12B5F8 !important;
            color: #ffffff !important;
        }
        
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
        }
        input[type="checkbox"]:checked::before {
            content: "\2713";
            background-color: #fff;
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            color: #000;
            font-size: 20px;
            font-weight: bold;
        }
        
        input[readonly], textarea[readonly] {
            background: #fff !important;
        }
        
        /* 区域联动状态样式 */
        .linkage-status-1 { color: #722ed1; font-weight: bold; }  /* 发送成功 - 紫色 */
        .linkage-status-2 { color: #1890ff; font-weight: bold; }  /* 已接收 - 蓝色 */
        .linkage-status-3 { color: #fa8c16; font-weight: bold; }  /* 人工确认 - 橙色 */
        .linkage-status-4 { color: #52c41a; font-weight: bold; }  /* 开始处理 - 绿色 */
        .linkage-status-5 { color: #8c8c8c; font-weight: bold; }  /* 处理结束 - 灰色 */
        
        .linkage-status-icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            border-radius: 50%;
            margin-right: 5px;
        }
        .linkage-status-icon-1 { background-color: #722ed1; }
        .linkage-status-icon-2 { background-color: #1890ff; }
        .linkage-status-icon-3 { background-color: #fa8c16; }
        .linkage-status-icon-4 { background-color: #52c41a; }
        .linkage-status-icon-5 { background-color: #8c8c8c; }
        
    </style>
</head>
<body>
    <div id="mainPanel_RegionLinkage" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">区域联动事件列表<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="linkageSearch" style="font-size: 14px;">
                    创建时间： <input id="startCreateTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endCreateTime" class="easyui-datetimebox" style="width:160px;" />
                    &nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>
                    <span>&nbsp;事故地址：</span>
                    <input id="address" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;发送中心：</span>
                    <input id="sourceRegion" class="easyui-combobox" style="width:150px;" 
                           data-options="valueField:'codeName',textField:'codeVale',editable:false">
                    <span>&nbsp;接收中心：</span>
                    <input id="targetRegion" class="easyui-combobox" style="width:150px;" 
                           data-options="valueField:'codeName',textField:'codeVale',editable:false">

                    <span>&nbsp;状态：</span>
                    <select id="feedbackStatus" class="easyui-combobox" name="feedbackStatus" style="width:120px;">
                        <option value="0">全部</option>
                        <option value="1">发送成功</option>
                        <option value="2">已接收</option>
                        <option value="3">人工确认</option>
                        <option value="4">开始处理</option>
                        <option value="5">处理结束</option>
                    </select>
                    
                    <span>&nbsp;方向：</span>
                    <select id="direction" class="easyui-combobox" name="direction" style="width:100px;">
                        <option value="">全部</option>
                        <option value="1">发送</option>
                        <option value="2">接收</option>
                    </select>
                    
                    &ensp;<input type="checkbox" id="autoRefreshList" name="autoRefreshList" value="0" />自动刷新
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getLinkageEventListDatas();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                    <a class="common-button" style="width: 120px; height: 30px; line-height: 30px;font-size: 14px; margin-left: 10px;" href="javascript:openCityCountyLinkage();">
                        <i class="fa fa-external-link"></i>&nbsp;打开市县联动
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-linkage-event-pages" style="height: Calc(100% - 100px)"
                       data-options="pagination:true,singleSelect:true,rownumbers:true,fit:true,fitColumns:false,toolbar:'#linkageTb'">
                    <thead data-options="frozen:true">
                        <tr>
                            <th data-options="field:'linkageStatusIcon',width:100,align:'center',formatter:setLinkageStatusIconFormatter">联动状态</th>
                        </tr>
                    </thead>
                    <thead>
                        <tr>
                            <th data-options="field:'eventId',width:210,align:'center'">外部业务ID</th>
                            <th data-options="field:'eventSource',width:100,align:'center',formatter:formatEventSource">事件来源</th>
                            <th data-options="field:'address',width:360,align:'center'">事故地址</th>
                            <th data-options="field:'sourceRegion',width:140,align:'center'">发送中心</th>
                            <th data-options="field:'targetRegion',width:140,align:'center'">接收中心</th>
                            <th data-options="field:'createTime',width:140,align:'center'">记录时间</th>
                            <th data-options="field:'linkageType',width:120,align:'center',formatter:formatLinkageType">联动类型</th>
                            <th data-options="field:'callIn',width:120,align:'center'">呼救电话</th>
                            <th data-options="field:'callInTimes',width:160,align:'center'">来电时间</th>
                            <th data-options="field:'callReason',width:300,align:'left'">呼叫原因</th>
                            <th data-options="field:'direction',width:80,align:'center',formatter:formatDirection">方向</th>
                            <th data-options="field:'linkageStatusStr',width:100,align:'center'">联动状态</th>
                            <th data-options="field:'contact',width:120,align:'center'">联系电话</th>
                            <th data-options="field:'contacter',width:100,align:'center'">联系人</th>
                           
                            <th data-options="field:'processingUser',width:100,align:'center'">操作人</th>
                            <th data-options="field:'linkageRemark',width:200,align:'center'">备注</th>
                          
                            <!-- <th data-options="field:'linkageId',width:210,align:'center'">联动编号</th> -->
                        </tr>
                    </thead>
                </table>

                <div id="linkageTb" style="height:45px;">
                    <div>
                        <a id="confirmLinkageBtn" class="easyui-linkbutton" href="javascript:confirmLinkageEvent();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-ok'">人工确认</a>
                        <a id="startProcessBtn" class="easyui-linkbutton" href="javascript:startProcessEvent();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-exec'">开始处理</a>
                        <a id="openLinkageDetailBtn" class="easyui-linkbutton" href="javascript:openLinkageDetail();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看详情</a>
                        <a id="refreshLinkageBtn" class="easyui-linkbutton" href="javascript:refreshLinkageList();" style="width: 70px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-reload'">刷新</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 联动事件详情窗口 -->
        <div id="linkageDetailWindow" class="easyui-window" title="联动事件详情" style="width:800px;height:600px"
             data-options="iconCls:'icon-show',modal:true, closed: true">
            <div style="padding: 20px; overflow-y: auto; height: calc(100% - 40px);">
                <div id="linkageDetailContent" style="font-size: 14px; line-height: 1.6;">
                    <!-- 详情内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
        
        // 添加String.formatStr方法
        String.prototype.formatStr = function () {
            var args = arguments;
            return this.replace(/\{(\d+)\}/g, function (m, i) {
                return args[i];
            });
        };
        
        // API数据将替代模拟数据
        _dict_country = [];
        _dict_race = [];
        _dict_gender =[];

        $(document).ready(function () {
            // 初始化时间控件
            var curDate = new Date();
            var fromTime = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
            $("#startCreateTime").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            
            // 为所有快速时间操作设置处理函数
            $('.inAnHour,.inToday').on("click", timeClickHandler);
            
            // 监听自动刷新复选框的变化
            $('#autoRefreshList').on('change', function() {
                if ($(this).is(':checked')) {
                    // 启动自动刷新
                    _linkageAutoRefreshInterval = true;
                    linkageAutoRefreshIntervalTimeout();
                } else {
                    // 停止自动刷新
                    _linkageAutoRefreshInterval = false;
                    clearTimeout(window.linkageAutoRefreshInterval);
                }
            });
            
            // 初始化DataGrid
            $('#dg-linkage-event-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
                animate: false,
                nowrap: true,
                rownumbers: true,
                singleSelect: true,
                fit: true,
                fitColumns: false,
                rowStyler: function (index, row) {
                    // 根据联动状态设置行样式
                    switch (row.linkageStatus) {
                        case '1':
                            return 'background-color:#f9f0ff;'; // 浅紫色 - 发送成功
                        case '2':
                            return 'background-color:#e6f7ff;'; // 浅蓝色 - 已接收
                        case '3':
                            return 'background-color:#fff2e6;'; // 浅橙色 - 人工确认
                        case '4':
                            return 'background-color:#f6ffed;'; // 浅绿色 - 开始处理
                        case '5':
                            return 'background-color:#f5f5f5;'; // 浅灰色 - 处理结束
                        default:
                            return '';
                    }
                },
                onClickRow: function (rowIndex, rowData) {
                    updateButtonState(rowData);
                },
                onDblClickRow: function(rowIndex, rowData) {
                    openLinkageDetail();
                }
            });
            
            // 页面加载完成后立即加载数据
            getLinkageEventListDatas();
            linkageAutoRefreshIntervalTimeout();

            // 查询国籍、民族存储到全局变量
            queryAllDic(["country"],function(data) {
                _dict_country = data;
            });
            queryAllDic(["race"],function(data) {
                _dict_race = data;
            });
            queryAllDic(["patient_gender"],function(data) {
                _dict_gender = data;
            });
            queryAllDic(["push_outer_type"], function(data) {
                console.log("字典数据加载成功：", data);
                
                // 检查数据结构
                if (!data || data.length === 0) {
                    console.error("未获取到字典数据");
                    return;
                }
                
                // 添加"全部"选项
                var allData = [{codeName: '', codeVale: '全部'}];
                
                // 合并数据
                if (data && data.length > 0) {
                    allData = allData.concat(data);
                }
                console.log('合并后的数据:', allData);
                
                // 初始化发送中心下拉框
                $('#sourceRegion').combobox({
                    data: allData,
                    valueField: 'codeName',
                    textField: 'codeVale',
                    editable: false,
                    onLoadSuccess: function() {
                        // 设置默认选中"全部"
                        $(this).combobox('setValue', '');
                    }
                });
                
                // 初始化接收中心下拉框
                $('#targetRegion').combobox({
                    data: allData,
                    valueField: 'codeName',
                    textField: 'codeVale',
                    editable: false,
                    onLoadSuccess: function() {
                        // 设置默认选中"全部"
                        $(this).combobox('setValue', '');
                    }
                });
            }, function(e, url, errMsg) {
                console.error('获取中心数据失败:', errMsg);
                $.messager.alert('错误', '获取中心数据失败: ' + errMsg, 'error');
            });
        });

        /**
         * 联动状态图标格式化
         */
        function setLinkageStatusIconFormatter(value, row, index) {
            var statusIcon = '';
            var statusText = '';
            
            switch (row.linkageStatus) {
                case '1':
                    statusIcon = '<span class="linkage-status-icon linkage-status-icon-1"></span>';
                    statusText = '<span class="linkage-status-1">发送成功</span>';
                    break;
                case '2':
                    statusIcon = '<span class="linkage-status-icon linkage-status-icon-2"></span>';
                    statusText = '<span class="linkage-status-2">已接收</span>';
                    break;
                case '3':
                    statusIcon = '<span class="linkage-status-icon linkage-status-icon-3"></span>';
                    statusText = '<span class="linkage-status-3">人工确认</span>';
                    break;
                case '4':
                    statusIcon = '<span class="linkage-status-icon linkage-status-icon-4"></span>';
                    statusText = '<span class="linkage-status-4">开始处理</span>';
                    break;
                case '5':
                    statusIcon = '<span class="linkage-status-icon linkage-status-icon-5"></span>';
                    statusText = '<span class="linkage-status-5">处理结束</span>';
                    break;
                default:
                    statusIcon = '<span class="linkage-status-icon"></span>';
                    statusText = '<span>未知状态</span>';
            }
            
            return statusIcon + statusText;
        }
        
        /**
         * 联动类型格式化
         */
        function formatLinkageType(value, row, index) {
            switch (value) {
                case '1':
                    return '120联动';
                case '2':
                    return '110联动';
                case '3':
                    return '122联动';
                case '4':
                    return '119联动';
                default:
                    return value || '';
            }
        }

        /**
         * 获取联动事件列表数据
         */
        function getLinkageEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }
            
            // 获取搜索条件
            var startCreateTimeVal = $('#startCreateTime').datetimebox('getValue');
                          var endCreateTimeVal = $('#endCreateTime').datetimebox('getValue');
            var sourceRegionVal = $('#sourceRegion').combobox('getValue');
            var targetRegionVal = $('#targetRegion').combobox('getValue');
              var addressVal = $('#address').val();
            var feedbackStatusVal = $('#feedbackStatus').combobox('getValue');
            var directionVal = $('#direction').combobox('getValue');
            
            // 构建API请求参数
            var requestParams = {
                page: pageNum,
                size: pageSize,
                beginTime: startCreateTimeVal || '',
                endTime: endCreateTimeVal || '',
                address: addressVal || '',
                feedbackStatus: feedbackStatusVal !== '0' ? feedbackStatusVal : '',
                sortBy: [{"recordTime":"1"}], // 按记录时间倒序排序
                direction: directionVal || ''
            };
            
            // 根据发送/接收中心筛选
            if (sourceRegionVal) {
                requestParams.sendRegionCode = sourceRegionVal;
            }
            
            if (targetRegionVal) {
                requestParams.receiveRegionCode = targetRegionVal;
            }
            
            console.log('请求参数:', requestParams);
            
            // 打开进度条
            $('#dg-linkage-event-pages').datagrid('loading');
            
            // 调用API获取数据
            getEventRegionLinkageList(requestParams, function(response) {
                console.log('API返回数据:', response);
                
                if (response && response.records) {
                    // 处理API返回的数据
                    var rows = processApiData(response.records);
                    
                    // 加载数据到表格
                $('#dg-linkage-event-pages').datagrid('loadData', { 
                        total: response.total || 0,
                        rows: rows
                });
                
                    // 更新分页控件
                var pg = $("#dg-linkage-event-pages").datagrid("getPager");
                if (pg && pg.length > 0) {
                    pg.pagination({
                            total: response.total || 0,
                        pageSize: pageSize,
                        pageNumber: pageNum,
                        pageList: [10, 20, 30, 40, 50],
                        onSelectPage: function(pageNumber, pageSize) {
                            getLinkageEventListDatas(pageNumber, pageSize);
                        }
                        });
                    }
                } else {
                    // 如果没有数据，显示空表格
                    $('#dg-linkage-event-pages').datagrid('loadData', {
                        total: 0,
                        rows: []
                    });
                }
                
                // 关闭进度条
                $('#dg-linkage-event-pages').datagrid('loaded');
            }, function(e, url, errMsg) {
                console.error('获取联动事件列表失败:', errMsg);
                $.messager.alert('错误', '获取联动事件列表失败: ' + errMsg, 'error');
                
                // 显示空表格
                $('#dg-linkage-event-pages').datagrid('loadData', {
                    total: 0,
                    rows: []
                });
                
                // 关闭进度条
                $('#dg-linkage-event-pages').datagrid('loaded');
            });
        }
        
        /**
         * 处理API返回的数据
         */
        function processApiData(records) {
            var rows = [];
            
            if (!records || !records.length) {
                return rows;
            }
            
            for (var i = 0; i < records.length; i++) {
                var record = records[i];
                
                // 构建行数据，使用API返回的字段
                var row = {
                    id: record.id,
                    eventId: record.externalBizId || '',
                    eventSource: record.ldType || '', // 事件来源依据联动类型
                    linkageId: record.id || '',
                    callReason: record.callReason || '',
                    sourceRegion: record.sendRegionName || '',
                    targetRegion: record.receiveRegionName || '',
                    createTime: record.recordTime || '',
                    linkageType: record.ldType || '',
                    urgencyLevel: '',
                    callIn: record.callIn || '',
                    callInTimes: record.callInTimes || '',
                    linkageStatus: record.feedbackStatus || '',
                    linkageStatusStr: getFeedbackStatusText(record.feedbackStatus),
                    eventType: '',
                    eventTypeStr: '',
                    contact: record.contact || '',
                    contacter: record.contacter || '',
                    address: record.address || '',
                    receivedTime: record.recordTime || '',
                    confirmedTime: '',
                    startProcessTime: '',
                    endProcessTime: '',
                    processingUser: record.optUser || '',
                    linkageRemark: record.remark || '',
                    direction: record.direction || ''
                };
                
                rows.push(row);
            }
            
            return rows;
        }
        
        /**
         * 获取反馈状态文本
         */
        function getFeedbackStatusText(status) {
            switch (status) {
                case '1':
                    return '发送成功';
                case '2':
                    return '已接收';
                case '3':
                    return '人工确认';
                case '4':
                    return '开始处理';
                case '5':
                    return '处理结束';
                default:
                    return '未知状态';
            }
        }

        /**
         * 更新按钮状态
         */
        function updateButtonState(rowData) {
            if (!rowData) {
                $('#confirmLinkageBtn').linkbutton('disable');
                $('#startProcessBtn').linkbutton('disable');
                return;
            }
            
            // 默认禁用所有按钮
            $('#confirmLinkageBtn').linkbutton('disable');
                    $('#startProcessBtn').linkbutton('disable');
            
            // 只有当方向为"2"接收时才考虑启用按钮
            if (rowData.direction === '2') {
                switch (rowData.linkageStatus) {
                    case '2': // 已接收
                        $('#confirmLinkageBtn').linkbutton('enable');
                    break;
                case '3': // 人工确认
                    $('#startProcessBtn').linkbutton('enable');
                    break;
                case '4': // 开始处理
                    $('#startProcessBtn').linkbutton('enable');
                    break;
                }
            }
        }

        /**
         * 人工确认联动事件
         */
        function confirmLinkageEvent() {
            var rowData = $('#dg-linkage-event-pages').datagrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请选择要确认的联动事件', 'warning');
                return;
            }
            
            if (rowData.direction !== '2') {
                $.messager.alert('提示', '只有接收方向的联动事件才能进行人工确认', 'warning');
                return;
            }
            
            if (rowData.linkageStatus !== '2') {
                $.messager.alert('提示', '只有"已接收"状态的事件可以进行人工确认', 'warning');
                return;
            }
            
            if (!rowData.eventId) {
                $.messager.alert('提示', '该联动事件没有外部业务ID，无法更新状态', 'warning');
                return;
            }
            
            $.messager.confirm('确认', '是否确认联动事件？', function(r) {
                if (r) {
                    // 显示进度条
                    showProcess(true, '温馨提示', '正在处理，请稍后...');
                    
                    // 构建API请求参数
                    var requestParams = {
                        externalBizId: rowData.eventId,
                        feedbackStatus: '3' // 人工确认
                    };
                    
                    // 调用API更新状态
                    updateEventRegionLinkageFeedbackStatus(requestParams, function() {
                        showProcess(false);
                    $.messager.alert('提示', '联动事件确认成功', 'info');
                    getLinkageEventListDatas();
                    }, function(e, url, errMsg) {
                        showProcess(false);
                        $.messager.alert('错误', '联动事件确认失败：' + errMsg, 'error');
                    });
                }
            });
        }

        /**
         * 开始处理联动事件
         */
        function startProcessEvent() {
            var rowData = $('#dg-linkage-event-pages').datagrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请选择要开始处理的联动事件', 'warning');
                return;
            }
            
            if (rowData.direction !== '2') {
                $.messager.alert('提示', '只有接收方向的联动事件才能开始处理', 'warning');
                return;
            }

            window.parent.opener.createNewEventByRegionLinkage(rowData.id,rowData.eventId);
            
            
        }

        

        /**
         * 查看联动事件详情
         */
        function openLinkageDetail() {
            var rowData = $('#dg-linkage-event-pages').datagrid('getSelected');
            if (!rowData) {
                $.messager.alert('提示', '请选择要查看的联动事件', 'warning');
                return;
            }
            
            // 显示进度条
            showProcess(true, '温馨提示', '正在获取详情，请稍后...');
            
            // 调用API获取详细信息
            getEventRegionLinkageById(rowData.id, function(detailData) {
                showProcess(false);
                
                // 构建详情HTML
            var detailHtml = '<div class="linkage-detail">';
            detailHtml += '<h3>联动事件详情</h3>';
            detailHtml += '<table style="width:100%; border-collapse: collapse;">';
                // detailHtml += '<tr><td style="width:150px; font-weight:bold; padding:8px; border:1px solid #ddd;">联动编号：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.id || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">外部业务ID：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.externalBizId || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">呼叫原因：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.callReason || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">发送中心：</td><td style="padding:8px; border:1px solid #ddd;">' + detailData.sendRegionName +'</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">接收中心：</td><td style="padding:8px; border:1px solid #ddd;">' + detailData.receiveRegionName +'</td></tr>';
                
                // 联动类型
                var linkageTypeText = '';
                switch (detailData.ldType) {
                    case '1': linkageTypeText = '120联动'; break;
                    case '2': linkageTypeText = '110联动'; break;
                    case '3': linkageTypeText = '112联动'; break;
                    case '4': linkageTypeText = '119联动'; break;
                    default: linkageTypeText = detailData.ldType || '-';
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">联动类型：</td><td style="padding:8px; border:1px solid #ddd;">' + linkageTypeText + '</td></tr>';
                
                // 联动方向
                var directionText = '';
                switch (detailData.direction) {
                    case '1': directionText = '发送'; break;
                    case '2': directionText = '接收'; break;
                    default: directionText = '-';
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">联动方向：</td><td style="padding:8px; border:1px solid #ddd;">' + directionText + '</td></tr>';
                
                // 反馈状态
                var feedbackStatusText = '';
                var statusClass = '';
                switch (detailData.feedbackStatus) {
                    case '1': 
                        feedbackStatusText = '发送成功'; 
                        statusClass = 'linkage-status-1';
                        break;
                    case '2': 
                        feedbackStatusText = '已接收'; 
                        statusClass = 'linkage-status-2';
                        break;
                    case '3': 
                        feedbackStatusText = '人工确认'; 
                        statusClass = 'linkage-status-3';
                        break;
                    case '4': 
                        feedbackStatusText = '开始处理'; 
                        statusClass = 'linkage-status-4';
                        break;
                    case '5': 
                        feedbackStatusText = '处理结束'; 
                        statusClass = 'linkage-status-5';
                        break;
                    default: 
                        feedbackStatusText = '未知状态';
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">反馈状态：</td><td style="padding:8px; border:1px solid #ddd;"><span class="' + statusClass + '">' + feedbackStatusText + '</span></td></tr>';
                
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">现场地址：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.address || '-') + '</td></tr>';
                
                // 经纬度信息
                if (detailData.lng && detailData.lat) {
                    detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">经纬度：</td><td style="padding:8px; border:1px solid #ddd;">经度: ' + detailData.lng + ', 纬度: ' + detailData.lat + '</td></tr>';
                }
                
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">联系人：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.contacter || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">联系电话：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.contact || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">呼救电话：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.callIn || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">来电时间：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.callInTimes || '-') + '</td></tr>';
                // detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">记录时间：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.recordTime || '-') + '</td></tr>';
                
                // 患者信息部分
                detailHtml += '<tr><td colspan="2" style="font-weight:bold; padding:8px; border:1px solid #ddd; background-color:#f5f5f5;">患者信息</td></tr>';
                
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">患者姓名：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.patientName || '-') + '</td></tr>';
                
                // 患者性别
                var genderText = '-';
                if (_dict_gender && _dict_gender.length > 0) {
                    var genderMatch = _dict_gender.find(function(item) {
                        return item.codeName === detailData.patientGender; 
                    });
                    if (genderMatch) {
                        genderText = genderMatch.codeVale;
                    }
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">患者性别：</td><td style="padding:8px; border:1px solid #ddd;">' + genderText + '</td></tr>';
                
                // 患者年龄
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">患者年龄：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.patientAge || '-') + '</td></tr>';
                
                // 患者国籍
                var countryText = '-';
                // 从字典中查找国籍名称
                if (_dict_country && _dict_country.length > 0) {
                    var countryMatch = _dict_country.find(function(item) {
                        return item.codeName === detailData.patientCountry;
                    });
                    if (countryMatch) {
                        countryText = countryMatch.codeVale;
                    }
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">患者国籍：</td><td style="padding:8px; border:1px solid #ddd;">' + countryText + '</td></tr>';
                
                // 患者民族
                var raceText = '';
                // 从字典中查找民族名称
                if (_dict_race && _dict_race.length > 0) {
                    var raceMatch = _dict_race.find(function(item) {
                        return item.codeName === detailData.patientRace;
                    });
                    if (raceMatch) {
                        raceText = raceMatch.codeVale;
                    }
                }
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">患者民族：</td><td style="padding:8px; border:1px solid #ddd;">' + raceText + '</td></tr>';
                
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">操作人：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.optUser || '-') + '</td></tr>';
                detailHtml += '<tr><td style="font-weight:bold; padding:8px; border:1px solid #ddd;">备注：</td><td style="padding:8px; border:1px solid #ddd;">' + (detailData.remark || '-') + '</td></tr>';
            detailHtml += '</table>';
            detailHtml += '</div>';
            
            $('#linkageDetailContent').html(detailHtml);
            $('#linkageDetailWindow').window('open');
            }, function(e, url, errMsg) {
                showProcess(false);
                $.messager.alert('错误', '获取联动事件详情失败：' + errMsg, 'error');
            });
        }

        /**
         * 刷新联动事件列表
         */
        function refreshLinkageList() {
            getLinkageEventListDatas();
        }

        /**
         * 时间快捷操作处理
         */
        function timeClickHandler() {
            var from = $(this).parent().children('input:nth-of-type(1)');
            var to = $(this).parent().children('input:nth-of-type(2)');
            var now = new Date();
            var fromTime = new Date();
            var hours = 24;
            if ($(this).hasClass('inAnHour')) {
                hours = 1;
            }
            fromTime.setHours(fromTime.getHours() - hours);
            from.val(fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            to.val(null);
            easyUiDateTimeSet();
            // 自动搜索
            getLinkageEventListDatas(1, 20);
        }

        /**
         * 设置时间控件值
         */
        function easyUiDateTimeSet() {
            $('.easyui-datetimebox').each(function () {
                var e = $(this);
                e.datetimebox('setValue', e.val());
            });
        }

        // 定时刷新任务开关
        var _linkageAutoRefreshInterval = true;
        
        /**
         * 设置联动事件列表刷新计时器
         */
        function linkageAutoRefreshIntervalTimeout() {
            clearTimeout(window.linkageAutoRefreshInterval);
            if (_linkageAutoRefreshInterval) {
                window.linkageAutoRefreshInterval = null;
                if ($('#autoRefreshList').is(':checked')) {
                    getLinkageEventListDatas(null, null);
                    window.linkageAutoRefreshInterval = setTimeout(linkageAutoRefreshIntervalTimeout, 60000);
                }
            }
        }

        /**
         * 打开市县联动页面（类似于openVideoCallingPage函数）
         */
        function openCityCountyLinkage() {
            try {
                console.log('openCityCountyLinkage 被调用');
                let title = '市县联动';
                
                // 检查父窗口是否存在second-screen-tabs
                if (window.parent && window.parent.$) {
                    var parentTabs = window.parent.$('#second-screen-tabs');
                    if (parentTabs.length > 0) {
                        // 检查是否已存在相同的tab
                        var exists = parentTabs.tabs('exists', title);
                        if (exists) {
                            // 如果已存在则选中该tab
                            parentTabs.tabs('select', title);
                            console.log('市县联动tab已存在，切换到该tab');
                        } else {
                            // 不存在则新建tab
                            parentTabs.tabs('add', {
                                title: title,
                                content: '<iframe name="cityCountyLinkage" id="cityCountyLinkageIframe" scrolling="no" src="cityCountyLinkage.html" frameborder="0" style="width:100%;height:100%;"></iframe>',
                                iconCls: 'icon-cityCountyLinkage',
                                closable: true,
                                selected: true
                            });
                            console.log('市县联动tab创建成功');
                        }
                    } else {
                        $.messager.alert('提示', '无法找到主界面tabs，请确保在副屏幕环境中运行', 'warning');
                    }
                } else {
                    $.messager.alert('提示', '无法访问父窗口，请确保在iframe环境中运行', 'warning');
                }
            } catch (error) {
                console.error('打开市县联动页面失败:', error);
                $.messager.alert('错误', '打开市县联动页面失败：' + error.message, 'error');
            }
        }


        /**
         * 事件来源格式化
         */
        function formatEventSource(value, row, index) {
            switch (value) {
                case '1':
                    return '120联动';
                case '2':
                    return '110联动';
                case '3':
                    return '112联动';
                case '4':
                    return '119联动';
                default:
                    return '未知';
            }
        }

        /**
         * 联动方向格式化
         */
        function formatDirection(value, row, index) {
            switch (value) {
                case '1':
                    return '发送';
                case '2':
                    return '接收';
                default:
                    return '-';
            }
        }
    </script>
</body>
</html> 