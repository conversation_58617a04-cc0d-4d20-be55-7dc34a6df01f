<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <title>主任座席页</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
      body{                    
        display: flex;
        flex-direction: column; 
      }
      .seatsList{
        flex:1;
        padding: 10px 21px;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        background-color: #F2F2F2;
        height: calc(100% - 42px)
      }
      .seat-header{
        background-color: #F2F2F2;
         padding: 10px 21px 0;
         display: flex;
         justify-content: space-between;
      }
      .creatmeeting {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 110px;
        height: 32px;
        background: #499AE0;
        border-radius: 2px 2px 2px 2px;
        font-size: 16px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 32px;
        text-align: center;
        cursor: pointer;
      }
      .seat-item{
        width: 298px;
        height: 400px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #D4D4D4;
        margin: 0 10px 10px 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-sizing: border-box;
      }
      .item-header{
        width: 296px;
        height: 57px;
        background: linear-gradient(180deg, #FFFFFF 0%, #F3F3F3 100%);
        border-radius: 6px 6px 0px 0px;
        opacity: 1;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #D4D4D4;
      }
      .header-left {
        width: 355px;
        text-align: center;
        line-height: 57px;
        font-size: 18px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
      .header-right-inline {
        margin-right: 15px;
        width: 76px;
        height: 28px;
        background: #1CBA3D;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        color: #fff;
        text-align: center;
        line-height: 28px;
      }
      .header-right-outline {
        margin-right: 15px;
        width: 76px;
        height: 28px;
        background: lightgray;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        color: #fff;
        text-align: center;
        line-height: 28px;
      }
      .header-right-hangup {
        margin-right: 15px;
        width: 76px;
        height: 28px;
        background: #fa5858;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        color: #fff;
        text-align: center;
        line-height: 28px;
      }
      .item-center {
        background-color: #fff;
        padding: 13px 0 1px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .item-img {
       
      }
      .item-img img{
        width: 60px;
        height: 60px;
      }
      .item-info {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
      }
      .info-name {
        font-size: 18px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 25px;
        margin: 0;
      }
      .info-no {
        font-size: 18px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 25px;
        margin: 0;
      }
      .item-bottom {
        display: flex;
        width: 100%;
        background-color: #fff;
        flex: 1;
        min-height: 0;
        overflow: hidden;
      }

      .bottom-left{
        width: 50%;
        padding: 10px 6px 8px 12px;
        position: relative;
      }
      .bottom-right{
        width: 50%;
        padding: 10px 6px 8px 12px;
        position: relative;
      }
      .bottom-line1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .bottom-line1 img {
        width: 17px;
        height: 17px;
      }
      .jiejing, .diaodu{
        font-size: 14px;
        margin-left: 10px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: bolder;
        color: #333333;
      }
      .bottom-line2{
        margin: 7px 0 0 0;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 20px;
        color: #333333;
      }
      .bottom-line3 {
        margin: 9px 0 0 0;
        line-height: 20px;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }
      .bottom-line4 {
        display: flex;
        margin-bottom: 8px;
      }
      .jianting-left, .qiangcha-left{
        height: 29px;
        margin-top: 12px;
        width: 50%;
        border-radius: 5px 5px 5px 5px;
        opacity: 1;
        border: 1px solid #BBBBBB;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        z-index: 0;
      }
      .jianting-left img, .qiangcha-left img{
        width: 16px;
        height: 16px;
      }
      .jianting-left span, .qiangcha-left span{
        font-size: 15px;
        margin-left: 3px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
      }
      .jianting-right, .qiangcha-right, .huiyi-right{
        height: 29px;
        margin-top: 12px;
        width: 65px;
        background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);
        border-radius: 5px 5px 5px 5px;
        opacity: 1;
        border: 1px solid #BBBBBB;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        z-index: 0;
      }
      .jianting-right span, .qiangcha-right span, .huiyi-right span{
        font-size: 15px;
        margin-left: 4px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
      }
      .jianting-right img, .qiangcha-right img, .huiyi-right img{
        width: 16px;
        height: 16px;
      }
      .phone-online{
        color: #67C23A;
        font-size: 14px;
        font-weight: bold;
      }
      .phone-outline{
        color: rgb(170, 168, 168);
        font-size: 14px;
        font-weight: bold;
      }
      .phone-calling{
        color: #EA0031;
        font-size: 14px;
        font-weight: bold;
      }
      .phone-waitCall{
        color: #E6A23C;
        font-size: 14px;
        font-weight: bold;
      }
      .meeting-dialog{
        width: 510px;
        height: 300px;
        background-color: #fff;
        position: fixed;
        right: 3px;
        bottom: 3px;
        border: 1px solid gray;
        border-radius: 3px;
        box-shadow: -2px 2px lightgrey;
      }
      .meeting-header{
        width: 100%;
        height: 47px;
        background: linear-gradient(180deg, #FFFFFF 0%, #F3F3F3 100%);
        border-radius: 3px 3px 0px 0px;
        opacity: 1;
        border-bottom: 1px solid #D4D4D4;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .meeting-header-left{
        line-height: 47px;
        font-size: 22px;
        margin-left: 20px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
      .meeting-header-right{
        margin-right: 20px;
        line-height: 47px;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .meeting-body{
        padding: 25px 20px 20px 20px;
      }
      .meeting-title{
        font-size: 20px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
      }
      .meeting-time {
        margin-top: 10px;
        font-size: 20px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
        margin: 0;
      }
      .meeting-num {
        width: 100%;
        display: flex;
      }
      .meeting-me, .meeting-others {
        width: 150px;
        height: 80px;
        background: #F8F8F8;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #D4D4D4;
      }
      .meeting-me, .meeting-others{
        padding: 5px 10px;
        margin-right: 8px;
        position: relative;
      }
      .line1{
        margin-top: 9px;
      }
      .line1, .line2 {
        white-space: nowrap;
        display: flex;
        flex-direction: column;
        text-align: center;
        font-size: 15px;
        line-height: 25px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #333333;
      }
      .clear{
        position: absolute;
        right: 2px;
        top: -6px;
        font-size: 25px;
        width: 15px;
        height: 15px;
        cursor: pointer;
      }
      .meeting-start{
        width: 113px;
        height: 35px;
        background: #155BD4;
        border-radius: 5px 5px 5px 5px;
        line-height: 35px;
        text-align: center;
        color: #fff;
        font-size: 15px;
        cursor: pointer;
      }
      .meeting-end{
        width: 113px;
        height: 35px;
        background: #FB562D;
        border-radius: 5px 5px 5px 5px;
        line-height: 35px;
        text-align: center;
        color: #fff;
        font-size: 15px;
        cursor: pointer;
      }
      .meeting-bottom{
        width: 100%;
        display: flex;
        justify-content: flex-end;
        padding-right: 8px;
      }
      .meeting-persons{
        margin-bottom: 14px;
      }
      @keyframes blink-background {
        0% {
          background-color: #fff;
        }
        50% {
          background-color: #ebc48a;
        }
        100% {
          background-color: #fff;
        }
      }

      #blink-background {
        animation: blink-background 1s infinite;
      }
      .huiyiLogo{
        position: fixed;
        right: 0;
        bottom: -5px;
        cursor: pointer;
      }
      .zhurenIcon{
        margin-right: 18px;
      }
      .time-info {
        padding: 8px 12px;
        background: #f0f7ff;
        border-top: 1px solid #e6e6e6;
        font-size: 12px;
        color: #333;
      }
      .time-info .time-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }
      .time-info .time-row:last-child {
        margin-bottom: 0;
      }
      .time-info .time-item {
        flex: 1;
        display: flex;
        align-items: center;
      }
      .time-info .time-label {
        color: #666;
        font-size: 11px;
        margin-right: 3px;
        white-space: nowrap;
      }
      .time-info .time-value {
        color: #333;
        font-weight: bold;
        font-size: 11px;
      }
      .director-global-stats {
        padding: 10px 21px;
        background: #f8f9fa;
        border-bottom: 1px solid #e6e6e6;
        font-size: 14px;
        color: #333;
        display: none; /* 默认隐藏，只有主任才显示 */
      }
      .director-global-stats .stats-grid {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 30px;
      }
      .director-global-stats .stats-item {
        flex: 1;
        text-align: center;
        white-space: nowrap;
      }
      .director-global-stats .stats-item .stats-label {
        font-size: 16px;
        color: #666;
        margin-right: 5px;
      }
      .director-global-stats .stats-item .stats-value {
        font-size: 18px;
        font-weight: bold;
        color: #1890ff;
      }
      .work-stats {
        padding: 8px 12px;
        background: #fafbfc;
        border-top: 1px solid #e6e6e6;
        font-size: 11px;
        color: #666;
        margin-top: auto;
        position: relative;
        z-index: 1;
        flex-shrink: 0;
      }
      .work-stats .work-stats-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
      }
      .work-stats .work-stats-row:last-child {
        margin-bottom: 0;
      }
      .work-stats .work-stats-item {
        flex: 1;
        text-align: center;
      }
      .work-stats .work-stats-label {
        color: #999;
        font-size: 10px;
      }
      .work-stats .work-stats-value {
        color: #333;
        font-weight: bold;
        font-size: 11px;
      }
    </style>
</head>

<body>
    <div class="seat-header">
        <div>
            <span>&nbsp;座席状态：</span>
            <select id="seatStatus" class="easyui-combobox" name="seatStatus" style="width:200px;">
                <option value="">全部</option>
                <option value="1">在线</option>
                <option value="2">挂起</option>
                <option value="0">离线</option>
            </select>
            <a class="common-button" id="getSeatListBtn" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getSeatList();">
                <i class="fa fa-search"></i>&nbsp;搜索
            </a>
            
        </div>
        <div class="creatmeeting" style="display: none;">
            <img src="style/img/meeting.png" alt="">
            会议
        </div>
    </div>
    
    <!-- 主任座席统计信息 -->
    <div class="director-global-stats" id="directorGlobalStats">
        <div class="stats-grid">
            <div class="stats-item">
                <span class="stats-label">今日任务总数：</span>
                <span class="stats-value" id="todayTaskTotal">0</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">进行中任务：</span>
                <span class="stats-value" id="ongoingTask">0</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">今日电话：</span>
                <span class="stats-value" id="todayPhoneTotal">0</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">今日视频：</span>
                <span class="stats-value" id="todayVideoTotal">0</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">骚扰电话/视频：</span>
                <span class="stats-value" id="spamCount">0/0</span>
            </div>
            <div class="stats-item">
              <span class="stats-label"></span>
              <span class="stats-value" id=""></span>
          </div>
        </div>
    </div>
    <div class="seatsList" id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
    </div>
    <div class="meeting-dialog" id="meetingDialog" style="display: none;">
        <div class="meeting-header">
            <div class="meeting-header-left">电话会议</div>
            <div class="meeting-header-right" onclick="shrink()"><img src="style/img/shrink.png" alt="">收缩</div>
        </div>
        <div class="meeting-body">
            <p id="meetingTime" class="meeting-time">会议时间： </p>
            <div class="meeting-persons">
                <p class="meeting-title">会议人员</p>
                <div class="meeting-num" id="meetingNum">
                    <div class="meeting-me" id="meeting-me">
                        <span class="line1" id="meetingSeat"></span>
                        <span class="line2" id="meetingNo"></span>
                    </div>
                    <!-- <div class="meeting-others">
                      <div class="clear">×</div>
                      <span class="line1">三号座席(黄伟玲)</span>
                      <span class="line2">21231241223</span>
                    </div>
                    <div class="meeting-others">
                      <div class="clear">×</div>
                      <span class="line1">三号座席(黄伟玲)</span>
                      <span class="line2">21231241223</span>
                    </div> -->
                </div>
            </div>
            <div class="meeting-bottom">
                <div class="meeting-start" id="meetingStart" onclick="startMeeting()">
                    开始会议
                </div>
                <div class="meeting-end" id="meetingEnd" style="display: none;" onclick="endMeeting()">
                    结束会议
                </div>
            </div>
        </div>
    </div>
    <div class="huiyiLogo" id="huiyiLogo" onclick="openMeeting()">
        <img src="style/img/huiyiLogo.png" alt="">
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript">
  
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        let children = ''; // 子元素的node列表
        let seatList = []; // 所有座席人的列表
        var gProxyW = null;
        var bsProxyW = null;
        var pSeat = null; // 座席端的用户信息
        var meetingList = [] // 点击会议按钮拉入会议的人
        var hasMeeting = false // 默认本主任座席没有发起会议
        var meetingDialog = false // 会议弹窗的控制
        var seatStatuse = '' //搜索座席状态的值
        var isSearching = false // 是否是点击搜索按钮触发的查询
        var lastStatsUpdate = 0 // 上次统计数据更新的时间戳
        $(document).ready(function () {
            // 获取所有座席列表数据
            // setTimeout(()=>{
            //   getList()
            // },5000)
            getList()
            // 检查是否为主任座席，如果是则显示统计信息
            checkDirectorPermission()
        });
        
        // 检查主任权限并显示统计信息
        function checkDirectorPermission() {
            try {
                pSeat = evalJson(window.parent.gProxy.getSeatInfo());
            } catch (e) {
                try {
                    pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
                } catch (e2) {
                    pSeat = null;
                }
            }
            
            // 如果是主任座席，显示统计信息
            if (pSeat && pSeat.directorPermission && pSeat.directorPermission == '1') {
                $('#directorGlobalStats').show();
                updateDirectorStats(); // 更新统计数据
            } else {
                $('#directorGlobalStats').hide();
            }
        }
        
        // 更新主任统计数据
        function updateDirectorStats() {
            // 防抖机制：10秒内不重复调用
            var now = Date.now();
            if (now - lastStatsUpdate < 10000) {
                return;
            }
            lastStatsUpdate = now;
            
            // 调用舆情监控统计API
            getPublicOpinionMonitorStatistics({}, function(res) {
                if (res && typeof res === 'object') {
                    // API直接返回数据对象，不是包装在success和data中
                    $('#todayTaskTotal').text(res.totalTasks || '0');
                    $('#ongoingTask').text(res.ongoingTasks || '0');
                    $('#todayPhoneTotal').text(res.totalPhoneCalls || '0');
                    $('#todayVideoTotal').text(res.totalVideoCalls || '0');
                    $('#spamCount').text((res.phoneHarassmentCalls || '0') + '/' + (res.videoHarassmentCalls || '0'));
                    
                } else {
                    // API调用失败时使用默认值
                    $('#todayTaskTotal').text('0');
                    $('#ongoingTask').text('0');
                    $('#todayPhoneTotal').text('0');
                    $('#todayVideoTotal').text('0');
                    $('#spamCount').text('0/0');
                }
            }, function(e, url, errMsg) {
                // API调用出错时使用默认值
                console.error('获取舆情监控统计数据失败:', errMsg);
                $('#todayTaskTotal').text('0');
                $('#ongoingTask').text('0');
                $('#todayPhoneTotal').text('0');
                $('#todayVideoTotal').text('0');
                $('#spamCount').text('0/0');
            });
        }
        // 开始会议调接口
        function startMeeting() {
            if (meetingList.length != 2) {
                return $.messager.alert('提示', '请点击空闲座席的会议按钮并邀请参加会议', 'error');
            }

            // 点击任何按钮时获取一次座席端的用户信息
            try {
                pSeat = evalJson(window.parent.gProxy.getSeatInfo()); 
            } catch (e) {
                pSeat = evalJson(window.parent.bsProxy.getSeatInfo()); 
            }
            let params = { // 目前接口只支持拉取两个人 会议，先写死
                fromExt: pSeat.callOut, // 座席端的分机号
                toExt1: meetingList[0].callOut,
                toExt2: meetingList[1].callOut
            }
            conference(params, function (resp) {
                $('#meetingStart').css('display', 'none')
                $('#meetingEnd').css('display', 'block')
                // 设置会议时间
                let nowTime = new Date()
                let y = nowTime.getFullYear()
                let m = nowTime.getMonth() + 1
                let d = nowTime.getDate()
                let hh = nowTime.getHours()
                let mm = nowTime.getMinutes()
                $('#meetingTime').text('会议时间：' + y + '.' + m + '.' + d + ' ' + hh + '：' + mm)
                $.messager.alert('提示', '创建会议成功', 'info');
                hasMeeting = true
                isInMeeting() // 会议开始后调用接口
            }, function (e2, url, errMsg2) {
                //失败才做处理
                $.messager.alert('提示', errMsg2);
            })
        }
        function getSeatList() {
            // 显示加载提示
            showProcess(true, '温馨提示', '正在查询数据，请稍后...');
            isSearching = true; // 标记为搜索触发
            seatStatuse = $("#seatStatus").val()
            getList()
            // 搜索时不在这里调用统计更新，因为getList成功后会调用checkDirectorPermission，避免重复调用
        }
        // 结束会议调接口
        function endMeeting() {
            let params = { // 目前接口只支持拉取两个人 会议，先写死
                fromExt: pSeat.callOut, // 座席端的分机号
                toExt1: meetingList[0].callOut,
                toExt2: meetingList[1].callOut
            }
            closeTheConference(params, function (resp) {
                $('#meetingStart').css('display', 'block')
                $('#meetingEnd').css('display', 'none')
                $('#meetingTime').text('会议时间：')
                // 删除拉取会议成员的所有信息
                meetingList.forEach((item, index) => {
                    $('#meetingOthers' + item.id.substr(-6, 6)).remove()
                })
                meetingList = []
                // $.messager.alert('提示', '会议已结束', 'info');
                hasMeeting = false // 会议已结束
            }, function (e2, url, errMsg2) {
                //失败才做处理
                $.messager.alert('提示', '请求错误：' + errMsg2);
            })
        }
        // 会议开始后 一秒调用一次 判断 是否在会议中，不在会议中自动结束会议
        function isInMeeting() {
            clearTimeout(window.seatIntervalInMeeting);
            window.seatIntervalInMeeting = null;
            let params = { // 目前接口只支持拉取两个人 会议，先写死
                ext: pSeat.callOut, // 座席端的分机号
            }
            isInMeetingNow(params, function (resp) {
                if (!resp) {
                    $('#meetingStart').css('display', 'block')
                    $('#meetingEnd').css('display', 'none')
                    $('#meetingTime').text('会议时间：')
                    // 删除拉取会议成员的所有信息
                    meetingList.forEach((item, index) => {
                        $('#meetingOthers' + item.id.substr(-6, 6)).remove()
                    })
                    meetingList = []
                    hasMeeting = false
                    $.messager.alert('提示', '会议已结束', 'info');
                } else {
                    hasMeeting = true;
                    window.seatIntervalInMeeting = setTimeout(isInMeeting, 1000)
                }
            }, function (e2, url, errMsg2) {
                //失败才做处理
                $.messager.alert('提示', '请求错误：' + errMsg2);
            })
        }
        function openMeeting() {
            $('#meetingSeat').text(pSeat.seatId + pSeat.user)
            $('#meetingNo').text(pSeat.userId)
            $('#meetingDialog').css('display', 'block')
            $('#huiyiLogo').css('display', 'none')
        }
        function shrink() {
            $('#meetingDialog').css('display', 'none')
            $('#huiyiLogo').css('display', 'block')
        }
        // 班长座席列表
        function getList() {
            children = ''
            clearTimeout(window.seatInterval);
            window.seatInterval = null;
            let params = {
                seatStatus: seatStatuse
            }
            getSeatsListWithStatistics(params, (res) => {
                seatList = res
                for (let i = 0; i < res.length; i++) {
                    try {
                        pSeat = evalJson(window.parent.gProxy.getSeatInfo());
                    } catch (e) {
                        pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
                    }
                    // 本座席要 放在第一个 ，这里 做了一个判断
                    if (pSeat && pSeat.id && pSeat.id == res[i].id) {
                        children = getContentHtml(i, res[i]) + children
                    } else { // 其余
                        children += getContentHtml(i, res[i]);
                    }
                }
                $('.seatsList').children().remove()
                $('.seatsList').append(children)
                // 每次刷新座席列表时，也检查主任权限（checkDirectorPermission内部会调用updateDirectorStats）
                checkDirectorPermission()
                // 如果是搜索触发的查询，隐藏加载提示
                if (isSearching) {
                    showProcess(false, '温馨提示', '正在查询数据，请稍后...');
                    isSearching = false; // 重置标志
                }
            },
                (e, url, errMsg) => {
                    // 查询失败时，如果是搜索触发的也要隐藏加载提示  
                    if (isSearching) {
                        showProcess(false, '温馨提示', '正在查询数据，请稍后...');
                        isSearching = false; // 重置标志
                    }
                })
            window.seatInterval = setTimeout(getList, 2000)
        }
        function getContentHtml(i, data) {
            let html = `
                    <div class="seat-item">
                      <div class="item-header">
                        <div class="header-left" >${data.seatId}</div>
                        <div class="${data.seatStatus == 0 ? 'header-right-outline' : data.seatStatus == 1 ? 'header-right-inline' : "header-right-hangup"}">${data.seatStatus == 0 ? '离线' : data.seatStatus == 1 ? '在线' : "挂起"}</div>
                      </div>
                      <div class="item-center">
                        <div class="item-info">
                          <p class="info-name" style='color:${data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>${data.seatStatus != 1 ? '已离线' : data.user}</p>
                          <p class="info-no" style='color:${data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>工号：${data.seatStatus != 1 ? '' : data.userName}</p>
                        </div>
                        <div class="zhurenIcon"><img src="style/img/zhurenIcon.png"/></div>
                      </div>
                      <div class="time-info">
                        <div class="time-row">
                          <div class="time-item">
                            <span class="time-label">班次时间：</span>
                            <span class="time-value">${data.statisticsData && data.statisticsData.scheduleStartTimeShort && data.statisticsData.scheduleEndTimeShort ? (data.statisticsData.scheduleStartTimeShort + ' ~ ' + data.statisticsData.scheduleEndTimeShort) : ''}</span>
                          </div>
                          <div class="time-item">
                            <span class="time-label">当前上班时长：</span>
                            <span class="time-value">${data.statisticsData && data.statisticsData.workTime ? data.statisticsData.workTime + 'm' : ''}</span>
                          </div>
                        </div>
                        <div class="time-row">
                          <div class="time-item">
                            <span class="time-label">最后离席时间：</span>
                            <span class="time-value">${data.statisticsData && data.statisticsData.lastLeaveSeatTime ? data.statisticsData.lastLeaveSeatTime : '无'}</span>
                          </div>
                          <div class="time-item">
                            <span class="time-label">离席时长：</span>
                            <span class="time-value">${data.statisticsData && typeof data.statisticsData.offTime !== 'undefined' ? data.statisticsData.offTime + 'm' : '无'}</span>
                          </div>
                        </div>
                      </div>
                      <div class="item-bottom">
                        <div class="bottom-left" id="${data.callInExtStatus == 'BUSY' && data.callInCallStatus == 'RING' ? 'blink-background' : ''}">
                          <div class="bottom-line1">
                            <div style="display:flex">
                              <img src="${data.callInExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'style/img/outlineJiejing.png' : 'style/img/jiejing.png'}" alt="">
                              <div class="jiejing" style='color:${data.callInExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>接警</div>
                            </div>
                            <div class="${changeStatus1(i) || ''}">
                              ${changeText1(i) || '异常'}
                            </div>
                          </div>
                          <div class="bottom-line2" style='color:${data.callInExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>分机号 ${data.callIn}</div>
                          <div class="bottom-line3" style='color:${data.callInExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>被叫号 ${data.callInNumber}</div>
                        </div>
                        <div class="bottom-right" id="${data.callOutExtStatus == 'BUSY' && data.callOutCallStatus == 'RING' ? 'blink-background' : ''}">
                          <div class="bottom-line1">
                            <div style="display:flex;align-items: center;">
                              <img src="${data.callOutExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'style/img/outlineDiaodu.png' : 'style/img/diaodu.png'}"  alt="">
                              <div class="diaodu"  style='color:${data.callOutExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>调度</div>
                            </div>
                            <div class="${changeStatus4(i) || ''}">
                              ${ changeText2(i) || '异常'}
                            </div>
                          </div>
                          <div class="bottom-line2" style='color:${data.callOutExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>分机号 ${data.callOut}</div>
                          <div class="bottom-line3" style='color:${data.callOutExtStatus == 'OFFLINE' || data.seatStatus != 1 ? 'rgb(170, 168, 168)' : ''}'>被叫号 ${data.callOutNumber}</div>
                        </div>
                      </div>
                      <div class="work-stats">
                          <div class="work-stats-row">
                            <div class="work-stats-item">
                              <div class="work-stats-label">10秒接听率</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.callInAcceptedRateIn10Seconds !== 'undefined' ? data.statisticsData.callInAcceptedRateIn10Seconds : ''}</div>
                            </div>
                            <div class="work-stats-item">
                              <div class="work-stats-label">事件标注率</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.eventAnnotatedRate !== 'undefined' ? data.statisticsData.eventAnnotatedRate : ''}</div>
                            </div>
                            <div class="work-stats-item">
                              <div class="work-stats-label">视频报警</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.vedioCount !== 'undefined' ? data.statisticsData.vedioCount : ''}</div>
                            </div>
                          </div>
                          <div class="work-stats-row">
                            <div class="work-stats-item">
                              <div class="work-stats-label">电话报警</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.callInCount !== 'undefined' ? data.statisticsData.callInCount : ''}</div>
                            </div>
                            <div class="work-stats-item">
                              <div class="work-stats-label">电话骚扰</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.callInHarassCount !== 'undefined' ? data.statisticsData.callInHarassCount : ''}</div>
                            </div>
                            <div class="work-stats-item">
                              <div class="work-stats-label">视频骚扰</div>
                              <div class="work-stats-value">${data.statisticsData && typeof data.statisticsData.vedioHarassCount !== 'undefined' ? data.statisticsData.vedioHarassCount : ''}</div>
                            </div>
                        </div>
                      </div>
                  </div>`;
            return html;
        }

        // 绑定事件 按钮的ajax请求事件
        // 点击 接警监听按钮
        function jianTingLeft(index) {
            if (hasMeeting) return
            if ((seatList[index].callInExtStatus == 'BUSY' && (seatList[index].callInCallStatus == 'ALERT' || seatList[index].callInCallStatus == 'ANSWER' || seatList[index].callInCallStatus == 'BYE'))) {
                try {
                    pSeat = evalJson(window.parent.gProxy.getSeatInfo());
                } catch (e) {
                    pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
                }
                let params = {
                    activeExt: pSeat.callOut, // 座席端的分机号
                    passiveExt: seatList[index].callIn // 点击人的分机号
                }
                monitor(params, function (resp) {
                    $.messager.alert('提示', '监听成功', 'info');
                }, function (e2, url, errMsg2) {
                    //失败才做处理
                    $.messager.alert('提示', '请求错误：' + errMsg2);
                })
            }
        }
        // 点击 电镀监听按钮
        // function jianTingRight(index) {
        //   pSeat = evalJson(gProxyW.getSeatInfo());// 点击任何按钮时获取一次座席端的用户信息
        //   let params = {
        //     activeExt: pSeat.callOut, // 座席端的分机号
        //     passiveExt: seatList[index].callOut // 点击人的分机号
        //   }
        //   monitor(params, function (resp) {
        //     $.messager.alert('提示', '监听成功', 'info');
        //   },function (e2, url, errMsg2) {
        //     //失败才做处理
        //     $.messager.alert('提示', '请求错误：' + errMsg2);
        //   })
        // }
        // 点击接警强插按钮
        function qiangChaLeft(index) {
            if (hasMeeting) return
            if ((seatList[index].callInExtStatus == 'BUSY' && (seatList[index].callInCallStatus == 'ALERT' || seatList[index].callInCallStatus == 'ANSWER' || seatList[index].callInCallStatus == 'BYE'))) {
                try {
                    pSeat = evalJson(window.parent.gProxy.getSeatInfo());
                } catch (e) {
                    pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
                }
                let params = {
                    activeExt: pSeat.callOut, // 座席端的分机号
                    passiveExt: seatList[index].callIn // 点击人的分机号
                }
                bargeIn(params, function (resp) {
                    $.messager.alert('提示', '强插成功', 'info');
                }, function (e2, url, errMsg2) {
                    //失败才做处理
                    $.messager.alert('提示', '请求错误：' + errMsg2);
                })
            }
        }
        // 点击调度强插 按钮
        // function qiangChaRight(index) {
        //   pSeat = evalJson(gProxyW.getSeatInfo());// 点击任何按钮时获取一次座席端的用户信息
        //   let params = {
        //     activeExt: pSeat.callOut, // 座席端的分机号
        //     passiveExt: seatList[index].callOut // 点击人的分机号
        //   }
        //   bargeIn(params, function (resp) {
        //     $.messager.alert('提示', '强插成功', 'info');
        //   },function (e2, url, errMsg2) {
        //     //失败才做处理
        //     $.messager.alert('提示', '请求错误：' + errMsg2);
        //   })
        // }
        // 点击会议按钮触发创建会议
        function huiYi(index) {
            try {
                pSeat = evalJson(window.parent.gProxy.getSeatInfo());
            } catch (e) {
                pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
            }
            if (seatList[index].id == pSeat.id) {
                return $.messager.alert('提示', '默认本座席已在会议中，请邀请其他人！', 'error');
            }
            $('#meetingSeat').text(pSeat.seatId + pSeat.user)
            $('#meetingNo').text(pSeat.userId)
            // 点击会议按钮，将空闲座席的调度分机拉入会议中
            if ((seatList[index].callOutExtStatus == 'IDLE' || seatList[index].callOutExtStatus == 'ONLINE') && seatList[index].seatStatus == 1) { // 座席必须在线
                // if(seatList[index].callOutExtStatus == 'IDLE' || seatList[index].callOutExtStatus  == 'ONLINE') {
                if (hasMeeting) { // 会议发起时，不能再点击
                    return $.messager.alert('提示', '当前座席已在会议中，请勿点击！', 'error');
                }
                let hasNode = false
                meetingList.forEach(item => {
                    if (item.id == seatList[index].id) {
                        hasNode = true
                    }
                })
                if (hasNode) {
                    return // 有点击相同的座席重复把一个人加入到会议时 不做反应
                }
                if (meetingList.length < 2) { // 拉取会议的人不足两个 才能拉
                    meetingList.push(seatList[index])
                    // 只要会议数组中加入了会议的人，弹窗就打开，关闭用户手动关闭就好
                    meetingDialog = true
                    // 打开弹窗操作
                    $('#meetingDialog').css('display', 'block')
                    $('#huiyiLogo').css('display', 'none')
                    // 添加了会议人员，所以重新渲染dom
                    let node = `
                      <div class="meeting-others" id="meetingOthers${seatList[index].id.substr(-6, 6)}">
                        <div class="clear" onclick="delMeeting(${seatList[index].id.substr(-6, 6)})">×</div>
                        <span class="line1">${seatList[index].seatId}(${seatList[index].user})</span>
                        <span class="line2">${seatList[index].userName}</span>
                      </div>
                      `
                    $('#meetingNum').append(node) //新增节点
                } else {
                    return $.messager.alert('提示', '仅支持三个人同时会议！', 'error');
                }
            }
        }
        function delMeeting(id) {
            if (hasMeeting) {
                return $.messager.alert('提示', '正在会议中！', 'error');
            }
            meetingList.forEach((item, index) => {
                if (item.id.substr(-6, 6) == id) {
                    meetingList.splice(index, 1)
                }
            })
            $('#meetingOthers' + id).remove()
        }
        function changeStatus1(i) { //接警分机状态显示
            if (seatList[i].seatStatus != 1) {
                return 'phone-outline'
            }
            if (seatList[i].callInExtStatus == 'IDLE' || seatList[i].callInExtStatus == 'ONLINE') {
                return 'phone-online'
            } else if (seatList[i].callInExtStatus == 'OFFLINE') {
                return 'phone-outline'
            } else if (seatList[i].callInExtStatus == 'BUSY' && seatList[i].callInCallStatus == 'RING') {
                return 'phone-waitCall'
            } else if (seatList[i].callInExtStatus == 'BUSY' && (seatList[i].callInCallStatus == 'ALERT' || seatList[i].callInCallStatus == 'ANSWER' || seatList[i].callInCallStatus == 'ANSWERED' || seatList[i].callInCallStatus == 'BYE')) {
                return 'phone-calling'
            }else{
               return 'phone-outline'
            }
        }
        function changeText1(i) { //接警分机状态显示
            if (seatList[i].seatStatus != 1) {
                return '离线'
            }
            if (seatList[i].callInExtStatus == 'IDLE' || seatList[i].callInExtStatus == 'ONLINE') {
                return '空闲'
            } else if (seatList[i].callInExtStatus == 'OFFLINE') {
                return '离线'
            } else if (seatList[i].callInExtStatus == 'BUSY' && seatList[i].callInCallStatus == 'RING') {
                return '响铃中'
            } else if (seatList[i].callInExtStatus == 'BUSY' && (seatList[i].callInCallStatus == 'ALERT' || seatList[i].callInCallStatus == 'ANSWER' || seatList[i].callInCallStatus == 'ANSWERED' || seatList[i].callInCallStatus == 'BYE')) {
                return '通话中'
            }else{
              return '异常'
            }
        }
        /**
         * i 是指坐席列表数据的脚标
         */
        function changeText2(i) { // 调度分机的状态显示
            if (seatList[i].seatStatus != 1) {
                return '离线'
            }
            if (seatList[i].callOutExtStatus == 'IDLE' || seatList[i].callOutExtStatus == 'ONLINE') {
                return '空闲'
            } else if (seatList[i].callOutExtStatus == 'OFFLINE') {
                return '离线'
            } else if (seatList[i].callOutExtStatus == 'BUSY' && seatList[i].callOutCallStatus == 'RING') {
                return '响铃中'
            } else if (seatList[i].callOutExtStatus == 'BUSY' && (seatList[i].callOutCallStatus == 'ALERT' || seatList[i].callOutCallStatus == 'ANSWER' || seatList[i].callOutCallStatus == 'ANSWERED' || seatList[i].callOutCallStatus == 'BYE')) {
                return '通话中'
            }else{
              return '异常'
            }
        }
        function changeStatus2(i) {
            //主任座席会议中，监听按钮置灰不可用
            if (hasMeeting) {
                return 'background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);color: #bcbec2;border: 1px solid #e2e0e0;margin-right: 6px;'
            } else if (seatList[i].isBargeInOrMonitor == '2') {
                return 'background-color: #1CBA3D;color: #fff;margin-right: 6px;'
            } else if (seatList[i].callInExtStatus == 'BUSY' && (seatList[i].callInCallStatus == 'ALERT' || seatList[i].callInCallStatus == 'ANSWER' || seatList[i].callInCallStatus == 'ANSWERED' || seatList[i].callInCallStatus == 'BYE')) {
                return 'margin-right: 6px;background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);'
            } else {
                return 'background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);color: #bcbec2;border: 1px solid #e2e0e0;margin-right: 6px;'
            }
        }
        function changeStatus3(i) {
            //主任座席会议中，强插按钮置灰不可用
            if (hasMeeting) {
                return 'background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);color: #bcbec2;border: 1px solid #e2e0e0;margin-right: 6px;'
            } else if (seatList[i].isBargeInOrMonitor == '1') {
                return 'background-color: #1CBA3D;color: #fff;margin-right: 6px;'
            } else if (seatList[i].callInExtStatus == 'BUSY' && (seatList[i].callInCallStatus == 'ALERT' || seatList[i].callInCallStatus == 'ANSWER' || seatList[i].callInCallStatus == 'ANSWERED' || seatList[i].callInCallStatus == 'BYE')) {
                return 'margin-right: 6px;background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);'
            } else {
                return 'background: linear-gradient(180deg, #FEFEFE 0%, #E7E7E7 100%);color: #bcbec2;border: 1px solid #e2e0e0;margin-right: 6px;'
            }
        }
        function changeStatus4(i) { // 调度分机的状态显示
            if (seatList[i].seatStatus != 1) {
                return 'phone-outline'
            }
            if (seatList[i].callOutExtStatus == 'IDLE' || seatList[i].callOutExtStatus == 'ONLINE') {
                return 'phone-online'
            } else if (seatList[i].callOutExtStatus == 'OFFLINE') {
                return 'phone-outline'
            } else if (seatList[i].callOutExtStatus == 'BUSY' && seatList[i].callOutCallStatus == 'RING') {
                return 'phone-waitCall'
            } else if (seatList[i].callOutExtStatus == 'BUSY' && (seatList[i].callOutCallStatus == 'ALERT' || seatList[i].callOutCallStatus == 'ANSWER' || seatList[i].callOutCallStatus == 'ANSWERED' || seatList[i].callOutCallStatus == 'BYE')) {
                return 'phone-calling'
            }else{
                return 'phone-outline'
            }
        }
        //挂起调度
        function hangUpBtn() {
            //console.log('222--')
        }

        //解除挂起调度
        function unHangUpBtn() {
            //console.log('333--')
        }
    </script>
</body>

</html>