<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/5/27
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    
    <!-- 处理Chrome扩展通信错误 -->
    <script type="text/javascript">
        // 抑制Chrome扩展通信错误
        (function() {
            // 检查是否有chrome.runtime对象
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                // 捕获并忽略chrome.runtime.lastError错误
                var originalError = console.error;
                console.error = function() {
                    var args = Array.prototype.slice.call(arguments);
                    var message = args.join(' ');
                    
                    // 忽略Chrome扩展连接相关的错误
                    if (message.indexOf('Could not establish connection') > -1 || 
                        message.indexOf('Receiving end does not exist') > -1 ||
                        message.indexOf('runtime.lastError') > -1) {
                        return; // 不输出这些错误
                    }
                    
                    // 其他错误正常输出
                    originalError.apply(console, arguments);
                };
            }
            
            // 全局错误处理器，捕获未处理的错误
            window.addEventListener('error', function(e) {
                if (e.message && (e.message.indexOf('Could not establish connection') > -1 || 
                                e.message.indexOf('Receiving end does not exist') > -1)) {
                    e.preventDefault();
                    return false;
                }
            });
        })();
    </script>
    <!--<script type="text/javascript" src="http://api.map.baidu.com/getscript?v=2.0&ak=4E2yR7vG9tb8KdjFQ6RRpt80ZdE4p98g"></script>-->
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        body {
            overflow: hidden; /* 禁用所有滚动条 */
            margin: 0; /* 防止有默认的外边距 */
            padding: 0; /* 防止有默认的内边距 */
        }
        a {
            color: #099DFC;
            font-weight: bold;
        }

        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 3px;
            font-weight: bold;
        }

        .datagrid-cell {
            font-size: 15px;
            font-weight: bold;
        }

        table td{
            font-size: 15px !important;
            height: 40px !important;
        }
        .group-box {
            min-width: 100px;
            min-height: 100px;
            border: solid #E2E7EA 1px;
            border-radius: 5px;
            margin: 3px 5px 5px 5px;
            padding: 0 16px;
            overflow: hidden;
        }

        /* 左右面板特殊样式，去掉默认margin */
        #leftPanel, #rightPanel {
            margin: 3px 0;
            padding: 0;
            height: 100%;
        }

        /* 确保flex容器和子元素的高度正确 */
        #mainFrame > div:last-child {
            min-height: 0; /* 允许flex子项缩小 */
        }

        /* 文字闪烁 定义keyframe动画，命名为blink */
        @keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }
        /* 添加兼容性前缀 */
        @-webkit-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-moz-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-ms-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        @-o-keyframes blink {
            0% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }
        /* 定义红色闪动blink类*/
        .redblink {
            font-size: 2.0em;
            color: #f10404;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        .msgblink {
            font-size: 16px;
            color: #f10404;
            margin-left: 10px;
            /* animation: blink 1s linear infinite; */
            /* 其它浏览器兼容性前缀 */
            /* -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite; */
        }

        .blueblink {
            font-size: 2.0em;
            color: #099DFC;
            margin-left: 10px;
        }

        .grayblink {
            font-size: 2.0em;
            color: #888888;
            margin-left: 10px;
        }

        .greenblink {
            font-size: 2.0em;
            color: #1e8804;
            margin-left: 10px;
            animation: blink 1s linear infinite;
            /* 其它浏览器兼容性前缀 */
            -webkit-animation: blink 1s linear infinite;
            -moz-animation: blink 1s linear infinite;
            -ms-animation: blink 1s linear infinite;
            -o-animation: blink 1s linear infinite;
        }

        .redlink {
            font-size: 2.0em;
            color: #f10404;
            margin-left: 5px;
        }

        .tabs-header {
            border-width: 0px;
            background: url(./style/img/grouptitle.png) repeat-x;
            width: 100% !important;
        }

        .panel-body {
            overflow: hidden;
        }

        .tabs-wrap {
            overflow-x: auto;
        }

            .tabs-wrap::-webkit-scrollbar {
                display: none;
            }

        .tabs {
            overflow: auto !important;;
        }
        /*
            tabel左边按钮
        */
        .tabs-button-left {
            display: inline-block;
            width: 0;
            height: 0;
            border: 7px solid;
            border-color: transparent #0000ff75 transparent transparent;
            position: absolute;
            right: 30px;
            top: 7px;
            cursor: pointer;
        }
        /*
            tabel右边按钮
        */
        .tabs-button-right {
            display: inline-block;
            width: 0;
            height: 0;
            border: 7px solid;
            border-color: transparent transparent transparent #0000ff75;
            position: absolute;
            right: 14px;
            top: 7px;
            cursor: pointer;
        }
        /*
            下拉框样式修改
        
        select {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        }
            */
        /*
            联级输入框样式修改
        */
        textarea {
            border-width: 2px;
            border-style: inset;
            border-color: initial;
            border-image: initial;
        }

        #eventDetail-dialog {
            width: 100% !important;
            height: 100% !important;
        }

        .eventDetail-content {
            width: 100%;
            height: calc(100% - 35px);
            overflow: auto;
        }

        .textbox-label {
            display: inline-block;
            width: 120px;
        }

        #updateMobileConditionDialog .textbox-label {
            display: inline-block;
            width: auto;
			margin-right: 5px;
        }

        .chckbox-status {
            width: 50px;
            height: 50px;
        }

        /*开关css配置*/
        .btn_fath {
            margin-top: 10px;
            position: relative;
            border-radius: 20px;
        }

        .btn1 {
            /*"开"字的位置*/
            float: left;
        }

        .btn2 {
            /*"关"字的位置*/
            float: right;
        }

        .btnSwitch {
            height: 25px;
            width: 25px;
            border: none;
            color: #fff;
            line-height: 25px;
            font-size: 14px;
            text-align: center;
            z-index: 1;
        }

        .move {
            /*按钮的设置*/
            z-index: 100;
            width: 25px;
            border-radius: 20px;
            height: 25px;
            position: absolute;
            cursor: pointer;
            border: 1px solid #828282;
            background-color: #f1eff0;
            box-shadow: 1px 1px 1px 1px #fff inset,0 0 1px 1px #999;
        }

        .on .move {
            left: 25px;
        }

        .on.btn_fath {
            /*开启效果的背景*/
            background-color: #44b549;
            height: 25px;
            width: 50px;
        }

        .off.btn_fath {
            /*关闭效果的背景 */
            background-color: #828282;
            height: 25px;
            width: 50px;
        }
        
        .none{
            /*禁止点击 */
            pointer-events: none;
        }

        /* 垂直分隔线样式 */
        #verticalDragSeparator:hover {
            background: #b3d9ff;
        }
        
        /* 水平分隔线样式 */
        #dragSeparator:hover {
            background: #b3d9ff;
        }

        /* 确保左右面板不会重叠 */
        #leftPanel, #rightPanel {
            box-sizing: border-box;
        }

        #left-list > .tabs-panels {
            height: 100%;
            width: 100% !important;
        }

            #left-list > .tabs-panels > .panel {
                height: 100%;
                width: 100% !important;
            }

                #left-list > .tabs-panels > .panel > .panel-body {
                    height: 100%;
                    width: 100% !important;
                }

        #missedCalls > .datagrid-row-selected {
            background-color: unset !important;
            background-color: transparent !important;
        }

        #missedCalls > a {
            color: #099DFC !important;
        }

        .l-btn-text {
            font-size: 15px !important;
        }

        .panel-tool-close {
            background-size: 360% 240%;
            background-position: -21px -1px;
        }

        .combobox-item {
            cursor: pointer;
        }

        .tabs {
            overflow: hidden;
        }
        .tabs li a.tabs-close {
            background-size: 360% 130%;
            background-position: -47px -2px;
            height: 19px;
            width: 19px;
            top: 13px;
        }

        .tabs-inner {
            padding: 0px 15px !important;
        }

        .tabs-title {
            font-size: 15px !important;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff; /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9; /* 未选中时的边框颜色 */
        }
        input[type="checkbox"]:checked::before {
            content: "\2713"; /* 对号的转义符 √ */
            background-color: #fff; /* 选中时的背景颜色 */
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc; /* 选中后的边框颜色 */
            color: #000; /* 选中后的文字颜色   */
            font-size: 20px;
            font-weight: bold;
        }
        .panel-body{
            overflow: hidden !important;
        }
        .tabs li.tabs-selected .tabs-inner{
            border-bottom: 1px solid #099dfc;
            background: #099dfc;
            color: #ffffff;
        }
        #stationRefuseDispatch{
            border-width: 1px;
        }
        /*数字软键盘样式*/
        .public-keyboard-pop{
            padding: 20px;
            background: #f5f5f7;
            color: #333;
            position: relative;
        }
        .public-keyboard-pop .keys-wrap{
            width: 100%;
            position: relative;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .public-keyboard-pop .key-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex:1 1 30%;
            margin: 0 10px 10px 0;
            /* border: 1px solid #ddd; */
            border-radius: 8px;
            font-size: 30px;
            font-weight: bold;
            line-height: 56px;
            text-align: center;
            cursor: pointer;

            /* background: #fff; */
        }
        #soft-keyboard-input{
          border: 1px solid #ddd;
        }
        .item-line1{
          line-height: 32px;
          margin-top: 17px;
        }
        .item-line2{
          font-size: 13px;
          line-height: 17px;
          color: rgba(0,0,0,0.4);
          font-weight: bold;
        }
        .public-keyboard-pop .key-item:active{
            background-color: #f7f5f0;
        }
        .public-keyboard-pop .key-item.soft-keyboard-input{
            flex:1 1 90%;
        }
        .public-keyboard-pop .key-item.back{
            position: relative;
        }
        .public-keyboard-pop .key-item.back:after,.public-keyboard-pop .key-item.back:before{
            content: '';
            display: block;
            background: #333;
            position: absolute;
            top: 50%;
            left: 50%;
            border-radius: 5px;
        }
        .public-keyboard-pop .key-item.back:after{
            width: 2px ;
            height: 25px;
            transform:translate(-50%,-50%) rotate(45deg);
        }
        .public-keyboard-pop .key-item.back:before{
            width: 30px;
            height: 2px;
            transform:translate(-50%,-50%) rotate(45deg);
        }
        .del-phone {
            position: absolute;
            top: 38px;
            right: 41px;
            z-index: 99;
            font-size: 25px;
            font-weight: 600;
            cursor: pointer;
            width: 28px;
            text-align: center;
            line-height: 28px;
        }
        /*呼叫与接听按钮样式*/
        #soft-phone-call-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #64B95B;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }
        /*挂断按钮*/
        #soft-phone-hangup-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #f30000;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }
        /*拒接按钮*/
        #soft-phone-reject-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #f30000;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }
        /*接听按钮*/
        #soft-phone-dialcall-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #64B95B;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }
        /*保持呼叫按钮样式*/
        #soft-phone-hold-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #64B95B;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }
        /*恢复呼叫按钮样式*/
        #soft-phone-unhold-phone {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 26px;
            background-color: #64B95B;
            text-align: center;
            line-height: 50px;
            font-size: 17px;
            font-weight: bold;
            color: white;
        }

        .delPhone-line {
            width: 50px;
            background-image: url(style/img/delPhone.png);
            background-size: 100% 100%;
            height: 46px;
            margin-top: 18px;
            margin-right: 10px;
        }
        .xiaoxi{
          width: 20px;
          height: 20px;
          position: absolute;
          right: 30px;
          cursor: pointer;
        }
        .xiaoxi img{
          width: 20px;
          height: 20px;
          margin-bottom: 13px;
        }
        .xiaoxi_badge{
          position: absolute;
          top: -2px;
          right: -21px;
          display: inline-block;
          width: 18px;
          height: 18px;
          font-size: 10px;
          line-height: 18px;
          text-align: center;
          color: #FFF;
          background-color: #EA0031;
          border-radius: 50%;
        }
        #dis-region .region-letters,
        #station-region .station-letters {
            padding: 3px 6px;
            cursor: pointer;
        }
        /* 调度选中样式 */
        #dis-region .region-letters.selected,
        #station-region .station-letters.selected {
            background: rgba(179,210,225,0.8);
        }
        /* 样式保证换行 */
        .datagrid-cell {
            white-space: normal; /* 允许换行 */
            word-wrap: break-word; /* 强制文本换行 */
        }

        .station-list_datagrid-cell-c3-remark {
            word-wrap: break-word; /* 长单词换行 */
            word-break: break-word; /* 如果单词过长，自动换行 */
            white-space: normal; /* 允许换行 */
            overflow-wrap: break-word; /* 对长单词进行换行 */
        }
        .buttonUniformStyle {
            width: 96px;
            height:28px;
            line-height: 28px; 
            font-size: 12px; 
            margin: 5px 3px 5px 0;
        }
        
        /* 右键菜单字体统一设置 */
        .menu-item {
            font-size: 14px !important;
            line-height: 22px !important;
        }
        
        /* 覆盖EasyUI默认的菜单文字样式 */
        .menu-text,
        .menu-text span {
            font-size: 14px !important;
            line-height: 22px !important;
        }
        
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <!--挂起弹窗-->
        <div id="hangUpDialog" class="easyui-dialog" title="座席挂起离席" style="width:300px;height:200px;"
             data-options="resizable:false,closable:false,modal:true,closed:true,buttons:'#tbHangUpDialog'">
            <div style="text-align:center;font-size:16px;color:brown;">
                <div style="height:40px;font-size:16px;color:brown;margin-top: 20px;">本座席已离席，话机被挂起,电话无法呼入...</div>
                密码：<input id="hangUpDialogPsw" title="请输入密码解除挂起离席状态" class="easyui-passwordbox" prompt="请输入密码" iconWidth="28" style="width:200px">
            </div>
        </div>
        <div id="tbHangUpDialog">
            <a href="javascript:unHangUpBtn()" class="easyui-linkbutton" data-options=""> 解除挂起离席 </a>
        </div>

        <!--交接班弹窗-->
        <div id="change-shifts-win" class="easyui-window" title="正在执行交接班" style="width:900px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <p style="text-align:center;margin: 10px;">
                <span id="shift-seat" style="display:inline-block;width:160px;font-size:18px;color: #F59A23;font-weight:bolder;"></span>
                <span style="display:inline-block;width:400px;font-size:18px;color: #F59A23;font-weight:bolder;">
                    交班：
                    <span id="shift-offGoingUser"></span>
                    [<span id="shift-offGoingUserName"></span>]
                </span>
                <span style="display:inline-block;width:160px;font-size:18px;color: #F59A23;font-weight:bolder;">
                    班次：
                    <span id="shift-curScheduleName"></span>
                </span>
            </p>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:100%;font-size:18px;color: #F59A23;font-weight:bolder;">
                    交班班次：
                    <span id="shift-offGoingScheduleName"></span>
                    ->
                    <span id="shift-successorScheduleName"></span>
                </span>
            </p>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;font-size:16px;color: #F59A23;">
                    <span id="shift-offGoingScheduleName-time"></span>时间：
<!--                    <span id="shift-offGoing-time"></span>-->
                    <input id="change-start-times" data-options="required:true,showSeconds:false"  class="easyui-datetimebox" style="width:150px;text-indent: 0.5em;" /> --
                    <input id="change-end-times" data-options="required:true,showSeconds:false" class="easyui-datetimebox" style="width:150px;text-indent: 0.5em;" />
                </span>
                
                <span style="display:inline-block;font-size:16px;color: #F59A23;margin-left: 15px;">
                    交班时间：
                     <input id="shift-handover-times" data-options="required:true,showSeconds:false"  class="easyui-datetimebox" style="width:150px;text-indent: 0.5em;" />
                </span>
            </p>
            <!-- <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:350px;font-size:16px;color: #F59A23;">
                    夏令时：
                    <span id="shift-daylight-saving-time"></span>
                </span>
            </p> -->
            <div style="text-align:center;margin: 10px;font-size:16px;">
                <table>
                    <tr>
                        <th colspan="8">交班内容</th>
                    </tr>
                    <tr>
                        <td><span style="color:red;">*</span>呼入(次)</td>
                        <td><span style="color:red;">*</span>受理(次)</td>
                        <td><span style="color:red;">*</span>派车(辆)</td>
                        <td><span style="color:red;">*</span>有效出车(辆)</td>
                        <td><span style="color:red;">*</span>空车(辆)</td>
                        <td><span style="color:red;">*</span>取消派车(辆)</td>
                        <td><span style="color:red;">*</span>患者人数(人)</td>
                        <td><span style="color:red;">*</span>摘机率(%)</td>
                    </tr>
                    <tr>
                        <td><input id="callIn" type="text" value="" oninput="inputCount(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="accept" type="text" value="" oninput="inputCount(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="dispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="vaildDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="emptyDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="cancelDispatch" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="patientNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 100%;text-align:center" maxlength="10" /></td>
                        <td><input id="pick" value="" style="height:100%;width: 100%;text-align:center" maxlength="10" disabled readonly /></td>
                    </tr>
                </table>
                <table style="margin-top: 15px;font-size:16px;">
                    <tr>
                        <th colspan="3">突发公共事件情况</th>
                    </tr>
                    <tr>
                        <td><span style="color:red;">*</span>次数</td>
                        <td><span style="color:red;">*</span>伤(人)</td>
                        <td><span style="color:red;">*</span>亡(人)</td>
                    </tr>
                    <tr>
                        <td><input id="publicEventNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 254px;text-align:center" maxlength="10" /></td>
                        <td><input id="woundedNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 254px;text-align:center" maxlength="10" /></td>
                        <td><input id="deadNum" type="text" value="" oninput="validateInputPositiveOrZero(this)" style="height:100%;width: 254px;text-align:center" maxlength="10" /></td>
                    </tr>
                </table>
            </div>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:60px;font-size:16px;text-align: right;"><span style="color:red;">*</span>接班人:</span>
                <input id="successor" name="dept" style="width: 500px;height: 32px;" value="">
            </p>
            <p style="text-align:center;margin: 10px;">
                <span style="display:inline-block;width:60px;font-size:16px;text-align: right;"><span style="color:red;">*</span>密码:</span>
                <input id="password" type="password" value="" style="width: 500px;height: 32px;" maxlength="20" />
            </p>
            <p style="text-align:center;margin: 15px 0;">
                <a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:doSuccessor();" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">交班</a>
                <a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:cancleSuccessor();" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">取消</a>
            </p>
        </div>

        <!--修改车辆状况-->
        <div id="updateMobileConditionDialog" class="easyui-dialog" title="修改救护车状况" style="width:400px;height:150px;font-size:16px;padding-left:20px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#dlg-buttons">
            <input type="hidden" value="" id="updateConditionMobileId" /><!--车辆id-->
            <input type="hidden" value="" id="mobileCondition" /><!--车辆状况-->
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('0')">
                <input class="easyui-checkbox" id="conditionZc" name="conditionUpdate" value="0" label="正常:">
            </span>
            <!--加入了报停功能，暂时隐藏维护和驻场按钮-->
            <!-- <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('1')">
                <input class="easyui-checkbox" id="conditionWh" name="conditionUpdate" value="1" label="维护:">
            </span>
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('2')">
                <input class="easyui-checkbox" id="conditionZd" name="conditionUpdate" value="2" label="驻场:">
            </span> -->
            <span style="float: left; margin: 10px 20px 0 0;" onclick="checkedCondition('3')">
                <input class="easyui-checkbox" id="conditionWzb" name="conditionUpdate" value="3" label="未值班:">
            </span>
        </div>
        <div id="dlg-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:openMobileConditionBtn($('#updateConditionMobileId').val(),$('#mobileCondition').val())" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeMobileConditionWindowBtn();" style="width:80px;">取消</a>
        </div>

        <!--设为待命接口-->
        <div id="updateAwaitMobileDialog" class="easyui-dialog" title="救护车完成任务并设置为待命状态" style="width:350px;height:420px;font-size:14px;padding-left:10px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#perform-buttons">
            <input type="hidden" value="" id="updateAwaitMobileProcessId" /><!--车辆当前任务id-->
            <input type="hidden" value="" id="updateAwaitMobileProcessIdToStatus" /><!--车辆当前任务状态-->
            <input type="hidden" value="" id="updateAwaitMobileProcessIdEventId" /><!--车辆当前任务状态-->
            <input type="hidden" value="" id="updateAwaitMobileProcessIdCarId" /><!--车辆当前任务状态-->
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">派车时间：</span>
                <input id="send-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">出车时间：</span>
                <input id="goto-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;" id="cancel-car-times-div">
                <span style="margin-left: 1em;">取消时间：</span>
                <input id="cancel-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">抵达时间：</span>
                <input id="arrive-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">离开现场：</span>
                <input id="leave-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;">
                <span style="margin-left: 1em;">回院时间：</span>
                <input id="back-car-times" class="easyui-datetimebox" style="width:210px;text-indent: 0.5em;" />
            </div>
            <div class="clear-fix" style="margin-top: 10px;margin-bottom: 10px;margin-left: 5px;">
                <span style="color:red">请注意：</span><br />
                <span style="margin-left: 1em;">当车辆是待派车状态时，如果填写了取消时间那么代表车辆是取消接诊，如果车辆非取消接诊那么不需要填写此时间。</span>
            </div>
        </div>
        <div id="perform-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:updateAwaitMobileBtn()" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeUpdateAwaitMobileDialog();" style="width:80px;">取消</a>
        </div>

        <!--纠正出车 填写原因-->
        <div id="changeMobileReasonDialog" class="easyui-dialog" title="纠正出车，请选择原因" style="width:350px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#correctionar-buttons">
            <input type="hidden" value="" id="change-mobile-process-id" /><!--改派车辆的任务ID-->
            <input type="hidden" value="" id="change-mobile-car-id" /><!--改派车辆的车辆ID-->
            <form id="changeMobileReasonForm">
                <div style="margin-bottom:20px;display:block;margin-left: 20px;">
                    <div id="changeMobileReasonFormRadio" style="margin-top: 20px;"></div>
                    <div style="margin-bottom:10px;display:block;margin-left: 20px;">
                        <input class="easyui-radiobutton" name="changeMobileReason" value="-1" data-options="onChange:otherCahrgeMobileReasonSelectBtn">
                        <span style="margin-left: 10px;margin-right: 5px;font-size: 14px;">其他</span>
                        <input class="easyui-textbox" id="otherChangeMobileReason" style="height:24px;width:200px">
                    </div>
                </div>

            </form>
        </div>
        <div id="correctionar-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:changeMobileProcessMobileExecBtn($('#change-mobile-process-id').val(),$('#change-mobile-car-id').val())" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript:closeReassigningMobileProcessExecBtn();" style="width:80px;">取消</a>
        </div>

        <!--撤销事件窗口-->
        <div id="revocationEventDialog" class="easyui-dialog" title="撤销事件，请输入原因" style="width:300px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#cxsj-buttons">
            <input type="hidden" value="" id="revocation-event-id" /><!--撤销事件的事件ID-->
            <form id="revocationEventForm">
                <div style="margin-top: 20px;height:40px; text-align:center;line-height: 40px; font-size: 16px;">
                    欲派无车：<select id="cancelEventNoCar" class="select-common" style="height:28px;width: 200px; font-size: 16px;">
                        <option value="0">否</option>
                        <option value="1">是</option>
                    </select>
                </div>
                <div style="height:40px; text-align:center;line-height: 40px; font-size: 16px;">
                    　　原因：<input class="easyui-textbox" id="revocationEventReason" style="height:28px;width:200px; font-size: 16px;" />
                </div>
            </form>
        </div>
        <div id="cxsj-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:cancelEventBtn()" style="width:80px;">普通撤销</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript:closeCancelEventBtnBtn();" style="width:80px;">取消</a>
        </div>

        <!--撤销调度窗口-->
        <div id="revocationSchedulingDialog" class="easyui-dialog" title="撤销调度，取消接诊，请选择原因" style="width:300px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#cxdd-buttons">
            <input type="hidden" value="" id="revocation-scheduling-process-id" /><!--撤销调度车辆的任务ID-->
            <input type="hidden" value="" id="revocation-scheduling-msg-id" /><!--如果是通过消息提醒来撤销调度的话，那么此项不为空字符串，有消息id-->
            <form id="revocationSchedulingForm">
                <!-- <div style="height:60px; text-align:center;line-height: 60px;">
                    原因：<input class="easyui-textbox" id="revocationSchedulingReason" style="height:24px;width:200px" />
                </div> -->
                <div style="height:60px; text-align:center; line-height: 60px; font-size: 16px;">
                    原因：
                    <div id="reasonContainer" style="display: inline-block; text-align:left; font-size: 16px;">
                        <!-- 根据返回数据，这个区域将动态替换为 input 或 select -->
                    </div>
                </div>                
                <div style="height:40px; text-align:center;line-height: 26px; font-size: 16px;">
                    是否分站拒绝派车：<select id="stationRefuseDispatch" class="select-common" style="height:28px;width: 125px; font-size: 16px;">
                        <option value="0">否</option>
                        <option value="1">是</option> 
                    </select>
                </div>
            </form>
        </div>
        <div id="cxdd-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:cancelMobileProcessBtn()" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeCancelMobileProcessBtn();" style="width:80px;">取消</a>
        </div>

        <!--<div id="videoDialog" class="easyui-dialog" title="远程音视频" style="width:100%;height:100%" data-options="resizable:false,modal:true,closed:true,closable:true">
            <iframe name="eventList" id="eventListIframe" scrolling="auto" src="" frameborder="0" style="width:98%;height:98%;"></iframe>
        </div>-->

        <!--改派车辆窗口-->
        <div id="changeCarForProcessDialog" class="easyui-dialog" title="改派车辆，请输入原车辆取消原因" style="width:300px;"
             data-options="resizable:false,modal:true,closed:true,closable:true" buttons="#gpcl-buttons">
            <input type="hidden" value="" id="change-car-process-id" /><!--改派车辆的原任务ID-->
            <input type="hidden" value="" id="change-newcar-id" /><!--改派车辆的新的车辆的ID-->
            <form id="changeCarForProcessForm">
                <div style="height:60px; text-align:center;line-height: 60px;">
                    原因：<input class="easyui-textbox" id="changeCarForProcessReason" style="height:24px;width:200px" />
                </div>
            </form>
        </div>
        <div id="gpcl-buttons">
            <a href="#" class="easyui-linkbutton" onclick="javascript:changeCarForProcessBtn()" style="width:80px;">确定</a>
            <a href="#" class="easyui-linkbutton" onclick="javascript: closeChangeCarForProcessBtn();" style="width:80px;">取消</a>
        </div>

        <div id="mainFrame" style="color: #6E6E6E; display: flex; flex-direction: column; height: 100vh;">
            <div id="header" style="flex-shrink: 0;">
                <div class="logo" style=""></div>
                <div class="title" style="background: url(style/img/edc120_title.png);"></div>
                <div class="account">
                    <!--班次：<span id="cur-schedule-name"></span>&nbsp;(<span id="cur-schedule-time"></span>)&nbsp;&nbsp;-->
                    班次：<span id="cur-schedule-name"></span>&nbsp;&nbsp;
                    调度员：<span id="dispatcher-name"></span>&nbsp;(<span id="dispatcher-code"></span>)
                    <a href="javascript:changeShiftSeat();" class="easyui-tooltip logout" title="座席交接班" style="font-size: 14px;"><i class="fa fa-exchange"></i>&nbsp;交接班</a>
                    <a href="javascript:hangUpBtn();" class="easyui-tooltip logout" title="座席离席挂起操作，离席挂起的同时120电话不能呼入" style="font-size: 14px;width:100px;"><i class="fa fa-pause-circle"></i>&nbsp;离席挂起</a>
                    <a href="javascript:openMobileVideoAndMap();" class="easyui-tooltip logout" title="车载视频与地图管理子系统" style="font-size: 14px;width:100px;"><i class="fa fa-file-video-o"></i>&nbsp;地图/视频</a>

                    <a href="javascript:LogOut();" class="easyui-tooltip logout" title="登出账号并重新登录另一个账号" style="font-size: 14px;"><i class="fa fa-sign-out"></i>&nbsp;注销</a>
                </div>
                <div class="timezone clear-fix" style="width:360px;height: 55px;position: relative;">
                    <span id="seat-num" style="float: left;font-weight: bold;height: 20px;line-height: 20px;margin-top: 5px;"></span>
                    <span id="seat-call-in-line-name" style="float: left; font-weight: bold;height: 20px; line-height: 20px;margin-top: 5px;">接警分机</span>
                    <span id="seat-call-out-line-name" style="float: left;font-weight: bold; height: 20px; line-height: 20px;margin-top: 5px;">调度分机</span>
                    <div class="xiaoxi" onclick="noticeDetail()">
                        <img src="style/img/xiaoxi.png" alt="">
                        <span class="xiaoxi_badge" id="xiaoxi_badge"></span>
                    </div>
                    <span id="cur-date-time" style="float: left;height: 20px;line-height: 20px;margin-top: 5px;"></span>
                    <span id="network-delay" style="float: right;height: 20px;line-height: 20px;margin-top: 5px;"></span>
                    <span id="version-number" style="float: right;height: 20px;line-height: 20px;margin-top: 5px;"></span>
                </div>
            </div>

            <!-- 主要内容区域使用flex布局 -->
            <div style="display: flex; flex: 1; overflow: hidden;">

                <!-- 左侧区域：呼叫与消息、未接来电等 -->
                <div id="leftPanel" class="group-box" style="width: 25%; flex-shrink: 0; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- 呼叫与消息区域 -->
                    <div id="callMessageArea" style="flex-shrink: 0; border-bottom: 1px solid #E2E7EA;">
                <div class="group-box-title" style="padding-left: 15px;padding: 0px 0px; margin: 0px 0px;width: 100%;padding-left:15px; height: 30px">
                    呼叫与消息
                    <div style="float:right;">
                        <a class="easyui-linkbutton" id="keepCallBtn" href="javascript:keepCall();" style="width: 70px;height:25px; font-size: 10px;margin: 5px 5px 5px 5px; display: none;">保持</a>
                        <a class="easyui-linkbutton" id="recoveryCallBtn" href="javascript:recoveryCall();" style="width: 70px;height:25px;line-height: 25px; font-size: 10px;margin: 5px 5px 5px 5px; display: none;">恢复</a>
                    </div>
<!--                    <a class="easyui-linkbutton" id="soft-phone-reject-call-in-btn" href="javascript:rejectSoftPhone('1');" style="float:right;width: 70px;height:24px;line-height: 24px; font-size: 10px; margin: 5px 5px 5px 5px;color:red" data-options="iconCls:'icon-softphone-Hangup'">挂断</a>&lt;!&ndash;软电话未接听直接挂断线路1的电话&ndash;&gt;-->
<!--                    <a class="easyui-linkbutton" id="soft-phone-hold-call-in-btn" href="javascript:holdAndUnholdSoftPhone('1');" style="float:right;width: 70px;height:24px;line-height: 24px; font-size: 10px; margin: 5px 5px 5px 5px;color:black" data-options="iconCls:'icon-softphone-hold'">保持</a>&lt;!&ndash;软电话保持当前通话&ndash;&gt;-->
<!--                    <a class="easyui-linkbutton" id="soft-phone-accept-call-in-btn" href="javascript:acceptAndHangupSoftPhone('1');" style="float:right;width: 70px;height:24px;line-height: 24px; font-size: 10px; margin: 5px 5px 5px 5px;color:green" data-options="iconCls:'icon-softphone-dialcall'">接听</a>&lt;!&ndash;软电话接听线路1的电话&ndash;&gt;-->
                    <input type="hidden" value="" id="soft-phone-accept-call-id" /><!--软电话呼入ID,用于接听中的相关操作-->
                </div>
                <!-- 120呼入状态显示 -->
                <div id="phoneCallInShow" style="padding: 0 0 0 5px; margin-top: 5px;">
                    <span id="currentCallNum" class="blueblink">空闲</span>
                    <span id="isHarass" style="display: none;"></span>
<!--                    <a class="easyui-linkbutton" id="keepCallBtn" href="javascript:keepCall();" style="margin-left: 10px; display: none">保持</a>-->
<!--                    <a class="easyui-linkbutton" id="recoveryCallBtn" href="javascript:recoveryCall();" style="margin-left: 10px; display: none">恢复</a>-->
                    <div style="float: right; width: 160px;">
                        <div style="display:inline-block;font-size: 18px;">电话呼入:</div>
                        <div id="callInStatus" class="btn_fath clearfix off" style="display:inline-block;vertical-align: text-bottom;" onclick="callInOm(this)">
                            <div class="move" data-state="off"></div>
                            <div class="btnSwitch btn1">开</div>
                            <div class="btnSwitch btn2">关</div>
                        </div>
                    </div>
                </div>

                <!-- 呼入状态分割线显示 默认隐藏 后台配置了座席是视频座席时，显示视频呼入状态-->
                <hr id="callInStatusLine" style="width: Calc(100% - 10px); background:#D4D4D4; display: none;" />

                <!-- 视频呼入状态显示 默认隐藏 后台配置了座席是视频座席时，显示视频呼入状态--->
                <div id="videoCallInShow" style="padding: 0 0 0 5px; margin-top: 5px; display: none;">
                    <span id="currentVideoCallNum" class="blueblink">空闲</span>
                   
                    <span id="isVideoHarass" style="display: none;"></span>
                    <div style="float: right; width: 160px;">
                        <div style="float:right; margin-right:30px;">
                            <a class="easyui-linkbutton" id="video-accept-btn" href="javascript:acceptVideoCall();" style="margin-right:5px;font-size:12px;color:green;display:none"><i class="fa fa-phone"></i> 接听</a>
                            <!-- <a class="easyui-linkbutton" id="video-transfer-btn" href="javascript:transferVideoCall();" style="margin-right:5px;font-size:12px;color:blue;display:none"><i class="fa fa-exchange"></i> 转移</a> -->
                            <a class="easyui-linkbutton" id="video-complete-btn" href="javascript:hangupVideoCall();" style="margin-right:5px;font-size:12px;color:blue;display:none"><i class="fa fa-check"></i> 完成挂断</a>
                        </div>
                        <div id="videoCallInStatusTxt" style="display:inline-block;font-size: 18px;">视频呼入:</div>
                        <div id="videoCallInStatus" class="btn_fath clearfix off" style="display:inline-block;vertical-align: text-bottom;" onclick="videoCallLineNoDisturb(this)">
                            <div class="move" data-state="off"></div>
                            <div class="btnSwitch btn1">开</div>
                            <div class="btnSwitch btn2">关</div>
                        </div>
                    </div>
                </div>
                <hr style="width: Calc(100% - 10px); background:#D4D4D4" />
                    </div>

                    <!-- 消息提醒区域 -->
                    <div id="messageArea" style="flex: 1; min-height: 100px; overflow: hidden;">
                        <div style="height: 100%; position: relative; font-size: 12px; margin: 5px;">
                            <ul id="dl-msg-list" class="easyui-datalist" title="消息提醒" lines="true" style="width:100%; height: 100%; overflow-y: auto; background-color: white !important;" fit="true"></ul>
                </div>
            </div>

                    <!-- 水平拖拽分隔条 -->
                    <div id="horizontalDragSeparator" style="width:100%;height:3px;background:#cce7ff;cursor:ns-resize;position:relative;flex-shrink:0;display:flex;align-items:center;justify-content:center;">
                        <div style="width:30px;height:1px;background:#87ceeb;border-radius:1px;"></div>
                    </div>

                    <!-- 未接来电等tabs区域 -->
                    <div id="tabsArea" style="flex: 1; min-height: 200px; overflow: hidden;">
                        <div id="left-list" class="easyui-tabs" style="height: 100%;">
                            <div title="未接来电" style="width: 100%;height: 100%;display:none;">
                                <div style="height: Calc(100%); position: relative;margin-left: 5px;padding-top: 5px;">
                                    <table class="easyui-datagrid" id="missedCalls" style="width:100%" singleSelect="true" rownumbers="false" fit="true">
                                        <thead data-options="frozen:true">
                                            <tr>
                                                <th field="phoneButton" width="38" align="center" formatter="phoneButtonFormatter">
                                                </th>
                                                <th field="number" width="110" align="center" formatter="numberFormatter">未接来电号码</th>
                                                <th field="toNumber" width="60" align="center">接听号码</th>
                                                <th field="callTime" width="140" align="center">来电时间</th>
                                                <th field="isAnswer" width="55" checkbox="false" formatter="isAnswerFormatter" align="center">状态</th>
                                                <th field="handleStatus" width="64" checkbox="false" formatter="handleStatusFormatter" align="center">处理状态</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                            <div title="座席与分站状态" style="width: 100%;height: 100%;display:none;">
                                <div style="height: Calc(100%); position: relative;padding: 5px;">
                                    <table class="easyui-datagrid" id="dg-seat-station-status-list" style="width:100%"
                                           pagination="false" singleSelect="true" rownumbers="false" fit="true">
                                        <thead data-options="frozen:true">
                                            <tr>
                                                <th field="seatStationStatusIcon" width="30" checkbox="false" align="center" formatter="setDgseatStationIconColumnFormatter"></th>
                                            </tr>
                                        </thead>
                                        <thead>
                                            <tr>
                                                <th field="seatStationStatusName" width="190" align="left">名称</th>
                                                <th field="seatStationStatusStatus" width="60" align="left">状态</th>
                                                <th field="seatStationStatusOtherInfo" width="150" checkbox="false" align="left">线路</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                            <!--软电话  -->
                            <div title="调度话机" style="width: 100%;height: 100%;display:none;" id="ruandianhua">
                                <div style="height: Calc(100%); position: relative;padding: 5px;">
                                    <div class="public-keyboard-pop">
                                        <div class="del-phone" id="delPhone">✖</div>
                                        <div class="keys-wrap">
                                            <span id="soft-keyboard-calling-msg" class="blueblink" style="display: none; width: 500px; font-size: 36px; text-align: center;">空闲</span><!--通话与正在拨打电话的时候显示-->
                                            <input class='key-item soft-keyboard-input' id='soft-keyboard-input' /><!--录入号码的时候显示-->
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">1</span><span></span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">2</span><span class="item-line2">ABC</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">3</span><span class="item-line2">DEF</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">4</span><span class="item-line2">GHI</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">5</span><span class="item-line2">JKL</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">6</span><span class="item-line2">MNO</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">7</span><span class="item-line2">PQRS</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">8</span><span class="item-line2">TUV</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'><span class="item-line1">9</span><span class="item-line2">WXYZ</span></span>
                                            <span class='key-item xing' lay-filter='normal-num-key-item'><span class="item-line1">*</span><span class="item-line2">(P)</span></span>
                                            <span class='key-item zero' lay-filter='normal-num-key-item'><span class="item-line1">0</span><span class="item-line2">+</span></span>
                                            <span class='key-item jing' lay-filter='normal-num-key-item'><span class="item-line1">#</span><span class="item-line2">(W)</span></span>
                                            <span class='key-item' lay-filter='normal-num-key-item'>
                                                <p id="soft-phone-hold-phone" style="display: none;" onclick="javascript:holdAndUnholdSoftPhone('2');">保持</p>
                                                <p id="soft-phone-unhold-phone" style="display: none;" onclick="javascript:holdAndUnholdSoftPhone('2');">恢复</p>
                                            </span>
                                            <span class='key-item' lay-filter='normal-num-key-item'>
                                                <p id="soft-phone-call-phone" onclick="javascript:softPhoneCallPhoneBykeyword();">呼叫</p>
                                                <p id="soft-phone-hangup-phone" style="display: none;" onclick="javascript:softPhoneHangupPhoneBykeyword('2');">挂断</p>
                                                <p id="soft-phone-dialcall-phone" onclick="javascript:acceptAndHangupSoftPhone('2')" style="display: none;">接听</p>
                                            </span>
                                            <span class='key-item' lay-filter='normal-num-key-item'>
                                                <span class="delPhone-line" id="soft-phone-del-phone">
                                                </span>
                                                <p id="soft-phone-reject-phone" onclick="javascript:rejectSoftPhone('2')" style="display: none;">拒接</p>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 垂直拖拽分隔线 -->
                <div id="verticalDragSeparator" style="width:3px;background:#cce7ff;cursor:ew-resize;position:relative;flex-shrink:0;display:flex;align-items:center;justify-content:center;">
                    <div style="width:1px;height:30px;background:#87ceeb;border-radius:1px;"></div>
                </div>

                <!-- 右侧区域：事件监控 -->
                <div id="rightPanel" class="group-box" style="flex: 1; border: none; overflow: hidden;">
                <div id="main-tabs" class="easyui-tabs" style="width: 100%; height:100%;margin-top:0px;border: solid #E2E7EA 1px;overflow:hidden;">
                    <div title="事件监控" style="display: none; overflow: hidden;">
                        <div id="eventList" style="width:100%;height: calc(50% - 2px); position: relative;font-size: 20px; margin-top: 5px; overflow: hidden;">
                            <table class="easyui-datagrid" id="dg-event-list" style="width:100%;font-weight: bold;" title=""
                                    pagination="false" singleSelect="true" rownumbers="false" fit="true" 
                                    data-options="
                                        toolbar: '#tb-event',
                                        footer: '',
                                        onRowContextMenu: onEventRowContextMenu">
                                <thead data-options="frozen:true">
                                    <tr>
                                        <th field="eventStatusIcon" width="30" checkbox="false" align="center" formatter="setDgEventStatusIconColumnFormatter"></th>
                                        <th field="openVideoPage" width="30" checkbox="false" align="center" formatter="videoFormatter"></th>
                                    </tr>
                                </thead>
                                <thead>
                                    <tr>
                                        <th field="eventStatusStr" width="90" checkbox="false" align="center" styler="setDgEventColumnBackColor">调度状态</th>
                                        <th field="taskPhone" width="120" checkbox="false" align="center" styler="setDgEventColumnBackColor" formatter="taskPhoneFormatter">任务联系电话</th>
                                        <th field="carContact" width="120" checkbox="false" align="center" styler="setDgEventColumnBackColor" formatter="carContactFormatter">车辆联系电话</th>
                                        <th field="callInTimes" width="150" align="center" styler="setDgEventColumnBackColor">来电/去电时间</th>
                                        <th field="callTypeName" width="80" checkbox="false" align="center" styler="setDgEventColumnBackColor">呼叫类型</th>
                                        <th field="eventName" width="500" checkbox="false" align="center" styler="setDgEventColumnBackColor">事件名称</th>
                                        <!--<th field="majorCall" width="200" checkbox="false" align="center" styler="setDgEventColumnBackColor">事件名称</th>-->
                                        <!--<th field="address" width="320" checkbox="false" align="center" styler="setDgEventColumnBackColor">现场地址</th>-->
                                        <th field="contact" width="110" align="center" styler="setDgEventColumnBackColor">联系电话</th>
                                        <th field="mobileStatusStr" width="70" align="center" styler="setDgEventColumnBackColor">任务状态</th>
                                        <th field="eventTypeStr" width="90" align="center">事件类型</th>
                                        <th field="stationName" width="160" checkbox="false" align="center" styler="setDgEventColumnBackColor">指派分站</th>
                                        <th field="processTime" width="150" checkbox="false" align="center" styler="setDgEventColumnBackColor">调度时间</th>
                                        <th field="plateNum" width="90" checkbox="false" align="center" styler="setDgEventColumnBackColor">指派车辆</th>
                                        <th field="seatUser" width="80" checkbox="false" align="center" styler="setDgEventColumnBackColor">调度员</th>
                                        
                                        <th field="callIn" width="110" align="center" styler="setDgEventColumnBackColor">呼救电话</th>
                                        <th field="eventSrcName" width="120" checkbox="false" align="center" styler="setDgEventColumnBackColor">事件来源</th>
                                        <th field="eventId" width="230" checkbox="false" align="center" styler="setDgEventColumnBackColor">事件编码</th>
                                        <th field="processId" width="230" checkbox="false" align="center" styler="setDgEventColumnBackColor">任务编码</th>
                                    </tr>
                                </thead>
                            </table>
                            <!-- 右键菜单 -->
                            <div id="event-context-menu" class="easyui-menu" style="width:120px;">
                                <div data-options="iconCls:'icon-waiting'" onclick="setEventToWaitingStatus()">设置待派</div>
                                <div class="menu-sep"></div>
                                <div data-options="iconCls:'icon-lock'" onclick="lockEvent()">锁定事件</div>
                                <div data-options="iconCls:'icon-unlock'" onclick="unlockEvent()">解锁事件</div>
                            </div>
                            <div id="tb-event" style="min-height: 40px; padding: 5px; font-weight: bold; overflow: hidden;">
                                <div style="display: flex; flex-wrap: wrap; gap: 5px; justify-content: flex-end;">
                                    <!-- 状态筛选组 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; margin-right: 10px;">
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="event-status-all" name="event-status-all" value="0" checked="checked" />
                                            <span class="l-btn-text" style="margin-left: 3px;">全部(<span id="event_counts">0</span>)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="event-status-appointment" name="event-status-appointment" value="7" />
                                            <span class="l-btn-text" style="margin-left: 3px;">预约(<span id="event_appointment_counts">0</span>)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="event-status-waiting" name="event-status-waiting" value="6" />
                                            <span class="l-btn-text" style="margin-left: 3px;">待派(<span id="event_waiting_counts">0</span>)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="event-status-unscheduled" name="event-status-unscheduled" value="1" />
                                            <span class="l-btn-text" style="margin-left: 3px;">落单(<span id="event_unscheduled_counts">0</span>)</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="event-status-scheduled" name="event-status-scheduled" value="2" />
                                            <span class="l-btn-text" style="margin-left: 3px;">已调度(<span id="event_scheduled_counts">0</span>)</span>
                                        </label>
                                    </div>
                                    <!-- 按钮组1 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 3px; margin-right: 0px;">
                                    <a class="easyui-linkbutton buttonUniformStyle" id="refresh-event-btn" href="javascript:refreshEventlist();" data-options="iconCls:'icon-reload'">刷新</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="create-event-btn" href="javascript:openCreateNewEventTab();" style="color:red" data-options="iconCls:'icon-add'">创建事件</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="show-event-btn" href="javascript:openEventInfoTab();" data-options="iconCls:'icon-show'">查看事件</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="sendTaskDialogOpen" href="javascript:sendTaskBtn();" data-options="iconCls:'icon-ambulance'">派车增援</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="changeCarForProcessDialogOpen" href="javascript:changeCarForProcessDialogOpen();" data-options="iconCls:'icon-filter'">改派车辆</a>
                                    </div>
                                    <!-- 按钮组2 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 3px; margin-right: 10px;">
                                    <a class="easyui-linkbutton buttonUniformStyle" id="changeMobileProcessMobileBtn" href="javascript:changeMobileProcessMobileBtn();" data-options="iconCls:'icon-sum'">纠正出车</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="cancelEventDialog" href="javascript:cancelEventDialog();" data-options="iconCls:'icon-undo'">撤销事件</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="cancelMobileProcessDialog" href="javascript:cancelMobileProcessDialog();" data-options="iconCls:'icon-redo'">撤销调度</a>
                                    <a class="easyui-linkbutton buttonUniformStyle" id="recoverMobileProcessBtn" href="javascript:recoverMobileProcessBtn();" data-options="iconCls:'icon-recover'">恢复接诊</a>
                                </div>
                                </div>
                            </div>
                        </div>

                        <!-- 拖拽分隔条 -->
                        <div id="dragSeparator" style="width:100%;height:3px;background:#cce7ff;cursor:ns-resize;position:relative;display:flex;align-items:center;justify-content:center;">
                            <div style="width:30px;height:1px;background:#87ceeb;border-radius:1px;"></div>
                        </div>

                        <!-- 车辆列表 -->
                        <div id="vehicleList" style="width:100%;height: calc(50% - 2px); position: relative;font-size: 14px; overflow: hidden;">
                            <table class="easyui-datagrid" id="dg-mobiels-list" style="width:100%;font-weight: bold;" title=""
                                   pagination="false" singleSelect="true" rownumbers="false" fit="true" toolbar="#tb-mobiles">
                                <thead data-options="frozen:true">
                                    <tr>
                                        <!--车辆状态图标-->
                                        <th field="mobileStatusIcon" width="30" checkbox="false" align="center" formatter="setDgMobileStatusIconColumnFormatter"></th>
                                        <!--车头平板和座席端的视频按钮-->
                                        <th field="openCarVideoPage" width="40" checkbox="false" align="center" formatter="videoCarFormatter"></th>
                                    </tr>
                                </thead>
                                <thead>
                                    <tr>
                                        <th field="stationName" width="160" align="center">分站名称</th>
                                        <th field="carName" width="70" align="center">车辆别名</th>
                                        <th field="plateNum" width="90" checkbox="false" align="center">车牌号码</th>
                                        <th field="carType" width="80" checkbox="false" align="center" formatter="carTypeFormatter">车辆类型</th>
                                        <th field="dispatchDoctor" width="100" checkbox="false" align="center">出车医生</th>
                                        <th field="dispatchNurse" width="100" checkbox="false" align="center">出车护士</th>
                                        <th field="dispatchDriver" width="100" checkbox="false" align="center">出车司机</th>
                                        <th field="mobileStatusStr" width="60" align="center">车辆状态</th>
                                        <!--<th field="mobileToEventDistance" width="90" align="center" formatter="distanceFormatter">距离</th>-->
                                        <th field="mobileToEventMapDistance" width="90" align="center" formatter="distanceFormatter">距离</th>
                                        <th field="statusStr" width="80" align="center">任务状态</th>
                                        <th field="sendTime" width="180" align="center">派车时间</th>
                                        <th field="outOfTime" width="180" align="center">出车时间</th>
                                        <th field="contact" width="120" align="center">随车电话</th>
                                        <th field="currentProcessId" width="200" align="center">任务编码</th>
                                        <th field="remark" width="500" align="center">事件备注</th>
                                    </tr>
                                </thead>
                            </table>
                            <!--tb-mobiles是给车辆列表dg-mobiels-list使用的toolbar-->
                            <div id="tb-mobiles" style="min-height: 40px; padding: 5px; font-weight: bold; overflow: hidden;">
                                <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
                                    <!-- 车辆数统计 -->
                                    <div style="font-size: 16px; white-space: nowrap;">
                                        车辆数：<span id="mobile_counts">0</span>
                                    </div>
                                    
                                    <!-- 查询输入框组 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px; align-items: center;">
                                        <input id="stationRegionId" class="easyui-textbox" style="width:150px;" prompt="输入分站区域查询">
                                        <input id="stationName" class="easyui-textbox" style="width:170px;" prompt="输入分站名称查询">
                                        <input id="plateNum" class="easyui-textbox" style="width:120px;" prompt="输入车牌号码查询">
                                    </div>
                                    
                                    <!-- 状态筛选组 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center;">
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="mobile-status-all" name="mobile-status-all" value="0" checked="checked" />
                                            <span class="l-btn-text" style="margin-left: 3px;">全部</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="mobile-status-await-orders" name="mobile-status-all" value="1" />
                                            <span class="l-btn-text" style="margin-left: 3px;">待命</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="mobile-status-running-task" name="mobile-status-all" value="2" />
                                            <span class="l-btn-text" style="margin-left: 3px;">任务</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="mobile-status-pause" name="mobile-status-all" value="3" />
                                            <span class="l-btn-text" style="margin-left: 3px;">暂停</span>
                                        </label>
                                        <label style="display: flex; align-items: center; white-space: nowrap;">
                                            <input type="checkbox" id="mobile-status-not-on-duty" name="mobile-status-all" value="4" />
                                            <span class="l-btn-text" style="margin-left: 3px;">未值班</span>
                                        </label>
                                    </div>
                                    
                                    <!-- 操作按钮组 -->
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                        <a class="easyui-linkbutton" id="refreshMobilesOrStationsBtn" href="javascript:refreshMobilesOrStations();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" data-options="iconCls:'icon-reload'">刷新</a>
                                        <a class="easyui-linkbutton" id="openMobileConditionWindowBtn" href="javascript:openMobileConditionWindowBtn();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" title="" data-options="iconCls:'icon-edit'">修改车况</a>
                                        <a class="easyui-linkbutton" id="openUpdateAwaitMobileDialog" href="javascript:openUpdateAwaitMobileDialog();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" title="强制设置车辆为待命状态，如果是取消接诊，那么需要录入取消接诊时间，如果非取消接诊需要录入车辆相关信息" data-options="iconCls:'icon-tip'">完成任务</a>
                                        <a class="easyui-linkbutton" id="updateForceTaskSyncBtn" href="javascript:updateForceTaskSyncBtn();" style="width: 100px;height:28px;line-height: 28px; font-size: 12px;" title="当急救平台车辆与120系统不同步的时候，强制同步" data-options="iconCls:'icon-tip'">强制同步</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分站列表 -->
                        <div id="substationsList" style="width:100%;height: 50%; position: relative;font-size: 14px; margin-top: 5px;display:flex;">
                            <div style="width:12%;margin-right:10px;">
                                <ul id="station-region" style="width: 100%; float: left; height: Calc(100% - 0.3em); display: inline-block;" class="edc-listbox">
                                    <input id="search-regionName" prompt="输入区县名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;">
                                </ul>
                            </div>
                            <table class="easyui-datagrid" id="station-list" style="width:88%;font-weight: bold;margin-bottom:35px;" title="" pagination="false" singleSelect="true" rownumbers="false" fit="true" toolbar="#tb-station">
                                <thead data-options="frozen:true">
                                    <tr>
                                        <th field="stationIsLoginIcon" width="30" checkbox="false" align="center" formatter="stationIsLoginIconFormatter"></th>
                                    </tr>
                                </thead>
                                <thead>
                                    <tr>
                                        <th field="stationName" width="200" align="center">分站名称</th>
                                        <th field="stationRegionName" width="70" align="center">所属区域</th>
                                        <th field="isLogin" width="50" align="center" formatter="stationIsLoginFormatter">状态</th>
                                        <th field="stationToEventDistance" width="100" align="center">事件距离(Km)</th>
                                        <th field="standByNum" width="60" checkbox="false" align="center">空闲车辆</th>
                                        <th field="statusCount" width="240" checkbox="false" align="center" formatter="vehicleStatusFormatter">车辆状态数量</th>
                                        <th field="stationContact" width="120" checkbox="false" align="center" formatter="stationContactFormatter">分站电话</th>
                                        <th field="remark" width="270" checkbox="false" align="center" formatter="stationRemarkFormatter">备注</th>
                                        <th field="operation" width="100" checkbox="false" align="center" formatter="stationOperationFormatter">操作</th>
                                    </tr>
                                </thead>
                            </table>
                            <!--tb-station-->
                            <div id="tb-station" style="height: 40px;line-height:40px;font-weight: bold;">
                                <div style="height:30px;line-height:0px;float:left; margin-right:10px;font-size:14px;position: relative;top: 4px;">
                                    <a class="easyui-linkbutton" id="refresh-button" style="width: 110px;height:26px;line-height: 26px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-reload'">刷新</a>
                                    <input type="checkbox" id="chkStationIsLogin" name="chkStationIsLogin" value="0" style="zoom:100%;position: relative;top: -1px;width:20px;height:18px;font-size:14px;" />分站在线
                                    <input type="checkbox" id="chkCarStandBy" name="chkCarStandBy" value="0" checked="checked" style="zoom:100%;position: relative;top: -1px;width:20px;height:18px;font-size:14px;" />有空闲车辆分站
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



        </div>

        <!--覆盖整个页面的登录框遮罩-->
        <div id="loginPanel" style="display: block; background-image: url(style/img/ddpt_background.png); background-size: 100% 100%;">
            <div class="titlebar dragmove" style="visibility: hidden !important;">
                <span></span>
                <ul class="sys-commands">
                    <li n-ui-command="minimize">
                        <i class="title-button min-btn"></i>
                    </li>
                    <!--<li n-ui-command="maximize">
                        <i class="title-button max-btn"></i>
                    </li>-->
                    <li n-ui-command="close">
                        <i class="title-button close-btn"></i>
                    </li>
                </ul>
            </div>
            <div id="login-window" class="clear-fix" style="width: 700px; height: 400px; left: Calc(50% - 350px); top: Calc(50% - 150px); position: absolute; background: #1F0772; border-radius: 6px; padding: 0; box-shadow: #09175a33 0 12px 33px 8px;">
                <div style="background-image: url(style/img/logo.png); background-repeat: no-repeat; background-size: 96px 96px; width: 96px; height: 96px; position: absolute; top: Calc(50% - 395px); left: Calc(50% - 48px);"></div>
                <div ondblclick="showVersionInfo();" style="background-image: url(style/img/edc120_title_login.png);background-repeat: no-repeat; width: 521px; height: 44px; position: absolute; top: Calc(50% - 275px); left: Calc(50% - 260px);"></div>
                <form action="" onsubmit="return doLogin()" id="loginForm" style="display: block; width: 100%; height: 120%; padding: 0; border-radius: 6px;">
                    <div style="background: #263163; color: white; display: inline-block; position: relative; float: left; width: 35%; height: 100%; border-radius: 6px 0 0 6px;">
                        <div style="border-bottom: white solid 1px; margin: 0 29px; padding: 50px 0 17px 0;">
                            <span style="font-size: 1.4em;">用户登录</span>
                            <span style="margin-left: 10px;"></span>
                        </div>
                        <div style="background: url(style/img/mark.png) no-repeat; width: 136px; height: 125px; position: absolute; bottom: 50px; left: Calc(50% - 68px);"></div>
                    </div>
                    <div style="background: white; color: #606266; width: 65%; height: 100%; float: left; border-radius: 0 6px 6px 0; padding: 20px 79px 0 58px;">
                        <div style="margin-bottom: 12px;"><i class="fa fa-desktop"></i>&nbsp;座席台:</div>
                        <div style="margin-bottom: 19px;">
                            <select id="login-seat" style="width: 100%; font-size: 16px; padding: 5px; margin-bottom: 0px;height: 40px;"></select>
                        </div>
                        <div style="margin-bottom: 12px;"><i class="fa fa-fax"></i>&nbsp;班次:</div>
                        <div style="margin-bottom: 19px;">
                            <select id="login-schedule-time-config-id" style="width: 100%; font-size: 16px; padding: 5px; margin-bottom: 0px;height: 40px;"></select>
                        </div>
                        <div style="margin-bottom: 12px;"><i class="fa fa-user-circle-o"></i>&nbsp;调度员:</div>
                        <div style="margin-bottom: 19px;">
                            <select id="login-un" style="width: 100%; font-size: 16px; padding: 5px; margin-bottom: 0px;height: 40px;"></select>
                        </div>
                        <div style="margin-bottom: 12px;"><i class="fa fa-lock"></i>&nbsp;密码:</div>
                        <div style="margin-bottom: 15px;"><input id="login-pwd" type="password" maxlength="50" style="width: 100%; font-size: 16px; padding: 5px; margin-bottom: 5px;height: 40px;" required value="" /></div>
                        <div style="margin-bottom: 12px;"><input id="is-save-psw" type="checkbox" value="1" />记住密码</div>

                        <a href="javascript:showChangePwd();" style="float: left; margin-top: 0.5em;">修改密码</a>
                        <input type="button" onclick="doLogin()" id="loginSubmitBtnd" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em; float: right;" value="登录">
                    </div>
                </form>
                <form action="" onsubmit="return doChangePwd()" id="changePwdForm" style="display: none; width: 100%; height: 100%; padding: 0; border-radius: 6px; background: white; color: #606266;">
                    <div style="display: inline-block; width: 30%; text-align: right; margin: 64px 5px 19px 0;">账号/工号:</div>
                    <div style="display: inline-block; width: 50%;"><input id="cp-un" type="text" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                    <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">旧密码:</div>
                    <div style="display: inline-block; width: 50%;"><input id="cp-pwd" type="password" maxlength="50" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                    <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 19px 0;">新密码:</div>
                    <div style="display: inline-block; width: 50%;"><input id="cp-newPwd1" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                    <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 65px 0;">确认密码:</div>
                    <div style="display: inline-block; width: 50%;"><input id="cp-newPwd2" type="password" pattern=".{6,20}" style="width: 100%; font-size: 1.3em; padding: 5px;" required /></div>
                    <div style="display: inline-block; width: 30%; text-align: right; margin: 15px 5px 0 0;"></div>
                    <div style="display: inline-block; width: 50%;"><a href="javascript:showLogin();" style="float: left; margin-top: 0.5em;">返回登录</a><input type="button" onclick="doChangePwd()" class="common-button" style="width: 200px; height: 43px; font-size: 1.1em;  float: right;" value="确认修改"></div>
                </form>
            </div>
        </div>
    </div>

    <div id="menu-station-fast" class="easyui-menu" style="width:120px;" data-options="onClick:menuHandler">
        <div data-options="name:'call_station'">拨打分站电话</div>
        <div data-options="name:'call_car'">拨打车辆电话</div>
    </div>

    <!--常用医院弹窗-->
    <div id="free-Window">
        <span style="font-size: 1.6em;color:#ffffff">电话簿</span>
        <div id="window-list">
            <h4>常用医院</h4>
            <div style="padding: 0 8px;">
                <input id="window-search-name" prompt="输入对象名称查询" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:100%;height:30px;"></input>
            </div>
            <div style="display: inline-block; width: 100%; height: calc(100% - 62px);padding: 8px;">
                <table cellpadding="0" cellspacing="0" class="edc-table" style="width: 100%;height: calc(100%);font-size: 1em;">
                    <thead>
                        <tr style="width: 100%;">
                            <th style="width: 30%;">对象名称</th>
                            <th style="width: 70%;" align="left"> &ensp;联系电话(点击号码拨号)</th>
                        </tr>
                    </thead>
                    <tbody id="window-cont-list-body"></tbody>
                </table>
            </div>
        </div>
    </div>

    <!--车辆确认接收派车界面 / 协助分站接收任务-->
    <div id="confirm-receive-editor" class="easyui-window" title="任务出车人员设置" style="width: 400px; height: 360px; text-align: center; padding-top:20px"
         data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
        <table tableframe=void style="padding-top:30px">
            <tr id="vehicleRow" style="display: none;">
                <td style="width: 100px;text-align:right;border-style:none;"><label>选择车辆:</label></td>
                <td style="border-style:none;"><select id="checkCar" name="checkCar" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><label>选择班次:</label></td>
                <td style="border-style:none;"><select id="dispatchClassesDri" name="dispatchClassesDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车医生:</label></td>
                <td style="border-style:none;"><select id="dispatchDoctorDri" name="dispatchDoctorDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车护士:</label></td>
                <td style="border-style:none;"><select id="dispatchNurseDri" name="dispatchNurseDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></tdstyle="border-style:none;">
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><!--<span style="color: red;">*</span>--><label>出车司机:</label></td>
                <td style="border-style:none;"><select id="dispatchDriverDri" name="dispatchDriverDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></td>
            </tr>
            <tr>
                <td style="width: 100px;text-align:right;border-style:none;"><label>出车护工:</label></td>
                <td style="border-style:none;"><select id="dispatchWorkerDri" name="dispatchWorkerDri" class="easyui-combobox" style="width:250px;line-height:20px;border:1px"></td>
            </tr>
        </table>
        <input type="hidden" value="" id="dispatchCarId" />
        <input type="hidden" value="" id="dispatchEventId" />
        <input type="hidden" value="" id="mobileProcessId" />
        <a href="javascript:confirmReceiveSubmit();" id="confirmReceiveSubmitBtn" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 20px 10px 0 0;">确认发车</a>
    </div>
    
    <audio id="audioPlay" src='./script/font/user_tts115014.wav'></audio>
    <!--播放报警声音-->
    <audio id="audioaAlert" src="./wav/alert.wav" controls="controls" loop="true" hidden="true"></audio>
    <!--播放拒绝派遣声音-->
    <audio id="audioRefuseDispatch" src="./wav/refuseToSend.wav" controls="controls" loop="false" hidden="true"></audio>

    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/jQuery.print.js"></script>

    <script type="text/javascript" src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript" src="script/120edc.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>

    <script type="text/javascript">
        //兼容web，如果非C#中执行，那么gProxy是未定义的，使用自定义的gProxy
        _baseUrl = "";
        bsProxy = null;
        //传数据到Electron进行处理
        // 方式1：设置回调函数
        window.onSystemOsReply = function(result) {
            console.log("electron返回获取到的系统数据：", result);
        };

        // 发送请求
        let sendElectronData = {
            "dataType": "systemOs",
            "data": ""
        }
        window.fromWebToElectron && window.fromWebToElectron(sendElectronData, function (electronResult) {
            console.log("electron确认收到请求：", electronResult);
        });

        //读取服务器接口地址
        try {
            _baseUrl = gProxy.getBaseUrl();
            console.log("C/S模式执行");
        } catch (e) {
            console.log("B/S模式执行");
            bsProxy = new gBsProxy(document.body);
            _baseUrl = bsProxy.getBaseUrl();
            console.log("_baseUrl:" + _baseUrl);
            
            //获取一机多屏的配置
            // 获取屏幕信息来动态计算位置
            var screenWidth = screen.width;
            var screenHeight = screen.height;
            var availWidth = screen.availWidth;
            console.log('屏幕信息 - 宽度:', screenWidth, '高度:', screenHeight, '可用宽度:', availWidth);
            querySysConf('config_zxt_yjdp',
                function (data) {
                    try {
                        // 解析返回的配置数据
                        var config = JSON.parse(data);
                        
                        // 保存到localStorage
                        localStorage.setItem('subScreenConfig', JSON.stringify(config));
                                               
                        // 遍历副屏配置并打开
                        if (config && config.subScreens) {
                            config.subScreens.forEach(function(screen) {
                                // 打开副屏
                                var screenUrl = 'second.html?screenId=' + screen.id + '&screenName=' + screen.name;
                                console.log('screenUrl:', screenUrl);
                                window.open(screenUrl, 'subScreen' + screen.id, 'width=1920,height=1080,left=' + screenWidth + ',top=0,resizable=yes,scrollbars=yes');
                            });
                        }
                    } catch (e) {
                        console.error('解析一机多屏配置失败:', e);
                        $.messager.alert('异常', '解析一机多屏配置失败：' + e.message);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取一机多屏配置失败，异常信息：' + errMsg);
                });

            //获取主屏配置
            querySysConf('config_zxt_zppz ',
                function (data) {
                    try {
                        // 解析返回的配置数据
                        var config = JSON.parse(data);
                        
                        // 保存到localStorage
                        localStorage.setItem('mainScreenConfig', JSON.stringify(config));
                        
                        // 应用主屏配置
                        if (config) {
                            applyMainScreenConfig(config);
                        }
                    } catch (e) {
                        console.error('解析一机多屏配置失败:', e);
                        $.messager.alert('异常', '解析一机多屏配置失败：' + e.message);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取一机多屏配置失败，异常信息：' + errMsg);
                });

            //获取受理页面配置
            querySysConf('config_zxt_slsj',
                function (data) {
                    try {
                        // 解析返回的配置数据
                        var config = JSON.parse(data);
                        
                        // 保存到localStorage
                        localStorage.setItem('acceptPageConfig', JSON.stringify(config));
                        
                        console.log('成功获取受理页面配置:', config);
                    } catch (e) {
                        console.error('解析受理页面配置失败:', e);
                        $.messager.alert('异常', '解析受理页面配置失败：' + e.message);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取受理页面配置失败，异常信息：' + errMsg);
                });
            
        }

        /**
         * 应用主屏配置
         * @param {Object} mainScreenConfig - 主屏配置对象
         */
        function applyMainScreenConfig(mainScreenConfig) {
            console.log('开始应用主屏配置:', mainScreenConfig);
            
            try {
                // 左侧功能配置
                if (mainScreenConfig.leftSide) {
                    var leftConfig = mainScreenConfig.leftSide;
                    
                    // 消息提醒区域
                    if (leftConfig.showMessageReminder === false) {
                        $('#messageArea').hide();
                        console.log('隐藏消息提醒区域');
                    } else {
                        $('#messageArea').show();
                        console.log('显示消息提醒区域');
                    }
                    
                    // 未接来电tab
                    if (leftConfig.showMissedCalls === false) {
                        $('#left-list').tabs('close', '未接来电');
                        console.log('隐藏未接来电tab');
                    } else {
                        // 确保tab存在且可见
                        var missedCallsTabExists = $('#left-list').tabs('exists', '未接来电');
                        if (!missedCallsTabExists) {
                            // 如果tab不存在，重新添加
                            console.log('重新添加未接来电tab');
                        }
                        console.log('显示未接来电tab');
                    }
                    
                    // 座席与分站状态tab
                    if (leftConfig.showSeatStationStatus === false) {
                        $('#left-list').tabs('close', '座席与分站状态');
                        console.log('隐藏座席与分站状态tab');
                    } else {
                        var seatStationTabExists = $('#left-list').tabs('exists', '座席与分站状态');
                        if (!seatStationTabExists) {
                            console.log('重新添加座席与分站状态tab');
                        }
                        console.log('显示座席与分站状态tab');
                    }
                }
                
                // 右侧功能配置
                if (mainScreenConfig.rightSide) {
                    var rightConfig = mainScreenConfig.rightSide;
                    
                    // 事件列表
                    if (rightConfig.showEventList === false) {
                        $('#eventList').hide();
                        // 调整车辆列表高度占满空间
                        if (rightConfig.showVehicleList !== false) {
                            $('#vehicleList').css('height', 'calc(100% - 5px)');
                            $('#dragSeparator').hide();
                        }
                        console.log('隐藏事件列表');
                    } else {
                        $('#eventList').show();
                        // 恢复原有高度
                        $('#eventList').css('height', 'calc(50% - 2px)');
                        if (rightConfig.showVehicleList !== false) {
                            $('#vehicleList').css('height', 'calc(50% - 2px)');
                            $('#dragSeparator').show();
                        }
                        console.log('显示事件列表');
                    }
                    
                    // 车辆列表
                    if (rightConfig.showVehicleList === false) {
                        $('#vehicleList').hide();
                        // 调整事件列表高度占满空间
                        if (rightConfig.showEventList !== false) {
                            $('#eventList').css('height', 'calc(100% - 5px)');
                            $('#dragSeparator').hide();
                        }
                        console.log('隐藏车辆列表');
                    } else {
                        $('#vehicleList').show();
                        // 恢复原有高度
                        $('#vehicleList').css('height', 'calc(50% - 2px)');
                        if (rightConfig.showEventList !== false) {
                            $('#eventList').css('height', 'calc(50% - 2px)');
                            $('#dragSeparator').show();
                        }
                        console.log('显示车辆列表');
                    }
                    
                    // 如果两个列表都隐藏，显示提示信息
                    if (rightConfig.showEventList === false && rightConfig.showVehicleList === false) {
                        $('#dragSeparator').hide();
                        // 可以在这里添加一个提示信息
                        console.log('事件列表和车辆列表都已隐藏');
                    }
                }
                
                // 重新调整布局
                setTimeout(function() {
                    try {
                        $('#dg-event-list').datagrid('resize');
                        $('#dg-mobiels-list').datagrid('resize');
                        $('#station-list').datagrid('resize');
                        $('#missedCalls').datagrid('resize');
                        $('#dg-seat-station-status-list').datagrid('resize');
                        $('#dl-msg-list').datalist('resize');
                        $(window).trigger('resize');
                    } catch(ex) {
                        console.warn('调整表格大小时出错:', ex);
                    }
                }, 100);
                
                // 保存配置状态到localStorage
                try {
                    var currentConfig = localStorage.getItem('subScreenConfig');
                    if (currentConfig) {
                        var config = JSON.parse(currentConfig);
                        if (!config.mainScreen) {
                            config.mainScreen = {};
                        }
                        // 可以在这里保存当前的显示状态
                        localStorage.setItem('subScreenConfig', JSON.stringify(config));
                    }
                } catch(e) {
                    console.warn('保存配置状态失败:', e);
                }
                
                console.log('主屏配置应用完成');
                
            } catch (e) {
                console.error('应用主屏配置时出错:', e);
            }
        }

        // 拖拽调节高度功能（事件列表和车辆列表之间）
        $(document).ready(function() {
            var isDragging = false;
            var startY = 0;
            var startEventPercent = 0;
            var startVehiclePercent = 0;
            var containerHeight = 0;
            
            $('#dragSeparator').mousedown(function(e) {
                isDragging = true;
                startY = e.clientY;
                
                // 获取容器总高度
                var tabContentElement = $(this).parent(); // 获取事件监控tab的内容区域
                containerHeight = tabContentElement.height();
                
                // 获取当前高度百分比（从CSS样式中提取）
                var eventListElement = $('#eventList');
                var vehicleListElement = $('#vehicleList');
                
                // 计算当前百分比
                var eventHeight = eventListElement.height();
                var vehicleHeight = vehicleListElement.height(); 
                var separatorHeight = 3; // 分隔条高度
                var totalUsedHeight = eventHeight + vehicleHeight + separatorHeight;
                
                startEventPercent = (eventHeight / totalUsedHeight) * 100;
                startVehiclePercent = (vehicleHeight / totalUsedHeight) * 100;
                
                // 防止文本选择
                $('body').addClass('no-select');
                e.preventDefault();
            });
            
            $(document).mousemove(function(e) {
                if (!isDragging) return;
                
                var deltaY = e.clientY - startY;
                var deltaPercent = (deltaY / containerHeight) * 100;
                
                var newEventPercent = startEventPercent + deltaPercent;
                var newVehiclePercent = startVehiclePercent - deltaPercent;
                
                // 设置最小和最大百分比限制
                var minPercent = 15; // 最小15%
                var maxPercent = 85; // 最大85%
                
                if (newEventPercent >= minPercent && newEventPercent <= maxPercent && 
                    newVehiclePercent >= minPercent && newVehiclePercent <= maxPercent) {
                    
                    // 确保两个百分比加起来等于100%（减去分隔条的固定高度）
                    var totalPercent = newEventPercent + newVehiclePercent;
                    if (Math.abs(totalPercent - 100) > 1) {
                        // 如果总和不是100%，按比例调整
                        newEventPercent = (newEventPercent / totalPercent) * 100;
                        newVehiclePercent = (newVehiclePercent / totalPercent) * 100;
                    }
                    
                    // 应用新的百分比高度，使用calc()来减去分隔条高度
                    $('#eventList').css('height', 'calc(' + newEventPercent + '% - 2px)');
                    $('#vehicleList').css('height', 'calc(' + newVehiclePercent + '% - 2px)');
                    
                    // 刷新数据表格以适应新高度
                    try {
                        setTimeout(function() {
                            // 强制调整大小
                            $('#dg-event-list').datagrid('resize');
                            $('#dg-mobiels-list').datagrid('resize');
                            // 触发窗口resize事件以确保所有组件调整
                            $(window).trigger('resize');
                        }, 10);
                    } catch(ex) {
                        // 忽略调整大小错误
                    }
                }
            });
            
            $(document).mouseup(function(e) {
                if (isDragging) {
                    isDragging = false;
                    $('body').removeClass('no-select');
                }
            });
            
            // 垂直拖拽调节左右宽度功能
            var isVerticalDragging = false;
            var startX = 0;
            var startLeftPercent = 0;
            var startRightPercent = 0;
            var containerWidth = 0;
            
            $('#verticalDragSeparator').mousedown(function(e) {
                isVerticalDragging = true;
                startX = e.clientX;
                
                // 获取容器总宽度（主要内容区域）
                var flexContainer = $('#leftPanel').parent();
                containerWidth = flexContainer.width();
                
                // 获取当前宽度百分比
                var leftPanelElement = $('#leftPanel');
                var rightPanelElement = $('#rightPanel');
                
                // 计算当前百分比
                var leftWidth = leftPanelElement.width();
                var rightWidth = rightPanelElement.width();
                var separatorWidth = 3; // 分隔条宽度
                var totalUsedWidth = leftWidth + rightWidth + separatorWidth;
                
                startLeftPercent = (leftWidth / containerWidth) * 100;
                startRightPercent = (rightWidth / containerWidth) * 100;
                
                // 防止文本选择
                $('body').addClass('no-select');
                e.preventDefault();
            });
            
            $(document).mousemove(function(e) {
                if (!isVerticalDragging) return;
                
                var deltaX = e.clientX - startX;
                var deltaPercent = (deltaX / containerWidth) * 100;
                
                var newLeftPercent = startLeftPercent + deltaPercent;
                var newRightPercent = startRightPercent - deltaPercent;
                
                // 设置最小和最大百分比限制
                var minPercent = 15; // 最小15%
                var maxPercent = 80; // 最大80%
                
                if (newLeftPercent >= minPercent && newLeftPercent <= maxPercent && 
                    newRightPercent >= minPercent && newRightPercent <= maxPercent) {
                    
                    // 应用新的百分比宽度
                    $('#leftPanel').css('width', newLeftPercent + '%');
                    // 右侧使用flex:1自动填充，不需要设置固定宽度
                    
                    // 刷新所有数据表格以适应新宽度
                    try {
                        setTimeout(function() {
                            // 强制调整所有表格大小
                            $('#dg-event-list').datagrid('resize');
                            $('#dg-mobiels-list').datagrid('resize');
                            $('#station-list').datagrid('resize');
                            $('#missedCalls').datagrid('resize');
                            $('#dg-seat-station-status-list').datagrid('resize');
                            $('#dl-msg-list').datalist('resize');
                            // 触发窗口resize事件
                            $(window).trigger('resize');
                        }, 10);
                    } catch(ex) {
                        // 忽略调整大小错误
                    }
                }
            });
            
            $(document).mouseup(function(e) {
                if (isVerticalDragging) {
                    isVerticalDragging = false;
                    $('body').removeClass('no-select');
                }
            });
            
            // 添加CSS样式防止拖拽时文本选择
            $('<style>')
                .prop('type', 'text/css')
                .html('.no-select { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }')
                .appendTo('head');
        });
    </script>
</body>
</html>