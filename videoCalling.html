<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>   
    <title>视频呼救通话</title>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />	
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    

    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f0f2f5;
        }

        .container {
            display: flex;
            height: 100vh;
            background: #fff;
        }

        /* 左侧聊天区域 */
        .chat-section {
            width: 30%;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }

        /* 右侧视频区域 */
        .video-section {
            width: 70%;
            background: #000;
        }

        /* 聊天头部 */
        .chat-header {
            padding: 15px;
            background: #fff;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header h2 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }

        .phone-container {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .phone-text {
            font-size: 14px;
            color: #666;
            font-weight: normal;
        }

        /* 聊天消息区域 */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #f0f2f5;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .message.received {
            align-items: flex-start;
        }

        .message.sent {
            align-items: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            font-size: 14px;
            margin: 5px 0;
        }

        .message.received .message-content {
            background: #fff;
            color: #333;
        }

        .message.sent .message-content {
            background: #1890ff;
            color: #fff;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin: 2px 5px;
        }

        .message-sender {
            font-size: 12px;
            color: #666;
            margin: 2px 5px;
        }

        /* 聊天输入区域 */
        .chat-input {
            padding: 15px;
            background: #fff;
            border-top: 1px solid #ddd;
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .message-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: none;
            height: 100px;
        }

        .send-button-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .send-button {
            padding: 8px 30px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .send-button:hover {
            background: #40a9ff;
        }

        /* 视频区域iframe */
        .video-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }

        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
                color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff; /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9; /* 未选中时的边框颜色 */
        }
        input[type="checkbox"]:checked::before {
            content: "\2713"; /* 对号的转义符 √ */
            background-color: #fff; /* 选中时的背景颜色 */
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc; /* 选中后的边框颜色 */
            color: #000; /* 选中后的文字颜色   */
            font-size: 20px;
            font-weight: bold;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important; 
        }
        #wavPlayWindow{
            overflow: initial;
        }
        .select-common{
            height: 32px;
            width: 240px;
            margin-left: 0;
            font-size: 1.4em;
        }
        /* 消息状态样式 */
        .message-status {
            font-size: 12px;
            margin-top: 2px;
            color: #999;
        }

        .message.sent .message-status {
            text-align: right;
            margin-right: 5px;
        }

        .message.received .message-status {
            display: none; /* 接收的消息不显示已读状态 */
        }

        .message-status.unread {
            color: #999;
        }

        .message-status.read {
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧聊天区域 -->
        <div class="chat-section">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <h2>视频呼救聊天</h2>
                <div class="phone-container">
                    <span class="phone-text" id="phoneText">呼救者手机号</span>
                </div>
            </div>
            
            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <!-- 消息将通过JavaScript动态添加 -->
            </div>
            
            <!-- 聊天输入区域 -->
            <div class="chat-input">
                <div class="input-container">
                    <textarea class="message-input" id="messageInput" placeholder="请输入消息..."></textarea>
                    <div class="send-button-container">
                        <button class="send-button" onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧视频区域 -->
        <div class="video-section">
            <iframe class="video-iframe" id="videoFrame" src="" frameborder="0" 
            allow=" microphone; camera;"></iframe>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        //获取当前登录用户信息
        var _userInfo = "";
        //获取当前座席信息
        var _seatInfo = "";
        //获取视频呼救详情
        var _currentInCallDetails = "";

        //视频呼救页面地址
        var _itcVideoSrc = "https://**************:9443/webVideo/index.do";

        // 轮询相关变量
        var _pollingInterval = null; // 轮询定时器
        var _lastMsgTime = ""; // 最后一条消息时间
        var _pollingIntervalTime = 3000; // 轮询间隔（3秒）

        // 快捷回复模板
        var QUICK_REPLIES = null;

        // 获取快捷回复模板数据
        function loadQuickReplyTemplates() {
            try {
                querySysConf('miniApp_chat_quick_re',
                    function(data) {                       
                        // 成功获取数据后的处理
                        if(data) {
                            try {
                                // 判断数据格式并处理
                                if (Array.isArray(data)) {
                                    // 直接是数组格式
                                    QUICK_REPLIES = data;
                                } else if (typeof data === 'string') {
                                    // 字符串格式，尝试解析JSON
                                    try {
                                        // 先尝试直接解析
                                        QUICK_REPLIES = JSON.parse(data);
                                    } catch (directParseError) {
                                        // 如果直接解析失败，尝试清理格式
                                        const cleanedData = data.trim();
                                        QUICK_REPLIES = JSON.parse(cleanedData);
                                    }
                                } else if (data.val) {
                                    // 包含val属性的对象格式
                                    if (typeof data.val === 'string') {
                                        QUICK_REPLIES = JSON.parse(data.val);
                                    } else if (Array.isArray(data.val)) {
                                        QUICK_REPLIES = data.val;
                                    } else {
                                        console.warn('快捷回复模板数据格式不正确，data.val类型:', typeof data.val);
                                        return;
                                    }
                                } else if (typeof data === 'object' && data !== null) {
                                    // 其他对象格式，尝试获取第一个属性的值
                                    const keys = Object.keys(data);
                                    if (keys.length > 0) {
                                        const firstValue = data[keys[0]];
                                        if (Array.isArray(firstValue)) {
                                            QUICK_REPLIES = firstValue;
                                        } else if (typeof firstValue === 'string') {
                                            QUICK_REPLIES = JSON.parse(firstValue);
                                        } else {
                                            console.warn('快捷回复模板数据格式不正确，第一个属性值类型:', typeof firstValue);
                                            return;
                                        }
                                    } else {
                                        console.warn('快捷回复模板数据为空对象');
                                        return;
                                    }
                                } else {
                                    console.warn('快捷回复模板数据格式不正确，数据类型:', typeof data);
                                    return;
                                }
                                
                                console.log('快捷回复模板加载成功:', QUICK_REPLIES);
                                
                                // 重新渲染快捷回复按钮
                                updateQuickReplyButtons();
                            } catch (parseError) {
                                console.error('解析快捷回复模板数据失败:', parseError);
                                console.log('使用默认快捷回复模板');
                            }
                        } else {
                            console.log('未获取到快捷回复模板数据，使用默认模板');
                        }
                    },
                    function(e, url, errMsg) {
                        // 错误处理
                        console.error('获取快捷回复模板失败:', errMsg);
                        console.log('使用默认快捷回复模板');
                    }
                );
            } catch (error) {
                console.error('调用快捷回复模板API异常:', error);
                console.log('使用默认快捷回复模板');
            }
        }

        
        // 页面加载完成后初始化
        window.onload = function() {
            //获取当前登录用户信息
            try {
                _userInfo = evalJson(window.parent.gProxy.getUserInfo());
                
            } catch (e) {
                _userInfo = evalJson(window.parent.bsProxy.getUserInfo());
            }
            //获取当前座席信息
            try {
                _seatInfo = evalJson(window.parent.gProxy.getSeatInfo());
            } catch (e) {
                _seatInfo = evalJson(window.parent.bsProxy.getSeatInfo());
            }
                    //获取url参数信息
            var urlParams = queryURLParams(window.location.search);
            console.log("callVideoId:" + urlParams.callVideoId);
            //获取视频呼救详情，并打开加载视频页面
            initVideoCalling(urlParams.callVideoId);

        };

        // 获取视频呼救详情通过ID
        function initVideoCalling(callVideoId){
            getVideoCallById({id: callVideoId}, 
                function(data) {
                    // 成功获取数据后的处理 https://**************:9443/webVideo/index.do?mt_id=a16fd158-8f2e-40df-85a3-c57c729a6709&l=zh_CN&name=555
                    if(data) {
                        _currentInCallDetails = data;
                        console.log('获取视频呼救详情成功:', data);
                        console.log("打开视频呼救对话页面:" + callVideoId);
                        console.log("用户信息:" , _userInfo);
                        console.log("座席信息:" , _seatInfo);
                        // 创建视频iframe
                        var itcVideoSrc = _itcVideoSrc + '?mt_id=' + data.meetRoomId + '&l=zh_CN&name='+_userInfo.DisplayName;
                        // 将iframe添加到视频容器中
                        document.getElementById('videoFrame').src = itcVideoSrc;

                        //加载历史消息
                        loadHistoryMessages(_currentInCallDetails.chatId, _seatInfo.id);
                        
                        // 加载快捷回复模板数据（数据加载完成后会自动添加按钮）
                        loadQuickReplyTemplates();
                        
                        // 启动消息轮询
                        startMessagePolling(_currentInCallDetails.chatId, _seatInfo.id);
                        
                        // 显示呼救者手机号码
                        displayCallerPhone(data.phoneNumber || data.phone || data.callerNumber);
                    }
                },
                function(e, url, errMsg) {
                    // 错误处理
                    console.error('获取视频呼救失败:', errMsg);
                }
            );
        }

        // 添加消息到聊天区域
        function appendMessage(message) {
            // 只处理文本消息
            if (message.type !== '1') {
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.senderId === _seatInfo.id ? 'sent' : 'received'}`;//接受者还是发送者
            messageDiv.setAttribute('data-message-id', message.id);
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = message.content;//消息内容
            
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = formatTimeForDisplay(message.time);//消息发送时间
            
            const messageSender = document.createElement('div');
            messageSender.className = 'message-sender';
            messageSender.textContent = message.sender;//发送者名称

            // 如果消息被撤回
            if (message.recallTime) {
                messageContent.className = 'message-content recalled';
                messageContent.textContent = '此消息已被撤回';
            }
            
            messageDiv.appendChild(messageSender);
            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageTime);
            
            document.getElementById('chatMessages').appendChild(messageDiv);
            scrollToBottom();
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            
            if (content) {
                // 准备发送消息的参数
                const params = {
                    sessionId: _currentInCallDetails.chatId,  // 从视频呼救详情中获取的chatId
                    senderId: _seatInfo.id,  // 当前座席ID
                    content: content  // 消息内容
                };

                try {
                    sendMessageToApi(params, 
                        function(data) {
                            if(data) {
                                // 发送成功，添加消息到聊天界面
                                appendMessage({
                                    id: data.id,
                                    type: data.msgType,//会话类型（1-单聊，2-群聊）
                                    content: data.content,//消息内容
                                    senderId: data.senderId,//消息发送者ID
                                    time: data.sendTime,//消息发送时间
                                    sender:  data.senderId === _seatInfo.id ? _seatInfo.seatId + "("+_seatInfo.user+")" :'呼救人',//消息发送者昵称
                                    role: data.senderId === _seatInfo.id ? 'seat' : 'caller',//消息发送者角色（seat-座席，caller-呼救者）
                                    status: data.msgStatus === 1 ? 'read' : 'unread',//消息状态（0-未读，1-已读）
                                    attachment: data.attachment,//消息附件
                                    recallTime: data.recallTime,//消息撤回时间
                                    recallUserId: data.recallUserId//消息撤回者ID  
                                    
                                });
                                
                                // 更新最后消息时间
                                _lastMsgTime = formatTimeToYYYYMMddHHmmss(data.sendTime);
                                
                                // 清空输入框
                                input.value = '';
                                
                                // 滚动到底部
                                scrollToBottom();
                            }
                        },
                        function(e, url, errMsg) {
                            // 发送失败处理
                            $.messager.alert('错误', '发送消息失败：' + errMsg);
                        }
                    );
                } catch (error) {
                    console.error('发送消息失败:', error);
                    $.messager.alert('错误', '发送消息失败，请重试');
                }
            }
        }


     

        // 滚动到底部
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 加载历史消息
        function loadHistoryMessages(sessionId, userId) {
            try {
                getNewSessionMessagesByUserId({sessionId: sessionId, userId: userId},
                function (data) {
                    if(data && data.length > 0) {
                        data.forEach(msg => {
                            appendMessage({
                                id: msg.id,
                                type: msg.msgType,//会话类型（1-单聊，2-群聊）
                                content: msg.content,//消息内容
                                senderId: msg.senderId,//消息发送者ID
                                time: msg.sendTime,//消息发送时间
                                sender:  msg.senderId === userId ?  _seatInfo.seatId + "("+_seatInfo.user+")" :'呼救人',//消息发送者昵称
                                role: msg.senderId === userId ? 'seat' : 'caller',//消息发送者角色（seat-座席，caller-呼救者）
                                status: msg.msgStatus === 1 ? 'read' : 'unread',//消息状态（0-未读，1-已读）
                                attachment: msg.attachment,//消息附件
                                recallTime: msg.recallTime,//消息撤回时间
                                recallUserId: msg.recallUserId//消息撤回者ID    
                            });
                        });
                        
                        // 更新最后消息时间为最后一条消息的时间
                        if(data.length > 0) {
                            const lastMsg = data[data.length - 1];
                            _lastMsgTime = formatTimeToYYYYMMddHHmmss(lastMsg.sendTime);
                        }
                    }
                }, function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('异常', '异常信息：' + errMsg);
                });
            } catch (error) {
                console.error('加载历史消息失败:', error);
            }
        }

        // 启动消息轮询
        function startMessagePolling(sessionId, userId) {
            // 清除之前的轮询
            if (_pollingInterval) {
                clearInterval(_pollingInterval);
            }
            
            // 开始轮询
            _pollingInterval = setInterval(function() {
                pollNewMessages(sessionId, userId);
            }, _pollingIntervalTime);
            
            console.log('消息轮询已启动，间隔：' + _pollingIntervalTime + 'ms');
        }

        // 停止消息轮询
        function stopMessagePolling() {
            if (_pollingInterval) {
                clearInterval(_pollingInterval);
                _pollingInterval = null;
                console.log('消息轮询已停止');
            }
        }

        // 轮询获取新消息
        function pollNewMessages(sessionId, userId) {
            try {
                var params = {
                    sessionId: sessionId,
                    userId: userId
                };
                
                // 如果有最后消息时间，则传入
                if (_lastMsgTime) {
                    params.lastMsgTime = _lastMsgTime;
                }
                
                getNewSessionMessagesByUserId(params,
                function (data) {
                    if(data && data.length > 0) {
                        console.log('轮询获取到新消息：', data.length + '条');
                        
                        data.forEach(msg => {
                            appendMessage({
                                id: msg.id,
                                type: msg.msgType,//会话类型（1-单聊，2-群聊）
                                content: msg.content,//消息内容
                                senderId: msg.senderId,//消息发送者ID
                                time: msg.sendTime,//消息发送时间
                                sender:  msg.senderId === userId ?  _seatInfo.seatId + "("+_seatInfo.user+")" :'呼救人',//消息发送者昵称
                                role: msg.senderId === userId ? 'seat' : 'caller',//消息发送者角色（seat-座席，caller-呼救者）
                                status: msg.msgStatus === 1 ? 'read' : 'unread',//消息状态（0-未读，1-已读）
                                attachment: msg.attachment,//消息附件
                                recallTime: msg.recallTime,//消息撤回时间
                                recallUserId: msg.recallUserId//消息撤回者ID    
                            });
                        });
                        
                        // 更新最后消息时间
                        const lastMsg = data[data.length - 1];
                        _lastMsgTime = formatTimeToYYYYMMddHHmmss(lastMsg.sendTime);
                    }
                }, function (e, url, errMsg) {
                    // 轮询失败不显示错误提示，避免频繁弹窗
                    console.error('轮询获取新消息失败:', errMsg);
                });
            } catch (error) {
                console.error('轮询获取新消息异常:', error);
            }
        }

        // 时间格式转换函数：将时间字符串转换为YYYYMMddHHmmss格式
        function formatTimeToYYYYMMddHHmmss(timeStr) {
            try {
                var date = new Date(timeStr);
                if (isNaN(date.getTime())) {
                    // 如果无法解析，尝试其他格式
                    return timeStr.replace(/[^\d]/g, '').substring(0, 14);
                }
                
                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var day = String(date.getDate()).padStart(2, '0');
                var hour = String(date.getHours()).padStart(2, '0');
                var minute = String(date.getMinutes()).padStart(2, '0');
                var second = String(date.getSeconds()).padStart(2, '0');
                
                return year + month + day + hour + minute + second;
            } catch (error) {
                console.error('时间格式转换失败:', error);
                return '';
            }
        }

        // 时间格式化函数：将YYYYMMddHHmmss格式转换为友好显示格式
        function formatTimeForDisplay(timeStr) {
            try {
                if (!timeStr) return '';
                
                // 如果是YYYYMMddHHmmss格式（14位数字）
                if (typeof timeStr === 'string' && /^\d{14}$/.test(timeStr)) {
                    var year = timeStr.substring(0, 4);
                    var month = timeStr.substring(4, 6);
                    var day = timeStr.substring(6, 8);
                    var hour = timeStr.substring(8, 10);
                    var minute = timeStr.substring(10, 12);
                    var second = timeStr.substring(12, 14);
                    
                    var date = new Date(year, month - 1, day, hour, minute, second);
                    var now = new Date();
                    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    var messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                    
                    // 判断是否是今天
                    if (messageDate.getTime() === today.getTime()) {
                        // 今天的消息只显示时分
                        return hour + ':' + minute;
                    } else {
                        // 不是今天的消息显示月日 时分
                        return parseInt(month) + '月' + parseInt(day) + '日 ' + hour + ':' + minute;
                    }
                } else {
                    // 尝试解析其他格式
                    var date = new Date(timeStr);
                    if (isNaN(date.getTime())) {
                        return timeStr; // 无法解析就返回原值
                    }
                    
                    var now = new Date();
                    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    var messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                    
                    // 判断是否是今天
                    if (messageDate.getTime() === today.getTime()) {
                        // 今天的消息只显示时分
                        return String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0');
                    } else {
                        // 不是今天的消息显示月日 时分
                        return (date.getMonth() + 1) + '月' + date.getDate() + '日 ' + 
                               String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0');
                    }
                }
            } catch (error) {
                console.error('时间格式化失败:', error);
                return timeStr; // 出错时返回原值
            }
        }

        

        // 添加快捷回复按钮
        function addQuickReplyButtons() {
            // 检查是否有快捷回复数据
            if (!QUICK_REPLIES || !Array.isArray(QUICK_REPLIES) || QUICK_REPLIES.length === 0) {
                console.log('快捷回复模板数据为空，跳过添加按钮');
                return;
            }

            const quickReplyContainer = document.createElement('div');
            quickReplyContainer.className = 'quick-reply-container';
            quickReplyContainer.id = 'quickReplyContainer';
            quickReplyContainer.style.padding = '10px';
            quickReplyContainer.style.borderTop = '1px solid #ddd';

            QUICK_REPLIES.forEach(reply => {
                const button = document.createElement('button');
                button.className = 'quick-reply-btn';
                button.textContent = reply;
                button.style.margin = '5px';
                button.style.padding = '5px 10px';
                button.style.border = '1px solid #ddd';
                button.style.borderRadius = '15px';
                button.style.background = '#f5f5f5';
                button.style.cursor = 'pointer';
                
                button.onclick = () => {
                    document.getElementById('messageInput').value = reply;
                };
                
                quickReplyContainer.appendChild(button);
            });

            document.querySelector('.chat-input').insertBefore(
                quickReplyContainer,
                document.querySelector('.input-container')
            );
        }

        // 更新快捷回复按钮
        function updateQuickReplyButtons() {
            // 移除现有的快捷回复容器
            const existingContainer = document.getElementById('quickReplyContainer');
            if (existingContainer) {
                existingContainer.remove();
            }
            
            // 重新添加快捷回复按钮
            addQuickReplyButtons();
        }

        // 监听回车键发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 显示呼救者手机号码
        function displayCallerPhone(phone) {
            const phoneText = document.getElementById('phoneText');
            if (phone && phone.trim() !== '') {
                phoneText.textContent = '呼救者电话：' + phone;
            } else {
                phoneText.textContent = '呼救者电话：未知';
            }
        }

                   
        // 标记消息为已读
        function markMessageAsRead(messageId) {
            const message = messages.find(m => m.id === messageId);
            if (message) {
                message.status = 'read';
                updateMessageStatus(messageId, 'read');
            }
        }

        // 更新消息状态显示
        function updateMessageStatus(messageId, status) {
            const messageDiv = document.querySelector(`.message[data-message-id="${messageId}"]`);
            if (messageDiv) {
                const statusDiv = messageDiv.querySelector('.message-status');
                if (statusDiv) {
                    statusDiv.className = `message-status ${status}`;
                    statusDiv.textContent = status === 'read' ? '已读' : '未读';
                }
            }
        }
        // 模拟呼救者回复
        function simulateCallerReply() {
            setTimeout(async () => {
                const replies = [
                    '在上海市浦东新区张杨路100号',
                    '老人还是昏迷状态',
                    '好的，我们在这里等待',
                    '老人有高血压病史'
                ];
                
                const reply = {
                    id: messages.length + 1,
                    type: 'received',
                    content: replies[Math.floor(Math.random() * replies.length)],
                    time: new Date().toLocaleTimeString(),
                    sender: CALLER.name,
                    role: 'caller',
                    status: 'unread'
                };
                
                const response = await API.sendMessage(reply);
                if (response.code === 200) {
                    messages.push(reply);
                    appendMessage(reply);
                }
            }, 1000);
        }

        /**
         * 返回参数对象
         * @param urlParams
         */
         function queryURLParams(urlParams) {
            // const url = location.search; // 项目中可直接通过search方法获取url中"?"符后的字串
            let url = urlParams.split("?")[1];
            let obj = {}; // 声明参数对象
            let arr = url.split("&"); // 以&符号分割为数组
            for (let i = 0; i < arr.length; i++) {
                let arrNew = arr[i].split("="); // 以"="分割为数组
                obj[arrNew[0]] = arrNew[1];
            }
            return obj;
        }

        // 页面关闭或离开时停止轮询
        window.addEventListener('beforeunload', function() {
            stopMessagePolling();
        });

        // 页面隐藏时停止轮询，显示时重新开始轮询
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopMessagePolling();
            } else if (_currentInCallDetails && _seatInfo) {
                startMessagePolling(_currentInCallDetails.chatId, _seatInfo.id);
            }
        });
    </script>
</body>
</html>