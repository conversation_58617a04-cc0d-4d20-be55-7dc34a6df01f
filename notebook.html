<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件 记事本
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 王晨波
* Created: 2025/5/19
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>

    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/ueditor.all.js"></script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="script/ueditor1_4_3_3/lang/zh-cn/zh-cn.js"></script>
    <style type="text/css">
        /* 全屏布局样式 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 防止出现滚动条 */
        }
        
        div {
            width: 100%;
        }
        
        /* 主容器样式 */
        #mainPanel_CallList {
            height: 100vh; /* 视窗高度 */
            width: 100vw;  /* 视窗宽度 */
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 组容器样式 */
        .group-box {
            height: calc(100vh - 10px) !important;
            width: calc(100vw - 10px) !important;
            margin: 5px !important;
            padding: 5px !important;
            box-sizing: border-box;
        }
        
        /* 布局容器样式 */
        .easyui-layout {
            width: 100% !important;
            height: calc(100vh - 140px) !important; /* 减去标题和搜索栏高度 */
        }
        
        .pagination-info {
            width: 60%;
            text-align: right;
        }
        
        /* 确保数据表格适应高度 */
        #notebookList {
            height: calc(100% - 60px) !important;
        }
        
        /* 编辑器容器样式 */
        #content {
            overflow: hidden; /* 防止编辑器超出容器 */
        }
        
        /* 编辑器脚本标签容器 */
        #editor {
            max-height: calc(100vh - 300px); /* 设置最大高度，防止超出 */
        }
        
        /* UEditor编辑器容器 */
        .edui-default {
            max-height: calc(100vh - 335px) !important;
            /*overflow: auto;*/
            overflow: visible; /* 关键：允许下拉菜单弹出 */
        }
        #edui1_iframeholder {
            padding-bottom: 40px;
        }
        
        /* 快速时间查询链接样式 */
        .inAnHour, .inToday {
            color: #099DFC;
            cursor: pointer;
            text-decoration: underline;
        }
        
        .inAnHour:hover, .inToday:hover {
            color: #0066CC;
        }
    </style>
    <title>120急救指挥中心调度平台第二屏幕</title>
</head>

<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box"
             style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;padding:5px;">
            <div class="group-box-title">
                记事本<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span>
            </div>
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;padding: 5px;">
                    创建时间： <input id="createTimeStart" class="easyui-datetimebox" style="width:200px; " />
                    <span>至</span>
                    <input id="createTimeEnd" class="easyui-datetimebox" style="width:200px;" />
                    &nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>
                    关键词：<input id="keyword" class="easyui-textbox" style="width:200px;" />
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;"
                       href="javascript:void(0)" onclick="getNotebookList()">
                        <i class="fa fa-search"></i>&nbsp;查询
                    </a>
                </div>
                <div class="easyui-layout" style="padding-top:10px;">
                    <div region="west" split="true" title="列表" style="width:50%;height:100%;padding:5px;">                       
                            <table id="notebookList" class="easyui-datagrid" style="width:100%;height: Calc(100% - 100px)"
                                   singleSelect="true" toolbar="#toolbar" pagination="true" rownumbers="true" fitColumns="true" fit="true"
                                   pageSize="20" autoRowHeight="true">
                                <thead>
                                    <tr>
                                        <th field="title" width="25%" formatter="formatterTitle">标题</th>
                                        <th field="content" width="35%" formatter="formatterContent">内容</th>
                                        <th field="createUser" width="10%">所属人</th>
                                        <th field="createTime" width="15%">创建时间</th>
                                        <th field="lastUpdateTime" width="15%">最后修改时间</th>
                                    </tr>
                                </thead>
                            </table>
                            <div id="toolbar">
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                                   onclick="addNotebook()">新增</a>
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
                                   onclick="editNotebook()">修改</a>
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
                                   onclick="destroyNotebook()">删除</a>
                            </div>
                    </div>
                    <div id="content" region="center" title="新增" style="padding:5px;">
                        <div class="layui-form">
                            <div class="datagrid-toolbar" style="text-align:right;">
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" plain="true"
                                   onclick="javascript:saveEditNotebook()">保存</a>
                             
                            </div>
                            <div class="layui-row"
                                 style="width:100%;height:30px;vertical-align: middle;text-align:center;padding:5px;margin:0;">
                                <div class="layui-col-md1" style="padding-top:10px;">
                                    <div class="grid-demo grid-demo-bg1">标题：</div>
                                </div>
                                <div class="layui-col-md11">
                                    <div class="grid-demo">
                                        <input id="title" type="text" name="title"
                                               placeholder="请输入标题" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row"
                                 style="width:100%;height:30px;vertical-align: middle;text-align:center;padding:5px;margin:0;">
                                <div class="layui-col-md1" style="padding-top:10px;">
                                    <div class="grid-demo grid-demo-bg1">内容：</div>
                                </div>
                                <div class="layui-col-md11" style="padding-top:10px;">
                                    <div class="grid-demo">
                                        <script id="editor" type="text/plain" style="width:100%;height:600px;">
                                        </script>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <!-- 移除不必要的secondScr_gaode.js引用，避免重复执行 -->
    <script type="text/javascript">
        var page = 1;
        var size = 20;
        var notebookList = [];
        var currentEditingId = null; // 当前正在编辑的记事本ID
        if (!gProxyW) {
            gProxyW = ProxyObj(window);
        }
        if (!bsProxyW) {
            bsProxyW = BsProxyObj(window);
        }   
       
             
        

        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        // 实例化编辑器 - 添加配置禁用上传功能
        var ue = UE.getEditor('editor', {
            // 禁用所有上传相关功能
            serverUrl: "",
            imageActionName: "",
            scrawlActionName: "",
            videoActionName: "",
            fileActionName: "",
            catcherActionName: "",
            snapscreenActionName: "",
            imageManagerActionName: "",
            fileManagerActionName: "",
            // 禁用自动保存
            enableAutoSave: false,
            saveInterval: 0,
            // 其他配置
            elementPathEnabled: false,
            wordCount: false
        });
        
        // 添加错误处理，避免控制台报错
        ue.ready(function() {
            console.log('UEditor 编辑器初始化完成，已禁用所有上传功能');
        });
        
        // 捕获可能的服务器请求错误
        ue.addListener('error', function(type, message) {
            if (message && message.indexOf('controller.jsp') > -1) {
                console.log('已忽略上传功能相关错误:', message);
                return false; // 阻止错误显示
            }
        });

        //保存编辑
        function saveEditNotebook() {
            let id = null;
            let isEditMode = $('#content').panel('options').title == '修改';
            
            if(isEditMode){
                // 如果是修改模式，优先使用全局变量中的ID
                if(currentEditingId){
                    id = currentEditingId;
                } else {
                    // 如果全局变量没有ID，尝试从选中的行获取ID
                var row = $('#notebookList').datagrid('getSelected');
                    if(row){
                        id = row.id;
                        currentEditingId = id; // 更新全局变量
                    }
                }
                // 注意：不再校验是否选中数据，因为用户在编辑过程中可能会查询导致选中状态丢失
            }
            
            var editor = UE.getEditor('editor');
            // 获取编辑器内容和标题
            var content = editor.getContent();
            var title = $('#title').val();
            
            // 判断标题是否为空
            if (!title) {
                return $.messager.alert('提示', '记事本标题不能为空。', 'error');
            }
            if (!content) {
                return $.messager.alert('提示', '记事本内容不能为空。', 'error');
            }

            // 构建请求参数
            let params = {
                "content": content,
                "title": title,
                "id": id
            };
            $('#notebookList').datagrid('loading');
            if(!id){
                // 新增记事本
                addNotebookApi(params,
                    function (res) {
                        $.messager.alert('提示', '保存成功！', 'success');
                        // 保存成功后只刷新左边列表，不清空右边编辑页面
                        // 将页面状态改为修改状态，这样用户可以继续编辑
                        $('#content').panel('setTitle', '修改');
                        
                        // 刷新列表
                        getNotebookList();
                        
                        // 如果返回了新创建记录的ID，尝试选中它
                        if (res && (res.id || res.data)) {
                            var newId = res.id || res.data || res;
                            setTimeout(function() {
                                selectNotebookById(newId);
                            }, 200);
                        }
                    },
                    function (e, url, errMsg) {
                        //失败做处理
                        $.messager.alert('提示', errMsg, 'error');
                        $('#notebookList').datagrid('loaded');
                    }
                );
            }else{
                // 更新记事本
                updateNotebook(params,
                    function (res) {
                        $.messager.alert('提示', '保存成功！', 'success');
                        // 保存成功后只刷新左边列表，保持右边编辑页面内容
                        getNotebookList();
                        // 保持当前选中状态
                        setTimeout(function() {
                            selectNotebookById(id);
                        }, 100);
                    },
                    function (e, url, errMsg) {
                        //失败做处理
                        $.messager.alert('提示', errMsg, 'error');
                        $('#notebookList').datagrid('loaded');
                    }
                );
            }
        }
        function addNotebook() {
            var editor = UE.getEditor('editor');
            $('#content').panel('setTitle', '新增');
            $('#title').val('');
            editor.setContent("");
            editor.reset();
            
            // 清除当前编辑ID，表示进入新增模式
            currentEditingId = null;
            
            // 清除左侧列表的选中状态，明确表示当前是新增模式
            $('#notebookList').datagrid('clearSelections');
            
            // 让标题输入框获得焦点
            setTimeout(function() {
                $('#title').focus();
            }, 100);
        }
        
        // 根据ID选中列表中的记录
        function selectNotebookById(id) {
            if (!id) return;
            
            var rows = $('#notebookList').datagrid('getRows');
            for (var i = 0; i < rows.length; i++) {
                if (rows[i].id == id) {
                    $('#notebookList').datagrid('selectRow', i);
                    break;
                }
            }
        }

        function formatterTitle(value, row, index){
            if (!value) return '';
            const maxLength = 15;
            return value.length > maxLength ? value.substring(0, maxLength) + '......' : value;
        }

        function formatterContent(value, row, index){
            if (!value) return '';
            const plainText = value.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
            const maxLength = 25;
            return plainText.length > maxLength ? plainText.substring(0, maxLength) + '......' : value;
        }


        //选中编辑
        function editNotebook() {
            UE.getEditor('editor').reset();
            var row = $('#notebookList').datagrid('getSelected');
            if (row) {
                $('#content').panel('setTitle', '修改');
                $('#title').val(row.title);
                UE.getEditor('editor').setContent(row.content);
                // 设置当前编辑的ID
                currentEditingId = row.id;
            }
        }
        
        function destroyNotebook(){
            var row = $('#notebookList').datagrid('getSelected');
            if (!row) {
                return $.messager.alert('提示', '请选择要删除的数据', 'warning');
            }
            
            // 检查当前编辑的是否是要删除的记录
            var currentTitle = $('#title').val();
            var isEditingThisRecord = ($('#content').panel('options').title == '修改') && 
                                     (currentTitle === row.title);
            
            $.messager.confirm('确认删除', '确定要删除选中的记录吗？', function (r) {
                if (r) {
                    $('#notebookList').datagrid('loading');
                    delNotebookApi({ id: row.id }, function (res) {
                        $.messager.alert('提示', '删除成功！', 'success');
                        getNotebookList();
                        
                        // 如果删除的是当前正在编辑的记录，切换到新增模式
                        if (isEditingThisRecord || (currentEditingId && currentEditingId == row.id)) {
                            setTimeout(function() {
                                addNotebook();
                            }, 100);
                        }
                    }, function (e, url, errMsg) {
                        $.messager.alert('提示', errMsg, 'error');
                        $('#notebookList').datagrid('loaded');
                    });
                }
            });
        }
        
        //获取窗口文档高度
        function getDocumentHeight() {
            return Math.max(
                document.body.scrollHeight,
                document.documentElement.scrollHeight,
                document.documentElement.clientHeight,
                document.documentElement.offsetHeight,
                document.body.offsetHeight
            );
        }

        // 自动设置布局高度，铺满整个屏幕
        function initLayoutHeight() {
            var windowHeight = $(window).height();
            var windowWidth = $(window).width();
            
            // 强制刷新easyui-layout组件
            setTimeout(function() {
                if ($('.easyui-layout').length > 0) {
                    $('.easyui-layout').layout('resize');
                }
                
                // 调整编辑器高度 - 需要适配右侧center区域的剩余空间
                try {
                    var editor = UE.getEditor('editor');
                    if (editor && editor.ready) {
                        // 更精确的高度计算
                        var searchBarHeight = 60;   // 搜索栏高度
                        var titleBarHeight = 30;    // 页面标题栏高度
                        var toolbarHeight = 50;     // 编辑器工具栏高度
                        var titleInputHeight = 60;  // 标题输入框区域高度
                        var padding = 50;           // 各种边距和间距
                        
                        var editorHeight = windowHeight - searchBarHeight - titleBarHeight - toolbarHeight - titleInputHeight - padding;
                        
                        // 确保编辑器高度在合理范围内
                        if (editorHeight < 200) {
                            editorHeight = 200;
                        } else if (editorHeight > windowHeight - 200) {
                            editorHeight = windowHeight - 200;
                        }
                        
                        editor.setHeight(editorHeight);
                        console.log('窗口高度:', windowHeight, '计算的编辑器高度:', editorHeight);
                    }
                } catch (e) {
                    console.log('调整编辑器高度时出错:', e);
                }
            }, 300);

        }

        function getNotebookList() {
            var createTimeStartVal = $('#createTimeStart').datetimebox('getValue');
            var startTime = createTimeStartVal == undefined || createTimeStartVal == "" ? null : new Date(createTimeStartVal).formatString("yyyy-MM-dd hh:mm:ss");

            var createTimeEndVal = $('#createTimeEnd').datetimebox('getValue');
            var endTime = createTimeEndVal == undefined || createTimeEndVal == "" ? null : new Date(createTimeEndVal).formatString("yyyy-MM-dd hh:mm:ss");
            
            let params = {
                "pageNum": page,
                "pageSize": size,
                "keyWord": $('#keyword').textbox('getValue'),
                "startTime": startTime,
                "endTime": endTime
            };
            notebookSelectPage(params, 
                function (data) {
                    notebookList = data.records;
                    $('#notebookList').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < notebookList.length; i++) {
                        $('#notebookList').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data.records[i].id,
                                title: data.records[i].title,
                                content: data.records[i].content,
                                createUser: data.records[i].createUser || '未知',
                                createTime: data.records[i].createTime,
                                lastUpdateTime: data.records[i].lastUpdateTime
                            }
                        });
                    }
                    $("#notebookList").datagrid("getPager").pagination({
                        total: data.total,//记录总数
                        size: data.size,
                        pageNumber: data.current,
                    });
                    $('#notebookList').datagrid('loaded');
                },
                function (e, url, errMsg) {
                    $.messager.alert('提示', errMsg);
                    $('#notebookList').datagrid('loaded');
                });
        }
        // 双击表格行获取数据
        $('#notebookList').datagrid({
            onDblClickRow: function (index, row) {
                // 这里可以添加处理双击行数据的逻辑
                $('#content').panel('setTitle', '修改');             
                UE.getEditor('editor').setContent(row.content);
                $('#title').val(row.title);
                // 设置当前编辑的ID
                currentEditingId = row.id;
            }
        });
        // 快速时间查询处理函数
        function timeClickHandler() {
            var from = $('#createTimeStart');
            var to = $('#createTimeEnd');
            var now = new Date();
            var fromTime = new Date();
            var hours = 24;
            if ($(this).hasClass('inAnHour')) {
                hours = 1;
            }
            fromTime.setHours(fromTime.getHours() - hours);
            from.datetimebox('setValue', fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            to.datetimebox('setValue', '');
            
            // 自动搜索
            getNotebookList();
        }

        function easyUiDateTimeSet() {
            $('.easyui-datetimebox').each(function () {
                var e = $(this);
                e.datetimebox('setValue', e.val());
            });
        }

        // 页面加载完成后初始化
        $(document).ready(function () {
            initLayoutHeight();
            // 窗口大小改变时重新调整
            $(window).resize(function () {
                initLayoutHeight();
            });
            
            // 设置默认查询24小时内的数据
            var curDate = new Date();
            var fromTime = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
            $("#createTimeStart").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            
            // 为快速时间操作设置处理函数
            $('.inAnHour,.inToday').on("click", timeClickHandler);
            $('#notebookList').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });
            //分页控件
            var pg = $("#notebookList").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    size: 20,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber;
                        size = pageSize;
                        getNotebookList();
                    }
                });
            }
            getNotebookList();
        });
    </script>
</body>

</html>