<!--
/* *****************************************************************
* 120EDC急救指挥中心报停车辆记录页面
* 本页面用于查看和管理车辆报停记录
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 系统自动生成
* Created: 2024/12/19
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心报停车辆</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .l-btn-left l-btn-icon-left {
            margin-top: -1px;
        }
        .datagrid-cell {
            font-size: 14px !important;
            font-weight: bold !important;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            height: 28px;
        }
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 0px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover,
        .datagrid-row-selected {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        .datagrid-wrap .datagrid-body tr:hover a,
        .datagrid-row-selected a {
            color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
        }
        input[type="checkbox"]:checked::before {
            content: "\2713";
            background-color: #fff;
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            color: #000;
            font-size: 20px;
            font-weight: bold;
        }
        
        /* 报停状态样式 */
        .status-active { color: #ff6b35; font-weight: bold; }
        .status-recovered { color: #28a745; font-weight: bold; }
        .status-timeout { color: #dc3545; font-weight: bold; }
        
        /* 报停原因样式 */
        .reason-repair { color: #ff6b35; }
        .reason-maintain { color: #17a2b8; }
        .reason-accident { color: #dc3545; }
        .reason-station { color: #28a745; }
        .reason-other { color: #6c757d; }
        
        /* 强制移除combobox disabled状态 */
        .combobox-item-disabled {
            color: #000 !important;
            cursor: pointer !important;
            pointer-events: auto !important;
            background-color: #fff !important;
        }
        
        .combobox-item-disabled:hover {
            background-color: #eaf2ff !important;
        }

        input[readonly], textarea[readonly] {
            background: #fff !important;
        }

        /* 确保所有输入框背景为白色 */
        input[type="text"] {
            background-color: #fff !important;
        }
        
        select {
            background-color: #fff !important;
        }
    </style>
</head>
<body>
    <div id="mainPanel_VehicleStopList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">报停车辆记录<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="vehicleStopSearch" style="font-size: 14px;">
                    报停时间： <input id="startTime" class="easyui-datetimebox" style="width:160px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endTime" class="easyui-datetimebox" style="width:160px;" />
                    &nbsp;<a href="javascript:void(0);" class="inToday">今天</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inWeek">本周</a>
                    <span>&nbsp;车牌号：</span>
                    <input id="vehicleNo" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;报停原因：</span>
                    <select id="stopReasonCode" class="easyui-combobox" style="width:120px;" 
                            data-options="valueField:'id',textField:'reasonName',editable:false">
                    </select>
                    <span>&nbsp;状态：</span>
                    <select id="status" class="easyui-combobox" style="width:100px;" 
                            data-options="valueField:'value',textField:'text',editable:false">
                        <option value="">全部</option>
                        <option value="1">报停中</option>
                        <option value="2">已恢复</option>
                        <option value="3">已超时</option>
                    </select>
                    <span>&nbsp;驻场站点：</span>
                    <input id="stationName" class="easyui-textbox" style="width:120px">
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:searchStopRecords();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-vehicle-stop-pages" style="height: Calc(100% - 100px)"
                       data-options="pagination:true,singleSelect:true,rownumbers:true,fit:true,fitColumns:false,toolbar:'#tb'">
                    <thead>
                        <tr>
                            <th data-options="field:'vehicleNo',width:120,align:'center'">车牌号</th>
                            <th data-options="field:'stopReasonName',width:120,align:'center'">报停原因</th>
                            <th data-options="field:'startTime',width:180,align:'center'">报停开始时间</th>
                            <th data-options="field:'expectedEndTime',width:180,align:'center'">预计结束时间</th>
                            <th data-options="field:'actualEndTime',width:180,align:'center'">实际结束时间</th>
                            <th data-options="field:'expectedDuration',width:120,align:'center'">预计时长(小时)</th>
                            <th data-options="field:'actualDuration',width:120,align:'center',formatter:formatActualDuration">实际时长(小时)</th>
                            <th data-options="field:'statusName',width:100,align:'center',formatter:formatStatus">状态</th>
                            <th data-options="field:'recorderName',width:100,align:'center'">记录人</th>
                            <th data-options="field:'remarks',width:200,align:'center'">备注</th>
                            <th data-options="field:'createTime',width:150,align:'center'">创建时间</th>
                            <th data-options="field:'audioFile',width:80,align:'center',formatter:formatAudioFile">录音</th>
                        </tr>
                    </thead>
                </table>

                <div id="tb" style="height:45px;">
                    <div>
                        <a id="addStopRecordBtn" class="easyui-linkbutton" href="javascript:addStopRecord();" style="width: 100px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-add'">新增报停</a>
                        <a id="viewStopRecordBtn" class="easyui-linkbutton" href="javascript:viewStopRecord();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看详情</a>
                        <a id="editStopRecordBtn" class="easyui-linkbutton" href="javascript:editStopRecord();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-edit'">编辑</a>
                        <a id="recoverVehicleBtn" class="easyui-linkbutton" href="javascript:recoverVehicle();" style="width: 90px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-ok'">恢复使用</a>
                        <a id="linkCallRecordBtn" class="easyui-linkbutton" href="javascript:linkCallRecord();" style="width: 120px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-filter'">关联通话记录</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新增/编辑报停记录窗口 -->
        <div id="stopRecordEditWindow" class="easyui-window" title="报停记录" style="width:900px;height:620px"
             data-options="iconCls:'icon-save',modal:true, closed: true">
            <iframe id="stopRecordEditIframe" name="stopRecordEditIframe" scrolling="auto" frameborder="0" 
                    style="width:100%;height:100%;"></iframe>
        </div>
        
        <!-- 详情查看窗口 -->
        <div id="stopRecordDetailWindow" class="easyui-window" title="报停记录详情" style="width:800px;height:600px"
             data-options="iconCls:'icon-info',modal:true, closed: true">
            <div style="padding: 20px;" id="stopRecordDetail">
                <!-- 详情内容将在这里动态加载 -->
            </div>
        </div>
        
        <!-- 统计报表窗口 -->
        <div id="statisticsWindow" class="easyui-window" title="报停统计报表" style="width:1000px;height:600px"
             data-options="iconCls:'icon-chart',modal:true, closed: true">
            <div style="padding: 20px;" id="statisticsContent">
                <!-- 统计报表内容将在这里动态加载 -->
            </div>
        </div>

        <!-- 关联通话记录窗口 -->
        <div id="callRecordSelectWindow" class="easyui-window" title="选择通话记录关联报停车辆（请选择一条通话记录双击进行关联）" style="width:1440px;height:900px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="padding: 10px;height: 100%;">
                <div style="margin-bottom:10px;height:34px;">
                    <div id="callRecordSearch" style="font-size: 14px;">
                        创建时间： <input id="callStartTime" class="easyui-datetimebox" style="width:150px; margin-top: 10px;" />
                        <span>至</span>
                        <input id="callEndTime" class="easyui-datetimebox" style="width:150px;" />
                        <span>&nbsp;主叫号码：</span>
                        <input id="callNumber" class="easyui-textbox" style="width:150px">
                        <span>&nbsp;呼叫类型：</span>
                        <select id="callTypeSelect" class="easyui-combobox" name="callType" style="width:100px;">
                            <option value="">全部</option>
                            <option value="IN">呼入</option>
                            <option value="OU">呼出</option>
                            <option value="LO">内部呼叫</option>
                        </select>
                        <span>&nbsp;是否接听：</span>
                        <select id="callIsAnswer" class="easyui-combobox" name="isAnswer" style="width:100px;">
                            <option value="">全部</option>
                            <option value="1">已接听</option>
                            <option value="0">未接听</option>
                        </select>
                        <a id="searchCallRecordBtn" class="easyui-linkbutton" href="javascript:searchCallRecords();" style="width: 80px;height:25px;line-height: 25px; font-size: 12px; margin: 5px">搜索通话</a>
                    </div>
                </div>
                <div style="width:100%;height: Calc(100% - 44px); position: relative;font-size: 14px;">
                    <table class="easyui-datagrid" id="dg-call-records" style="width: 100%; height:100%;"
                           pagination="true" singleSelect="true" rownumbers="true" fit="false">
                        <thead>
                            <tr>
                                <th field="callTime" width="180" align="center">来/去电时间</th>
                                <th field="number" width="160" align="center">主叫号码</th>
                                <th field="toNumber" width="160" align="center">被叫号码</th>
                                <th field="callType" width="160" align="center" formatter="callTypeFormatter">呼叫类型</th>
                                <th field="endTime" width="180" align="center">挂断时间</th>
                                <th field="duration" width="100" align="center">通话时长（秒）</th>
                                <th field="isAnswer" width="100" align="center" formatter="isAnswerFormatter">是否接听</th>
                                <th field="seatUser" width="100" align="center">接听人</th>
                                <th field="eventId" width="210" align="center">关联事件</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
        

        
        // 加载报停原因列表
        function loadStopReasonList() {
            console.log('开始加载报停原因列表...');
            
            // 延迟确保EasyUI完全初始化
            setTimeout(function() {
                selectMobileStopReasonList(
                    function (data) {
                        console.log('报停原因数据:', data);
                        
                        // 添加"全部"选项
                        var allData = [{id: '', reasonName: '全部'}];
                        if(data && data.length > 0) {
                            allData = allData.concat(data);
                        }
                        console.log('合并后的数据:', allData);
                        
                        // 使用combobox方式加载数据
                        $('#stopReasonCode').combobox({
                            data: allData,
                            valueField: 'id',
                            textField: 'reasonName',
                            editable: false,
                            onLoadSuccess: function(data) {
                                console.log('combobox加载成功, 数据量:', data.length);
                                // 设置默认选中"全部"
                                $(this).combobox('setValue', '');
                                // 移除可能存在的disabled类
                                $('.combobox-item-disabled').removeClass('combobox-item-disabled');
                            }
                        });
                    },
                    function (e, url, errMsg) {
                        console.error('加载报停原因失败:', errMsg);
                        $.messager.alert('提示', '加载报停原因列表失败：' + errMsg);
                    }
                );
            }, 300);
        }

        function initializePage(){
            // 加载报停原因列表
            loadStopReasonList();
            
            // 初始化状态下拉框
            $('#status').combobox({
                data: [
                    {value: '', text: '全部'},
                    {value: '1', text: '报停中'},
                    {value: '2', text: '已恢复'},
                    {value: '3', text: '已超时'}
                ],
                valueField: 'value',
                textField: 'text',
                editable: false,
                value: '1'  // 默认选中"报停中"
            });
            
            // 初始化时间范围，默认为空
            $("#startTime").datetimebox("setValue", "");
            $("#endTime").datetimebox("setValue", "");
            
            // 为时间快速选择设置处理函数
            $('.inToday,.inWeek').on("click", timeClickHandler);
            
            // 初始化数据表格
            $('#dg-vehicle-stop-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
                nowrap: true,
                rownumbers: true,
                singleSelect: true,
                fit: true,
                fitColumns: false,
                pagination: true,
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                onBeforeLoad: function(param) {
                    // 当分页器被点击时，会触发此事件
                    console.log('分页参数:', param);
                    return false; // 阻止默认加载，使用自定义加载
                },
                rowStyler: function (index, row) {
                    // 根据状态设置行样式
                    switch (row.status) {
                        case 1:
                        case '1':
                            return 'background-color:#fff2e6;'; // 报停中 - 橙色背景
                        case 2:
                        case '2':
                            return 'background-color:#e6f7e6;'; // 已恢复 - 绿色背景
                        case 3:
                        case '3':
                            return 'background-color:#ffe6e6;'; // 已超时 - 红色背景
                    }
                },
                onClickRow: function (rowIndex, rowData) {
                    // 根据选中行的状态控制按钮
                    updateButtonStates(rowData);
                },
                onDblClickRow: function(rowIndex, rowData) {
                    viewStopRecord();
                }
            });
            
            // 绑定分页器事件
            var pager = $('#dg-vehicle-stop-pages').datagrid('getPager');
            pager.pagination({
                onSelectPage: function(pageNumber, pageSize) {
                    console.log('分页事件触发:', pageNumber, pageSize);
                    getVehicleStopListData(pageNumber, pageSize);
                }
            });
            
            // 页面加载完成后加载数据
            getVehicleStopListData();
        }
        
        
        /**
         * 格式化状态文本
         */
        function formatStatus(value, row) {
            var className = 'status-active';
            var text = value;
            switch (row.status) {
                case 1:
                case '1':
                    className = 'status-active';
                    text = '报停中';
                    break;
                case 2:
                case '2':
                    className = 'status-recovered';
                    text = '已恢复';
                    break;
                case 3:
                case '3':
                    className = 'status-timeout';
                    text = '已超时';
                    break;
            }
            return '<span class="' + className + '">' + text + '</span>';
        }
        
        /**
         * 格式化实际时长
         */
        function formatActualDuration(value, row) {
            if (row.status == 1 || row.status == '1') {
                // 报停中，计算已经报停的时长
                var startTime = new Date(row.startTime);
                var now = new Date();
                var duration = (now - startTime) / (1000 * 60 * 60); // 转换为小时
                return duration.toFixed(1) + ' (进行中)';
            } else if (row.actualEndTime) {
                // 已结束，计算实际时长
                var startTime = new Date(row.startTime);
                var endTime = new Date(row.actualEndTime);
                var duration = (endTime - startTime) / (1000 * 60 * 60); // 转换为小时
                return duration.toFixed(1);
            }
            return '-';
        }
        
        /**
         * 格式化录音文件
         */
        function formatAudioFile(value, row) {
            if (row.audioFileId) {
                return '<a href="javascript:playAudioFromStopRecord(\'' + row.audioFileId + '\')" style="color: #007bff;">播放</a>';
            }
            return '-';
        }
        
        /**
         * 播放报停记录关联的录音文件
         */
        function playAudioFromStopRecord(audioFileId) {
            if (!audioFileId) {
                $.messager.alert('提示', '没有录音文件', 'warning');
                return;
            }
            
            // 显示加载提示
            showProcess(true, '温馨提示', '正在加载录音文件，请稍后...');
            
            // 调用getCallById接口获取通话记录详情
            getCallById({ id: audioFileId },
                function(callData) {
                    showProcess(false);
                    
                    if (callData && callData.recordAddr && callData.recordAddr !== '' && callData.recordAddr !== ' ') {
                        // 调用播放录音函数
                        playWav(callData.recordAddr, callData.recordName || '录音文件');
                    } else {
                        $.messager.alert('提示', '该通话记录没有录音文件', 'warning');
                    }
                },
                function(e, url, errMsg) {
                    showProcess(false);
                    console.error('获取通话记录失败:', errMsg);
                    $.messager.alert('错误', '获取录音文件失败：' + errMsg, 'error');
                }
            );
        }
        
        /**
         * 播放录音文件 - 参考callList.html
         */
        function playWav(address, name) {
            if (!address) {
                $.messager.alert('提示', '录音地址无效', 'warning');
                return;
            }
            
            // 创建录音播放窗口
            if ($('#wavPlayWindow').length === 0) {
                $('body').append(
                    '<div id="wavPlayWindow" class="easyui-window" title="录音回放" style="background-color:#FAFAFA; width: 600px; height: 100px; text-align: center; padding-top:5px"' +
                    ' data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">' +
                    '<div style="position: relative;">' +
                    '<audio id="playWavAudio" src="" style="width: 100%;height: 42px;" autostart="false" controls></audio>' +
                    '<img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="http://preview.qiantucdn.com/58pic/20220321/00S58PIC5Xkc6mdzM5xU3_PIC2018_PIC2018.jpg!w290_290" />' +
                    '</div>' +
                    '</div>'
                );
                
                // 初始化录音窗口
                $('#wavPlayWindow').window({
                    onBeforeClose: function () {
                        var playWavAudio = document.getElementById('playWavAudio');
                        if (playWavAudio) {
                            playWavAudio.pause(); // 暂停播放
                        }
                    }
                });
                
                // 绑定下载按钮事件
                $('#download').off('click').on('click', function() {
                    downloadURL(address, name || '录音文件');
                });
            }
            
            // 设置录音地址并打开窗口
            $('#playWavAudio').attr('src', address);
            $('#wavPlayWindow').window('open');
        }
        
        /**
         * 下载录音文件
         */
        function downloadURL(url, name) {
            try {
                window.parent.gProxy.download(url, name);
            } catch (e) {
                console.error('下载失败:', e);
                // 如果gProxy不可用，尝试其他下载方式
                var link = document.createElement('a');
                link.href = url;
                link.download = name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        
        /**
         * 获取报停记录列表数据 - 使用接口数据
         */
        function getVehicleStopListData(pageNum, pageSize) {
            if (!pageNum) pageNum = 1;
            if (!pageSize) pageSize = 20;
            
            $('#dg-vehicle-stop-pages').datagrid('loading');
            
            // 获取搜索条件
            var searchParams = {
                beginStartTime: $('#startTime').datetimebox('getValue') || '',
                beginEndTime: $('#endTime').datetimebox('getValue') || '',
                vehicleNo: $('#vehicleNo').textbox('getValue') || '',
                stopReasonCode: $('#stopReasonCode').combobox('getValue') || '',
                status: $('#status').combobox('getValue') ? parseInt($('#status').combobox('getValue')) : '',
                page: pageNum,
                size: pageSize,
                sortBy: [{"startTime": "1"}] // 按开始时间倒序排列
            };
            
            console.log('接口请求参数:', searchParams);
            
            // 调用接口获取数据
            selectMobileStopRecordsPage(
                searchParams,
                function(res) {
                    console.log('接口返回数据:', res);
                     // 处理返回的数据，转换为datagrid需要的格式
                    var processedData = {
                        total: res.total || 0,
                        rows: processDataRows(res.records || [])
                    };
                        
                    $('#dg-vehicle-stop-pages').datagrid('loadData', processedData);
                    
                    $('#dg-vehicle-stop-pages').datagrid('loaded');
                },
                function(e, url, errMsg) {
                    console.error('接口调用失败:', e, errMsg);
                    $.messager.alert('错误', '接口调用失败：' + errMsg, 'error');
                    $('#dg-vehicle-stop-pages').datagrid('loadData', {total: 0, rows: []});
                    $('#dg-vehicle-stop-pages').datagrid('loaded');
                }
            );
        }
        
        /**
         * 处理接口返回的数据行
         */
        function processDataRows(records) {
            var processedRows = [];
            
            for (var i = 0; i < records.length; i++) {
                var record = records[i];
                
                // 计算状态名称
                var statusName = '';
                switch (record.status) {
                    case 1:
                        statusName = '报停中';
                        break;
                    case 2:
                        statusName = '已恢复';
                        break;
                    case 3:
                        statusName = '已超时';
                        break;
                    default:
                        statusName = '未知';
                }
                
                // 计算实际时长
                var actualDuration = '';
                if (record.status === 1) {
                    // 报停中，计算已经报停的时长
                    if (record.startTime) {
                        var startTime = new Date(record.startTime);
                        var now = new Date();
                        var duration = (now - startTime) / (1000 * 60 * 60); // 转换为小时
                        actualDuration = duration.toFixed(1) + ' (进行中)';
                    }
                } else if (record.actualEndTime && record.startTime) {
                    // 已结束，计算实际时长
                    var startTime = new Date(record.startTime);
                    var endTime = new Date(record.actualEndTime);
                    var duration = (endTime - startTime) / (1000 * 60 * 60); // 转换为小时
                    actualDuration = duration.toFixed(1);
                }
                
                var processedRow = {
                    id: record.id,
                    vehicleNo: record.vehicleNo || '',
                    vehicleId: record.vehicleId || '',
                    stopReasonCode: record.stopReasonCode || '',
                    stopReasonId: record.stopReasonId || '',
                    stopReasonName: record.stopReason || '', // 使用stopReason作为显示名称
                    startTime: record.startTime || '',
                    expectedEndTime: record.expectedEndTime || '',
                    actualEndTime: record.actualEndTime || '',
                    expectedDuration: record.expectedDuration || 0,
                    actualDuration: actualDuration,
                    status: record.status,
                    statusName: statusName,
                    recorderName: record.seatUser || '', // 记录人姓名
                    stopReason: record.stopReason || '', // 详细原因
                    remarks: record.remarks || '',
                    createTime: record.startTime || '', // 使用开始时间作为创建时间
                    audioFileId: record.audioFileId || '',
                    reminderCount: 0, // 提醒次数，如果后端有可以映射
                    warningThreshold: record.warningThreshold || 0,
                    reminderInterval: record.reminderInterval || 0,
                    seatCode: record.seatCode || '',
                    seatId: record.seatId || '',
                    seatUser: record.seatUser || '',
                    seatUserId: record.seatUserId || '',
                    seatUserName: record.seatUserName || ''
                };
                
                processedRows.push(processedRow);
            }
            
            return processedRows;
        }
        
        /**
         * 时间快速选择处理函数
         */
        function timeClickHandler() {
            var now = new Date();
            var fromTime = new Date();
            var hours = 7 * 24; // 默认一周
            
            if ($(this).hasClass('inToday')) {
                hours = 24; // 今天
                fromTime.setHours(0, 0, 0, 0);
            } else {
                fromTime.setHours(fromTime.getHours() - hours);
            }
            
            $('#startTime').datetimebox('setValue', fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            $('#endTime').datetimebox('setValue', '');
            
            // 自动搜索，重置到第一页
            searchStopRecords();
        }
        
        /**
         * 搜索报停记录（重置到第一页）
         */
        function searchStopRecords() {
            // 重置分页器到第一页
            var pager = $('#dg-vehicle-stop-pages').datagrid('getPager');
            pager.pagination('select', 1);
            
            // 加载第一页数据
            getVehicleStopListData(1, 20);
        }
        
        /**
         * 更新按钮状态
         */
        function updateButtonStates(rowData) {
            if (!rowData) {
                // 没有选中行，禁用大部分按钮
                $('#viewStopRecordBtn,#editStopRecordBtn,#recoverVehicleBtn,#playAudioBtn').linkbutton('disable');
                return;
            }
            
            // 启用基本按钮
            $('#viewStopRecordBtn').linkbutton('enable');
            
            // 根据状态控制其他按钮
            if (rowData.status == 1 || rowData.status == '1') {
                // 报停中状态
                $('#editStopRecordBtn,#recoverVehicleBtn').linkbutton('enable');
            } else {
                // 已恢复或已超时状态
                $('#editStopRecordBtn,#recoverVehicleBtn').linkbutton('disable');
            }
            
            // 录音按钮
            if (rowData.audioFileId) {
                $('#playAudioBtn').linkbutton('enable');
            } else {
                $('#playAudioBtn').linkbutton('disable');
            }
        }
        
        /**
         * 新增报停记录
         */
        function addStopRecord() {
            $('#stopRecordEditWindow').window({
                title: '新增报停记录'
            });
            
            var href = "vehicleStopEdit.html";
            $('#stopRecordEditIframe').attr('src', href);
            $('#stopRecordEditWindow').window('open');
        }
        
        /**
         * 查看报停记录详情
         */
        function viewStopRecord() {
            var row = $('#dg-vehicle-stop-pages').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要查看的记录', 'warning');
                return;
            }
            
            // 构建详情HTML
            var detailHtml = buildDetailHtml(row);
            $('#stopRecordDetail').html(detailHtml);
            $('#stopRecordDetailWindow').window('open');
        }
        
        /**
         * 构建详情HTML
         */
        function buildDetailHtml(row) {
            return '<div style="font-size: 14px; line-height: 1.8;">' +
                '<h3 style="margin-bottom: 20px; color: #333;">报停记录详情</h3>' +
                '<table style="width: 100%; border-collapse: collapse;">' +
                '<tr style="height: 40px;"><td style="width: 120px; font-weight: bold;">车牌号：</td><td>' + row.vehicleNo + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">报停原因：</td><td>' + row.stopReasonName + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">开始时间：</td><td>' + row.startTime + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">预计结束时间：</td><td>' + row.expectedEndTime + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">实际结束时间：</td><td>' + (row.actualEndTime || '-') + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">预计时长：</td><td>' + row.expectedDuration + ' 小时</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">状态：</td><td>' + formatStatus('', row) + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">记录人：</td><td>' + row.seatUser + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">备注：</td><td>' + (row.remarks || '-') + '</td></tr>' +
                '<tr style="height: 40px;"><td style="font-weight: bold;">创建时间：</td><td>' + row.createTime + '</td></tr>' +
                '</table></div>';
        }
        
        /**
         * 编辑报停记录
         */
        function editStopRecord() {
            var row = $('#dg-vehicle-stop-pages').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要编辑的记录', 'warning');
                return;
            }
            
            if (row.status != 1 && row.status != '1') {
                $.messager.alert('提示', '只能编辑报停中的记录', 'warning');
                return;
            }
            
            $('#stopRecordEditWindow').window({
                title: '编辑报停记录'
            });
            
            var href = "vehicleStopEdit.html?id=" + row.id;
            $('#stopRecordEditIframe').attr('src', href);
            $('#stopRecordEditWindow').window('open');
        }
        
        /**
         * 恢复车辆使用
         */
        function recoverVehicle() {
            var row = $('#dg-vehicle-stop-pages').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要恢复使用的车辆', 'warning');
                return;
            }
            
            if (row.status != 1 && row.status != '1') {
                $.messager.alert('提示', '只能恢复报停中的车辆', 'warning');
                return;
            }
            
            $.messager.confirm('确认', '确定要恢复车辆 ' + row.vehicleNo + ' 的使用吗？', function(r) {
                if (r) {
                    // 显示进度提示
                    showProcess(true, '温馨提示', '正在恢复车辆使用，请稍后...');
                    
                    // 构建请求参数
                    var requestData = {
                        actualEndTime: '', // 不传则使用当前时间
                        id: row.id
                    };
                    
                    console.log('准备调用恢复接口，参数：', requestData);
                    
                    // 调用恢复接口
                    recoverVehicleStop(requestData,
                        function(response) {
                            showProcess(false);
                            console.log('恢复成功，返回数据：', response);
                            $.messager.alert('成功', '车辆恢复使用成功', 'info', function() {
                                // 刷新列表数据
                                getVehicleStopListData();
                            });
                        },
                        function(e, url, errMsg) {
                            showProcess(false);
                            console.error('恢复失败:', errMsg);
                            $.messager.alert('错误', '车辆恢复使用失败：' + errMsg, 'error');
                        }
                    );
                }
            });
        }
        
        /**
         * 播放录音
         */
        function playAudio() {
            var row = $('#dg-vehicle-stop-pages').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要播放录音的记录', 'warning');
                return;
            }
            
            if (!row.audioFileId) {
                $.messager.alert('提示', '该记录没有关联录音文件', 'warning');
                return;
            }
            
            playAudioFile(row.audioFileId);
        }
        
        /**
         * 播放录音文件
         */
        function playAudioFile(audioFileId) {
            $.messager.alert('播放录音', '正在播放录音文件: ' + audioFileId + '\n（这里应该集成音频播放器）', 'info');
        }
        
        /**
         * 导出数据
         */
        function exportData() {
            $.messager.alert('导出数据', '导出功能正在开发中...', 'info');
        }
        
        /**
         * 显示统计报表
         */
        function showStatistics() {
            $('#statisticsContent').html('<div style="text-align: center; padding: 50px;">统计报表功能正在开发中...</div>');
            $('#statisticsWindow').window('open');
        }
        
        /**
         * 刷新报停记录列表（供子页面调用）
         */
        function refreshStopRecordList() {
            // 获取当前分页信息
            var pager = $('#dg-vehicle-stop-pages').datagrid('getPager');
            var pageNumber = pager.pagination('options').pageNumber || 1;
            var pageSize = pager.pagination('options').pageSize || 20;
            
            getVehicleStopListData(pageNumber, pageSize);
        }

        //Bs主屏幕与副屏幕通信，通过storage来监听
        window.addEventListener('storage', event => {
            //登录成功后调用
            if (event.key === 'loginSuccess') {
                let eventData = evalJson(event.newValue);
                if (eventData != null) {
                    initializePage();
                }
            }
        });

        $(document).ready(function () {
            initializePage();
        });

        /**
         * 关联通话记录
         */
        function linkCallRecord() {
            var row = $('#dg-vehicle-stop-pages').datagrid('getSelected');
            if (!row) {
                $.messager.alert('提示', '请选择要关联通话记录的报停车辆', 'warning');
                return;
            }
            
            // 保存当前选中的报停记录ID
            window.selectedStopRecordId = row.id;
            
            // 设置默认查询时间为最近24小时
            var now = new Date();
            var fromTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            $('#callStartTime').datetimebox('setValue', fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            $('#callEndTime').datetimebox('setValue', '');
            
            // 打开通话记录选择窗口
            $('#callRecordSelectWindow').window('open');
            
            // 初始化通话记录列表
            searchCallRecords();
        }
        
        /**
         * 搜索通话记录
         */
        function searchCallRecords(pageNum, pageSize) {
            if (!pageNum) pageNum = 1;
            if (!pageSize) pageSize = 20;
            
            var beginCallTimeVal = $('#callStartTime').datetimebox('getValue');
            var beginCallTime = beginCallTimeVal == undefined || beginCallTimeVal == "" ? null : new Date(beginCallTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endCallTimeVal = $('#callEndTime').datetimebox('getValue');
            var endCallTime = endCallTimeVal == undefined || endCallTimeVal == "" ? null : new Date(endCallTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            // 打开进度条
            $('#dg-call-records').datagrid('loading');
            
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "beginCallTime": beginCallTime,
                "endCallTime": endCallTime,
                "number": $('#callNumber').val(),
                "isAnswer": $('#callIsAnswer').combobox('getValue'),
                "handleStatus": "3", // 只查询未处理的
                "callType": $('#callTypeSelect').val(),
                "ext": ""
            };
            
            console.log('搜索通话记录参数:', params);
            
            getCallPageList(params, function (data) {
                $('#dg-call-records').datagrid('loadData', { total: 0, rows: [] });
                
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-call-records').datagrid('insertRow', {
                        index: i,
                        row: {
                            id: data.content[i].id,
                            number: data.content[i].number,
                            toNumber: data.content[i].toNumber,
                            callType: data.content[i].callType,
                            callTime: data.content[i].callTime,
                            endTime: data.content[i].endTime,
                            duration: data.content[i].duration,
                            isAnswer: data.content[i].isAnswer,
                            seatUser: data.content[i].seatUser || '无',
                            eventId: data.content[i].eventId || '-'
                        }
                    });
                }
                
                $("#dg-call-records").datagrid("getPager").pagination({
                    total: data.total,
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                
                $('#dg-call-records').datagrid('loaded');
            }, function (e, url, errMsg) {
                $('#dg-call-records').datagrid('loaded');
                console.error('获取通话记录失败:', errMsg);
                $.messager.alert('错误', '获取通话记录失败：' + errMsg, 'error');
            });
        }
        
        /**
         * 格式化呼叫类型
         */
        function callTypeFormatter(value, row, index) {
            switch (row.callType) {
                case "IN":
                    return "呼入";
                case "OU":
                    return "呼出";
                case "LO":
                    return "内部呼叫";
                default:
                    return value || '-';
            }
        }
        
        /**
         * 格式化是否接听
         */
        function isAnswerFormatter(value, row, index) {
            switch (row.isAnswer) {
                case "0":
                    return "未接听";
                case "1":
                    return "已接听";
                default:
                    return value || '-';
            }
        }
        
        /**
         * 关联通话记录到报停车辆
         */
        function linkCallToStopRecord(callId) {
            if (!window.selectedStopRecordId) {
                $.messager.alert('错误', '未选择报停记录', 'error');
                return;
            }
            
            $.messager.confirm('确认', '确定要关联选中的通话记录吗？', function(r) {
                if (r) {
                    // 显示进度提示
                    showProcess(true, '温馨提示', '正在关联通话记录，请稍后...');
                    
                    // 构建请求参数
                    var requestData = {
                        audioFileId: callId,
                        recordId: window.selectedStopRecordId
                    };
                    
                    console.log('关联通话记录参数:', requestData);
                    
                    // 调用关联接口
                    linkAudioFileToStopRecord(requestData,
                        function(response) {
                            showProcess(false);
                            console.log('关联成功，返回数据：', response);
                            $.messager.alert('成功', '通话记录关联成功', 'info', function() {
                                $('#callRecordSelectWindow').window('close');
                                // 刷新报停记录列表
                                getVehicleStopListData();
                            });
                        },
                        function(e, url, errMsg) {
                            showProcess(false);
                            console.error('关联失败:', errMsg);
                            $.messager.alert('错误', '通话记录关联失败：' + errMsg, 'error');
                        }
                    );
                }
            });
        }

        // 初始化通话记录表格
        $('#dg-call-records').datagrid({
            loadMsg: '数据加载，请稍等.....',
            onDblClickRow: function(rowIndex, rowData) {
                // 双击关联通话记录
                linkCallToStopRecord(rowData.id);
            }
        });
        
        // 绑定通话记录分页器事件
        var callPager = $('#dg-call-records').datagrid('getPager');
        callPager.pagination({
            onSelectPage: function(pageNumber, pageSize) {
                searchCallRecords(pageNumber, pageSize);
            }
        });
    </script>
</body>
</html> 