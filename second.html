<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <!-- <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652"></script> -->
    <script type="text/javascript" src="script/viewcore.js"></script>
    
    <!-- URL参数解析功能 - 支持多屏幕参数传递 -->
    <script type="text/javascript">
        // 全局变量存储屏幕参数
        window.screenParams = {};
        
        // 解析URL参数的函数
        function parseUrlParams() {
            var params = {};
            var search = window.location.search;
            
            if (search && search.length > 1) {
                var pairs = search.substring(1).split('&');
                for (var i = 0; i < pairs.length; i++) {
                    var pair = pairs[i].split('=');
                    if (pair.length === 2) {
                        params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
                    }
                }
            }
            
            return params;
        }
        
        // 页面加载时解析参数
        window.screenParams = parseUrlParams();
        
        // 输出接收到的参数信息
        console.log('=== 副屏幕参数解析结果 ===');
        console.log('屏幕ID:', window.screenParams.screenId || '未指定');
        console.log('所有参数:', window.screenParams);
        
        // 动态更新页面标题（如果有screenName则显示屏幕名称）
        if (window.screenParams.screenName) {
            document.title = window.screenParams.screenName;
        }
        
        // 提供获取参数的全局方法
        window.getScreenParam = function(paramName, defaultValue) {
            return window.screenParams[paramName] || defaultValue;
        };
        
        // 获取屏幕ID
        window.getScreenId = function() {
            return parseInt(window.screenParams.screenId) || 0;
        };
    </script>
    
    <style>
        #second-screen-tabs .tabs-title {
            font-size: 14px;
            font-weight: bold;
        }
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }
        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        #second-screen-tabs .tabs-panels > .panel > .panel-body {
            overflow: hidden;
        }

        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }
        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        a {
            text-decoration: none !important;
        }

        tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }
        tr:hover a {
                color: #ffffff !important;
            }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;

        }
        .input-border>.textbox{
            /* border: none !important; */
            margin: 10px 0;
        }
        .textbox .textbox-prompt {
            font-size: 16px !important;
        }
        .panel-title{
            font-size: 15px;
        }

        /* 地图工具栏容器 */
        .map-toolbar {
          position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 2px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
            width: 120px;
        }
        
        /* 工具栏组 */
        .toolbar-group {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(135, 206, 235, 0.2);
            padding: 0;
            backdrop-filter: blur(20px);
            overflow: visible;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1000;
        }
        
        .toolbar-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .toolbar-group.collapsed .vehicle-tools,
        .toolbar-group.collapsed .map-tools {
            display: none;
        }
        
        .toolbar-group.collapsed {
            margin-bottom: 0;
        }
        
        .toolbar-group.collapsed .toolbar-group-title {
            border-radius: 16px;
            margin-bottom: 0;
            box-shadow: 0 4px 12px rgba(184, 230, 255, 0.2);
        }
        
        .toolbar-group.collapsed .toolbar-group-title:hover {
            box-shadow: 0 6px 16px rgba(184, 230, 255, 0.3);
        }
        
        .toolbar-group-title {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: #ffffff;
            padding: 10px 12px;
            margin: 0;
            font-size: 11px;
            font-weight: 600;
          text-align: center;
            cursor: pointer;
            user-select: none;
            border-radius: 12px 12px 0 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: none;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
            letter-spacing: 0.3px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .toolbar-group-title:hover {
            background: linear-gradient(135deg, #0984e3 0%, #2d3436 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
        }
        
        .toolbar-group-title:active {
            transform: translateY(0);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        /* 移除标题图标 */
        
        /* 工具按钮容器 */
        .vehicle-tools {
            padding: 7px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-start;
            background: rgba(248, 250, 252, 0.8);
            transition: all 0.3s ease;
        }
        
        .map-tools {
            padding: 7px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-start;
            background: rgba(248, 250, 252, 0.8);
            transition: all 0.3s ease;
        }
        
        /*车辆工具：每行2个按钮 */
        .vehicle-tools .map-tool-btn {
            flex: 0 0 48px;
            width: 48px;
        }
        
        /* 地图工具：每行2个按钮 */
        .map-tools .map-tool-btn,
        .map-tools .range-dropdown {
            flex: 0 0 48px;
            width: 48px;
        }
        
        /* 工具按钮基础样式 */
        .map-tool-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 48px;
            height: 48px;
            border: 2px solid transparent;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 9px;
            font-weight: 600;
            color: #64748b;
            margin: 0;
            position: relative;
          text-align: center;
            line-height: 1;
          white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        .map-tool-btn::before {
            content: attr(data-icon);
            font-size: 18px;
            margin-bottom: 3px;
            display: block;
            transition: all 0.3s ease;
        }
        
        .map-tool-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        .map-tool-btn:hover::before {
            transform: scale(1.1);
        }
        
        .map-tool-btn:active {
            transform: translateY(-1px);
        }
        
        /*车辆工具组按钮颜色 */
        #follow-btn:hover {
            border-color: #10b981;
            color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #follow-btn.active {
            border-color: #10b981;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #track-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #track-btn.active {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #config-btn:hover {
            border-color: #8b5cf6;
            color: #8b5cf6;
            background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #config-btn.active {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        /* 地图工具组按钮颜色 */
        #zoomin-btn:hover, #zoomout-btn:hover {
            border-color: #52c41a;
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        }
        
        #zoomin-btn.active, #zoomout-btn.active {
            border-color: #52c41a;
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
            color: #fff;
        }
        
        #scale-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
            background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
        }
        
        #scale-btn.active {
            border-color: #722ed1;
            background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
            color: #fff;
        }
        
        #traffic-btn:hover {
            border-color: #fa8c16;
            color: #fa8c16;
            background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
        }
        
        #traffic-btn.active {
            border-color: #fa8c16;
            background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
            color: #fff;
        }
        
        #measure-btn:hover {
            border-color: #13c2c2;
            color: #13c2c2;
            background: linear-gradient(135deg, #e6fffb 0%, #87e8de 100%);
        }
        
        #measure-btn.active {
            border-color: #13c2c2;
            background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
            color: #fff;
        }
        
        #marker-btn:hover {
            border-color: #eb2f96;
            color: #eb2f96;
            background: linear-gradient(135deg, #fff0f6 0%, #ffadd2 100%);
        }
        
        #marker-btn.active {
            border-color: #eb2f96;
            background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
            color: #fff;
        }
        
        #range-btn:hover {
            border-color: #fa541c;
            color: #fa541c;
            background: linear-gradient(135deg, #fff2e8 0%, #ffbb96 100%);
        }
        
        #range-btn.active {
            border-color: #fa541c;
            background: linear-gradient(135deg, #fa541c 0%, #ff7a45 100%);
            color: #fff;
        }
        
        #overview-btn:hover {
            border-color: #2f54eb;
            color: #2f54eb;
            background: linear-gradient(135deg, #f0f5ff 0%, #adc6ff 100%);
        }
        
        #overview-btn.active {
            border-color: #2f54eb;
            background: linear-gradient(135deg, #2f54eb 0%, #597ef7 100%);
            color: #fff;
        }
        
        #refresh-btn:hover {
            border-color: #389e0d;
            color: #389e0d;
            background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
        }
        
        #refresh-btn.active {
            border-color: #389e0d;
            background: linear-gradient(135deg, #389e0d 0%, #52c41a 100%);
            color: #fff;
        }
        
        #clear-btn:hover {
            border-color: #cf1322;
            color: #cf1322;
            background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
        }
        
        #fence-monitor-btn:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
            background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
            box-shadow: 0 8px 20px rgba(255, 77, 79, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #fence-monitor-btn.active {
            border-color: #ff4d4f;
            background: linear-gradient(135deg, #ff4d4f 0%, #f5222d 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(255, 77, 79, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        /* 地图资源组按钮颜色 */
        #vehicles-display-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #vehicles-display-btn.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #stations-display-btn:hover {
            border-color: #52c41a;
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
            box-shadow: 0 8px 20px rgba(82, 196, 26, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #stations-display-btn.active {
            border-color: #52c41a;
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(82, 196, 26, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .map-tool-btn.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            transform: translateY(-1px);
        }
        

        
        .map-tool-btn.active::before {
            content: attr(data-icon-active);
        }
        
        .map-tool-btn.active:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
            border-color: #40a9ff;
        }
        
        /* 车辆工具组按钮 */
        .vehicle-tools {
            display: flex;
            flex-wrap: wrap;
            /* gap: 4px; */
            max-width: 120px;
            justify-content: flex-start;
        }
        
        /* 地图操作组按钮 */
        .map-tools {
            display: flex;
            flex-wrap: wrap;
            /* gap: 4px; */
            max-width: 150px;
            justify-content: flex-start;
        }
        
        /* 范围工具下拉菜单 */
        .range-dropdown {
            position: relative;
            display: inline-block;
        }
        
        /* 范围下拉框容器 */
        .range-dropdown {
            position: relative;
        }
        
        .range-menu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            z-index: 10002;
            min-width: 120px;
            display: none;
            overflow: visible;
            margin-top: 4px;
        }
        
        .range-menu.show {
            display: block;
            animation: fadeInDown 0.2s ease;
        }
        
        .range-menu-item {
            padding: 8px 12px;
          cursor: pointer;
            font-size: 12px;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }
        
        .range-menu-item:last-child {
            border-bottom: none;
        }
        
        .range-menu-item:hover {
            background: #f0f8ff;
            color: #1890ff;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
        }
        }
        
        /* 工具提示 */
        .map-tool-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateX(-100%) translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            z-index: 1002;
        }
        
        .map-tool-btn:hover::after {
            opacity: 1;
        }
        
        /* 鹰眼地图样式 */
        .amap-overview {
            border: 2px solid #fff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
        .amap-marker-label {
            border: none;
        }
        .amap-logo{
            display: none !important;
        }
        .amap-copyright{
            display: none !important;
        }
        /* .BMap_noprint{
          display: none;
        } */



        .edc-listbox .title {
          font-size: 14px;
          font-weight: 400;
          position: relative;
          box-sizing: border-box;
          padding: 12px 12px 12px calc(16px + var(--side-pd));
          line-height: 1.5;
          background: rgb(245,245,245);
          display: flex;
          justify-content: space-between;
        }

        .edc-listbox .title::before {
          content: '';
          position: absolute;
          top: 50%;
          left: var(--side-pd);
          transform: translateY(-50%);
          height: 15px;
          width: 5px;
          background: rgb(17,179,248);
        }

        .title .title-toolbar-list {
          display: flex;
          align-items: center;
        }

        .title .title-toolbar {
          display: flex;
          align-items: center;
        }

        .title .title-toolbar.hide {
          display: none;
        }

        /* 救护车列表滚动条样式 */
        #map-station {
            list-style: none;
        }
        
        /* 救护车列表容器滚动条样式 */
        .edc-listbox section > div[style*="overflow-y: auto"] {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }
        
        /* Webkit浏览器滚动条样式 */
        .edc-listbox section > div[style*="overflow-y: auto"]::-webkit-scrollbar {
            width: 8px;
        }
        
        .edc-listbox section > div[style*="overflow-y: auto"]::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .edc-listbox section > div[style*="overflow-y: auto"]::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .edc-listbox section > div[style*="overflow-y: auto"]::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 车辆右键菜单样式 - 参考首页样式 */
        #vehicle-context-menu {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 1px solid #ddd;
        }
        
        /* EasyUI菜单项样式调整 - 与首页保持一致 */
        .menu-item {
            padding: 6px 12px !important;
            font-size: 14px !important;
            color: #333 !important;
            line-height: 22px !important;
            text-align: left !important;
            vertical-align: middle !important;
            display: block !important;
            cursor: pointer !important;
        }
        
        /* 覆盖EasyUI默认的菜单文字样式 */
        .menu-text,
        .menu-text span {
            font-size: 14px !important;
            line-height: 22px !important;
        }
        
        /* 菜单项悬停效果 - 使用EasyUI默认的蓝色主题 */
        .menu-item:hover {
            background-color: #e0ecff !important;
            color: #000 !important;
            border-color: #99bbe8 !important;
        }
        
        /* 菜单项选中效果 */
        .menu-item-selected {
            background-color: #e0ecff !important;
            color: #000 !important;
            border-color: #99bbe8 !important;
        }
        
        /* 确保菜单文字垂直居中 */
        .menu-item-text {
            line-height: 22px !important;
            vertical-align: middle !important;
        }
        
        /* 车组人员对话框样式 */
        .crew-members-container {
            padding: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .crew-members-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .crew-members-grid {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .crew-member-card {
            width: 120px;
            padding: 15px 10px;
            border: 2px solid #e8e8e8;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .crew-member-card:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 16px rgba(24,144,255,0.3);
            transform: translateY(-2px);
        }
        
        .crew-member-icon {
            font-size: 32px;
            margin-bottom: 8px;
            display: block;
        }
        
        .crew-member-icon.doctor {
            color: #52c41a;
        }
        
        .crew-member-icon.nurse {
            color: #1890ff;
        }
        
        .crew-member-icon.driver {
            color: #faad14;
        }
        
        .crew-member-name {
            font-weight: bold;
            color: #333;
            font-size: 14px;
            margin-bottom: 4px;
        }
        
        .crew-member-position {
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }
        
        .crew-member-phone {
            color: #999;
            font-size: 11px;
            word-break: break-all;
        }
        
        /* 围栏违规提醒区域 */
        .fence-violation-alert {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 77, 79, 0.95);
            color: #fff;
            padding: 12px 20px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(255, 77, 79, 0.4);
            cursor: pointer;
            z-index: 1000;
            font-size: 14px;
            font-weight: bold;
            display: none;
            animation: alertBlink 1s infinite;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            min-width: 200px;
            text-align: center;
        }
        
        .fence-violation-alert:hover {
            background: rgba(255, 77, 79, 1);
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 77, 79, 0.6);
        }
        
        @keyframes alertBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        
        /* 围栏违规详情弹窗 */
        .fence-violation-details {
            max-height: 400px;
            overflow-y: auto;
            padding: 0;
            margin: 0;
        }
        
        .violation-item {
            background: #fff;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .violation-item:hover {
            border-color: #ff4d4f;
            box-shadow: 0 4px 16px rgba(255, 77, 79, 0.2);
        }
        
        .violation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .violation-title {
            font-size: 16px;
            font-weight: bold;
            color: #ff4d4f;
        }
        
        .violation-duration {
            background: #ff4d4f;
            color: #fff;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .violation-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }
        
        .violation-info span {
            padding: 2px 0;
        }
        
        .violation-info .label {
            font-weight: bold;
            color: #333;
        }
        
        .violation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .violation-actions {
            flex-shrink: 0;
        }
        
        .violation-actions button:hover {
            background: #40a9ff !important;
        }

        /* 地图资源统计显示区域 */
        .map-resources-stats {
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
            pointer-events: none;
        }
        
        .resource-stat-item {
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(135, 206, 235, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            color: #333;
        }
        
        .resource-stat-item i {
            font-size: 16px;
            color: #1890ff;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .stat-count {
            color: #1890ff;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
        }
        
        /* 不同资源类型的图标颜色 */
        .resource-stat-item#vehicles-stats i {
            color: #52c41a;
        }
        
        .resource-stat-item#hospitals-stats i {
            color: #ff4d4f;
        }
        
        .resource-stat-item#stations-stats i {
            color: #1890ff;
        }
        
        .resource-stat-item#bloodstations-stats i {
            color: #722ed1;
        }
        
        .resource-stat-item#cdc-stats i {
            color: #fa8c16;
        }

    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false" style="height: 100%;">
        <div id="hangUpDialog" class="easyui-dialog" title="座席挂起" style="width:300px;height:150px;"
             data-options="resizable:false,modal:true,closed:false,closable:false">
            <div style="text-align:center;font-size:16px;color:brown;">
                <div style="height:40px;font-size:16px;color:brown;margin-top: 20px;">本座席已被挂起或处于未登录状态，副屏幕将不能操作，请输入密码解除此状态！！！</div>
            </div>
        </div>
        <div id="second-screen-tabs" class="easyui-tabs" style="width: 100%; height: 100%;">
            <div title="地图调度" style="display: none;" iconCls="icon-map">
                <div style="display: inline-block; width: Calc(100% - 31em); height: 100%; position: relative;">
                    <div id="map-search" style="width: 50%; height: 3em; position: absolute; left: 70px; top: 85px; z-index: 2;">
                        <input type="text" id="map-search-box" style="width: 60%; height: 100%; font-size: 2em;" />
                        <a href="javascript:manualSearch();" style="display: inline-block; position: absolute; top: 0; right: 40%;">
                            <i class="fa fa-search fa-3x"></i>
                        </a>
                        <div id="searchResultPanel" style="border: 1px solid #C0C0C0; width: 100%; height: auto; display: none;"></div>
                    </div>
                    
                    <!-- 地图工具栏 -->
                    <div class="map-toolbar" id="map-toolbar">
                        <!-- 工具栏内容 -->
                        <div class="toolbar-content">
                            <!-- 车辆工具组 -->
                            <div class="toolbar-group" id="vehicle-group">
                                <div class="toolbar-group-title" onclick="toggleToolbarGroup('vehicle-group')">车辆工具</div>
                                <div class="vehicle-tools">
                                    <div id="follow-btn" class="map-tool-btn" data-tooltip="跟随车辆" data-icon="🎯" data-icon-active="⏹️">跟随</div>
                                    <div id="track-btn" class="map-tool-btn" data-tooltip="显示轨迹" data-icon="📍" data-icon-active="👁️">轨迹</div>
                                    <div id="config-btn" class="map-tool-btn" data-tooltip="显示配置" data-icon="⚙️" data-icon-active="✅">配置</div>
                                </div>
                            </div>
                            
                            <!-- 地图操作组 -->
                            <div class="toolbar-group" id="map-tools-group">
                                <div class="toolbar-group-title" onclick="toggleToolbarGroup('map-tools-group')">地图工具</div>
                                <div class="map-tools">
                                    <div id="zoomin-btn" class="map-tool-btn" data-tooltip="放大地图" data-icon="🔍" data-icon-active="🔍">放大</div>
                                    <div id="zoomout-btn" class="map-tool-btn" data-tooltip="缩小地图" data-icon="🔎" data-icon-active="🔎">缩小</div>
                                    <div id="scale-btn" class="map-tool-btn" data-tooltip="比例尺" data-icon="📐" data-icon-active="📏">比例</div>
                                    <div id="traffic-btn" class="map-tool-btn" data-tooltip="路况信息" data-icon="🚦" data-icon-active="🟢">路况</div>
                                    <div id="measure-btn" class="map-tool-btn" data-tooltip="测距工具" data-icon="📏" data-icon-active="✏️">测距</div>
                                    <div id="marker-btn" class="map-tool-btn" data-tooltip="标注工具" data-icon="📌" data-icon-active="✅">标注</div>
                                    <div class="range-dropdown">
                                        <div id="range-btn" class="map-tool-btn" data-tooltip="范围工具" data-icon="⭕" data-icon-active="🎯">范围</div>
                                        <div class="range-menu" id="range-menu">
                                            <div class="range-menu-item" data-radius="1000">1公里</div>
                                            <div class="range-menu-item" data-radius="2000">2公里</div>
                                            <div class="range-menu-item" data-radius="3000">3公里</div>
                                            <div class="range-menu-item" data-radius="5000">5公里</div>
                                            <div class="range-menu-item" data-radius="10000">10公里</div>
                                            <div class="range-menu-item" data-radius="20000">20公里</div>
                                            <div class="range-menu-item" data-radius="30000">30公里</div>
                                            <div class="range-menu-item" data-radius="40000">40公里</div>
                                            <div class="range-menu-item" data-radius="50000">50公里</div>
                                        </div>
                                    </div>
                                    <div id="overview-btn" class="map-tool-btn" data-tooltip="鹰眼地图" data-icon="🗺️" data-icon-active="👁️">鹰眼</div>
                                    <div id="clear-btn" class="map-tool-btn" data-tooltip="清除标注" data-icon="🧹" data-icon-active="🧹">清除</div>
                                    <div id="fence-monitor-btn" class="map-tool-btn" data-tooltip="围栏违规监控" data-icon="🚨" data-icon-active="🔴">围栏</div>
                                </div>
                            </div>
                            
                            <!-- 地图资源组 -->
                            <div class="toolbar-group" id="map-resources-group">
                                <div class="toolbar-group-title" onclick="toggleToolbarGroup('map-resources-group')">地图资源</div>
                                <div class="map-tools">
                                    <div id="vehicles-display-btn" class="map-tool-btn active" data-tooltip="车辆显示" data-icon="🚗" data-icon-active="✅">车辆</div>
                                    <div id="stations-display-btn" class="map-tool-btn" data-tooltip="分站显示" data-icon="🏥" data-icon-active="✅">分站</div>
                                    <div id="hospitals-display-btn" class="map-tool-btn" data-tooltip="医院显示" data-icon="🏥" data-icon-active="✅">医院</div>
                                    <div id="bloodstations-display-btn" class="map-tool-btn" data-tooltip="血站显示" data-icon="🏥" data-icon-active="✅">血站</div>
                                    <div id="cdc-display-btn" class="map-tool-btn" data-tooltip="疾控中心显示" data-icon="🏥" data-icon-active="✅">疾控</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 围栏违规提醒区域 -->
                    <div id="fence-violation-alert" class="fence-violation-alert" onclick="showFenceViolationDetails()">
                        <span id="violation-count">0</span> 辆车违规超出围栏
                    </div>
                    
                    <!-- 地图资源统计显示区域 -->
                    <div id="map-resources-stats" class="map-resources-stats">
                        <div id="vehicles-stats" class="resource-stat-item" style="display: none;">
                            <i class="fa fa-ambulance"></i>
                            <span class="stat-label">车辆:</span>
                            <span id="vehicles-count" class="stat-count">0</span>
                        </div>
                        <div id="hospitals-stats" class="resource-stat-item" style="display: none;">
                            <i class="fa fa-hospital-o"></i>
                            <span class="stat-label">医院:</span>
                            <span id="hospitals-count" class="stat-count">0</span>
                        </div>
                        <div id="stations-stats" class="resource-stat-item" style="display: none;">
                            <i class="fa fa-building"></i>
                            <span class="stat-label">分站:</span>
                            <span id="stations-count" class="stat-count">0</span>
                        </div>
                        <div id="bloodstations-stats" class="resource-stat-item" style="display: none;">
                            <i class="fa fa-medkit"></i>
                            <span class="stat-label">血站:</span>
                            <span id="bloodstations-count" class="stat-count">0</span>
                        </div>
                        <div id="cdc-stats" class="resource-stat-item" style="display: none;">
                            <i class="fa fa-shield"></i>
                            <span class="stat-label">疾控:</span>
                            <span id="cdc-count" class="stat-count">0</span>
                        </div>
                    </div>
                    
                    <div id="the-map" style="width: 100%; height: 100%; overflow: hidden; margin: 0;"></div>
                </div>
                <div style="width: 30em; height: 100%; position: relative; right: 0; top: 0; display: inline-block;">
                    <div id="config-dialog" class="easyui-dialog" title="显示配置" style="width:160px;height:170px;left: 1260px;top: 76px;"
                         data-options="">
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="fenzhan-checkbox" name="fenzhan-checkbox" checked value="1" label="所属分站">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="chepai-checkbox" name="config2" checked value="2" label="车牌号">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="sudu-checkbox" name="config3" checked value="3" label="速度">
                        </div>
                        <div style="margin: 10px 10px 0 15px;">
                            <input class="easyui-checkbox" id="status-checkbox" name="config4" checked value="4" label="状态">
                        </div>
                    </div>
                    <div style="width: 100%; float: left; height: Calc(100% - 0.2em); display: flex; flex-direction: column; font-size: 1.6em;" class="edc-listbox">
                        <section style="flex-shrink: 0;">
                            <div class="title">
                                <span>救治能力</span>
                                <ul class="title-toolbar-list">
                                    <!-- 重置按钮 -->
                                    <li class="title-toolbar title-toolbar-reset hide" style="margin-right: 8px;">
                                        <span style="color: rgb(17,179,248); cursor: pointer" onclick="cleanRescueSelected()">重置</span>
                                    </li>
                                    <!-- 全选按钮 -->
                                    <li class="title-toolbar rescue-all-select hide">
                                        <input style="width: 14px; height: 14px; margin: 0 4px" type="checkbox" id="rescue-all-checkbox" name="rescue-all-checkbox" />
                                        <label style="line-height: 1" for="rescue-all-checkbox">全选</label>
                                    </li>
                                </ul>
                            </div>
                            <form id="rescue-center-form">
                                <ul class="checkbox-list">
                                    <!--                          <li>
                                                        <input type="checkbox" id="1" name="rescueCenter" />
                                                        <label for="1">胸</label>
                                                      </li>
                                                      <li>
                                                        <input type="checkbox" id="2" name="rescueCenter" />
                                                        <label for="2">卒</label>
                                                      </li>
                                                      <li>
                                                        <input type="checkbox" id="3" name="rescueCenter" checked />
                                                        <label for="3">创</label>
                                                      </li>
                                                      <li>
                                                        <input type="checkbox" id="4" name="rescueCenter" />
                                                        <label for="4">毒</label>
                                                      </li>
                                                      <li>
                                                        <input type="checkbox" id="5" name="rescueCenter" />
                                                        <label for="5">妇</label>
                                                      </li>
                                                      <li>
                                                        <input type="checkbox" id="6" name="rescueCenter" />
                                                        <label for="6">AED</label>
                                                      </li>-->
                                </ul>
                            </form>
                        </section>
                        <section style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">
                            <div class="title" style="display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                                <span>救护车列表</span>
                                <div style="margin-right: 10px;">
                                    <label style="margin-right: 10px; line-height: 1;">
                                        <input type="checkbox" id="dispatch-status-waiting" name="dispatch-status" value="waiting" style="margin-right: 3px;"> 待派
                                    </label>
                                    <label style="line-height: 1;">
                                        <input type="checkbox" id="dispatch-status-dispatched" name="dispatch-status" value="dispatched" style="margin-right: 3px;"> 调派
                                    </label>
                            </div>
                            </div>
                            <div style="flex: 1; overflow-y: auto; overflow-x: hidden;">
                                <ul id="map-station" style="margin: 0; padding: 0;"></ul>
                            </div>
                            <!-- 车辆右键菜单 -->
                            <div id="vehicle-context-menu" class="easyui-menu" style="width:150px; display: none;">
                                <div id="menu-call-station">与分站通话</div>
                                <div id="menu-call-vehicle">与车辆通话</div>
                                <div id="menu-call-crew">与车组人员通话</div>
                                <div class="menu-sep"></div>
                                <div id="menu-vehicle-resend">补发</div>
                                <div class="menu-sep"></div>
                                <div id="menu-vehicle-condition">修改车况</div>
                                <div class="menu-sep"></div>
                                <div id="menu-track-replay">轨迹回放</div>
                            </div>
                            <!-- 车辆类型选择区域 -->
                            <div id="vehicle-type-section" style="display: none; background-color: #f9f9f9; flex-shrink: 0;">
                                <div class="title" style="display: flex; justify-content: space-between; align-items: center; margin: 0; padding: 12px 12px 12px calc(16px + var(--side-pd)); background: transparent; font-size: 12px;">
                                    <span style="font-weight: bold;">车辆类型</span>
                                    <div id="vehicle-type-checkboxes" style="font-size: 12px;">
                                        <!-- 车辆类型多选框将通过JavaScript动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div title="电话簿" style="display: none;" iconCls="icon-phonebooks">
                <iframe name="phoneBooks" id="phoneBooksIframe" scrolling="no" src="phonebookes.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="事件列表" style="display: none;" iconCls="icon-tasklist">
                <iframe name="eventList" id="eventListIframe" scrolling="no" src="eventList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="通话列表" style="display: none;" iconCls="icon-calllist">
                <iframe name="callList" id="callListIframe" scrolling="no" src="callList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="预约事件" style="display: none;" iconCls="icon-eventReservation">
                <iframe name="eventReservation" id="eventReservationIframe" scrolling="no" src="eventReservationList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="视频呼救" style="display: none;" iconCls="icon-calllVideoList">
                <iframe name="videoCallList" id="videoCallListIframe" scrolling="no" src="videoCallList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="电子围栏" style="display: none;" iconCls="icon-fenceViolationRecord-tab">
                <iframe name="fenceViolationRecord" id="fenceViolationRecordIframe" scrolling="no" src="fenceViolationRecord.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <!-- <div title="街道列表" style="display: none;" iconCls="icon-search">
                <iframe name="streetList" id="streetListIframe" scrolling="auto" src="streetList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div> -->
            <div title="个人交接班" style="display: none;" iconCls="icon-shiftRecord-tab">
                <iframe name="shiftSeatRecordList" id="shiftSeatRecordIframe" scrolling="no" src="shiftRecord.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="班组交接班" style="display: none;" iconCls="icon-shiftRecord-tab">
                <iframe name="scheduleShiftSummaryList" id="scheduleShiftSummaryIframe" scrolling="no" src="scheduleShiftSummary.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="重大事件" style="display: none;" iconCls="icon-largeEvent">
                <iframe name="largerEvent" id="largerEventIframe" scrolling="no" src="largerEvent.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="记事本" style="display: none;" iconCls="icon-show">
                <iframe name="notebook" id="notebookIframe" scrolling="no" src="notebook.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="满意度回访" style="display: none;" iconCls="icon-large-smartart">
                <iframe name="visitList" id="visitListIframe" scrolling="no" src="visitList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="车辆报停" style="display: none;" iconCls="icon-vehicle-stop">
                <iframe name="vehicleStopList" id="vehicleStopListIframe" scrolling="no" src="vehicleStopList.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
            <div title="区域联动" style="display: none;" iconCls="icon-regionLinkage">
                <iframe name="regionLinkage" id="regionLinkageIframe" scrolling="no" src="regionLinkage.html" frameborder="0" style="width:100%;height:100%;"></iframe>
            </div>
        </div>
    </div>
    <!--播放报警声音-->
    <audio id="audioaAlert" src="./wav/alert.wav" controls="controls" loop="true" hidden="true"></audio>
    <!--播放拒绝派遣声音-->
    <audio id="audioRefuseDispatch" src="./wav/refuseToSend.wav" controls="controls" loop="false" hidden="true"></audio>

    <!-- 任务出车人员设置界面 - 副屏专用 -->
    <div id="confirm-receive-editor-second" class="easyui-window" 
         data-options="title:'任务出车人员设置',modal:true,collapsible:false,resizable:false,maximizable:false,closed:true" 
         style="width:400px;height:360px;text-align:center;padding-top:20px;">
        <form id="confirm-receive-form-second" method="post" enctype="application/x-www-form-urlencoded">
            <table tableframe=void style="padding-top:30px">
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;"><label>选择班次:</label></td>
                    <td style="border-style:none;"><select id="dispatchClassesDri-second" name="dispatchClassesDri-second" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></select></td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;"><label>出车医生:</label></td>
                    <td style="border-style:none;"><select id="dispatchDoctorDri-second" name="dispatchDoctorDri-second" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"><font color="red">*</font></select></td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;"><label>出车护士:</label></td>
                    <td style="border-style:none;"><select id="dispatchNurseDri-second" name="dispatchNurseDri-second" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"></select></td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;"><label>出车司机:</label></td>
                    <td style="border-style:none;"><select id="dispatchDriverDri-second" name="dispatchDriverDri-second" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"></select></td>
                </tr>
                <tr>
                    <td style="width: 100px;text-align:right;border-style:none;"><label>出车护工:</label></td>
                    <td style="border-style:none;"><select id="dispatchWorkerDri-second" name="dispatchWorkerDri-second" class="easyui-combobox" style="width:250px;line-height:20px;border:1px;"></select></td>
                </tr>
            </table>
            
            <!-- 隐藏字段用于存储必要信息 -->
            <input type="hidden" id="dispatchEventId-second" />
            <input type="hidden" id="dispatchCarId-second" />
            <input type="hidden" id="mobileProcessId-second" />
            
            <a href="javascript:confirmReceiveSubmitSecondScreen();" id="confirmReceiveSubmitBtn-second" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 20px 10px 0 0;">确认发车</a>
        </form>
    </div>

    <script src="script/gpsConvert.js"></script>

    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>

    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <!-- <script type="text/javascript" src="script/secondScr.js"></script> -->
    <script type="text/javascript" src="script/secondScr_gaode.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript">
        console.log('=== second.html 内联脚本开始执行 ===', new Date().toISOString());
        console.log('second.html 中 gBsProxy 类型:', typeof gBsProxy);
        console.log('second.html 中 gProxy 类型:', typeof gProxy);
        
        // 输出屏幕参数信息
        console.log('=== 当前副屏幕参数信息 ===');
        console.log('屏幕ID:', getScreenId());
        
        //兼容web，如果非C#中执行，那么gProxy是未定义的，使用自定义的gProxy
        _baseUrl = "";
        bsProxy = null;
        //读取服务器接口地址
        try {
            console.log('second.html 尝试调用 gProxy.getBaseUrl()...');
            _baseUrl = gProxy.getBaseUrl();
            console.log("second.html C/S模式执行，baseUrl:", _baseUrl);
        } catch (e) {
            console.log("second.html B/S模式执行，错误信息:", e.message);
            console.log('second.html 准备创建 bsProxy...');
            console.log('second.html gBsProxy 是否定义:', typeof gBsProxy);
            
            if (typeof gBsProxy === 'undefined') {
                console.error('second.html 中 gBsProxy 未定义！');
            } else {
                bsProxy = new gBsProxy(document.body);
                _baseUrl = bsProxy.getBaseUrl();
                console.log('second.html 创建 bsProxy 成功，baseUrl:', _baseUrl);
            }
        }
        
        // 根据屏幕ID进行特定初始化
        function initializeScreenById() {
            var screenId = getScreenId();
            
            console.log('根据屏幕ID进行初始化:', screenId);
            
            switch(screenId) {
                case 1:
                    console.log('初始化第1号副屏幕...');
                    // 这里可以添加第1号屏幕的特定初始化逻辑
                    break;
                    
                case 2:
                    console.log('初始化第2号副屏幕...');
                    // 这里可以添加第2号屏幕的特定初始化逻辑
                    break;
                    
                case 3:
                    console.log('初始化第3号副屏幕...');
                    // 这里可以添加第3号屏幕的特定初始化逻辑
                    break;
                    
                default:
                    console.log('未指定屏幕ID或使用默认初始化逻辑...');
            }
        }
        
        // 延迟执行初始化，确保页面完全加载
        setTimeout(initializeScreenById, 100);
        
        console.log('=== second.html 内联脚本执行完成 ===');
        //var tabs = ['', '', 'eventListIframe', 'callListIframe', 'streetListIframe', 'seatsListIframe', 'shiftSeatRecordIframe', 'scheduleShiftSummaryIframe', 'largerEventIframe']
        var tabs = ['', '', 'eventListIframe', 'callListIframe', 'videoCallListIframe', 'fenceViolationRecordIframe', 'shiftSeatRecordIframe', 'scheduleShiftSummaryIframe', 'largerEventIframe', 'notebookIframe', 'visitListIframe']
        //页面加载完成后执行
        window.onload = function(){
            $('#second-screen-tabs').tabs({
                onSelect: function (title,index) {
                  $('#config-dialog').window('close');
                  //判断是否有iframe
                  if(tabs[index]){
                      let iframe = document.getElementById(tabs[index])
                      console.log("==========================================",iframe)
                      //判断是否有子方法 如果有就尝试调用
                      iframe && iframe.contentWindow && iframe.contentWindow.childFnt && iframe.contentWindow.childFnt()
                  }
                }
            })
            
            // 初始化围栏违规监控功能
            if (typeof initFenceMonitor === 'function') {
                initFenceMonitor();
            }
            // $(document).on('click', function(event) {
            //   var targetElement = event.target; // 获取点击的目标元素
            //   var containerElement = $('#config-dialog'); // 获取需要监听的容器元素
            //   var containerElement2 = $('#configs'); // 获取需要监听的容器元素
            //   // 检查点击的目标元素是否在容器元素内
            //   if (!containerElement.is(targetElement) && containerElement.has(targetElement).length === 0 && !containerElement2.is(targetElement) && containerElement2.has(targetElement).length === 0) {
            //     // 执行你的代码
            //     if (configDialog) {// 配置弹窗打开了 点击其他地方都关闭
            //       $('#config-dialog').window('close', true);
            //     }
            //   }
            // });
        }

    </script>
</body>
</html>
