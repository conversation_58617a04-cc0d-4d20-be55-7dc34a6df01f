<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>双击定位功能测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 32px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #fff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        .feature-card .icon {
            font-size: 24px;
            margin-right: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
        }
        .feature-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .feature-steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .feature-steps h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .feature-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .feature-steps li {
            margin-bottom: 8px;
            color: #555;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-implemented {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .expected-behavior {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .expected-behavior h4 {
            color: #1976d2;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight h4 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
            border: 2px dashed #dee2e6;
        }
        .test-section h2 {
            color: #495057;
            margin-top: 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 双击定位功能测试指南</h1>
            <p>市县联动系统 - 数据模块管理与任务信息管理双击定位功能</p>
        </div>

        <div class="feature-grid">
            <!-- 医院双击定位 -->
            <div class="feature-card">
                <h3>
                    <span class="icon">🏥</span>
                    医院双击定位
                </h3>
                <span class="status-badge status-implemented">✅ 已实现</span>
                
                <div class="feature-description">
                    在数据模块管理中双击医院列表行，自动定位到指定医院地图位置，同时开启医院图标显示。
                </div>

                <div class="feature-steps">
                    <h4>测试步骤：</h4>
                    <ol>
                        <li>点击顶部"数据查询"按钮</li>
                        <li>切换到"医院管理"标签页</li>
                        <li>双击任意医院行</li>
                    </ol>
                </div>

                <div class="expected-behavior">
                    <h4>预期行为：</h4>
                    <ul>
                        <li>数据模块窗口自动关闭</li>
                        <li>地图资源中的医院图标自动开启</li>
                        <li>地图定位到指定医院位置（16级缩放）</li>
                        <li>显示临时高亮标记（5秒后消失）</li>
                        <li>显示定位成功提示信息</li>
                    </ul>
                </div>

                <div class="code-snippet">
function locateHospitalOnMap(hospital) {
    // 关闭数据模块窗口
    $('#dataPanel').window('close');
    
    // 开启医院图标显示
    const hospitalBtn = document.getElementById('hospitals-display-btn');
    if (!hospitalBtn.classList.contains('active')) {
        hospitalBtn.classList.add('active');
        loadHospitalsData();
    }
    
    // 地图定位
    cityCountyMap.setZoomAndCenter(16, [hospital.longitude, hospital.latitude]);
}
                </div>
            </div>

            <!-- 血站双击定位 -->
            <div class="feature-card">
                <h3>
                    <span class="icon">🩸</span>
                    血站双击定位
                </h3>
                <span class="status-badge status-implemented">✅ 已实现</span>
                
                <div class="feature-description">
                    在数据模块管理中双击血站列表行，自动定位到指定血站地图位置，同时开启血站图标显示。
                </div>

                <div class="feature-steps">
                    <h4>测试步骤：</h4>
                    <ol>
                        <li>点击顶部"数据查询"按钮</li>
                        <li>切换到"血站管理"标签页</li>
                        <li>双击任意血站行</li>
                    </ol>
                </div>

                <div class="expected-behavior">
                    <h4>预期行为：</h4>
                    <ul>
                        <li>数据模块窗口自动关闭</li>
                        <li>地图资源中的血站图标自动开启</li>
                        <li>地图定位到指定血站位置</li>
                        <li>显示临时高亮标记</li>
                        <li>显示定位成功提示信息</li>
                    </ul>
                </div>
            </div>

            <!-- 疾控中心双击定位 -->
            <div class="feature-card">
                <h3>
                    <span class="icon">🏛️</span>
                    疾控中心双击定位
                </h3>
                <span class="status-badge status-implemented">✅ 已实现</span>
                
                <div class="feature-description">
                    在数据模块管理中双击疾控中心列表行，自动定位到指定疾控中心位置，同时开启疾控图标显示。
                </div>

                <div class="feature-steps">
                    <h4>测试步骤：</h4>
                    <ol>
                        <li>点击顶部"数据查询"按钮</li>
                        <li>切换到"疾控中心"标签页</li>
                        <li>双击任意疾控中心行</li>
                    </ol>
                </div>

                <div class="expected-behavior">
                    <h4>预期行为：</h4>
                    <ul>
                        <li>数据模块窗口自动关闭</li>
                        <li>地图资源中的疾控图标自动开启</li>
                        <li>地图定位到指定疾控中心位置</li>
                        <li>显示临时高亮标记</li>
                        <li>显示定位成功提示信息</li>
                    </ul>
                </div>
            </div>

            <!-- 任务双击定位 -->
            <div class="feature-card">
                <h3>
                    <span class="icon">📋</span>
                    任务双击定位
                </h3>
                <span class="status-badge status-implemented">✅ 已实现</span>
                
                <div class="feature-description">
                    在任务信息管理中双击任务列表行，自动定位到指定任务位置，同时开启事故图标显示。
                </div>

                <div class="feature-steps">
                    <h4>测试步骤：</h4>
                    <ol>
                        <li>点击顶部"任务信息"按钮</li>
                        <li>双击任意任务行</li>
                    </ol>
                </div>

                <div class="expected-behavior">
                    <h4>预期行为：</h4>
                    <ul>
                        <li>任务信息窗口自动关闭</li>
                        <li>地图资源中的事故图标自动开启</li>
                        <li>地图定位到指定任务位置</li>
                        <li>显示临时高亮标记</li>
                        <li>显示定位成功提示信息</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="highlight">
            <h4>🔧 技术实现要点</h4>
            <ul>
                <li><strong>统一的双击事件处理：</strong>所有表格都添加了 onDblClickRow 事件处理</li>
                <li><strong>自动开启对应图标：</strong>检查按钮状态，自动激活对应的地图资源显示</li>
                <li><strong>智能窗口管理：</strong>双击后自动关闭当前打开的EasyUI窗口</li>
                <li><strong>临时高亮标记：</strong>使用动画效果突出显示定位位置，5秒后自动消失</li>
                <li><strong>友好的用户反馈：</strong>显示定位成功提示和错误处理</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 完整测试流程</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                    <h4 style="color: #3498db; margin-top: 0;">1. 医院定位测试</h4>
                    <p>数据查询 → 医院管理 → 双击医院行</p>
                    <small>验证医院图标开启和地图定位</small>
                </div>
                
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #e74c3c;">
                    <h4 style="color: #e74c3c; margin-top: 0;">2. 血站定位测试</h4>
                    <p>数据查询 → 血站管理 → 双击血站行</p>
                    <small>验证血站图标开启和地图定位</small>
                </div>
                
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2ecc71;">
                    <h4 style="color: #2ecc71; margin-top: 0;">3. 疾控中心定位测试</h4>
                    <p>数据查询 → 疾控中心 → 双击疾控中心行</p>
                    <small>验证疾控图标开启和地图定位</small>
                </div>
                
                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12;">
                    <h4 style="color: #f39c12; margin-top: 0;">4. 任务定位测试</h4>
                    <p>任务信息 → 双击任务行</p>
                    <small>验证事故图标开启和地图定位</small>
                </div>
            </div>
        </div>

        <div style="background: #e8f5e8; border: 1px solid #4caf50; border-radius: 8px; padding: 20px; margin-top: 30px; text-align: center;">
            <h3 style="color: #2e7d32; margin-top: 0;">✅ 功能实现完成</h3>
            <p style="color: #388e3c; margin-bottom: 0;">
                所有双击定位功能已成功实现，包括自动开启对应图标显示、窗口管理、地图定位和用户反馈。
                <br>请按照上述测试流程验证各项功能的正确性。
            </p>
        </div>
    </div>
</body>
</html>
