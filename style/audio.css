@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.green-audio-player {
    min-width: 300px;
    height: 56px;
    -webkit-box-shadow: 0 4px 16px 0 rgba(0, 0, 0, .07);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, .07);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 24px;
    padding-right: 24px;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fff;
    position: relative;
}

.green-audio-player img,
.green-audio-player svg {
    display: block
}

.green-audio-player .play-pause-btn {
    display: none;
    cursor: pointer
}

.green-audio-player .loading .loading__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #b0b0b0;
    border-right-color: transparent;
    border-radius: 50%;
    -webkit-animation: spin .4s linear infinite;
    animation: spin .4s linear infinite
}

.green-audio-player .slider {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    background-color: #d8d8d8;
    cursor: pointer;
    position: relative
}

.green-audio-player .slider .progress {
    background-color: #78797e;
    border-radius: inherit;
    position: absolute;
    pointer-events: none
}

.green-audio-player .slider .progress .pin {
    height: 16px;
    width: 16px;
    border-radius: 8px;
    background-color: #ffffff;
    position: absolute;
    pointer-events: all;
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .32);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .32)
}

.green-audio-player .controls {
    font-family: Roboto, sans-serif;
    font-size: 16px;
    line-height: 18px;
    color: #55606e;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: 24px;
    margin-right: 24px;
    position: relative;
}

.green-audio-player .controls .controls__slider {
    margin-left: 16px;
    margin-right: 16px;
    border-radius: 2px;
    height: 4px
}

.green-audio-player .controls .controls__slider .controls__progress {
    width: 0;
    height: 100%
}

.green-audio-player .controls .controls__slider .controls__progress .progress__pin {
    right: -8px;
    top: -6px
}

.green-audio-player .controls span {
    cursor: default
}

.green-audio-player .volume {
    position: relative
}

.green-audio-player .volume .volume__button {
    cursor: pointer
}

.green-audio-player .volume .volume__button.open path {
    fill: #0c64eb;
}

.green-audio-player .volume .volume__controls {
    width: 30px;
    height: 135px;
    background-color: rgba(0, 0, 0, .62);
    border-radius: 7px;
    position: absolute;
    left: -3px;
    bottom: 52px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.green-audio-player .volume .volume__controls.hidden {
    display: none
}

.green-audio-player .volume .volume__controls.top {
    bottom: 52px;
    left: -3px
}

.green-audio-player .volume .volume__controls.middle {
    bottom: -54px;
    left: 54px
}

.green-audio-player .volume .volume__controls.bottom {
    bottom: -164px;
    left: -3px
}

.green-audio-player .volume .volume__controls .volume__slider {
    margin-top: 12px;
    margin-bottom: 12px;
    width: 6px;
    border-radius: 3px
}

.green-audio-player .volume .volume__controls .volume__slider .volume__progress {
    bottom: 0;
    height: 100%;
    width: 6px
}

.green-audio-player .volume .volume__controls .volume__slider .volume__progress .volume__pin {
    left: -5px;
    top: -8px
}
.green-audio-player .download{
    margin-left: 15px;
}
.green-audio-player .download .download-img{
    height:30px;
    cursor: pointer;
}