@font-face {
    font-family: fun;
    src: url('fonts/zhankufun.ttf');
}

html, body {
    height: 100%;
}

body {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    background-color: white;
    color: #4D4D4D;
    /*font-family: fun;*/
}

div {
    margin: 0;
}

a {
    /*text-decoration: none;*/
    color: #099DFC;
    /*text-shadow: gray 1px 1px 3px;*/
}

table {
    word-break: break-all;
    border-collapse: collapse;
    border-spacing: 0;
}

    table td {
        border: 1px solid;
        overflow: hidden;
    }

input:not([type="checkbox"]) {
    /* border-radius: 3px; */
    box-sizing: border-box;
    vertical-align: middle;
    border: 1px solid #ccc;
    border-radius: 3px;
}

/* checkbox特殊样式 */
input[type="checkbox"] {
    zoom: 100%;
    position: relative;
    top: -1px;
    width: 18px;
    height: 18px;
    margin: 0;
    vertical-align: middle;
}

    input [type=datetime-local] {
        padding: 5px;
    }

    input[readonly], textarea[readonly] {
        background: #dddddd;
    }

textarea {
    border-radius: 3px;
    padding: 5px;
    box-sizing: border-box;
    vertical-align: middle;
    line-height: 1.4;
    border: 1px solid #ccc;
    font-size: 1em;
}

.clear-fix:after {
    content: "";
    display: block;
    clear: both;
}

.dragmove {
    /*-webkit-app-region: drag;*/
}

.nodragmove {
    _-webkit-app-region: no-drag;
}

.text-justify {
    text-align: justify;
}

    .text-justify:after {
        display: inline-block;
        width: 100%;
        content: '';
    }

.title-button {
    width: 16px;
    height: 16px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: 16px 16px;
}

.common-button {
    text-decoration: none;
    cursor: pointer;
    background: #099DFC;
    color: white;
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    border: #000000 0 solid;
}

    .common-button:hover {
        background: #0880d9;
    }

.max-btn {
    background-image: url(img/maximize.png);
}

.min-btn {
    background-image: url(img/minimize.png);
}

.close-btn {
    background-image: url(img/close.png);
}

.titlebar {
    width: 100%;
    height: 1.5em;
    font-size: 1.2em;
    line-height: 1.5em;
    vertical-align: middle;
    /*background-color: #A64A00;*/
    color: #6949D7;
    padding: 0 5px 0 5px;
    float: left;
    cursor: default;
}

.sys-commands {
    list-style: none;
    display: inline-block;
    margin: 0;
    -webkit-app-region: no-drag;
    float: right;
}

    .sys-commands li {
        display: inline-block;
        cursor: pointer;
    }


.group-box {
    min-width: 100px;
    min-height: 100px;
    border: solid #E2E7EA 1px;
    border-radius: 5px;
    margin: 15px 5px 5px 5px;
    padding: 0 16px;
}

    .group-box > .group-box-title {
        height: 34px;
        width: Calc(100% + 32px);
        border-radius: 5px 5px 0 0;
        line-height: 34px;
        vertical-align: middle;
        font-size: 15px;
        color: #013F61;
        font-weight: bold;
        background: url(img/grouptitle.png) repeat-x;
        display: inline-block;
        padding: 0 15px;
        margin: 0 -16px;
    }

.waitBox {
    position: absolute;
    background: #1ACAF555;
    color: white;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

    .waitBox > div {
        width: 3em;
        height: 3em;
        margin: 50px auto;
    }

.edc-table {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    border: solid #B3D2E1 1px;
    text-align: center;
    color: #6D6D6E;
    font-size: 14px;
    table-layout: fixed;
}

    .edc-table thead {
        background: url(img/tabletitle.png) repeat-x;
        width: 100%;
        font-size: 15px;
        color: #013F61;
    }

        .edc-table thead tr {
            height: 30px;
            display: table;
            width: Calc(100% - 1em - 2px);
        }

    .edc-table tbody {
        background: white;
        height: 100%;
        display: block;
        overflow-y: auto;
        width: 100%;
    }

        .edc-table tbody tr {
            cursor: pointer;
            height: 36px;
            display: table;
            width: 100%;
        }

            .edc-table tbody tr:hover {
                background: #F5F9FA;
            }

            .edc-table tbody tr:nth-of-type(even) {
                background: #F7FBFC;
            }

    .edc-table td {
        border: solid #B3D2E1 1px;
    }

    .edc-table th {
        border: solid #B3D2E1 1px;
    }


.select-common {
    font-size: 1em;
    height: 2em;
    border-radius: 3px;
    margin-left: 0px;
    box-sizing: border-box;
    vertical-align: middle;
    line-height: 2em;
    border: 1px solid #ccc;
}

/* 所有select元素统一样式 */
select {
    border: 1px solid #ccc;
    border-radius: 3px;
    box-sizing: border-box;
    vertical-align: middle;
    font-size: 1em;
}

/* 禁用状态的select样式 */
select:disabled {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    cursor: not-allowed;
}

.edc-listbox {
    list-style: none;
    margin: 0;
    border: solid #B3D2E1 1px;
    background: white;
    color: #4B4C4C;
    overflow-y: scroll;

    --side-pd: 16px;
}



    .edc-listbox .checkbox-list {
        position: relative;
        display: flex;
        padding: 12px var(--side-pd);
        font-size: 14px;
        justify-content: space-between;
    }

    .edc-listbox .checkbox-list > li > label {
        --label-size: 40px;

        display: block;
        border-radius: 50%;
        border: 1px solid rgba(0,0,0,0.08);
        width: var(--label-size);
        height: var(--label-size);
        line-height: var(--label-size);
        text-align: center;
        background: #FFFFFF;
        color: #000000;
    }

    .edc-listbox .checkbox-list > li > input:checked + label {
        background: #11B3F8;
        color: #ffffff;
    }

    .edc-listbox .checkbox-list input {
        opacity: 0.2;
        position: absolute;
        z-index: -1;
    }

    .edc-listbox .station-group {
        margin-top: 4px;
        padding: 0 var(--side-pd);
    }

    .edc-listbox .station-group > h3 {
        padding: 0;
        margin: 0;
        font-size: 14px;
        line-height: 2.25;
    }

    .edc-listbox ul {
        padding: 0;
        margin: 0;
    }

    .edc-listbox li {
        list-style: none;
    }

    .edc-listbox .station-group-list {
        margin: 0 -8px;
    }

    .edc-listbox .station-group-list .line {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 400;
        box-sizing: border-box;
        padding: 8px;
        line-height: 1.5;
        cursor: pointer;
    }

    .edc-listbox .station-group-list .line:hover {
        background: rgb(237,248,255);
    }

    .edc-listbox .station-group-list .line.selected {
        background: rgba(17,179,248,0.2);
    }

#mainPanel_McWinformMVC {
    /* height: calc(100% - 1.5em - 10px); */
}

@keyframes flashanim {
    50% {
        color: #00000000;
    }
}

.anim-flash {
    animation: flashanim 1s infinite;
}

.tr-red {
    background: red;
}

.vertical-center {
    position: relative;
    top: 50%;
    left: 50%;
    right: 0;
    bottom: 0;
    transform: translate(-50%, 50%);
}

.inAnHour, .inToday {
    font-size: 12px;
}

#header {
    height: 87px;
    overflow: hidden;
    color: white;
    background: linear-gradient(to right,#0085ff,#1acaf5);
}

    #header .logo {
        display: inline-block;
        background: url(img/logo.png) no-repeat;
        background-size: 64px 64px;
        float: left;
        width: 64px;
        height: 64px;
        margin: 12px 0 0 27px;
    }

    #header .title {
        width: 348px;
        height: 31px;
        display: inline-block;
        float: left;
        margin: 28px 0 0 22px;
    }

    #header .account {
        display: inline-block;
        width: Calc(100% - 27px - 22px - 348px - 64px - 414px - 24px - 5px);
        height: 87px;
        float: left;
        text-align: center;
        font-size: 1em;
    }

        #header .account > span {
            display: inline-block;
            margin-top: 30px;
        }

        #header .account .logout {
            display: inline-block;
            text-decoration: none;
            width: 65px;
            height: 30px;
            line-height: 30px;
            vertical-align: middle;
            background: url(img/huanban.png) repeat-x;
            color: #013F61;
            border-radius: 2px;
            font-size: 1em;
        }

    #header .timezone {
        width: 414px;
        height: 40px;
        line-height: 40px;
        vertical-align: middle;
        display: inline-block;
        float: right;
        text-align: center;
        margin: 23px 24px 0 0;
        background: #B9EBFD;
        border-radius: 20px;
        color: #013F61;
        font-size: 0.8em;
        padding: 0 10px;
    }

        #header .timezone span {
            margin: 0 10px;
        }

#loginPanel {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #2A4580;
    z-index: 100;
}

#mainFrame {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 0;
}

#call-queue {
    width: Calc(100% - 20px);
    height: Calc(100% - 175px);
}

#call-records {
    width: Calc(100% - 20px);
    height: 100%;
}

#event-records {
    width: Calc(100% - 20px);
    height: 100%;
}

#event-calls {
    width: 100%;
    height: Calc(100% - 55px);
    margin: 10px 0;
}

#eventDetailRegion {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: Rgba(128, 128, 128, 0.7);
}

#eventDetail {
    position: relative;
    left: 10%;
    top: 10%;
    width: 80%;
    height: 80%;
    border: solid #ADB1B3;
    border-width: 0 1px 1px 1px;
    background: white;
}

    #eventDetail .detail-title {
        background: #099DFC;
        height: 35px;
        line-height: 35px;
        vertical-align: middle;
        width: 100%;
        color: white;
        font-size: 14px;
    }

#windowHandlePanel .l-btn {
    background: white;
    color: #099DFC;
    height: 28px;
    width: 85px;
    font-size: 13px;
    border-radius: 13px;
    margin-right: 30px;
}

#sendEvent {
    background: red;
    color: white;
    width: 100%;
}

    #sendEvent .l-btn-text {
        font-size: 1em;
    }

#detail-left {
    background: white;
    width: 80%;
    height: 415px;
    display: inline-block;
    float: left;
    padding: 10px;
}

    #detail-left > form > div {
        /* padding: 5px; */
        /*border-bottom: solid 1px black;*/
    }

        #detail-left > form > div input:not([type="checkbox"]) {
            font-size: 1em;
            height: 2em;
            padding: 0 5px;
            line-height: 2em;
            box-sizing: border-box;
            vertical-align: middle;
            /* border-radius: 3px;*/
        }

        #detail-left > form > div > span:first-child {
            display: inline-block;
            width: 5em;
            text-align: right;
        }

		#detail-left > form > div > span:nth-child(2) {
            display: inline-block;
            width: 5em;
            text-align: right;
        }

#region-call-voices {
    width: 20%;
    height: 435px;
    background: white;
    display: inline-block;
    float: left;
}

#second-screen-tabs .tabs-title {
    font-size: 14px;
}

.datagrid-cell {
    font-size: 12px;
}

    .datagrid-cell a {
        color: #5e2580;
    }

.datagrid-row-selected a {
    color: yellow;
}

#system-config {
    font-size: 1em;
}

    #system-config > div {
        min-height: 2.5em;
        padding: 5px 0 5px 0;
        border-bottom: solid 1px #6e86d6;
    }

        #system-config > div > span:first-child {
            width: 20%;
            text-align: right;
            display: inline-block;
        }

        #system-config > div > span:nth-child(2) {
            width: 30%;
            display: inline-block;
        }

        #system-config > div > span:last-child {
            width: 49%;
            display: inline-block;
        }

#dis-mobileStatus li {
    font-size: 1.3em;
}

.li-fixed {
    background: #888888;
}

#print-zone td {
    padding: 5px;
}

    #print-zone td.title {
        width: 15%;
        text-align: right;
    }

.call-alert-form {
    padding: 3px;
    position: absolute;
    /*z-index: 200;*/
    width: 300px;
    height: 100px;
    background: linear-gradient(to bottom, #0085ff,#1acaf5);
    border: #2a4580 1px solid;
    box-shadow: #4b4c4c 2px 2px 2px;
    border-radius: 4px;
}

    .call-alert-form select {
        width: 80%;
        height: 40px;
        font-size: 25px;
        border-radius: 3px;
    }

    .call-alert-form > div:nth-of-type(1) {
        display: inline-block;
        width: 92px;
        height: 92px;
        line-height: 92px;
        font-size: 64px;
        color: green;
        vertical-align: middle;
        text-align: center;
        float: left;
    }

    .call-alert-form > div:nth-of-type(2) {
        display: inline-block;
        width: 200px;
        height: 92px;
        float: right;
    }

        .call-alert-form > div:nth-of-type(2) > div:first-child {
            height: 60px;
            color: white;
            padding: 5px;
            line-height: 60px;
            vertical-align: middle;
        }

        .call-alert-form > div:nth-of-type(2) > div:last-child {
            height: 350px;
            text-align: right;
            padding: 0 5px;
        }

            .call-alert-form > div:nth-of-type(2) > div:last-child .common-button {
                height: 30px;
                width: 80px;
                line-height: 30px;
                background: red;
            }

@keyframes backflash {
    50% {
        background: red;
    }
}

#car-status .flash-tr {
    animation: backflash 1s infinite;
}
.panel-title{
    font-size: 15px;
}
/* 常用医院弹窗================================ */
#free-Window{
    position: fixed;
    top:0px;
    right: 24%;
    width: 77px;
    text-align: center;
    cursor: pointer;
}
#free-Window:hover #window-list{
    height: 700px;
}
#free-Window>#window-list{
    position: absolute;
    top: 0;
    left: 0;
    width:450px;
    height: 0px;
    background-color: #ffffff;
    transition: height 0.3s;
    overflow: hidden;
    box-shadow: 0 0 5px 1px #d2cfcf;;
}
#free-Window>#window-list>h4{
    padding: 5px 8px;
    margin: 0;
    text-align: left;
}

/*.datagrid-row  {
    background-color: #f5f5f5;
}*/
.datagrid-body{
    background-color: #f5f5f5 !important;
}

/* 修复现场地址输入框布局问题 */
#detail-left > form > div.clear-fix {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    min-height: 2.5em;
}

#detail-left > form > div.clear-fix > span {
    white-space: nowrap;
    vertical-align: middle;
    line-height: 2em;
    flex-shrink: 0;
}

#detail-left > form > div.clear-fix > input[type="text"],
#detail-left > form > div.clear-fix > input[type="number"] {
    vertical-align: middle;
    line-height: 2em;
    box-sizing: border-box;
    height: 2em;
    padding: 0 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

/* 现场地址输入框特殊样式 */
#det-address {
    vertical-align: middle !important;
    line-height: 2em !important;
    height: 2em !important;
    flex-shrink: 1;
    padding: 0 5px !important;
}

/* EasyUI按钮在flex布局中的对齐 */
#detail-left > form > div.clear-fix > .easyui-linkbutton {
    vertical-align: middle;
    margin-left: 5px;
    flex-shrink: 0;
}

/* textarea在flex布局中的样式 */
#detail-left > form > div.clear-fix > textarea {
    vertical-align: middle;
    box-sizing: border-box;
    flex-shrink: 1;
}

/* select下拉框在flex布局中的样式 */
#detail-left > form > div.clear-fix > select {
    vertical-align: middle;
    height: 2em;
    flex-shrink: 0;
}

/* 120备注textarea特殊样式 */
#det-120remark {
    font-size: 1em !important;
    padding: 0 5px !important;
    line-height: 1.5em !important;
}

/* 患者信息textarea统一样式 */
#patient-info {
    font-size: 1em !important;
    padding: 5px !important;
}

/* 登录页面下拉框统一样式 */
#login-seat,
#login-schedule-time-config-id,
#login-un {
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    box-sizing: border-box !important;
}

/* EasyUI combobox容器样式 */
.combo {
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    box-sizing: border-box !important;
}

/* EasyUI textbox容器样式 */
.textbox {
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    box-sizing: border-box !important;
}

/* EasyUI messager 弹窗字体大小设置为14px */
.messager-body {
    font-size: 14px !important;
}

.messager-body .messager-p-msg {
    font-size: 14px !important;
}

.messager-body .messager-input {
    font-size: 14px !important;
}

/* messager按钮字体大小 */
.messager-button .l-btn-text {
    font-size: 14px !important;
}

/* messager标题字体大小 */
.panel-title {
    font-size: 14px !important;
}

/* 确保所有messager相关的文本都使用14px字体 */
.window .window-header .panel-title {
    font-size: 14px !important;
}

.window .window-body {
    font-size: 14px !important;
}

/* 消息提醒列表白色背景 */
#dl-msg-list {
    background-color: white !important;
}

#dl-msg-list .datagrid-view {
    background-color: white !important;
}

#dl-msg-list .datagrid-body {
    background-color: white !important;
}

#dl-msg-list .datagrid-row {
    background-color: white !important;
}

#dl-msg-list .datagrid-header {
    background-color: white !important;
}


