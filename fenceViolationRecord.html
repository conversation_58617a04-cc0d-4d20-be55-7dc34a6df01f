<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>电子围栏违规记录</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            Height: 28px;
        }

        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
            color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff;
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9;
        }
        input[type="checkbox"]:checked::before {
            content: "\2713";
            background-color: #fff;
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc;
            color: #000;
            font-size: 20px;
            font-weight: bold;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important; 
        }
        .select-common{
            height: 32px;
            width: 240px;
            margin-left: 0;
            font-size: 1.4em;
        }
        
        /* 统计信息样式 */
        .statistics-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 10px 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 12px 16px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            min-width: 140px;
            margin-right: 20px;
        }
        
        .stat-item:last-child {
            margin-right: 0;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-right: 8px;
            font-weight: normal;
            white-space: nowrap;
        }
        
        .stat-value {
            font-weight: bold;
            font-size: 18px;
            color: #1890ff;
            line-height: 1.2;
            white-space: nowrap;
        }
        
        .stat-value.highlight {
            color: #ff4d4f;
            font-size: 20px;
        }

        input[readonly], textarea[readonly] {
            background: #fff;
        }
    </style>
</head>
<body>
    <div id="mainPanel_FenceViolation" data-options="fit:true,border:false" style="height: 100%;">
        <!-- 违规记录查询区域 -->
        <div class="group-box" style="height: 100%; width: Calc(100% - 10px); display: inline-block; margin: 5px;">
            <div class="group-box-title">电子围栏违规记录<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="violationSearch" style="font-size: 14px;">
                    违规开始时间： <input id="beginViolationTime" class="easyui-datetimebox" style="width:180px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endViolationTime" class="easyui-datetimebox" style="width:180px;" />
                    &nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>
                    <span>&nbsp;车牌号：</span>
                    <input id="vehiclePlateNum" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;所属分站：</span>
                    <input id="stationName" class="easyui-textbox" style="width:120px">
                    <span>&nbsp;是否结束：</span>
                    <select id="isFinished" class="easyui-combobox" style="width:100px;" 
                            data-options="valueField:'value',textField:'text',editable:false,value:'false'">
                        <option value="">全部</option>
                        <option value="true">已结束</option>
                        <option value="false">未结束</option>
                    </select>
                    &ensp;<input type="checkbox" id="autoRefreshViolationList" name="autoRefreshViolationList" value="0" />自动刷新
                    <a class="common-button" id="getViolationListBtn" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getViolationListData();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
                
                <!-- 统计信息区域 -->
                <div class="statistics-info">
                    <div class="stat-item">
                        <span class="stat-label">总违规次数</span>
                        <span class="stat-value" id="totalViolationCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总违规时长</span>
                        <span class="stat-value" id="totalViolationDuration">0小时</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">违规车辆数</span>
                        <span class="stat-value" id="violationVehicleCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">平均违规时长</span>
                        <span class="stat-value" id="avgViolationDuration">0分钟</span>
                    </div>
                </div>
            </div>
            <!-- 表格区域 -->
            <div style="width:100%;height: Calc(100% - 180px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-violation-pages" style="height: 100%"
                       pagination="true" singleSelect="true" rownumbers="true" fit="true">
                    <thead>
                        <tr>
                            <th field="vehicleName" width="140" align="center">车辆名称</th>
                            <th field="vehiclePlateNum" width="140" align="center">车牌号</th>
                            <th field="stationName" width="180" align="center">所属分站</th>
                            <th field="startTime" width="200" align="center" formatter="dateTimeFormatter">违规开始时间</th>
                            <th field="endTime" width="200" align="center" formatter="dateTimeFormatter">违规结束时间</th>
                            <th field="durationSeconds" width="140" align="center" formatter="durationFormatter">违规时长</th>
                            <th field="violationType" width="140" align="center" formatter="violationTypeFormatter">违规类型</th>
                            <th field="startLng" width="150" align="center" formatter="coordinateFormatter">违规位置经度</th>
                            <th field="startLat" width="150" align="center" formatter="coordinateFormatter">违规位置纬度</th>
                            <th field="vehicleId" width="120" align="center" formatter="trackReplayFormatter">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }

        $(document).ready(function () {
            // 获得开始时间 - 默认为空
            $("#beginViolationTime").datetimebox("setValue", "");
            $("#endViolationTime").datetimebox("setValue", "");

            $('#dg-violation-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });

            // 为所有快速时间操作设置处理函数
            $('.inAnHour,.inToday').on("click", timeClickHandler);

            // 违规记录分页控件
            var pg = $("#dg-violation-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: 20,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        // 刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        getViolationListData(pageNumber, pageSize);
                    }
                });
            }

            // 初始加载数据
            getViolationListData();
            violationListAutoRefreshIntervalTimeout();
        });

        // 获取违规记录列表数据
        function getViolationListData(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }

            var beginTimeVal = $('#beginViolationTime').datetimebox('getValue');
            var beginTime = beginTimeVal == undefined || beginTimeVal == "" ? null : new Date(beginTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endTimeVal = $('#endViolationTime').datetimebox('getValue');
            var endTime = endTimeVal == undefined || endTimeVal == "" ? null : new Date(endTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            // 打开进度条
            $('#dg-violation-pages').datagrid('loading');
            $('#getViolationListBtn').attr('disabled', 'disabled');

            var params = {
                "page": pageNum,
                "size": pageSize,
                "startTimeBegin": beginTime,
                "startTimeEnd": endTime,
                "vehiclePlateNum": $('#vehiclePlateNum').val(),
                "stationName": $('#stationName').val(),
                "isFinished": $('#isFinished').val() === "" ? null : $('#isFinished').val() === "true",
                "sortBy": [{}]
            };

            getFenceViolationRecordPage(params, function (data) {
                $('#dg-violation-pages').datagrid('loadData', { total: 0, rows: [] });
                if (data && data.records) {
                    for (var i = 0; i < data.records.length; i++) {
                        $('#dg-violation-pages').datagrid('insertRow', {
                            index: i,
                            row: data.records[i]
                        });
                    }
                    $("#dg-violation-pages").datagrid("getPager").pagination({
                        total: data.total,
                        pageSize: data.size,
                        pageNumber: data.current,
                    });
                }
                
                // 获取统计数据
                getStatisticsData();
                
                $('#dg-violation-pages').datagrid('loaded');
                $('#getViolationListBtn').removeAttr('disabled');
            }, function (e, url, errMsg) {
                $('#dg-violation-pages').datagrid('loaded');
                $('#getViolationListBtn').removeAttr('disabled');
                console.error('获取违规记录失败:', errMsg);
            });
        }

        // 获取统计数据
        function getStatisticsData() {
            var beginTimeVal = $('#beginViolationTime').datetimebox('getValue');
            var beginTime = beginTimeVal == undefined || beginTimeVal == "" ? null : new Date(beginTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endTimeVal = $('#endViolationTime').datetimebox('getValue');
            var endTime = endTimeVal == undefined || endTimeVal == "" ? null : new Date(endTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var statisticsParams = {
                "startTimeBegin": beginTime,
                "startTimeEnd": endTime,
                "stationName": $('#stationName').val(),
                "vehiclePlateNum": $('#vehiclePlateNum').val(),
                "isFinished": $('#isFinished').val() === "" ? null : $('#isFinished').val() === "true"
            };

            console.log('获取统计数据参数:', statisticsParams);

            getViolationStatisticsByVehicle(statisticsParams, function (data) {
                console.log('统计数据返回:', data);
                updateStatistics(data);
            }, function (e, url, errMsg) {
                console.error('获取统计数据失败:', errMsg);
                // 失败时显示默认值
                updateStatistics([]);
            });
        }

        // 更新统计信息显示
        function updateStatistics(statisticsData) {
            if (!statisticsData || statisticsData.length === 0) {
                $('#totalViolationCount').text('0').removeClass('highlight');
                $('#totalViolationDuration').text('0小时').removeClass('highlight');
                $('#violationVehicleCount').text('0').removeClass('highlight');
                $('#avgViolationDuration').text('0分钟').removeClass('highlight');
                return;
            }

            // 计算汇总统计数据
            var totalCount = 0;
            var totalDurationSeconds = 0;
            var vehicleCount = statisticsData.length; // 车辆数量就是返回记录的数量

            statisticsData.forEach(function(item) {
                if (item.violationCount) {
                    totalCount += parseInt(item.violationCount);
                }
                if (item.totalDurationSeconds) {
                    totalDurationSeconds += parseInt(item.totalDurationSeconds);
                }
            });

            var totalDurationHours = (totalDurationSeconds / 3600).toFixed(1);
            var avgDurationMinutes = totalCount > 0 ? Math.round(totalDurationSeconds / totalCount / 60) : 0;

            // 更新显示并添加高亮效果
            var $totalCount = $('#totalViolationCount').text(totalCount);
            var $totalDuration = $('#totalViolationDuration').text(totalDurationHours + '小时');
            var $vehicleCount = $('#violationVehicleCount').text(vehicleCount);
            var $avgDuration = $('#avgViolationDuration').text(avgDurationMinutes + '分钟');

            // 根据数值添加高亮效果
            if (totalCount > 10) $totalCount.addClass('highlight');
            else $totalCount.removeClass('highlight');
            
            if (parseFloat(totalDurationHours) > 2) $totalDuration.addClass('highlight');
            else $totalDuration.removeClass('highlight');
            
            if (vehicleCount > 5) $vehicleCount.addClass('highlight');
            else $vehicleCount.removeClass('highlight');
            
            if (avgDurationMinutes > 30) $avgDuration.addClass('highlight');
            else $avgDuration.removeClass('highlight');

            console.log('统计信息更新完成:', {
                totalCount: totalCount,
                totalDurationHours: totalDurationHours,
                vehicleCount: vehicleCount,
                avgDurationMinutes: avgDurationMinutes
            });
        }

        // 格式化日期时间
        function dateTimeFormatter(value, row, index) {
            if (!value) return '';
            try {
                var date = new Date(value);
                return date.formatString('yyyy-MM-dd hh:mm:ss');
            } catch (e) {
                return value;
            }
        }

        // 格式化时长
        function durationFormatter(value, row, index) {
            if (!value || value <= 0) return '0秒';
            
            var hours = Math.floor(value / 3600);
            var minutes = Math.floor((value % 3600) / 60);
            var seconds = value % 60;
            
            if (hours > 0) {
                return hours + '小时' + minutes + '分' + seconds + '秒';
            } else if (minutes > 0) {
                return minutes + '分' + seconds + '秒';
            } else {
                return seconds + '秒';
            }
        }

        // 格式化违规类型
        function violationTypeFormatter(value, row, index) {
            switch (value) {
                case 'OUT_OF_FENCE':
                    return '超出围栏';
                default:
                    return value || '-';
            }
        }

        // 格式化坐标
        function coordinateFormatter(value, row, index) {
            if (!value) return '-';
            return parseFloat(value).toFixed(6);
        }

        // 格式化轨迹回放操作
        function trackReplayFormatter(value, row, index) {
            if (row.vehicleId) {
                return '<a href="#" style="color: #099DFC;" onclick="openTrackReplay(\'' + 
                       row.vehicleId + '\', \'' + (row.vehicleName || '') + '\', \'' + (row.vehiclePlateNum || '') + '\', \'' + (row.startTime || '') + '\')">轨迹回放</a>';
            }
            return '-';
        }

        // 打开轨迹回放
        function openTrackReplay(vehicleId, vehicleName, plateNum, startTime) {
            console.log('🚗 打开轨迹回放 - 接收参数:', {
                vehicleId: vehicleId,
                vehicleName: vehicleName,
                plateNum: plateNum,
                startTime: startTime
            });
            
            // 构建轨迹回放页面URL，包含startTime参数
            let trackReplayUrl = `trackReplay.html?vehicleId=${vehicleId}`;
            
            if (vehicleName) {
                trackReplayUrl += `&carName=${encodeURIComponent(vehicleName)}`;
            }
            if (plateNum) {
                trackReplayUrl += `&plateNum=${encodeURIComponent(plateNum)}`;
            }
            if (startTime) {
                trackReplayUrl += `&startTime=${encodeURIComponent(startTime)}`;
            }
            
            console.log('🔗 构建的完整URL:', trackReplayUrl);
            
            // 创建新的弹窗
            let dialogId = 'trackReplayDialog_' + vehicleId;
            let dialogTitle = `车辆轨迹回放 - ${vehicleName || ''} ${plateNum || ''}`;
            
            // 如果已存在相同ID的弹窗，先移除
            if ($('#' + dialogId).length > 0) {
                $('#' + dialogId).dialog('destroy');
                $('#' + dialogId).remove();
            }
            
            // 创建弹窗容器
            $('<div>').attr('id', dialogId).appendTo('body');
            
            // 创建iframe
            let iframe = $('<iframe>')
                .attr('src', trackReplayUrl)
                .attr('frameborder', '0')
                .attr('width', '100%')
                .attr('height', '100%')
                .appendTo('#' + dialogId);
            
            // 使用easyui创建弹窗
            $('#' + dialogId).dialog({
                title: dialogTitle,
                width: Math.min(1200, $(window).width() * 0.95),
                height: Math.min(800, $(window).height() * 0.95),
                closed: false,
                cache: false,
                modal: true,
                onClose: function() {
                    // 关闭时销毁弹窗
                    $(this).dialog('destroy');
                    $(this).remove();
                }
            });
            
            // 调整iframe样式
            iframe.css({
                'width': '100%',
                'height': '100%',
                'border': 'none',
                'margin': '0',
                'padding': '0'
            });
        }

        // 违规记录时间快速选择处理
        function timeClickHandler() {
            var from = $(this).parent().children('input:nth-of-type(1)');
            var to = $(this).parent().children('input:nth-of-type(2)');
            var now = new Date();
            var fromTime = new Date();
            var hours = 24;
            if ($(this).hasClass('inAnHour')) {
                hours = 1;
            }
            fromTime.setHours(fromTime.getHours() - hours);
            from.val(fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            to.val(null);
            easyUiDateTimeSet();
            // 自动搜索
            getViolationListData(1, 20);
        }

        function easyUiDateTimeSet() {
            $('.easyui-datetimebox').each(function () {
                var e = $(this);
                e.datetimebox('setValue', e.val());
            });
        }

        // 定时刷新任务开关
        var _violationListAutoRefreshInterval = true;
        
        // 设置违规记录列表刷新计时器
        function violationListAutoRefreshIntervalTimeout() {
            clearTimeout(window.violationListAutoRefreshInterval);
            if (_violationListAutoRefreshInterval) {
                window.violationListAutoRefreshInterval = null;
                if (_token && _token != "") {
                    if ($('#autoRefreshViolationList').is(':checked')) {
                        getViolationListData(null, null);
                        // 自动刷新时也同时刷新统计数据
                        getStatisticsData();
                    }
                }
                window.violationListAutoRefreshInterval = setTimeout(violationListAutoRefreshIntervalTimeout, 60000);
            }
        }

    </script>
</body>
</html> 