<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <link rel="stylesheet" type="text/css" href="script/layui/css/layui.css">
    <script type="text/javascript" src="script/layui/layui.js"></script>
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/sign.js"></script>
    <script type="text/javascript" src="script/eventReport.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }

        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
                color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff; /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9; /* 未选中时的边框颜色 */
        }
        input[type="checkbox"]:checked::before {
            content: "\2713"; /* 对号的转义符 √ */
            background-color: #fff; /* 选中时的背景颜色 */
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc; /* 选中后的边框颜色 */
            color: #000; /* 选中后的文字颜色   */
            font-size: 20px;
            font-weight: bold;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important;
        }
        #wavPlayWindow{
            overflow: initial;
        }
        .larger-event-message {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50px;
            background-color: rgb(242, 242, 242);
            line-height: 50px;
        }
        .larger-event-btns {
            height: 100%;
            display: flex;
            align-items: center;
        }
        .larger-event-title{
            width: calc(100% - 210px);
            white-space: nowrap;
            color: rgb(245, 154, 35);
            font-size: 16px;
            padding-left: 10px;
            text-align: left;
            overflow: hidden;
        }
        .larger-form-line1 {
            display: flex;
            width: 100%;
            height: 40px;
            align-items: center;
        }
        .larger-form-item{
            width: 33.3%;
            height: 40px;
            display: flex;
            align-items: center;
        }
        .larger-form-code{
            width: 100%;
            font-size: 1.1em;
            height: 2.1em;
            padding: 5px;
            border-radius: 3px;
        }
        .larger-form-label{
            margin-left: 5px;
            width: 140px;
            font-size: 15px;
            text-align: right;
        }
        .datebox {
            height: 1.7rem; /* 设置 DateTimeBox 输入框的高度 */
        }
        .clear-fix .datebox{
            height: 2.1rem; /* 设置 DateTimeBox 输入框的高度 */
        }
        .larger-form-line2{
            height: 40px;
            display: flex;
            align-items: center;
            width: 100%;
            padding-left: 20px;
        }
        .larger-form-line2-item{
            width: 16.6%;
            height: 40px;
            display: flex;
            align-items: center;
        }
        .larger-form-line3{
            width: 100%;
            margin-bottom: 6px;
        }
        .larger-form-line3-item{
            width: 100%;
            display: flex;
        }
        .larger-event-flowOfCasualties{
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .larger-event-uploadList, .larger-event-flowOfCasualties{
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 35px;
            background-color: rgb(242, 242, 242);
            line-height: 35px;
        }
        .larger-event-flowOfCasualties{
            margin-top: 6px;
        }
        .larger-event-uploadList-title, .larger-event-flowOfCasualties-title{
            text-align: left;
            color: rgb(245, 154, 35);
            font-size: 15px;
            padding-left: 10px;
        }
        .larger-form-line4 {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
        }
        .larger-form-line4-item{
            height: 40px;
            display: flex;
            align-items: center;
        }
        .larger-form-line5{
            width: 100%;
        }
        .larger-form-line5-item{
            display: flex;
            width: 100%;
        }
        #chooseCarNoId .layui-tree-entry, #chooseStationId .layui-tree-entry{
            text-align: left;
        }
    </style>
</head>
<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">重大事件<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;">
                    发生时间： <input id="occurrenceTimeStart" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="occurrenceTimeEnd" class="easyui-datetimebox" style="width:200px;" />
                    上报时间： <input id="reportTimeStart" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="reportTimeEnd" class="easyui-datetimebox" style="width:200px;" />

                    <span>&nbsp;上报类别：</span>
                    <select id="reportTypeCodeSelect" class="easyui-combobox" name="reportTypeCodeSelect" style="width:100px;">
                        <option value="">全部</option>
                        <option value="01">首报</option>
                        <option value="02">追报</option>
                        <option value="03">终报</option>
                    </select>
                    <span>&nbsp;事件类型：</span>
                    <select id="largeEventTypeSelect" class="easyui-combobox" name="largeEventTypeSelect" style="width:100px;">
                        <option value="">全部</option>
                        <option value="01">自然灾害</option>
                        <option value="02">事故灾难</option>
                        <option value="03">公共卫生事件</option>
                        <option value="04">社会安全事件</option>
                    </select>
                    <span>&nbsp;状态：</span>
                    <select id="largeStatusSelect" class="easyui-combobox" name="largeStatusSelect" style="width:100px;">
                        <option value="">全部</option>
                        <option value="10">进行中</option>
                        <option value="20">已完结</option>
                    </select>
                    <a class="common-button" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getLargerEventList();">
                        <i class="fa fa-search"></i>&nbsp;查询
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="larger-event-list" style="height: Calc(100% - 100px)"
                       pagination="true" singleSelect="true" rownumbers="true" fit="true" toolbar="#tbCallList">
                    <thead>
                        <tr>
                            <th field="occurrenceTime" width="8%" checkbox="false" align="center" formatter="createTimeFormatter">发生时间</th>
                            <th field="eventCode" width="12%" checkbox="false" align="center">上报编号</th>
                            <th field="eventTypeName" width="8%" align="center">事件类型</th>
                            <th field="eventLevelName" width="8%" align="center">事件等级</th>
                            <th field="woundedNum" width="5%" align="center">受伤人数</th>
                            <th field="statusName" width="5%" align="center" formatter="statusNameFormatter">状态</th>
                            <th field="reportTime" width="8%" align="center" formatter="reportTimeFormatter">上报时间</th>
                            <th field="reportTypeName" width="5%" align="center">上报类别</th>
                            <th field="reportContent" width="31%" align="center" formatter="reportContentFormatterList">情况描述</th>
                            <th field="operation" width="10%" align="center" formatter="uploadOperationFormatter">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <!-- 上传重大事件的弹窗 -->
        <div id="large-event-upload" class="easyui-window" title="上传重大事件" style="width: 1200px; height: 850px; text-align: center;"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="width: 100%;height:  calc(100% - 50px);padding: 10px 10px 0;box-sizing: border-box;">
                <div class="larger-event-message">
                    <div class="larger-event-title" id="largerEventTitle">
                        <div style="height: 25px;line-height: 25px;">地址：<span id="largerEventAddress"></span></div>
                        <div style="height: 25px;line-height: 25px;">呼叫原因：<span id="largerEventZS"></span></div>
                        <input type="hidden" id="largerEvent-lat">
                        <input type="hidden" id="largerEvent-lng">
                    </div>
                </div>
                <div class="larger-event-form">
                    <div class="larger-form-line1">
                        <div class="larger-form-item">
                            <span class="larger-form-label">事件编号：</span>
                            <input class="larger-form-code" disabled id="largerFormEventId" type="text" value="" />
                        </div>
                        <div class="larger-form-item">
                            <span class="larger-form-label">事件类型<span style="color:red;">*</span>：</span>
                            <select id="largeEventType" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                <option value=""> </option>
                                <option value="01">自然灾害</option>
                                <option value="02">事故灾难</option>
                                <option value="03">公共卫生事件</option>
                                <option value="04">社会安全事件</option>
                            </select>
                        </div>
                        <div class="larger-form-item">
                            <span class="larger-form-label">事件等级<span style="color:red;">*</span>：</span>
                            <select id="largeEventLevel" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                <option value=""> </option>
                                <option value="01">特大（Ⅰ级）</option>
                                <option value="02">重大（Ⅱ级）</option>
                                <option value="03">较大（Ⅲ级）</option>
                                <option value="04">一般（Ⅳ级）</option>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- 上报列表 -->
                <div class="larger-event-uploadList">
                    <div class="larger-event-uploadList-title">上报列表</div>
                    <div class="larger-event-btns">
                        <input id="largeEventId" type="hidden" value="" />
                        <!-- <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a> -->
                        <a href="javascript:phoneList(false);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                        <a href="javascript:phoneList(true);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                    </div>
                </div>
                <div class="larger-event-form">
                    <div class="larger-form-line1">
                        <div class="larger-form-item">
                            <span class="larger-form-label">上报类别<span style="color:red;">*</span>：</span>
                            <select id="reportTypeCode" onchange="selectLargerEvent()" class="select-common" style="width: 100%;margin-left: 0;">
                                <option value=""> </option>
                                <option value="01">首报</option>
                                <option value="02">追报</option>
                                <option value="03">终报</option>
                            </select>
                        </div>
                        <div class="larger-form-item">
                            <span class="larger-form-label">上报时间<span style="color:red;">*</span>：</span>
                            <input id="largeEventReportTime" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                        </div>
                        <div class="larger-form-item">
                        </div>
                    </div>
                    <div class="larger-form-line2">
                        <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label" style="min-width: 82px;">出动车辆<span style="color:red;">*</span>：</span>
                            <input id="vehicleDispatchedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">辆</span>
                        </div>
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label">伤员<span style="color:red;">*</span>：</span>
                            <input id="woundedNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">人</span>
                        </div>
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label">死亡<span style="color:red;">*</span>：</span>
                            <input id="deadNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">人</span>
                        </div>
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label">重伤<span style="color:red;">*</span>：</span>
                            <input id="woundedSevereNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">人</span>
                        </div>
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label">轻伤<span style="color:red;">*</span>：</span>
                            <input id="woundedSlightNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">人</span>
                        </div>
                        <div class="larger-form-line2-item">
                            <span class="larger-form-label">转送<span style="color:red;">*</span>：</span>
                            <input id="transferNum" oninput="validateInput(this)" onblur="selectLargerEvent()" maxlength="5" type="text" value="" style="width: 5.8rem;height: 1.7rem;" />
                            <span style="font-size: 14px;margin-left: 5px;">人</span>
                        </div>
                    </div>
                    <div class="larger-form-line3">
                        <div class="larger-form-line3-item">
                            <span class="larger-form-label" style="width: 101px;">汇报内容<span style="color:red;">*</span>：</span>
                            <textarea rows="3" id="reportContent" style="width:calc(100% - 106px);resize:none;font-size:1.4em;"></textarea>
                        </div>
                    </div>
                </div>
                <div style="height: 140px;">
                    <table class="easyui-datagrid" id="larger-event-upload-list" style="width: 100%" title=""
                           pagination="false" singleSelect="true" rownumbers="false" fit="true">
                        <thead>
                            <tr>
                                <th field="reportTime" width="150" align="center">上报时间</th>
                                <th field="reportTypeName" width="70" align="center">类别</th>
                                <th field="reportUserName" width="80" checkbox="false" align="center">上报人</th>
                                <!--<th field="eventTypeName" width="100" align="center">事件类型</th>-->
                                <th field="eventLevelName" width="100" align="center">事件等级</th>
                                <th field="vehicleDispatchedNum" width="80" checkbox="false" align="center">出动车辆</th>
                                <th field="woundedNum" width="60" checkbox="false" align="center">伤员</th>
                                <th field="deadNum" width="60" checkbox="false" align="center">死亡</th>
                                <th field="woundedSevereNum" width="60" checkbox="false" align="center">重伤</th>
                                <th field="woundedSlightNum" width="60" checkbox="false" align="center">轻伤</th>
                                <th field="transferNum" width="60" checkbox="false" align="center">转送</th>
                                <th field="reportContent" width="280" align="center" formatter="reportContentFormatter">汇报内容</th>
                                <th field="operation" width="100" align="center" formatter="operation">操作</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <!-- 伤员流向 -->
                <div class="larger-event-flowOfCasualties">
                    <div class="larger-event-flowOfCasualties-title">伤员流向</div>
                    <div class="larger-event-btns">
                        <a href="javascript:flowOfCasualtiesAddUpload();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">添加并上报</a>
                        <a href="javascript:flowOfCasualtiesAddUpload(true);" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">修改并保存</a>
                    </div>
                </div>
                <div class="larger-form-line4">
                    <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                    <div class="larger-form-line4-item" style="width: 22%;">
                        <span class="larger-form-label" style="min-width: 82px;">发车时间<span style="color:red;">*</span>：</span>
                        <input id="eventWoundedTransferId" type="hidden" value="" />
                        <input id="vehicleDispatchedTime_liudong" class="easyui-datetimebox" style="width:100%;text-indent: 0.5em;" />
                    </div>
                    <div class="larger-form-line4-item">
                        <span class="larger-form-label" style="width: 84px;">车牌号<span style="color:red;">*</span>：</span>
                        <input id="vehiclePlateNo_liudong" maxlength="8" type="text" value="" style="width: 6rem;height: 1.7rem;margin-right: 5px;" />
                        <a href="javascript:chooseCar();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                    </div>
                    <div class="larger-form-line4-item">
                        <span class="larger-form-label" style="width: 100px;">转送人数<span style="color:red;">*</span>：</span>
                        <input id="transferNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line4-item">
                        <span class="larger-form-label" style="width: 74px;">死亡<span style="color:red;">*</span>：</span>
                        <input id="deadNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line4-item">
                        <span class="larger-form-label" style="width: 74px;">重伤<span style="color:red;">*</span>：</span>
                        <input id="woundedSevereNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line4-item">
                        <span class="larger-form-label" style="width: 74px;">轻伤<span style="color:red;">*</span>：</span>
                        <input id="woundedSlightNum_liudong" oninput="validateInput(this)" maxlength="5" type="text" value="" style="width: 3.5rem;height: 1.7rem;" />
                        <span style="font-size: 14px;margin-left: 5px;">人</span>
                    </div>
                    <div class="larger-form-line4-item" style="width: 100%">
                        <span class="larger-form-label" style="width: 82px;">送往地点<span style="color:red;">*</span>：</span>
                        <input id="transferAddress_liudong" type="text" value="" style="width: calc(100% - 154px);height: 1.7rem;margin-right: 5px;" />
                        <a href="javascript:chooseStation();" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 60px; font-size: 12px;">选择</a>
                    </div>
                </div>
                <div class="larger-form-line5">
                    <!-- 出动车辆 伤员 死亡 重伤 轻伤 转送 -->
                    <div class="larger-form-line5-item">
                        <span class="larger-form-label" style="width: 81px;">伤情备注：</span>
                        <textarea rows="3" id="injuryRemark" style="width:calc(100% - 81px); resize: none; font-size: 1.4em;"></textarea>
                    </div>
                </div>
                <div style="height: 140px;margin-top: 6px;">
                    <table class="easyui-datagrid" id="larger-event-flowOfCasualties-list" style="width: 100%" title=""
                           pagination="false" singleSelect="true" rownumbers="false" fit="true">
                        <thead>
                            <tr>
                                <th field="vehicleDispatchedTime" width="160" align="center">发车时间</th>
                                <th field="vehiclePlateNo" width="80" align="center">车牌号</th>
                                <th field="transferNum" width="80" align="center">转送人数</th>
                                <th field="transferAddress" width="274" checkbox="false" align="center" formatter="transferAddressFormatter">送往地点</th>
                                <th field="deadNum" width="80" checkbox="false" align="center">死亡</th>
                                <th field="woundedSevereNum" width="80" checkbox="false" align="center">重伤</th>
                                <th field="woundedSlightNum" width="80" checkbox="false" align="center">轻伤</th>
                                <th field="injuryRemark" width="330" checkbox="false" align="center" formatter="injuryRemarkFormatter">伤情备注</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- 点击伤员流动选择车牌号 -->
            <div id="chooseCarNoId" class="easyui-window" title="选择车辆" style="width: 350px; height: 400px; text-align: center;;padding:10px;overflow-y: auto;" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                <input id="chooseCarInput" oninput="chooseCarInput()" type="text" value="" placeholder="请输入车牌号筛选" style="height: 1.7rem;width: 100%;" />
                <div style="width: 100%;">
                    <div id="car-number-tree"></div>
                </div>
            </div>
            <!-- 点击伤员流动选择分站 -->
            <div id="chooseStationId" class="easyui-window" title="选择分站" style="width: 280px; height: 400px; text-align: center;overflow-y: auto;" data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
                <input id="chooseStationInput" oninput="chooseStationInput()" type="text" value="" placeholder="请输入分站名称筛选" style="height: 1.7rem;width: 100%;" />
                <div style="width: 100%;">
                    <div id="station-tree"></div>
                </div>
            </div>
            <!-- <a href="javascript:largeEventSubmit();" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: white; background: #39c; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;">上传</a> -->
            <a href="javascript:largeEventCancel();" class="easyui-linkbutton" style="height: 30px;line-height: 30px;color: #000; background: #fff; width: 140px; font-size: 1em;  margin: 10px 10px 10px 0;border: 1px solid lightgray;">关闭</a>
        </div>

        <!-- 通信录弹窗 -->
        <div id="phone-list" class="easyui-window" title="选择通讯录" style="width: 600px;"
        data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">

            <div id="phone-book" style="height: 300px;"></div>

            <div style="text-align: center;margin-bottom: 20px;">
                <a href="javascript:largeEventAddUpload();" class="easyui-linkbutton"
                style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">保存并发送短信</a>
                <a href="javascript:cancelPhoneList();" class="easyui-linkbutton"
                style="height: 25px;line-height: 20px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;">取消</a>
            </div>


        </div>


    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat;
        var page = 1;
        var size = 20;
        var largerList = [] // 重大事件列表
        var largerEventList = [] //重大事件的列表
        var flowOfCasualtiesList = [] //伤员流向的列表
        var clickIndex;
        var hasLargerEventEdit = false // 判断是否有正在编辑的重大事件
        var hasFlowOfCasualtiesEdit = false // 判断是否有正在编辑的伤员流向表单
        var phoneTree =null
        var isEdit = false
        $(document).ready(function () {
            $('#larger-event-list').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });
            //分页控件
            var pg = $("#larger-event-list").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    size: 20,
                    pageNumber: 1,
                    pageList: [5, 10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        page = pageNumber
                        size = pageSize
                        getLargerEventList();
                    }
                });
            }
        })
        // 重大事件弹窗关闭按钮
        function largeEventCancel() {
            $('#large-event-upload').window('close')
        }
        // 查询重大事件模块的信息
        function getLargerEventInfo() {
            let idEvent = largerList[clickIndex].eventCode
            eventReportItem({ eventCode: idEvent, page: 1, size: 9999 },
                function (data) {
                    largerEventList = data
                    //设置默认上报类型
                    let defaultReportTypeCode = getDefaultReportTypeCode(data);
                    $("#reportTypeCode").val(defaultReportTypeCode);
                    selectLargerEvent() // 回显汇报内容

                    resetReportContentIfAbsent(idEvent)

                    $('#larger-event-upload-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].reportTime) {
                            data[i].reportTime = convertTimeToRead(data[i].reportTime);
                        }
                        $('#larger-event-upload-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                reportTime: data[i].reportTime,
                                reportTypeName: data[i].reportTypeName,
                                reportTypeCode: data[i].reportTypeCode,
                                reportUserName: data[i].reportUserName,
                                eventTypeName: data[i].eventTypeName,
                                eventLevelName: data[i].eventLevelName,
                                vehicleDispatchedNum: data[i].vehicleDispatchedNum,
                                woundedNum: data[i].woundedNum,
                                deadNum: data[i].deadNum,
                                woundedSevereNum: data[i].woundedSevereNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                transferNum: data[i].transferNum,
                                reportContent: data[i].reportContent,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-upload-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#largeEventId').val(rowData.id)
                            $('#reportTypeCode').val(rowData.reportTypeCode)
                            $('#largeEventReportTime').datetimebox('setValue', rowData.reportTime)
                            $('#vehicleDispatchedNum').val(rowData.vehicleDispatchedNum)
                            $('#woundedNum').val(rowData.woundedNum)
                            $('#deadNum').val(rowData.deadNum)
                            $('#woundedSevereNum').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum').val(rowData.woundedSlightNum)
                            $('#transferNum').val(rowData.transferNum)
                            setTimeout(() => { // 为了防止select 事件覆盖此条记录 不能去掉延时器
                                $('#reportContent').val(rowData.reportContent)
                            }, 0);
                            // $('#larger-event-upload-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasLargerEventEdit = true // 表示有正在编辑的数据不让再双击其它数据
                            selectLargerEvent()

                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                });
        }
        function validateInput(input) {
            const value = input.value;
            const regex = /^(0|[1-9]\d*)$/; // 正则表达式匹配正整数和0
            if (!regex.test(value)) {
                input.value = ""; // 清空输入框
            }
        }
        // 查询流动伤员的列表详情
        function getEventWoundedTransfer() {
            let idEvent = largerList[clickIndex].eventCode
            eventWoundedTransfer({ eventCode: idEvent },
                function (data) {
                    flowOfCasualtiesList = data
                    $('#larger-event-flowOfCasualties-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].vehicleDispatchedTime) {
                            data[i].vehicleDispatchedTime = convertTimeToRead(data[i].vehicleDispatchedTime)
                        }
                        $('#larger-event-flowOfCasualties-list').datagrid('insertRow', {
                            index: i,  // 索引从0开始
                            row: {
                                id: data[i].id,
                                vehiclePlateNo: data[i].vehiclePlateNo,
                                vehicleDispatchedTime: data[i].vehicleDispatchedTime,
                                transferNum: data[i].transferNum,
                                transferAddress: data[i].transferAddress,
                                woundedSevereNum: data[i].woundedSevereNum,
                                deadNum: data[i].deadNum,
                                woundedSlightNum: data[i].woundedSlightNum,
                                injuryRemark: data[i].injuryRemark,
                            }
                        });
                    }
                    //监听表格的双击事件 数据回填 用户操作方便 可以在上一条基础上做简单修改
                    $('#larger-event-flowOfCasualties-list').datagrid({
                        onDblClickRow: function (rowIndex, rowData) {
                            $('#eventWoundedTransferId').val(rowData.id)
                            $('#vehiclePlateNo_liudong').val(rowData.vehiclePlateNo)
                            $('#transferNum_liudong').val(rowData.transferNum)
                            $('#transferAddress_liudong').val(rowData.transferAddress)
                            $('#woundedSevereNum_liudong').val(rowData.woundedSevereNum)
                            $('#woundedSlightNum_liudong').val(rowData.woundedSlightNum)
                            $('#deadNum_liudong').val(rowData.deadNum)
                            $('#injuryRemark').val(rowData.injuryRemark)
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', rowData.vehicleDispatchedTime)
                            // $('#larger-event-flowOfCasualties-list').datagrid('deleteRow', rowIndex); // 把该行数据再删除掉
                            // hasFlowOfCasualtiesEdit = true // 表示有正在编辑的数据不让再双击其它数据
                        }
                    });
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                })

        }
        function resetuploadEventPage() {
            $('#largerFormEventId').val('')
            $('#largeEventType').val('')
            $('#largeEventLevel').val('')
            $('#reportTypeCode').val('')
            $('#vehicleDispatchedNum').val('')
            $('#woundedNum').val('')
            $('#deadNum').val('')
            $('#woundedSevereNum').val('')
            $('#woundedSlightNum').val('')
            $('#transferNum').val('')
            $('#reportContent').val('')
            $('#eventWoundedTransferId').val('')
            $('#vehiclePlateNo_liudong').val('')
            $('#transferNum_liudong').val('')
            $('#transferAddress_liudong').val('')
            $('#woundedSevereNum_liudong').val('')
            $('#woundedSlightNum_liudong').val('')
            $('#deadNum_liudong').val('')
            $('#injuryRemark').val('')
        }
        // 保存座席人员电话，保存时需要传参
        let seatUserPhone = ''
        //上传重大事件页面信息初始化
        function largerEventUpload(index) {
            clickIndex = index
            $('#largeEventLevel').val('')
            $('#largeEventType').val('')
            _pSeat = null;
            try {
                _pSeat = evalJson(window.parent.gProxy.getSeatInfo()); //将座席信息存储到全局变量
            } catch (e) {
                _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
            }

            // 查询重大事件的信息
            let idEvent = largerList[clickIndex].eventCode
            // 获取事件基本信息的接口
            getImportantEventInfo({ id: idEvent },
                function (res) {
                    seatUserPhone = res.seatUserPhone
                    $('#large-event-upload').window({
                        title: `<div style="display:flex;justify-content: space-between;align-items: center;">
                                <div>
                                    <span style="margin-right:20px">重大事件上报</span>
                                    <span>来电时间：<span id='largeEventCallInTimes'>${res.callInTimes ? res.callInTimes : ''}</span></span>
                                </div>
                                <div style="margin-right: 60px">
                                    调度员：${res.seatUser}
                                </div>
                            </div>`, // 替换 esay ui的title内容
                        onClose: function () {
                            resetuploadEventPage()
                        }
                    });
                    let nowTimeStr = getNowTimeStr();
                    //上报时间默认取当前时间
                    $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
                    //出动车辆时间默认取当前时间
                    $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)
                    $('#largerEvent-lat').val(res.lat) // 重大事件的标题设置回显
                    $('#largerEvent-lng').val(res.lng) // 重大事件的标题设置回显
                    $('#largerEventAddress').text(res.address) // 重大事件的标题设置回显
                    $('#largerEventZS').text(res.majorCall) // 重大事件的标题设置回显
                    $('#largerFormEventId').val(res.eventId) // 重大事件的编号回显
                    // 事件类型和事件等级回显
                    // $('#largeEventType').val(res.largeEventType)
                    // $('#largeEventLevel').val(res.largeEventLevel)

                    $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                    $('#woundedNum').val(res.woundedNum)// 伤员人数
                    $('#deadNum').val(res.deadNum)// 死亡人数
                    $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                    $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                    $('#transferNum').val(res.transferNum)// 转送人数

                    $('#large-event-upload').window('open'); //打开上报重大事件页面
                    //查询重大事件的上报列表
                    getLargerEventInfo()
                    //查询重大事件的伤员流向列表
                    getEventWoundedTransfer()
                },
                function (e, url, errMsg) {
                    //失败做处理
                    $.messager.alert('提示', errMsg);
                });
            // 查询重大事件类型和等级的两个字段单独用了一个接口
            getByEventCode({ eventCode: idEvent },
                function (res) {
                    // 事件类型和事件等级回显
                    $('#largeEventLevel').val(res.eventLevelCode)
                    $('#largeEventType').val(res.eventTypeCode)
                    selectLargerEvent() // 第一次打开页面时也要回显汇报内容的默认话术
                },
                function (e, url, errMsg) {
                    //失败不做处理
                });
        }
        // 重大事件表单保存接口
        function largeEventAddUpload() {
            let selectPhone = phoneTree.getChecked('phone-list'); // 获取选中节点的数据
            let sendMsgList =[]
            for(let s=0;s<selectPhone.length;s++){
                let item = selectPhone[s]
                for(let c=0;c<item.children.length;c++){
                    let child = item.children[c]
                    let obj = {
                        "name": child.title,
			            "phone": child.number
                    }
                    sendMsgList.push(obj)
                }
            }
            var id = $('#largeEventId').val();
            var reportTypeCodeSelected = document.getElementById("reportTypeCode");
            var reportTypeCode = reportTypeCodeSelected.value; //上报类别编码
            var reportTypeName = reportTypeCodeSelected.options[reportTypeCodeSelected.selectedIndex].text;//上报类别名称
            var reportTimeStr = $('#largeEventReportTime').val(); //上报时间
            var reportTime = reportTimeStr.replace(/[\s:-]/g, "");
            var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()// 出动车辆
            var woundedNum = $('#woundedNum').val()// 伤员人数
            var deadNum = $('#deadNum').val()// 死亡人数
            var woundedSevereNum = $('#woundedSevereNum').val()// 重伤人数
            var woundedSlightNum = $('#woundedSlightNum').val()// 轻伤人数
            var transferNum = $('#transferNum').val()// 转送人数
            var reportContent = $('#reportContent').val()// 汇报内容
            var objLE = {
                reportTypeCode,
                reportTypeName,
                reportTime,
                vehicleDispatchedNum,
                woundedNum,
                deadNum,
                woundedSevereNum,
                woundedSlightNum,
                transferNum,
                reportContent,
                reportUserName: _pSeat.user,
                reportUserPhone: seatUserPhone
            }
            if (isEdit) {
                objLE.id = id
                var largeEventTypeSelected = document.getElementById("largeEventType");
                var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
                var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
                var largeEventLevelSelected = document.getElementById("largeEventLevel");
                var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
                var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
                let objValue = {
                    eventTypeCode,
                    eventTypeName,
                    eventLevelCode,
                    eventLevelName,
                }
                const values = Object.values(objLE).map(item => {
                    return item.trim();
                })
                if (objLE.id == '') {
                    return $.messager.alert('提示', '如需修改事件请先双击表格内已上报的事件，否则请先添加并上报新事件。', 'error');
                }
                if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                    return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
                }
                $.messager.confirm("提示", '是否修改并立即上报重大事件？', function (r) {
                    if (r) {
                        largeUpdateById({ ...objLE, ...objValue,users:sendMsgList },
                            function (res) {
                                $.messager.alert('提示', '修改成功', 'success');
                                // 表单编辑完全部清空
                                $('#largeEventId').val('')
                                $('#reportTypeCode').val('')
                                $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                                $('#vehicleDispatchedNum').val('')
                                $('#woundedNum').val('')
                                $('#deadNum').val('')
                                $('#woundedSevereNum').val('')
                                $('#woundedSlightNum').val('')
                                $('#transferNum').val('')
                                $('#reportContent').val('')
                                // 表格重新渲染一下
                                getLargerEventInfo()
                                cancelPhoneList()
                            },
                            function (e, url, errMsg) {
                                //失败做处理
                                $.messager.alert('提示', errMsg);
                            });
                    }
                });
            } else {
                // 修改重大事件，调用专用的接口
                largeEventSubmit('1', objLE,sendMsgList)
            }
        }
        // 伤员流向表单新增
        function flowOfCasualtiesAddUpload(isEditFlow) {
            // 伤员的流动数据
            var id = $('#eventWoundedTransferId').val()// id
            var vehicleDispatchedTime = $('#vehicleDispatchedTime_liudong').val().replace(/[\s:-]/g, "")// 出动车辆时间
            var vehiclePlateNo = $('#vehiclePlateNo_liudong').val()// 出动车辆数量
            var transferNum = $('#transferNum_liudong').val()// 转送人数
            var transferAddress = $('#transferAddress_liudong').val()// 送往地点
            var woundedSevereNum = $('#woundedSevereNum_liudong').val()// 重伤
            var woundedSlightNum = $('#woundedSlightNum_liudong').val()// 轻伤
            var deadNum = $('#deadNum_liudong').val()// 死亡
            var injuryRemark = $('#injuryRemark').val()// 伤情备注
            let objFOC = {
                vehicleDispatchedTime,
                vehiclePlateNo,
                transferNum,
                transferAddress,
                woundedSevereNum,
                deadNum,
                woundedSlightNum,
                injuryRemark,
            }
            if (isEditFlow) {
                objFOC.id = id
                flowOfCasualtiesUpdate(objFOC)
            } else {
                largeEventSubmit('2', objFOC)
            }
        }
        // 伤员流向更新
        function flowOfCasualtiesUpdate(obj) {
            const values = Object.keys(obj).filter(val => {
                if (val != 'injuryRemark' && obj[val] == '') {
                    return val
                }
            })
            if (obj.id == '') {
                return $.messager.alert('提示', '如需修改伤员流向请先双击表格内已添加的伤员流向，否则请先添加一条新的伤员流向。', 'error');
            }
            if (values.length > 0) {
                return $.messager.alert('提示', '流动伤员必要信息未填写，无法上报。', 'error');
            }
            $.messager.confirm("提示", '是否修改并立即上报伤员流向', function (r) {
                if (r) {
                    let params = {
                        ...obj
                    }
                    eventWoundedTransferUpdateById(params,
                        function (res) {
                            $.messager.alert('提示', '修改成功', 'success');
                            // 表单编辑完全部清空
                            $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                            $('#vehiclePlateNo_liudong').val('')
                            $('#transferNum_liudong').val('')
                            $('#transferAddress_liudong').val('')
                            $('#woundedSevereNum_liudong').val('')
                            $('#woundedSlightNum_liudong').val('')
                            $('#deadNum_liudong').val('')
                            $('#injuryRemark').val('')
                            // 表格重新渲染一下
                            getEventWoundedTransfer()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        /**
         * 重大事件保存按钮提交数据
         * @param type 1-上报列表，2-伤员流向
         * @param obj
         * @param sendMsgList
         */
        function largeEventSubmit(type, obj,sendMsgList) {
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            let objValue = {}
            if (type == '2') {
                objValue.injuryRemark = obj.injuryRemark
                delete obj.injuryRemark
            }
            const values = Object.values(obj).map(item => {
                return item.trim();
            })
            if (values.indexOf('') != -1 || eventTypeCode == '' || eventLevelCode == '') {
                return $.messager.alert('提示', '重大事件必要信息未填写，无法上报。', 'error');
            }
            // 暂时不存在编辑重大事件 先注释后续可能会加此功能
            // if(hasLargerEventEdit || hasFlowOfCasualtiesEdit) {
            //     return $.messager.alert('提示', '请保存正在编辑的数据，再上报');
            // }
            $.messager.confirm("提示", `${type == '1' ? '是否新增并立即上报重大事件？' : '是否新增并立即上报伤员流向'}`, function (r) {
                if (r) {
                    let params = {
                        eventInfo: {
                            eventTypeCode,
                            eventTypeName,
                            eventLevelCode,
                            eventLevelName,
                            eventCode: largerList[clickIndex].eventCode,
                            address: largerList[clickIndex].address,
                            latitude: largerList[clickIndex].latitude,
                            longitude: largerList[clickIndex].longitude,
                            occurrenceTime: largerList[clickIndex].occurrenceTime.replace(/[\s:-]/g, ""),
                            seatUserName: _pSeat.user
                        },
                        reportItems: type == '1' ? [obj] : [],
                        woundedTransfers: type == '2' ? [{ ...obj, ...objValue }] : [],
                        users:sendMsgList
                    }
                    eventInfoReport(params,
                        function (res) {
                            $.messager.alert('提示', '上报成功', 'success');
                            // 上报成功表单全清空
                            if (type == '1') {
                                // 表单编辑完,全清空
                                $('#reportTypeCode').val('')
                                $('#largeEventReportTime').datetimebox('setValue', getNowTimeStr())
                                $('#vehicleDispatchedNum').val('')
                                $('#woundedNum').val('')
                                $('#deadNum').val('')
                                $('#woundedSevereNum').val('')
                                $('#woundedSlightNum').val('')
                                $('#transferNum').val('')
                                $('#reportContent').val('')
                                // 表格重新渲染一下
                                getLargerEventInfo()
                                // 查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasLargerEventEdit = false //事件保存后 允许再双击其它事件 目前不存在编辑，先注释 后续可能会加此功能
                            } else {
                                // 表单编辑完全部清空
                                $('#vehicleDispatchedTime_liudong').datetimebox('setValue', getNowTimeStr())
                                $('#vehiclePlateNo_liudong').val('')
                                $('#transferNum_liudong').val('')
                                $('#transferAddress_liudong').val('')
                                $('#woundedSevereNum_liudong').val('')
                                $('#woundedSlightNum_liudong').val('')
                                $('#deadNum_liudong').val('')
                                $('#injuryRemark').val('')
                                // 查询重大事件的伤员流向列表
                                getEventWoundedTransfer()
                                // hasFlowOfCasualtiesEdit = false //事件保存后 允许再双击其它事件/目前不存在编辑，先注释 后续可能会加此功能
                            }
                            // $('#large-event-upload').window('close')
                            cancelPhoneList()
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }

        /**
         * 重置汇报内容
         * @param idEvent 120事件id
         */
        function resetReportContentIfAbsent(idEvent) {
            if (!$('#reportContent').val()) {
                // 获取事件基本信息的接口
                getImportantEventInfo({ id: idEvent },
                    function (res) {
                        let nowTimeStr = getNowTimeStr();
                        //上报时间默认取当前时间
                        $('#largeEventReportTime').datetimebox('setValue', nowTimeStr)
                        //出动车辆时间默认取当前时间
                        $('#vehicleDispatchedTime_liudong').datetimebox('setValue', nowTimeStr)

                        $('#vehicleDispatchedNum').val(res.vehicleDispatchedNum)// 出动车辆
                        $('#woundedNum').val(res.woundedNum)// 伤员人数
                        $('#deadNum').val(res.deadNum)// 死亡人数
                        $('#woundedSevereNum').val(res.woundedSevereNum)// 重伤人数
                        $('#woundedSlightNum').val(res.woundedSlightNum)// 轻伤人数
                        $('#transferNum').val(res.transferNum)// 转送人数

                        selectLargerEvent()
                    },
                    function (e, url, errMsg) {
                        //失败做处理
                        $.messager.alert('提示', errMsg);
                    });
            }
        }

        // 重大事件类型等级选择时触发，第一次打开页面或事件等级和事件类型选择时，回显汇报内容
        function selectLargerEvent() {
            var largeEventTypeSelected = document.getElementById("largeEventType");
            var eventTypeCode = largeEventTypeSelected.value; //上报事件类型编码
            var eventTypeName = largeEventTypeSelected.options[largeEventTypeSelected.selectedIndex].text;//上报事件类型名称
            var largeEventLevelSelected = document.getElementById("largeEventLevel");
            var eventLevelCode = largeEventLevelSelected.value; //上报事件等级编码
            var eventLevelName = largeEventLevelSelected.options[largeEventLevelSelected.selectedIndex].text;//上报事件等级名称
            var reportTypeCode = $('#reportTypeCode').val()
            var reportTime = $('#largeEventReportTime').val()
            var vehicleDispatchedNum = $('#vehicleDispatchedNum').val()
            var woundedNum = $('#woundedNum').val()
            var deadNum = $('#deadNum').val()
            var woundedSevereNum = $('#woundedSevereNum').val()
            var woundedSlightNum = $('#woundedSlightNum').val()
            var transferNum = $('#transferNum').val()
            if (!$('#reportContent').val()) {
                if (eventTypeName && eventLevelName) {
                    //默认汇报内容模板
                    var largeEventCallInTimes = $('#largeEventCallInTimes').text().replace(/[\s:-]/g, "");// 来电时间
                    let timeArr = largeEventCallInTimes.split('')
                    let y = timeArr.slice(0, 4).join('')
                    let m = timeArr.slice(4, 6).join('')
                    let d = timeArr.slice(6, 8).join('')
                    let hh = timeArr.slice(8, 10).join('')
                    let mm = timeArr.slice(10, 12).join('')
                    let ss = timeArr.slice(12, 14).join('')
                    var callInTimes = y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
                    var largerEventAddress = $('#largerEventAddress').text() // 地点
                    var largerEventZS = $('#largerEventZS').text() // 呼叫原因
                    if (reportTypeCode == '01') {
                        //var str = `在【${callInTimes}】接到来电，在【${largerEventAddress}】地点，发生【${eventLevelName}】的【${eventTypeName}】事件。`
                        var str = createEventFirstReportContent(callInTimes, largerEventAddress, eventLevelName, eventTypeName);
                        $('#reportContent').val(str)
                    } else if ((reportTypeCode == '02' || reportTypeCode == '03') && vehicleDispatchedNum && woundedNum && deadNum && woundedSevereNum && woundedSlightNum && transferNum && reportTime) {
                        //追报默认汇报内容模板
                        var str = createEventReportContent(convertTime(reportTime.replace(/[\s:-]/g, "")), vehicleDispatchedNum, woundedNum, deadNum, woundedSevereNum, woundedSlightNum, transferNum);
                        $('#reportContent').val(str)
                    }

                }
            }

        }
        function operation(value, row, index) {
            let node = '';
            //首报和终报不可以删除
            if (row.reportTypeCode === '02') {
                node += `
                    <a href="javascript:largeEventDel(${index});" class="easyui-linkbutton" style="height: 25px;line-height: 25px;color: white; background: #39c; width: 90px; font-size: 12px;margin-right: 10px;top: 0;padding: 3px 8px;border-radius: 5px;">删 除</a>
                    `
            }
            return node
        }
        function largeEventDel(index) {
            $.messager.confirm("提示", '是否删除此条重大事件？', function (r) {
                if (r) {
                    var id = largerEventList[index].id
                    let params = {
                        id
                    }
                    largeDeleteById(params,
                        function (res) {
                            $.messager.alert('提示', '删除成功', 'success');
                            getLargerEventInfo() // 表格数据重新渲染下
                        },
                        function (e, url, errMsg) {
                            //失败做处理
                            $.messager.alert('提示', errMsg);
                        });
                }
            })
        }
        // 重大事件的汇报内容气泡弹窗（列表页）
        function reportContentFormatterList(value, row, index) {
            let node = `
                <div id="reportContentFormatterList${index}" onmouseout="closeAllTips()"  onmouseover="reportContentHoverList(${index})">${row.reportContent}</div>
              `
            return node
        }
        function reportContentHoverList(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + largerList[index].reportItemList[0].reportContent + "</span>", `#reportContentFormatterList${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        // 重大事件的汇报内容气泡弹窗
        function reportContentFormatter(value, row, index) {
            let node = `
                <div id="reportContentFormatter${index}" onmouseout="closeAllTips()"  onmouseover="reportContentHover(${index})">${row.reportContent}</div>
              `
            return node
        }
        function reportContentHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + largerEventList[index].reportContent + "</span>", `#reportContentFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        // 重大事件的伤情备注气泡
        function injuryRemarkFormatter(value, row, index) {
            let node = `
                <div id="injuryRemarkFormatter${index}" onmouseout="closeAllTips()"  onmouseover="injuryRemarkHover(${index})">${row.injuryRemark}</div>
              `
            return node
        }
        function injuryRemarkHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].injuryRemark + "</span>", `#injuryRemarkFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        // 重大事件的送往地点气泡
        function transferAddressFormatter(value, row, index) {
            let node = `
                <div id="transferAddressFormatter${index}" onmouseout="closeAllTips()"  onmouseover="transferAddressHover(${index})">${row.transferAddress}</div>
              `
            return node
        }
        function transferAddressHover(index) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                // 创建提示
                layer.tips("<span style='color:black'>" + flowOfCasualtiesList[index].transferAddress + "</span>", `#transferAddressFormatter${index}`, {
                    tips: [1, '#fff'], // 提示样式，1 表示方向向上，#FF5722 是提示框的背景颜色
                    time: 0, // 提示持续时间，单位为毫秒
                });
            });
        }
        // 点击伤员流动的车牌号选择弹窗打开
        function chooseCar() {
            $('#chooseCarNoId').window('open');
            chooseCarInput()
        }
        // 查询车辆
        function chooseCarInput() {
            let params = {
                plateNum: $('#chooseCarInput').val(),// 车牌号
                stationName: '' // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item,
                        spread: $('#chooseCarInput').val() ? true : false,
                        type: '1',
                        children: []
                    }
                    if (data[item].length > 0) {
                        data[item].forEach(val => {
                            obj.children.push({
                                spread: $('#chooseCarInput').val() ? true : false,
                                type: '2',
                                title: val.plateNum
                            })
                        })

                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#car-number-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            if (obj.data.type == '2') {
                                $('#chooseCarNoId').window('close');
                                // 输入框赋值
                                $('#vehiclePlateNo_liudong').val(obj.data.title)
                            }
                        }
                    });
                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        // 点击伤员流动的车牌号选择弹窗打开
        function chooseStation() {
            $('#chooseStationId').window('open');
            chooseStationInput()
        }
        // 查询车辆
        function chooseStationInput() {
            let params = {
                plateNum: '',// 车牌号
                stationName: $('#chooseStationInput').val() // 分站名称
            }
            getMobileList(params, function (data) {
                let keys = Object.keys(data)
                let arr = []
                keys.forEach(item => {
                    let obj = {
                        title: item
                    }
                    arr.push(obj)
                })
                layui.use(function () {
                    var tree = layui.tree;
                    // 渲染
                    tree.render({
                        elem: '#station-tree',
                        data: arr,
                        onlyIconControl: true,
                        showLine: false,  // 是否开启连接线
                        click: function (obj) {
                            // 输入框赋值
                            $('#chooseStationId').window('close');
                            $('#transferAddress_liudong').val(obj.data.title)
                        }
                    });

                });
            }, function (e, url, errMsg) {
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        function closeAllTips() {
            layui.use('layer', function () {
                var layer = layui.layer;
                layer.closeAll('tips');
            });
        }
        function createTimeFormatter(value, row, index) {
            let timeArr = row.occurrenceTime.split('')
            let y = timeArr.slice(0, 4).join('')
            let m = timeArr.slice(4, 6).join('')
            let d = timeArr.slice(6, 8).join('')
            let hh = timeArr.slice(8, 10).join('')
            let mm = timeArr.slice(10, 12).join('')
            let ss = timeArr.slice(12, 14).join('')
            let timeStr = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
            return timeStr
        }
        function reportTimeFormatter(value, row, index) {
            let timeArr = row.reportTime.split('')
            let y = timeArr.slice(0, 4).join('')
            let m = timeArr.slice(4, 6).join('')
            let d = timeArr.slice(6, 8).join('')
            let hh = timeArr.slice(8, 10).join('')
            let mm = timeArr.slice(10, 12).join('')
            let ss = timeArr.slice(12, 14).join('')
            let timeStr = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
            return timeStr
        }
        function statusNameFormatter(value, row, index) {
            let node = ''
            if (row.status == '10') {
                node = `<div style="color:#67C23A">${row.statusName}</div>`
            } else if (row.status == '20') {
                node = `<div style="color:#909399">${row.statusName}</div>`
            } else {
                node = `<div>${row.statusName}</div>`
            }
            return node
        }
        //获得列表数据
        function getLargerEventList() {
            var reportTypeCodeSelect = document.getElementById("reportTypeCodeSelect");
            var reportTypeCode = reportTypeCodeSelect.value; //上报类型编码
            var largeEventTypeSelect = document.getElementById("largeEventTypeSelect");
            var eventTypeCode = largeEventTypeSelect.value; //事件类型编码
            var status = document.getElementById("largeStatusSelect").value;
            _pSeat = null;
            try {
                _pSeat = evalJson(window.parent.gProxy.getSeatInfo()); //将座席信息存储到全局变量
            } catch (e) {
                _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
            }

            var reportTimeStart = $('#reportTimeStart').val().replace(/[\s:-]/g, "");
            var reportTimeEnd = $('#reportTimeEnd').val().replace(/[\s:-]/g, "");
            var occurrenceTimeStart = $('#occurrenceTimeStart').val().replace(/[\s:-]/g, "");
            var occurrenceTimeEnd = $('#occurrenceTimeEnd').val().replace(/[\s:-]/g, "");

            //打开进度条：
            $('#larger-event-list').datagrid('loading');//打开等待div
            var params = {
                "page": page,
                "size": size,
                'eventTypeCode': eventTypeCode,
                'reportTypeCode': reportTypeCode,
                'reportTimeStart': reportTimeStart,
                'reportTimeEnd': reportTimeEnd,
                'occurrenceTimeStart': occurrenceTimeStart,
                'occurrenceTimeEnd': occurrenceTimeEnd,
                'status': status
            };
            selectPage(params, function (data) {
                largerList = data.records
                $('#larger-event-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.records.length; i++) {
                    let eventLevelCode = data.records[i].eventLevelCode
                    let eventLevelName = data.records[i].eventLevelName;
                    //红色、橙色、黄色、蓝色
                    if (eventLevelCode === '01') {//一级
                        eventLevelName = '<p style="background-color:red"> ' + eventLevelName + ' </p>'
                    } else if (eventLevelCode === '02') {//二级
                        eventLevelName = '<p style="background-color:orange"> ' + eventLevelName + ' </p>'
                    } else if (eventLevelCode === '03') {//三级
                        eventLevelName = '<p style="background-color:yellow"> ' + eventLevelName + ' </p>'
                    } else if (eventLevelCode === '04') {//四级
                        eventLevelName = '<p style="background-color:lightblue"> ' + eventLevelName + ' </p>'
                    }
                    //状态
                    let status = data.records[i].status;
                    let statusName = '未知';
                    if (status === '10') {
                        statusName = "进行中";
                    } else if (status === '20') {
                        statusName = "已完结";
                    }
                    $('#larger-event-list').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.records[i].id,
                            reportTime: data.records[i].reportItemList[0].reportTime, // 上报时间
                            reportTypeName: data.records[i].reportItemList[0].reportTypeName, // 上报类别
                            reportTypeCode: data.records[i].reportItemList[0].reportTypeCode, // 上报类别
                            reportContent: data.records[i].reportItemList[0].reportContent,// 汇报内容
                            woundedNum: data.records[i].reportItemList[0].woundedNum,//伤员人数
                            occurrenceTime: data.records[i].occurrenceTime,// 发生时间
                            eventTypeCode: data.records[i].eventTypeCode, //事件类型
                            eventTypeName: data.records[i].eventTypeName, // 事件类型
                            eventLevelCode: eventLevelCode, // 事件等级
                            eventLevelName: eventLevelName, // 事件等级
                            eventCode: data.records[i].eventCode,// 事件编码
                            status: status,
                            statusName: statusName
                        }
                    });
                }
                $("#larger-event-list").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    size: data.size,
                    pageNumber: data.current,
                });
                $('#larger-event-list').datagrid('loaded');//关闭loding进度条；

            }, function (e, url, errMsg) {
                $('#larger-event-list').datagrid('loaded');//关闭loding进度条；
                //失败才做处理
            });
        }
        // 事件研判
        function largerEventYP(index) {
            let eventCode = largerList[index].eventCode;
            let id = largerList[index].id;
            let token = null;
            try {
                token = window.parent.gProxy.getUserToken(); //将座席信息存储到全局变量
            } catch (e) {
                token = window.parent.bsProxy.getUserToken(); //将座席信息存储到全局变量
            }
            //let url = 'http://192.168.2.122:8888/emergency-management-web/#/?type=yp&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
            //let url = 'http://corsyn.uicp.net:8888/emergency-management-web/#/?type=yp&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
            //window.parent.gProxy.openBrowser(url);

            querySysConf('emergency_management_web_url',
                function (data) {
                    //data值例子：http://192.168.2.122:8888/emergency-management-web/#/
                    let url = data + '?type=yp&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
                    try {
                        window.parent.gProxy.openBrowser(url);
                    } catch (e) {
                        window.parent.opener.windowOpenUrl(url);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
                });
        }
        // 事件处置
        function largerEventCZ(index) {
            let eventCode = largerList[index].eventCode;
            let id = largerList[index].id;
            let token = null;
            try {
                token = window.parent.gProxy.getUserToken(); //将座席信息存储到全局变量
            } catch (e) {
                token = window.parent.bsProxy.getUserToken(); //将座席信息存储到全局变量
            }
            //let url = 'http://192.168.2.122:8888/emergency-management-web/#/?&type=cz&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
            //let url = 'http://corsyn.uicp.net:8888/emergency-management-web/#/?&type=cz&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
            //window.parent.gProxy.openBrowser(url);

            querySysConf('emergency_management_web_url',
                function (data) {
                    //data值例子：http://192.168.2.122:8888/emergency-management-web/#/
                    let url = data + '?&type=cz&eventInfoId=' + id + '&eventCode=' + eventCode + '&token=' + encodeURIComponent(token);
                    try {
                        window.parent.gProxy.openBrowser(url);
                    } catch (e) {
                        window.parent.opener.windowOpenUrl(url);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取跳转链接失败，异常信息：' + errMsg);
                });
        }
        function uploadOperationFormatter(value, row, index) {
            let node = `
                <a href="javascript:largerEventUpload(${index});" class="easyui-linkbutton" style="display: inline-block;height: 22px;line-height: 22px;color: white;background: #39c;width: auto;padding: 0 6px;font-size: 1em;margin: 5px 3px 5px 0;border-radius: 5px;}">详情</a>
                `;
            if (row.status === '10') {
                node += `
                    <a href="javascript:largerEventYP(${index});" class="easyui-linkbutton" style="display: inline-block;height: 22px;line-height: 22px;color: white;background: #39c;width: auto;padding: 0 6px;font-size: 1em;margin: 5px 3px 5px 0;border-radius: 5px;}">研判</a>
                    <a href="javascript:largerEventCZ(${index});" class="easyui-linkbutton" style="display: inline-block;height: 22px;line-height: 22px;color: white;background: #39c;width: auto;padding: 0 6px;font-size: 1em;margin: 5px 3px 5px 0;border-radius: 5px;}">处置</a>
                    `
            }
            return node
        }

        // 通信录弹窗
        function phoneList(flag) {
            isEdit = flag
            $('#phone-list').window({
                title: `<div>选择通讯录</div>`
            });
            let phoneArr = []
            getPhoneList({}, function (res) {
                for(let i=0;i<res.length;i++){
                    let parent = {
                        title:res[i].groupName,
                        id:res[i].id,
                        children:[]
                    }
                    for(let c=0;c<res[i].phoneBooks.length;c++){
                        let itemChild = res[i].phoneBooks[c]
                        let child = {
                            title:itemChild.contactor,
                            id:itemChild.id,
                            number:itemChild.number
                        }

                        parent.children.push(child)
                    }
                    phoneArr.push(parent)
                }

                layui.use(function () {
                    phoneTree = layui.tree;
                var layer = layui.layer;
                var util = layui.util;
                // 渲染
                phoneTree.render({
                    elem: '#phone-book',
                    data: phoneArr,
                    id: 'phone-list',
                    showCheckbox: true,
                    edit: [], // 开启节点的右侧操作图标

                });
            });
            $('#phone-list').window('open')
            }, function (e, url, errMsg) {
                //失败做处理
                $.messager.alert('提示', errMsg);
            })


        }

        //关闭通讯录弹窗
        function cancelPhoneList(){
            $('#phone-list').window('close')
        }

        //将yyyyMMddHHmmss转换为yyyy年MM月dd日 HH时mm分
        function convertTime(time) {
            let timeArr = time.split('')
            let y = timeArr.slice(0, 4).join('')
            let m = timeArr.slice(4, 6).join('')
            let d = timeArr.slice(6, 8).join('')
            let hh = timeArr.slice(8, 10).join('')
            let mm = timeArr.slice(10, 12).join('')
            let ss = timeArr.slice(12, 14).join('')
            return y + '年' + m + '月' + d + '日 ' + hh + '点' + mm + '分'
        }
    </script>
</body>
</html>
