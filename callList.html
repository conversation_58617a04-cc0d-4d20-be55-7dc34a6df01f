<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端第二屏幕界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 陈蒋耀
* Created: 2019/6/5
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>120急救指挥中心调度平台第二屏幕</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
        .l-btn-text {
            display: inline-block;
            vertical-align: top;
            width: auto;
            line-height: 28px;
            font-size: 14px;
            padding: 0;
            margin: 0 6px;
            font-weight: bold;
        }
        .datagrid-cell {
            font-size: 14px;
            font-weight: bold;
        }
        /*datagrid表头高度与字体设置*/
        .datagrid-header span {
            font-size: 12px !important;
        }

        .datagrid-header-row {
            /*background-color: #e3e3e3; /* color: #111 */
            Height: 28px;
        }
        /*去掉百度地图上面的文字*/
        .anchorBL {
            display: none;
        }

        .window-body {
            overflow-y: hidden;
        }
        /*datagrid行高和字体设置设置*/
        .datagrid-row {
            height: 28px;
        }

        .datagrid-btable tr {
            height: 28px;
            font-size: 12px !important;
        }
        .datagrid-wrap .datagrid-body tr:hover {
            background-color: #12B5F8 !important;
            color: #ffffff !important;
        }

        .datagrid-wrap .datagrid-body tr:hover a {
                color: #ffffff !important;
        }
        .panel-tool-close{
            background-size: 360% 240%;
            background-position: -21px -1px;
            width: 20px;
            height: 20px;
        }
        .textbox .textbox-text{
            font-size: 14px;
        }
        .panel-title{
            font-size: 15px;
        }
        .l-btn-text{
            vertical-align: unset;
        }
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
            vertical-align: top;
            line-height: 18px;
            position: relative;
        }
        input[type="checkbox"]::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            background: #fff; /* 未选中时的背景颜色 */
            width: 100%;
            height: 100%;
            border: 1px solid #d9d9d9; /* 未选中时的边框颜色 */
        }
        input[type="checkbox"]:checked::before {
            content: "\2713"; /* 对号的转义符 √ */
            background-color: #fff; /* 选中时的背景颜色 */
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            border: 1px solid #ccc; /* 选中后的边框颜色 */
            color: #000; /* 选中后的文字颜色   */
            font-size: 20px;
            font-weight: bold;
        }
        a {
            text-decoration: none !important;
        }
        .window{
            overflow: initial !important; 
        }
        #wavPlayWindow{
            overflow: initial;
        }
        .select-common{
            height: 32px;
            width: 240px;
            margin-left: 0;
            font-size: 1.4em;
        }
    </style>
</head>
<body>
    <div id="mainPanel_CallList" data-options="fit:true,border:false" style="height: 100%;">
        <div class="group-box" style="height: Calc(100% - 20px); width: Calc(100% - 10px);display: inline-block; margin: 5px 5px 5px 5px;">
            <div class="group-box-title">通话列表<span style="font-size: 9px; margin-left: 5px; font-weight: normal;"></span></div>
            <div style="padding: 10px;">
                <div id="callSearch" style="font-size: 14px;">
                    创建时间： <input id="beginCallTime" class="easyui-datetimebox" style="width:200px; margin-top: 10px;" />
                    <span>至</span>
                    <input id="endCallTime" class="easyui-datetimebox" style="width:200px;" />
                    &nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>
                    <span>&nbsp;主叫号码：</span>
                    <input id="number" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;被叫号码：</span>
                    <input id="ext" class="easyui-textbox" style="width:150px">
                    <span>&nbsp;处理情况：</span>
                    <select id="handleStatus" class="easyui-combobox" name="handleStatus" style="width:100px;">
                        <option value="">全部</option>
                        <option value="0">无效通话</option>
                        <option value="1">已处理</option>
                        <option value="2">已合并</option>
                        <option value="3">未处理</option>

                    </select>
                    <span>&nbsp;呼叫类型：</span>
                    <select id="callType" class="easyui-combobox" name="callType" style="width:100px;">
                        <option value="">全部</option>
                        <option value="IN">呼入</option>
                        <option value="OU">呼出</option>
                        <option value="LO">内部呼叫</option>
                    </select>
                    <span>&nbsp;是否接听：</span>
                    <select id="isAnswer" class="easyui-combobox" name="isAnswer" style="width:100px;">
                        <option value="">全部</option>
                        <option value="1">已接听</option>
                        <option value="0">未接听</option>
                    </select>
                    &ensp;<input type="checkbox" id="autoRefreshList" name="autoRefreshList" value="0" checked="checked" />自动刷新
                    <a class="common-button" id="getDgCallListDatasBtn" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:getDgCallListDatas();">
                        <i class="fa fa-search"></i>&nbsp;搜索
                    </a>
                </div>
            </div>
            <div style="width:100%;height: Calc(100% - 85px); position: relative;font-size: 14px;">
                <table class="easyui-datagrid" id="dg-calls-pages" style="height: Calc(100% - 100px)"
                       pagination="true" singleSelect="true" rownumbers="true" fit="true" toolbar="#tbCallList">
                    <thead>
                        <tr>
                            <th field="callTime" width="180" align="center">来/去电时间</th>
                            <th field="number" width="160" checkbox="false" align="center">主叫号码</th>
                            <th field="toNumber" width="160" align="center">被叫号码</th>
                            <th field="callType" width="160" align="center" formatter="callTypeFormatter">呼叫类型</th>
                            <th field="type" width="160" align="center" formatter="typeFormatter">类型</th>
                            <th field="endTime" width="180" align="center">挂断时间</th>
                            <th field="handleStatus" width="80" align="center" formatter="handleStatusFormatter">处理结果</th>
                            <th field="duration" width="100" align="center">通话时长（秒）</th>
                            <th field="isAnswer" width="100" align="center" formatter="isAnswerFormatter">是否接听</th>
                            <th field="seatUser" width="100" align="center">接听人</th>
                            <th field="eventId" width="210" align="center" formatter="eventIdFormatter">关联事件</th>
                            <th field="recordAddr" width="60" align="center" formatter="wavPlayFormatter">录音</th>
                            <th field="callbackTel" width="60" align="center" formatter="callbackPhoneFormatter">回拨</th>
                            <th field="id" width="120" align="center" formatter="deauthenticationFormatter">取消关联</th>
                        </tr>
                    </thead>
                </table>
                <div id="tbCallList" style="height:45px;">
                    <div>
                        <a id="relevanceEventInfoBtn" class="easyui-linkbutton" href="javascript:relevanceEventInfo();" style="width: 100px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-filter'">关联事件</a>
                        <a id="updateCallStatusAndTypeBtn" class="easyui-linkbutton" href="javascript:updateCallStatusAndTypeBtn();" style="width: 100px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-edit'">更新通话</a>
                        <a id="addBlacklistBtn" class="easyui-linkbutton" href="javascript:addBlacklist();" style="width: 150px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-add'">加入黑名单</a>
                        <a id="checkBlacklistBtn" class="easyui-linkbutton" href="javascript:checkBlacklist();" style="width: 150px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-show'">查看黑名单</a>
                        <!-- 打开一个web页面，现在做了三屏和四屏，可以不要这个功能 -->
                        <!-- <a id="redirectAllCallBtn" class="easyui-linkbutton" href="javascript:redirect();" style="width: 150px;height:30px;line-height: 30px; font-size: 12px; margin: 5px" data-options="iconCls:'icon-redo'">查看全部通话</a> -->
                    </div>
                </div>
                
            </div>
        </div>
        <!-- 关联事件窗口 -->
        <div id="eventInfoSelectWindow" class="easyui-window" title="选择事件关联呼救电话（请选择一条事件点击关联保存操作）" style="width:1440px;height:900px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="padding: 10px;height: 100%;">
                <div style="margin-bottom:10px;height:34px;">
                    <div id="eventSearch" style="font-size: 14px;">
                        创建时间： <input id="startCreateTime" class="easyui-datetimebox" style="width:150px; margin-top: 10px;" />
                        <span>至</span>
                        <input id="endCreateTime" class="easyui-datetimebox" style="width:150px;" />
                        <!--&nbsp;<a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>-->
                        <span>&nbsp;呼叫原因：</span>
                        <input id="majorCall" class="easyui-textbox" style="width:150px">
                        <span>&nbsp;地址：</span>
                        <input id="address" class="easyui-textbox" style="width:150px">
                        <span>&nbsp;任务状态：</span>
                        <select id="eventStatus" class="easyui-combobox" name="dept" style="width:100px;">
                            <option value="0">全部</option>
                            <option value="7">未调度-预约</option>
                            <option value="1">未调度-落单</option>
                            <option value="6">未调度-待派</option>
                            <option value="2">已调度</option>
                            <option value="3">已完成</option>
                            <option value="4">已撤销</option>
                        </select>
                        <a id="getDgEventListDatasBtn" class="easyui-linkbutton" href="javascript:getDgEventListDatas();" style="width: 80px;height:25px;line-height: 25px; font-size: 12px; margin: 5px">搜索事件</a>
                        <a id="updateEventIdAndHandleStatusBtn" class="easyui-linkbutton" href="javascript:updateEventIdAndHandleStatusBtn();" style="width: 80px;height:25px;line-height: 25px; font-size: 12px; margin: 5px">关联保存</a>
                    </div>
                </div>
                <div style="width:100%;height: Calc(100% - 44px); position: relative;font-size: 14px;">
                    <table class="easyui-datagrid" id="dg-event-pages" style="width: 100%; height:100%;"
                           pagination="true" singleSelect="true" rownumbers="true" fit="false">
                        <thead>
                            <tr>
                                <th field="eventId" width="180" checkbox="false" align="center">编号</th>
                                <th field="createTime" width="150" checkbox="false" align="center">创建时间</th>
                                <th field="eventName" width="400" checkbox="false" align="center">事件名称</th>
                                <!--<th field="majorCall" width="180" checkbox="false" align="center">呼叫原因</th>-->
                                <!--<th field="address" width="320" align="center">地址</th>-->
                                <th field="callIn" width="100" align="center">呼救电话</th>
                                <th field="callInTimes" width="150" align="center">来电时间</th>
                                <th field="eventStatusStr" width="80" align="center">状态</th>
                                <th field="eventSrcName" width="80" align="center">来源</th>
                                <th field="callTypeName" width="80" align="center">类型</th>
                                <th field="contact" width="100" align="center">联系电话</th>
                                <th field="contacter" width="100" align="center">联系人</th>
                                <th field="processCount" width="100" align="center">任务总数(派车)</th>
                                <th field="runProcessCount" width="80" align="center">运行任务数</th>
                                <th field="finishedProcessCount" width="80" align="center">完成任务数</th>
                                <th field="cancelProcessCount" width="100" align="center">撤销任务数(中止)</th>
                                <th field="eventCreateSeatUser" width="80" align="center">创建人</th>
                                <th field="eventCreateSeatCode" width="80" align="center">创建座席</th>
                                <th field="centerRemark" width="200" align="center">备注</th>
                                <th field="firstSeatUser" width="100" align="center">首次调度员</th>
                                <th field="firstCreateTime" width="150" align="center">首次受理时刻</th>
                                <th field="firstCarDispatchedTime" width="150" align="center">首次派车时刻</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        <!--更新通话类型弹窗-->
        <div id="updateCall" class="easyui-window" title="更新通话" style="width:400px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
             <p>
                <input type="hidden" value="" id="update-call-id" />
                <span style="display:inline-block;width:90px;font-size:16px;text-align: right;">处理结果:</span>
                <select id="update-call-handleStatus" class="select-common">
                    <option value="0">无效</option>
                    <option value="1">已处理</option>
                    <option value="2">已合并</option>
                    <option value="3">未处理</option>
                </select>
            </p>
            <p>
                <span style="display:inline-block;width:90px;font-size:16px;text-align: right;">类型:</span>
                <select id="update-call-type" class="select-common">
                </select>
            </p>
            <p style="text-align:right">
                <a class="easyui-linkbutton" id="refresh-call-btn" href="javascript:$('#updateCall').window('close');" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">取 消</a>
                <a class="easyui-linkbutton" id="refresh-call-btn" href="javascript:affirmUpdateCall();" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">确 定</a>
            </p>
        </div>
        <!--加入黑名单弹窗-->
        <div id="blacklist" class="easyui-window" title="加入黑名单" style="width:400px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <p>
                <span style="display:inline-block;width:90px;font-size:16px;text-align: right;">电话:</span>
                <input id="blacklist-number" class="easyui-numberbox" value="" style="height:32px;width: 240px;">
            </p>
            <p>
                <span style="display:inline-block;width:90px;font-size:16px;text-align: right;">时间间隔:</span>
                <select id="blacklist-period" class="select-common">
                    <option value="30">30分钟</option>
                    <option value="60">1小时</option>
                    <option value="300">5小时</option>
                    <option value="1440">24小时</option>
                </select>
            </p>
            <p>
                <span style="display:inline-block;width:90px;font-size:16px;text-align: right;">备注:</span>
                <input id="blacklist-remark" class="easyui-textbox" value="" style="height:32px;width: 240px;" />
            </p>
            <p style="text-align:right">
                <a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:$('#blacklist').window('close');" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">取 消</a>
                <a class="easyui-linkbutton" id="refresh-event-btn" href="javascript:affirmBlacklist();" style="width: 80px;height:30px;line-height: 30px; font-size: 12px; margin: 5px;">确 定</a>
            </p>
        </div>
        <!--查看黑名单弹窗-->
        <div id="check-blacklist" class="easyui-window" title="黑名单列表" style="width:1200px;height: 800px;"
             data-options="resizable:false,modal:true,closed:true,closable:true,minimizable:false,maximizable:false,draggable:false,collapsible:false">
            <div style="padding: 10px;height: 100%;">
                <div style="padding: 10px;">
                    <div id="callSearch" style="font-size: 14px;">
                        <span>&nbsp;电话号码：</span>
                        <input id="query-blacklist-number" class="easyui-textbox" style="width:150px">
                        <span>&nbsp;操作人：</span>
                        <input id="blacklist-actionUser" class="easyui-textbox" style="width:150px">
                        <a class="common-button" id="getblacklist" style="width: 70px; height: 30px; line-height: 30px;font-size: 14px;" href="javascript:queryBlacklist();">
                            <i class="fa fa-search"></i>&nbsp;搜索
                        </a>
                    </div>
                </div>
                <div style="width:100%;height: Calc(100% - 55px); position: relative;font-size: 14px;">
                    <table class="easyui-datagrid" id="blacklist-list" style="width: 100%; height: 100%;"
                           pagination="true" singleSelect="true" rownumbers="true" fit="false">
                        <thead>
                            <tr>
                                <th field="number" width="180" checkbox="false" align="center">电话号码</th>
                                <th field="period" width="100" checkbox="false" align="center">拉黑时长</th>
                                <th field="startTime" width="180" checkbox="false" align="center">开始时间</th>
                                <th field="endTime" width="180" checkbox="false" align="center">结束时间</th>
                                <th field="createUser" width="120" checkbox="false" align="center">操作人</th>
                                <th field="remark" width="200" checkbox="false" align="center">备注</th>
                                <th field="id" width="150" checkbox="false" formatter="feature" align="center">功能</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        <!-- 录音回放窗口 -->
        <div id="wavPlayWindow" class="easyui-window" title="录音回放" style="background-color:#FAFAFA; width: 600px; height: 100px; text-align: center; padding-top:5px"
             data-options="resizable:false,modal:true,maximizable:false,minimizable:false,collapsible:false,closed:true">
            <div style="position: relative;">
                <audio id="playWavAudio" src="" style="width: 100%;height: 42px;" autostart="false" controls></audio>
                <img id="download" style="position: absolute;top:12px;right:5px;height: 24px;" src="http://preview.qiantucdn.com/58pic/20220321/00S58PIC5Xkc6mdzM5xU3_PIC2018_PIC2018.jpg!w290_290" />
            </div>
        </div>
        <!--事件详情窗口-->
        <div id="eventInfoWindow" class="easyui-window" title="" style="width:600px;height:400px"
             data-options="iconCls:'icon-save',modal:true, closed: true">
            <!-- <iframe id="eventInfoIframe" name="eventInfoIframe" scrolling="auto" src="eventInfo.html" frameborder="0" style="width:100%;height:100%;"></iframe> -->
            <iframe id="eventInfoIframe" name="eventInfoIframe" scrolling="auto" frameborder="0" style="width:100%;height:100%;"></iframe>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/gpsConvert.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        _baseUrl = "";
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _pSeat = null;
        try {
            _pSeat = evalJson(window.parent.gProxy.getSeatInfo());
        } catch (e) {
            _pSeat = evalJson(window.parent.bsProxy.getSeatInfo());
        }
        var relevanceCallId = "";//需要关联的呼入记录ID
        var callLogid = ''//通话记录id
        var downloadUrl = ''
        var playWavAudio = null //语音播放插件
        var callTypeList = [] //呼叫类型
        $(document).ready(function () {
            //按钮格式调整,自动计算出来的可能会出现问题。直接设置
            $("#relevanceEventInfoBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            $("#updateCallStatusAndTypeBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            $("#addBlacklistBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            $("#checkBlacklistBtn").find('.l-btn-icon-left').css("margin-top", "0px");
            //获得开始时间
            var curDate = new Date();
            var fromTime = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
            $("#beginCallTime").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd hh:mm:ss'));

            $('#dg-calls-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
            });
            //为所有快速时间操作设置处理函数
            $('.inAnHour,.inToday').on("click", timeClickHandler);
            //分页控件
            var pg = $("#dg-calls-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: 20,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        getDgCallListDatas(pageNumber, pageSize);
                    }
                });
            }

            //关闭播放wav录音的窗口时
            $('#wavPlayWindow').window({
                onBeforeClose: function () {
                    var playWavAudio = document.getElementById('playWavAudio');
                    playWavAudio.pause(); //暂停播放
                }
            });
            //事件选择window
            $('#eventInfoSelectWindow').window({
                onBeforeOpen: function () {
                    var curDate = new Date();
                    var fromTime = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
                    $("#startCreateTime").datetimebox("setValue", fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
                    getDgEventListDatas(1, 20);
                },
                onBeforeClose: function () {

                }
            });


            $('#dg-event-pages').datagrid({
                loadMsg: '数据加载，请稍等.....',
                onClickRow: function (rowIndex, rowData) {
                    if (rowData.eventStatus == '1' || rowData.eventStatus == '2') {
                        $('#updateRecoverEventBtn').linkbutton("disable");
                    } else {
                        $('#updateRecoverEventBtn').linkbutton("enable");
                    }
                },
            });
            //分页控件
            var pg = $("#dg-event-pages").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: 20,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //getDgEventListDatas();
                        //刷新之前执行
                        //console.log('dg-event-pages before refresh');
                    },
                    onRefresh: function (pageNumber, pageSize) {
                        //getDgEventListDatas();
                        //console.log('dg-event-pages onRefresh pageNumber' + pageNumber + "; pageSize:" + pageSize);
                    },
                    onChangePageSize: function () {
                        //getDgEventListDatas();
                        //console.log('dg-event-pages pagesize changed');
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        getDgEventListDatas(pageNumber, pageSize);
                        //console.log('dg-event-pages onSelectPage pageNumber' + pageNumber + "; pageSize:" + pageSize);
                    }
                });
            }

            getDgCallListDatas();
            callListAutoRefreshIntervalTimeout();
        });

        //获得120电话呼入列表数据
        function getDgCallListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }

            var beginCallTimeVal = $('#beginCallTime').datetimebox('getValue');
            var beginCallTime = beginCallTimeVal == undefined || beginCallTimeVal == "" ? null : new Date(beginCallTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endCallTimeVal = $('#endCallTime').datetimebox('getValue');
            var endCallTime = endCallTimeVal == undefined || endCallTimeVal == "" ? null : new Date(endCallTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            //打开进度条：
            $('#dg-calls-pages').datagrid('loading');//打开等待div

            $('getDgCallListDatasBtn').attr('disabled', 'disabled');//按钮变成不能使用
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "beginCallTime": beginCallTime,
                "endCallTime": endCallTime,
                "number": $('#number').val(),
                "isAnswer": $('#isAnswer').combobox('getValue'),
                "handleStatus": $('#handleStatus').combobox('getValue'),
                "callType": $('#callType').val(),
                "ext": $('#ext').val()
            };
            getCallPageList(params, function (data) {
                $('#dg-calls-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-calls-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            number: data.content[i].number,
                            toNumber: data.content[i].toNumber,
                            callType: data.content[i].callType,
                            callTime: data.content[i].callTime,
                            handleTime: data.content[i].handleTime,
                            endTime: data.content[i].endTime,
                            handleStatus: data.content[i].handleStatus,
                            eventId: data.content[i].eventId,
                            recordAddr: data.content[i].recordAddr,
                            callId: data.content[i].callId,
                            visitorId: data.content[i].visitorId,
                            outerId: data.content[i].outerId,
                            duration: data.content[i].duration,
                            createTime: data.content[i].createTime,
                            createUser: data.content[i].createUser,
                            lastUpdateTime: data.content[i].lastUpdateTime,
                            lastUpdateUser: data.content[i].lastUpdateUser,
                            isAnswer: data.content[i].isAnswer,
                            seatUserName: data.content[i].seatUserName || '无',
                            seatUser: data.content[i].seatUser || '无',
                            recordName: data.content[i].recordName,
                            type: data.content[i].type
                        }
                    });
                }
                $("#dg-calls-pages").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                $('#dg-calls-pages').datagrid('loaded');//关闭loding进度条；
                $('#getDgCallListDatasBtn').removeAttr('disabled');//按钮变成可用
            }, function (e, url, errMsg) {
                $('#dg-calls-pages').datagrid('loaded');//关闭loding进度条；
                $('#getDgCallListDatasBtn').removeAttr('disabled');//按钮变成可用
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        //查询相关字典
        queryAllDic(["call_type"],
            function (data) {
                _dics = data
                //呼叫类型
                callTypeList = insertIntoSelect('update-evd-call-type', 'call_type', data);
                insertIntoSelect('update-call-type', 'call_type', data);
            }, function (e, url, errMsg) { }
        );
        function insertIntoSelect(id, typeCode, dicts) {
            var ui = $('#' + id);
            var list = getDicList(typeCode, dicts);
            ui.empty();
            for (var i = 0; i < list.length; i++) {
                var d = list[i];
                ui.append('<option value="{0}">{1}</option>'.formatStr(d.codeName, d.codeVale));
            }
            if (list != null && list.length > 0) {
                ui.val(list[0].codeName);
            }
            return list;
        }

        //关联事件窗口
        function relevanceEventInfo() {
            //获得当前选中行
            var rows = $('#dg-calls-pages').datagrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条通话记录进行关联事件!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条通话记录进行关联事件!', 'error');
                return;
            }
            relevanceCallId = rows[0].id;
            $('#eventInfoSelectWindow').window('open');
            getDgEventListDatas(1, 20);

        }

        //拨打电话
        function callbackPhoneFormatter(value, row, index) {
            let phone = row.callType == 'OU' ? row.toNumber : row.callType == 'IN' ? row.number : row.number
            if (phone) {
                return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#" style="color: #099DFC;" onclick="callingPhone(\'' + phone + '\')">拨打</a>';
            }
        }
        function eventDeauthentication(id) {
            $.messager.confirm("取消关联", "是否确定取消关联？", function (r) {
                if (r) {
                    unbindCallAndEvent({ id }, function (res) {
                        getDgCallListDatas()
                        $.messager.alert('成功', res.msg);
                    },
                        function (e, url, errMsg) {
                            $.messager.alert('异常', '异常信息：' + errMsg);
                        }
                    )
                }
            })
        }
        //处理结果
        function handleStatusFormatter(value, row, index) {
            switch (row.handleStatus) {
                case "0":
                    return "无效通话";
                case "1":
                    return "已处理";
                case "2":
                    return "已合并";
                case "3":
                    return "未处理";
            }
        }

        function isAnswerFormatter(value, row, index) {
            switch (row.isAnswer) {
                case "0":
                    return "未接听";
                case "1":
                    return "已接听";
            }
        }
        function callTypeFormatter(value, row, index) {
            switch (row.callType) {
                case "IN":
                    return "呼入";
                case "OU":
                    return "呼出";
                case "LO":
                    return "内部呼叫";
            }
        }
        function typeFormatter(callType) {
            let obj = callTypeList.filter(r => r.codeName == callType)[0]
            if (obj) {
                return obj.codeVale
            }
            return ''
        }
        function eventIdFormatter(value, row, index) {
            if (row.eventId != null && row.eventId != "") {
                return '<a href="#"  style="color: #099DFC;" onclick="openEventInfo(\'' + row.eventId + '\')">' + row.eventId + '</a>';
            } else {
                //如果没有关联事件的话，那么创建事件按钮（非报停车辆，未处理
                if (row.handleStatus == "3" && row.type != "70") {
                    let phone = row.callType == 'OU' ? row.toNumber : row.callType == 'IN' ? row.number : row.number;
                    try {
                        _baseUrl = window.parent.gProxy.getBaseUrl();
                        return '<a href="#"  style="color: #099DFC;" onclick="window.parent.gProxy.createNewEventByPhone(\'' + phone + '\',\'' + row.id + '\',\'' + row.callTime + '\')">创建事件</a>';
                    } catch (e) {
                        return '<a href="#"  style="color: #099DFC;" onclick="window.parent.opener.createNewEventByPhone(\'' + phone + '\',\'' + row.id + '\',\'' + row.callTime + '\')">创建事件</a>';
                    }
                    
                }
                //报停车辆处理（已处理）
                if(row.handleStatus == '1' && row.type == "70"){
                    return '<a href="#" onclick="showStopRecordDetail(\'' + row.id + '\')" style="color: #1890ff;">报停详情</a>';
                }
                
            }
        }
        function deauthenticationFormatter(value, row, index) {
            if (row.eventId && row.eventId !== " ") {
                return '<a href="#"  style="color: #099DFC;" onclick="eventDeauthentication(\'' + row.id + '\')">取消关联</a>'
            }
            return ''
        }
        //播放录音
        function wavPlayFormatter(value, row, index) {
            var address = "";
            if (row.recordAddr && row.recordAddr !== '' && row.recordAddr !== ' ') {
                address = row.recordAddr;
                if (row.number) {
                    return '<i class="fa fa - volume - control - phone"></i>&nbsp;<a href="#"  style="color: #099DFC;" onclick="playWav(\'' + address + '\',\'' + row.recordName + '\')">播放</a>';
                }
            } else {
                return '';
            }

        }

        let throttle = null;
        //录音窗口打开
        function playWav(address, name) {
            $('#playWavAudio').attr('src', address);
            $('#wavPlayWindow').window('open');
            downloadUrl = address
            $('#download').off('click').on('click', () => {
                if (!throttle) {
                    throttle = true
                    downloadURL(downloadUrl, downloadUrl.substring(downloadUrl.lastIndexOf("/") + 1))
                    setTimeout(() => {
                        throttle = null;
                    }, 1000);
                }
            })
        }
        function downloadURL(url, name) {
            window.parent.gProxy.download(url, name)
        }
        
        //获得事件列表数据
        function getDgEventListDatas(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }

            var startCreateTimeVal = $('#startCreateTime').datetimebox('getValue');
            var startCreateTime = startCreateTimeVal == undefined || startCreateTimeVal == "" ? null : new Date(startCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            var endCreateTimeVal = $('#endCreateTime').datetimebox('getValue');
            var endCreateTime = endCreateTimeVal == undefined || endCreateTimeVal == "" ? null : new Date(endCreateTimeVal).formatString("yyyy-MM-dd hh:mm:ss");

            //打开进度条：
            $('#dg-event-pages').datagrid('loading');//打开等待div
            
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "startCreateTime": startCreateTime,
                "endCreateTime": endCreateTime,
                "majorCall": $("#majorCall").val(),
                "address": $('#address').val(),
                "eventStatus": $('#eventStatus').combobox('getValues'),
            };
            getEventPages(params, function (data) {
                $('#dg-event-pages').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#dg-event-pages').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            eventId: data.content[i].eventId,
                            eventStatus: data.content[i].eventStatus,
                            eventStatusStr: data.content[i].eventStatusStr,
                            callIn: data.content[i].callIn,
                            callInTimes: data.content[i].callInTimes,
                            createTime: data.content[i].createTime,
                            majorCall: data.content[i].majorCall ? data.content[i].majorCall.replace("_", " ") : "",
                            eventName: data.content[i].address + " + " + (data.content[i].patientName ? data.content[i].patientName : "无名氏") + " + " + (data.content[i].majorCall ? data.content[i].majorCall.replace("_", " ") : ""),
                            eventSrcCode: data.content[i].eventSrcCode,
                            eventSrcName: data.content[i].eventSrcName,
                            callTypeCode: data.content[i].callTypeCode,
                            lng: data.content[i].lng,
                            lat: data.content[i].lat,
                            callTypeName: data.content[i].callTypeName,
                            contact: data.content[i].contact,
                            contacter: data.content[i].contacter,
                            addressWait: data.content[i].addressWait,
                            address: data.content[i].address,
                            createUser: data.content[i].createUser,
                            eventCreateSeatCode: data.content[i].eventCreateSeatCode,
                            eventCreateSeatUserId: data.content[i].eventCreateSeatUserId,
                            eventCreateSeatUserName: data.content[i].eventCreateSeatUserName,
                            eventCreateSeatUser: data.content[i].eventCreateSeatUser,
                            processCount: data.content[i].processCount,
                            runProcessCount: data.content[i].runProcessCount,
                            finishedProcessCount: data.content[i].finishedProcessCount,
                            cancelProcessCount: data.content[i].cancelProcessCount,
                            centerRemark: data.content[i].centerRemark,
                            firstSeatUserName: data.content[i].firstSeatUserName,
                            firstSeatUser: data.content[i].firstSeatUser,
                            firstCreateTime: data.content[i].firstCreateTime,
                            firstCarDispatchedTime: data.content[i].firstCarDispatchedTime
                        }
                    });
                }
                $("#dg-event-pages").datagrid({
                    rowStyler: function (index,row) {
                            switch (row.eventStatus) {
                                case '1':
                                    return 'background-color:#dad6d557;';//灰色
                                case "2":
                                    return 'background-color:#f9a296ad;';//红色
                                case "3":
                                    return 'background-color:#dad6d557;';//灰色
                                case "4":
                                    return 'background-color:#dad6d557;';//灰色
                            }
                        },   
                });
                $("#dg-event-pages").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                $('#dg-event-pages').datagrid('loaded');//关闭loding进度条；
            }, function (e, url, errMsg) {
                $('#dg-event-pages').datagrid('loaded');//关闭loding进度条；
                //失败才做处理
                //$.messager.alert('异常', '异常信息：' + errMsg);
            });
        }
        
        function openEventInfo(eventId) {
            var title = "事件详情" + "(" + eventId + ")"
            //获得当前选中行
            $('#eventInfoWindow').window({
                width: 1440,
                height: 950,
                modal: true,
                title: title,
                collapsible: false,
                minimizable: false,
                maximizable: false,
                draggable: false,//不能拖拽
                resizable: false,
                //left: ($(window).width() - 650) * 0.5,
                //top: ($(window).width() - 650) * 0.5,
            });

            href = "eventInfo.html?eventId=" + eventId + "&value=secondScr";
            $('#eventInfoIframe').attr('src', href);
            $('#eventInfoWindow').window('open');

        }

        //关联事件ID
        function updateEventIdAndHandleStatusBtn() {
            //获得当前选中行
            var rows = $('#dg-event-pages').datagrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件与通话记录进行关联!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件与通话记录进行关联!', 'error');
                return;
            }
            showProcess(true, '温馨提示', '正在关联通话记录，请稍后...');
            bindCallAndEvent(relevanceCallId, rows[0].eventId,
                function (data) {
                    showProcess(false);
                    $.messager.alert('提示', '关联通话记录成功', 'info');
                    $('#eventInfoWindow').window('close', true);
                }, function (e, url, errMsg) {
                    showProcess(false);
                    //失败才做处理
                    $.messager.alert('异常', '异常信息：' + errMsg);
                });
        }

        //更新通话处理结果和类型
        function updateCallStatusAndTypeBtn() {
            //获得当前选中行
            var rows = $('#dg-calls-pages').datagrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件与通话记录进行关联!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件与通话记录进行关联!', 'error');
                return;
            }
            $('#update-call-id').val(rows[0].id);
            $('#update-call-handleStatus').val(rows[0].handleStatus);
            $('#update-call-type').val(rows[0].type);
            $('#updateCall').window('open'); //打开更新通话弹窗
        }
        function affirmUpdateCall() {
            var params = {
                id: $('#update-call-id').val(),
                handleStatus: $('#update-call-handleStatus').val(),
                type: $('#update-call-type').val(),
            }
            //更新到通话记录
            updateHandleStatusAndType(params, function (res) {
                showProcess(false);
                $('#updateCall').window('close');
                getDgCallListDatas()
            },
            function (e, url, errMsg) {
                showProcess(false);
                $.messager.alert('提示', '更新通话记录失败' + errMsg, 'error');
            })
        }
        //加入黑名单按钮事件
        function addBlacklist() {
            //获得当前选中行
            var rows = $('#dg-calls-pages').datagrid('getSelections');
            var num = rows.length;
            if (num == 0) {
                $.messager.alert('提示', '请选择一条事件与通话记录进行加入黑名单!', 'error');
                return;
            } else if (num > 1) {
                $.messager.alert('提示', '您选择了多条记录,只能选择一条事件与通话记录进行加入!', 'error');
                return;
            }
            callLogid = rows[0].id
            $('#blacklist-number').textbox('setValue', rows[0].number);
            $('#blacklist-period').val('30');
            $('#blacklist-remark').textbox('setValue', '');
            $('#blacklist').window('open'); //打开黑名单弹窗
        }
        //黑名单加入确定后调用接口
        function affirmBlacklist() {
            let params = {
                number: $('#blacklist-number').val(),
                period: $('#blacklist-period').val(),
                remark: $('#blacklist-remark').val(),
            }
            let submitState = true
            for (let key in params) {
                if (submitState && !params[key]) {
                    submitState = false
                }
            }
            if (submitState) {
                if (params.number.length > 11) {
                    return $.messager.alert('提示', '号码最多只能11位并且是数字！', 'error');
                }
                showProcess(true, '温馨提示', '正在加入中，请稍后...');
                insertBlacklist(params, function (res) {
                    if (res.success) {
                        $.messager.alert('提示', '加入成功', 'info');
                        var params = {
                            id: callLogid,
                            handleStatus: '0'
                        }
                        //保存到通话记录
                        updateHandleStatusAndType(params, function (res) {
                            showProcess(false);
                            $('#blacklist').window('close');
                            getDgCallListDatas()
                        },
                            function (e, url, errMsg) {
                                showProcess(false);
                                $.messager.alert('提示', '保存通话记录失败' + errMsg, 'error');
                            })
                    } else {
                        $.messager.alert('提示', '加入失败，异常信息：' + res.msg, 'error');
                    }
                },
                    function (e, url, errMsg) {
                        showProcess(false);
                        //失败才做处理
                        $.messager.alert('提示', '事件保存失败，异常信息：' + errMsg);
                    });
            } else {
                $.messager.alert('提示', '请选择加入时间间隔和备注!', 'error');
            }
        }
        //查看黑名单按钮事件
        function checkBlacklist(pageNum, pageSize) {
            $('#check-blacklist').window('open'); //打开黑名单弹窗
            queryBlacklist(1, 20)
            var pg = $("#blacklist-list").datagrid("getPager");
            if (pg) {
                $(pg).pagination({
                    total: 0,
                    pageSize: 20,
                    pageNumber: 1,
                    pageList: [10, 20, 30, 40, 50],
                    onBeforeRefresh: function () {
                        //刷新之前执行
                    },
                    onRefresh: function (pageNumber, pageSize) {
                    },
                    onChangePageSize: function () {
                    },
                    onSelectPage: function (pageNumber, pageSize) {
                        queryBlacklist(pageNumber, pageSize);
                    }
                });
            }
        }
        //查询黑名单列表
        function queryBlacklist(pageNum, pageSize) {
            try {
                if (pageNum == null) {
                    pageNum = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
            } catch (e) {
                pageNum = 1;
                pageSize = 20;
            }
            var params = {
                "pageNum": pageNum,
                "pageSize": pageSize,
                "number": $('#query-blacklist-number').val(),
                "actionUser": $('#blacklist-actionUser').val()
            };
            //打开进度条：
            $('#blacklist-list').datagrid('loading');//打开等待div
            getBlacklistPages(params, function (data) {
                $('#blacklist-list').datagrid('loadData', { total: 0, rows: [] });//清理数据
                for (var i = 0; i < data.content.length; i++) {
                    $('#blacklist-list').datagrid('insertRow', {
                        index: i,  // 索引从0开始
                        row: {
                            id: data.content[i].id,
                            number: data.content[i].number,
                            period: data.content[i].period ? data.content[i].period + '分钟' : '',
                            startTime: data.content[i].startTime,
                            endTime: data.content[i].endTime,
                            createUser: data.content[i].createUser,
                            remark: data.content[i].remark,
                        }
                    });
                }
                $("#blacklist-list").datagrid("getPager").pagination({
                    total: data.total,//记录总数
                    pageSize: data.pageSize,
                    pageNumber: data.pageNum,
                });
                $('#blacklist-list').datagrid('loaded');//关闭loding进度条；
            },
                function (e, url, errMsg) {
                    $('#blacklist-list').datagrid('loaded');//关闭loding进度条；
                })
        }
        //黑名单功能列表列
        function feature(value, row, index) {
            return '<a href="#"  style="color: #099DFC;" onclick="relieveBlacklist(\'' + row.number + '\')">解除黑名单</a>';
        }
        //解除黑名单方法
        function relieveBlacklist(number) {
            $.messager.confirm("提示", "是否确认解除" + number + "的黑名单？", function (r) {
                if (r) {
                    showProcess(true, '温馨提示', '正在解除黑名单，请稍后...');
                    relieveunBlacklist({ number }, function (red) {
                        showProcess(false);
                        queryBlacklist()
                        $.messager.alert('提示', '解除黑名单成功', 'info');
                    },
                        function (e, url, errMsg) {
                            showProcess(false);
                            //失败才做处理
                            $.messager.alert('异常', '异常信息：' + errMsg);
                        })
                }
            });
        }
        function timeClickHandler() {
            var from = $(this).parent().children('input:nth-of-type(1)');
            var to = $(this).parent().children('input:nth-of-type(2)');
            var now = new Date();
            var fromTime = new Date();
            var hours = 24;
            if ($(this).hasClass('inAnHour')) {
                hours = 1;
            }
            fromTime.setHours(fromTime.getHours() - hours);
            from.val(fromTime.formatString('yyyy-MM-dd hh:mm:ss'));
            to.val(null);
            var parentId = $(this).parent().attr('id');
            easyUiDateTimeSet();
            //自动搜索
            getDgCallListDatas(1, 20);

        }

        function easyUiDateTimeSet() {
            $('.easyui-datetimebox').each(function () {
                var e = $(this);
                e.datetimebox('setValue', e.val());
            });
        }

        //定时刷新任务开关
        var _callListAutoRefreshInterval = true;
        /** 设置通话列表刷新计时器,定时刷新通话列表 */
        function callListAutoRefreshIntervalTimeout() {

            clearTimeout(window.callListAutoRefreshInterval);
            if (_callListAutoRefreshInterval) {
                window.callListAutoRefreshInterval = null;
                if (_token != "") {
                    if ($('#autoRefreshList').is(':checked')) {//选中才自动刷新
                        getDgCallListDatas(null, null);
                    }
                }
                window.callListAutoRefreshInterval = setTimeout(callListAutoRefreshIntervalTimeout, 60000);
            }
        }

        /** 重定向到通话记录 */
        function redirect() {
            querySysConf('call_list_url',
                function (data) {
                    try {
                        window.parent.gProxy.openBrowser(data);
                    } catch (e) {
                        window.parent.parent.gProxy.openBrowser(data);
                    }
                },
                function (e, url, errMsg) {
                    //失败才做处理
                    $.messager.alert('异常', '获取通话记录页面配置失败，异常信息：' + errMsg);
                });
            }

        /**
         * 显示报停记录详情
         */
        function showStopRecordDetail(callId) {
            // 显示加载提示
            showProcess(true, '温馨提示', '正在加载报停记录，请稍后...');
            
            // 调用接口获取报停记录
            getStopRecordByAudioFileId(
                { id: callId },
                function(stopRecord) {
                    showProcess(false);
                    
                    if (!stopRecord) {
                        $.messager.alert('提示', '未找到相关的报停记录', 'warning');
                        return;
                    }
                    
                    // 构建详情HTML
                    var detailHtml = '<div style="font-size: 14px; line-height: 1.8;">' +
                        '<h3 style="margin-bottom: 20px; color: #333;">报停记录详情</h3>' +
                        '<table style="width: 100%; border-collapse: collapse;">' +
                        '<tr style="height: 40px;"><td style="width: 120px; font-weight: bold;">车牌号：</td><td>' + stopRecord.vehicleNo + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">报停原因：</td><td>' + stopRecord.stopReason + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">开始时间：</td><td>' + stopRecord.startTime + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">预计结束时间：</td><td>' + stopRecord.expectedEndTime + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">实际结束时间：</td><td>' + (stopRecord.actualEndTime || '-') + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">预计时长：</td><td>' + stopRecord.expectedDuration + ' 小时</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">状态：</td><td>' + formatStopStatus(stopRecord.status) + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">记录人：</td><td>' + stopRecord.seatUser + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">备注：</td><td>' + (stopRecord.remarks || '-') + '</td></tr>' +
                        '<tr style="height: 40px;"><td style="font-weight: bold;">创建时间：</td><td>' + stopRecord.startTime + '</td></tr>' +
                        '</table></div>';
                    
                    // 创建详情窗口（如果不存在）
                    if ($('#stopRecordDetailWindow').length === 0) {
                        $('body').append(
                            '<div id="stopRecordDetailWindow" class="easyui-window" title="报停记录详情" ' +
                            'style="width:800px;height:600px" data-options="iconCls:\'icon-info\',modal:true,closed:true">' +
                            '<div style="padding: 20px;" id="stopRecordDetail"></div>' +
                            '</div>'
                        );
                        $('#stopRecordDetailWindow').window();
                    }
                    
                    // 显示详情
                    $('#stopRecordDetail').html(detailHtml);
                    $('#stopRecordDetailWindow').window('open');
                },
                function(e, url, errMsg) {
                    showProcess(false);
                    console.error('获取报停记录失败:', errMsg);
                    $.messager.alert('错误', '获取报停记录失败：' + errMsg, 'error');
                }
            );
        }
        
        /**
         * 格式化报停状态
         */
        function formatStopStatus(status) {
            var className = 'status-active';
            var text = '';
            switch (status) {
                case 1:
                case '1':
                    className = 'status-active';
                    text = '报停中';
                    break;
                case 2:
                case '2':
                    className = 'status-recovered';
                    text = '已恢复';
                    break;
                case 3:
                case '3':
                    className = 'status-timeout';
                    text = '已超时';
                    break;
                default:
                    text = '未知';
            }
            return '<span class="' + className + '">' + text + '</span>';
        }
        
        // 添加状态样式
        $('head').append('<style>' +
            '.status-active { color: #ff6b35; font-weight: bold; }' +
            '.status-recovered { color: #28a745; font-weight: bold; }' +
            '.status-timeout { color: #dc3545; font-weight: bold; }' +
            '</style>'
        );

    </script>
</body>
</html>