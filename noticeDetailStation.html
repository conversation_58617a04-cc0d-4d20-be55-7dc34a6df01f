<!--
/* *****************************************************************
* 120EDC急救指挥中心客户端界面文件
* 本窗体仅有这一个文件，所有展示界面都由div进行切换
* 兼容性：该文件将运行在CEF3.2987.1601版本下（等同于Chrome的同版本号浏览器）
* -----------------------------------------------------------------
* Creator: 黄灵
* Created: 2022/4/19
* -----------------------------------------------------------------
* Modification:
* Modifier:
* Modified:
* *****************************************************************/
-->
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>公告详情界面</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <link rel="stylesheet" type="text/css" href="style/audio.css">
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <style>
      #mainPanel_McWinformMVC{
        height: calc(100% - 1.5em - 10px);
      }
      .notice-title{
        margin: 15px  0 15px 15px;
        display: flex;
        align-items: flex-start;
      }
      .notice-user{
        margin: 0  0 15px 15px;
        display: flex;
        align-items: flex-start;
      }
      .notice-content{
        margin: 0  0 15px 15px;
        display: flex;
        align-items: flex-start;
      }
      .notice-reply{
        margin: 0  0 15px 15px;
        display: flex;
        align-items: flex-start;
      }
      .title, .user-title, .content-title, .reply-title{
        width: 90px;
        text-align: right;
        margin-top: 6px;
      }
      .title-content, .user-content, .content, .reply{
        width: calc(100% - 135px);
        border: 1px solid lightgray;
        border-radius: 5px;
        margin-left: 10px;
        padding: 6px;
      }
      .content{
        padding: 10px 15px;
       }
      .reply{
        height: 37px;
      }
    </style>
</head>
<body>
    <div id="mainPanel_McWinformMVC" data-options="fit:true,border:false">
        <div>
            <div class="notice-title">
                <div class="title">公告标题：</div>
                <div class="title-content" id="title-content"></div>
            </div>
            <div class="notice-user">
                <div class="user-title">发布人：</div>
                <div class="user-content" id="user-content"></div>
            </div>
            <div class="notice-content">
                <div class="content-title">发布内容：</div>
                <div class="content" id="content"></div>
            </div>
            <!-- <div class="notice-reply">
              <div class="reply-title">回复内容：</div>
              <input class="reply" id="reply" type="text">
            </div> -->
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript">
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.parent.bsProxy.getBaseUrl();
        }
        var _user = null; //用户数据
        var id = null
        try {
            _user = evalJson(window.parent.gProxy.getUserInfo());
        } catch (e) {
            _user = evalJson(window.parent.bsProxy.getUserInfo());
        }
        $(document).ready(function () {
            id = window.location.search.replace("?id=", "");
            getNotice()
        });
        //刷新车辆信息并显示在车辆监控列表中，点击车辆监控列表的刷新界面
        function getNotice() {
            var params = {
                id
            };
            //获取本站车辆信息
            getNoticeDetail(params, function (data) {
                $('#title-content').text(data.noticeTitle)
                $('#user-content').text(data.createUser)
                $('#content').html(data.noticeContent)
            }, function (e, url, errMsg) {
                //失败才做处理
                $.messager.alert('异常', '异常信息：' + errMsg);
            });
        };
    </script>
</body>
</html>