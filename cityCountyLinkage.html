<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>市县联动指挥调度平台</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652"></script>
    
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        /* 主容器 */
        .main-container {
            width: 100%;
            height: 100vh;
            position: relative;
            background: #f5f5f5;
        }
        
        /* 地图容器 */
        .map-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        #cityCountyMap {
            width: 100%;
            height: 100%;
        }
        
        /* 页面标题栏 */
        .page-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .page-title {
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .header-tools {
            margin-left: auto;
            display: flex;
            gap: 15px;
        }
        
        .header-tool-btn {
            background: rgba(255,255,255,0.2);
            color: #fff;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .header-tool-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        
        /* 底部功能菜单栏 */
        .bottom-menu {
            position: absolute;
            bottom: 60px;
            left: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .menu-item {
            background: rgba(255,255,255,0.95);
            border: 2px solid #1890ff;
            border-radius: 12px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: bold;
            color: #1890ff;
            min-width: 120px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(24,144,255,0.2);
            backdrop-filter: blur(10px);
        }
        
        .menu-item:hover {
            background: #1890ff;
            color: #fff;
            transform: translateX(5px) translateY(-2px);
            box-shadow: 0 6px 20px rgba(24,144,255,0.4);
        }
        
        .menu-item.active {
            background: #1890ff;
            color: #fff;
            box-shadow: 0 6px 20px rgba(24,144,255,0.4);
        }
        
        .menu-item i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        /* 地图工具栏 - 完全参考second.html样式 */
        .map-toolbar {
            position: absolute;
            top: 80px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 2px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
            width: 120px;
        }
        
        /* 工具栏组 */
        .toolbar-group {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(135, 206, 235, 0.2);
            padding: 0;
            backdrop-filter: blur(20px);
            overflow: visible;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1000;
        }
        
        .toolbar-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .toolbar-group.collapsed .vehicle-tools,
        .toolbar-group.collapsed .map-tools {
            display: none;
        }
        
        .toolbar-group.collapsed {
            margin-bottom: 0;
        }
        
        .toolbar-group.collapsed .toolbar-group-title {
            border-radius: 16px;
            margin-bottom: 0;
            box-shadow: 0 4px 12px rgba(184, 230, 255, 0.2);
        }
        
        .toolbar-group.collapsed .toolbar-group-title:hover {
            box-shadow: 0 6px 16px rgba(184, 230, 255, 0.3);
        }
        
        .toolbar-group-title {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: #ffffff;
            padding: 10px 12px;
            margin: 0;
            font-size: 11px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            user-select: none;
            border-radius: 12px 12px 0 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border: none;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
            letter-spacing: 0.3px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .toolbar-group-title:hover {
            background: linear-gradient(135deg, #0984e3 0%, #2d3436 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
        }
        
        .toolbar-group-title:active {
            transform: translateY(0);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        /* 工具按钮容器 */
        .vehicle-tools {
            padding: 7px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-start;
            background: rgba(248, 250, 252, 0.8);
            transition: all 0.3s ease;
        }
        
        .map-tools {
            padding: 7px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-start;
            background: rgba(248, 250, 252, 0.8);
            transition: all 0.3s ease;
        }
        
        /*车辆工具：每行2个按钮 */
        .vehicle-tools .map-tool-btn {
            flex: 0 0 48px;
            width: 48px;
        }
        
        /* 地图工具：每行2个按钮 */
        .map-tools .map-tool-btn,
        .map-tools .range-dropdown {
            flex: 0 0 48px;
            width: 48px;
        }
        
        /* 工具按钮基础样式 */
        .map-tool-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 48px;
            height: 48px;
            border: 2px solid transparent;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 9px;
            font-weight: 600;
            color: #64748b;
            margin: 0;
            position: relative;
            text-align: center;
            line-height: 1;
            white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        .map-tool-btn::before {
            content: attr(data-icon);
            font-size: 18px;
            margin-bottom: 3px;
            display: block;
            transition: all 0.3s ease;
        }
        
        .map-tool-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        .map-tool-btn:hover::before {
            transform: scale(1.1);
        }
        
        .map-tool-btn:active {
            transform: translateY(-1px);
        }
        
        /*车辆工具组按钮颜色 */
        #follow-btn:hover {
            border-color: #10b981;
            color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #follow-btn.active {
            border-color: #10b981;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #track-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #track-btn.active {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #config-btn:hover {
            border-color: #8b5cf6;
            color: #8b5cf6;
            background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #config-btn.active {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        /* 地图工具组按钮颜色 */
        #zoomin-btn:hover, #zoomout-btn:hover {
            border-color: #52c41a;
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        }
        
        #zoomin-btn.active, #zoomout-btn.active {
            border-color: #52c41a;
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
            color: #fff;
        }
        
        #scale-btn:hover {
            border-color: #722ed1;
            color: #722ed1;
            background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
        }
        
        #scale-btn.active {
            border-color: #722ed1;
            background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
            color: #fff;
        }
        
        #traffic-btn:hover {
            border-color: #fa8c16;
            color: #fa8c16;
            background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
        }
        
        #traffic-btn.active {
            border-color: #fa8c16;
            background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
            color: #fff;
        }
        
        #measure-btn:hover {
            border-color: #13c2c2;
            color: #13c2c2;
            background: linear-gradient(135deg, #e6fffb 0%, #87e8de 100%);
        }
        
        #measure-btn.active {
            border-color: #13c2c2;
            background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
            color: #fff;
        }
        
        #marker-btn:hover {
            border-color: #eb2f96;
            color: #eb2f96;
            background: linear-gradient(135deg, #fff0f6 0%, #ffadd2 100%);
        }
        
        #marker-btn.active {
            border-color: #eb2f96;
            background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
            color: #fff;
        }
        
        #range-btn:hover {
            border-color: #fa541c;
            color: #fa541c;
            background: linear-gradient(135deg, #fff2e8 0%, #ffbb96 100%);
        }
        
        #range-btn.active {
            border-color: #fa541c;
            background: linear-gradient(135deg, #fa541c 0%, #ff7a45 100%);
            color: #fff;
        }
        
        #overview-btn:hover {
            border-color: #2f54eb;
            color: #2f54eb;
            background: linear-gradient(135deg, #f0f5ff 0%, #adc6ff 100%);
        }
        
        #overview-btn.active {
            border-color: #2f54eb;
            background: linear-gradient(135deg, #2f54eb 0%, #597ef7 100%);
            color: #fff;
        }
        
        #refresh-btn:hover {
            border-color: #389e0d;
            color: #389e0d;
            background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
        }
        
        #refresh-btn.active {
            border-color: #389e0d;
            background: linear-gradient(135deg, #389e0d 0%, #52c41a 100%);
            color: #fff;
        }
        
        #clear-btn:hover {
            border-color: #cf1322;
            color: #cf1322;
            background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
        }
        

        
        /* 地图资源组按钮颜色 */
        #vehicles-display-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #vehicles-display-btn.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #accidents-display-btn:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
            background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
            box-shadow: 0 8px 20px rgba(255, 77, 79, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #accidents-display-btn.active {
            border-color: #ff4d4f;
            background: linear-gradient(135deg, #ff4d4f 0%, #f5222d 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(255, 77, 79, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }
        
        #stations-display-btn:hover {
            border-color: #52c41a;
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
            box-shadow: 0 8px 20px rgba(82, 196, 26, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #stations-display-btn.active {
            border-color: #52c41a;
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(82, 196, 26, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        #hospitals-display-btn:hover {
            border-color: #ff85c0;
            color: #ff85c0;
            background: linear-gradient(135deg, #fff0f8 0%, #ffd6e7 100%);
            box-shadow: 0 8px 20px rgba(255, 133, 192, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #hospitals-display-btn.active {
            border-color: #ff85c0;
            background: linear-gradient(135deg, #ff85c0 0%, #f759ab 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(255, 133, 192, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        #bloodstations-display-btn:hover {
            border-color: #ff7875;
            color: #ff7875;
            background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
            box-shadow: 0 8px 20px rgba(255, 120, 117, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #bloodstations-display-btn.active {
            border-color: #ff7875;
            background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(255, 120, 117, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        #cdc-display-btn:hover {
            border-color: #95de64;
            color: #95de64;
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
            box-shadow: 0 8px 20px rgba(149, 222, 100, 0.25), inset 0 1px 0 rgba(255,255,255,0.9);
        }
        
        #cdc-display-btn.active {
            border-color: #95de64;
            background: linear-gradient(135deg, #95de64 0%, #52c41a 100%);
            color: #fff;
            box-shadow: 0 8px 20px rgba(149, 222, 100, 0.4), inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .map-tool-btn.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            transform: translateY(-1px);
        }
        
        .map-tool-btn.active::before {
            content: attr(data-icon-active);
        }
        
        .map-tool-btn.active:hover {
            transform: translateY(-2px);
        }

        /* 范围下拉菜单样式 */
        .range-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .range-menu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            border: 1px solid #e6f7ff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            display: none;
            min-width: 120px;
            animation: fadeInDown 0.3s ease-out;
            overflow: hidden;
            margin-top: 4px;
        }
        
        .range-menu-item {
            padding: 10px 15px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f8ff;
            transition: all 0.2s ease;
        }
        
        .range-menu-item:last-child {
            border-bottom: none;
        }
        
        .range-menu-item:hover {
            background: #f0f8ff;
            color: #1890ff;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        @keyframes tipFadeIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        @keyframes tipFadeOut {
            from {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
        }
        
        /* 工具提示 */
        .map-tool-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateX(-100%) translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            z-index: 1002;
        }
        
        .map-tool-btn:hover::after {
            opacity: 1;
        }

        /* 连接状态显示区域 - 重新设计 */
        .connection-status-panel {
            position: absolute;
            top: 80px;
            left: 15px;
            background: rgba(255, 255, 255, 0.92);
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 8px 12px;
            z-index: 1000;
            width: 200px;
            font-size: 14px;
            backdrop-filter: blur(8px);
            transition: all 0.3s ease;
        }

        .connection-status-panel.collapsed {
            width: 160px;
            padding: 6px 8px;
        }

        .connection-status-panel .panel-title {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            user-select: none;
        }

        .connection-status-panel .panel-title .toggle-icon {
            font-size: 10px;
            color: #999;
            transition: transform 0.3s ease;
        }

        .connection-status-panel.collapsed .toggle-icon {
            transform: rotate(180deg);
        }

        .status-items {
            transition: all 0.3s ease;
        }

        .connection-status-panel.collapsed .status-items {
            display: none;
        }

        .connection-status-panel .connection-summary {
            display: none;
        }

        .status-item {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-bottom: 3px;
            padding: 3px 6px;
            border-radius: 3px;
            transition: background-color 0.2s ease;
            min-height: 20px;
        }

        .status-item:hover {
            background-color: #f5f5f5;
        }

        .status-item .center-name {
            font-size: 12px;
            color: #666;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .connection-status-indicator {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 3px;
            min-width: 16px;
            height: 16px;
        }

        .status-dot {
            width: 10px !important;
            height: 10px !important;
            border-radius: 50% !important;
            animation: pulse 2s infinite;
            flex-shrink: 0 !important;
            display: block !important;
            z-index: 10 !important;
            margin: 0 !important;
            padding: 0 !important;
            position: relative !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .status-dot.online {
            background-color: #52c41a !important;
            box-shadow: 0 0 6px rgba(82, 196, 26, 0.6) !important;
            border: 1px solid #389e0d !important;
        }

        .status-dot.offline {
            background-color: #ff4d4f !important;
            box-shadow: 0 0 6px rgba(255, 77, 79, 0.6) !important;
            border: 1px solid #cf1322 !important;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .connection-summary {
            font-size: 14px;
            color: #666;
            text-align: center;
            margin-top: 3px;
            padding-top: 3px;
            border-top: 1px solid #f0f0f0;
        }

        .connection-status-panel.collapsed .connection-summary {
            display: block;
        }

        /* 查询工具栏样式 */
        .search-toolbar {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 5px;
        }

        .search-toolbar label {
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }

        .search-toolbar input,
        .search-toolbar select {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
        }

        .search-toolbar button {
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .search-toolbar button:hover {
            background: #40a9ff;
        }

        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #333;
            font-size: 13px;
        }

        .data-table td {
            font-size: 12px;
            color: #666;
        }

        .data-table tr:hover {
            background: #f9f9f9;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-badge.online {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-badge.offline {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffb3b3;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            margin-right: 4px;
            transition: all 0.2s ease;
        }

        .action-btn.edit {
            background: #e6f7ff;
            color: #1890ff;
        }

        .action-btn.edit:hover {
            background: #1890ff;
            color: #fff;
        }

        .action-btn.locate {
            background: #fff0f6;
            color: #eb2f96;
        }

        .action-btn.locate:hover {
            background: #eb2f96;
            color: #fff;
        }





        /* 高德地图样式隐藏和保护 - 增强版 */
        .amap-marker-label {
            border: none !important;
        }
        .amap-logo{
            display: none !important;
        }
        .amap-copyright{
            display: none !important;
        }
        
        /* 修复高德地图样式冲突 - 增强保护 */
        .amap-container {
            font-family: 'Microsoft YaHei', sans-serif !important;
        }
        .amap-container * {
            box-sizing: content-box !important;
        }
        .amap-container div,
        .amap-container span,
        .amap-container img {
            max-width: none !important;
            max-height: none !important;
        }
        
        /* 防止样式冲突的通用保护 */
        #cityCountyMap .amap-container,
        #cityCountyMap .amap-container * {
            box-sizing: content-box !important;
            font-size: initial !important;
            line-height: initial !important;
        }
        
        /* 修复地图内部元素样式冲突 */
        .amap-controls .amap-scale-text,
        .amap-controls .amap-scale-line,
        .amap-marker,
        .amap-marker div,
        .amap-info-window,
        .amap-info-window * {
            font-family: Arial, sans-serif !important;
            box-sizing: content-box !important;
        }
        
        /* 防止全局样式影响地图 - 排除信息窗口 
        .amap-container:not(.amap-info-window),
        .amap-container *:not(.amap-info-window):not(.amap-info-window *),
        .amap-layers,
        .amap-layers *,
        .amap-overlays:not(.amap-info-window),
        .amap-overlays *:not(.amap-info-window):not(.amap-info-window *),
        .amap-controls,
        .amap-controls * {
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            text-shadow: none !important;
            filter: none !important;
            opacity: 1 !important;
            visibility: visible !important;
            transform: none !important;
            transition: none !important;
            animation: none !important;
        }
        */
        
        /* 修复信息窗口透明问题 - 单独设置信息窗口样式 */
        .amap-info-window {
            background: #fff !important;
            border: 1px solid #d9d9d9 !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
        
        .amap-info-window .amap-info-content {
            padding: 8px 12px !important;
            line-height: 1.4 !important;
            font-size: 12px !important;
        }
        
        .amap-info-window font {
            display: inline !important;
            font-size: inherit !important;
        }
        
        .amap-info-window hr {
            margin: 2px 0 !important;
            border-top: 1px solid #ddd !important;
            height: 1px !important;
        }
        
        .amap-info-window span {
            display: inline !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        /* 修复可能引起length错误的样式 */
        .amap-container .amap-marker-label,
        .amap-container .amap-marker-content {
            position: absolute !important;
            z-index: auto !important;
            display: block !important;
        }
        
        /* 确保地图标记不受全局样式影响 */
        .amap-marker img,
        .amap-marker canvas,
        .amap-marker svg {
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
            object-fit: none !important;
        }

        /* 鹰眼地图样式 */
        .amap-overview {
            border: 2px solid #fff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
        
        /* 功能面板基础样式 */
        .function-panel {
            position: absolute;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            border: 1px solid #e8e8e8;
            z-index: 1001;
            display: none;
            user-select: none; /* 防止拖拽时选中文字 */
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .panel-header {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            padding: 4px 15px;
            border-radius: 12px 12px 0 0;
            font-weight: bold;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move; /* 拖拽光标 */
            user-select: none; /* 防止选中文字 */
            height: 30px; /* 减少10px高度 */
            position: relative;
        }
        
        .panel-header .panel-title {
            flex: 1;
            cursor: pointer; /* 标题区域可点击收缩 */
        }
        
        .panel-header .panel-toggle {
            background: none;
            border: none;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }
        
        .panel-header .panel-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .function-panel.collapsed .panel-content {
            display: none;
        }
        
        .function-panel.collapsed {
            height: auto !important;
        }
        
        .panel-header:active {
            cursor: grabbing; /* 拖拽时的光标 */
        }
        
        .panel-close {
            cursor: pointer;
            font-size: 18px;
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
            padding: 4px;
            border-radius: 4px;
            user-select: none;
        }
        
        .panel-close:hover {
            color: #fff;
            background: rgba(255,255,255,0.2);
        }
        
        .panel-content {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        /* 拖拽时的样式 */
        .panel-dragging {
            transition: none !important;
            box-shadow: 0 12px 40px rgba(0,0,0,0.25) !important;
            transform: scale(1.02);
            z-index: 1010 !important;
        }
        
        /* 激活面板的样式 */
        .panel-active {
            border: 2px solid #1890ff !important;
            box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2) !important;
        }
        
        .panel-active .panel-header {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%) !important;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        /* 状态指示器 */
        .status-indicator {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 15px;
        }
        
        .status-item {
            background: rgba(255,255,255,0.95);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #52c41a;
        }
        
        .status-icon.warning {
            background: #faad14;
        }
        
        .status-icon.error {
            background: #ff4d4f;
        }
        
        /* 去掉输入框和下拉框的聚焦黑框 */
        input:focus,
        select:focus,
        textarea:focus,
        button:focus {
            outline: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .bottom-menu {
                bottom: 10px;
                left: 10px;
            }
            
            .menu-item {
                padding: 8px 12px;
                font-size: 12px;
                min-width: 100px;
            }
            
            .map-toolbar {
                right: 10px;
                top: 70px;
            }
        }
        
        /* 修复EasyUI Tabs和DataGrid样式 - 参考second.html */
        #dataTabs .tabs-title {
            font-size: 14px;
            font-weight: bold;
        }
        
        #dataTabs .tabs-panels > .panel > .panel-body {
            overflow: hidden;
        }
        
        /* 确保datagrid有合适的边框 */
        .datagrid {
            border: 1px solid #ddd !important;
        }
        
        .datagrid-wrap {
            border: 1px solid #ddd !important;
        }
        
        /* tab面板样式修复 */
        .tabs-panels .panel {
            overflow: hidden;
        }
        
        .tabs-panels .panel-body {
            overflow: hidden;
        }
        
        /* 车辆过滤按钮样式 */
        .vehicle-filter-btn {
            margin-left: 12px;
            padding: 6px 12px;
            border: 1px solid #1890ff;
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .vehicle-filter-btn:hover {
            background: #1890ff;
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        .vehicle-filter-btn.active {
            background: #1890ff;
            color: #fff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
        }
        
        /* 车辆状态过滤弹窗样式 */
        .vehicle-filter-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 2000;
            display: none;
            overflow: hidden;
        }
        
        .vehicle-filter-dialog .dialog-header {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: #fff;
            padding: 16px 20px;
            font-size: 16px;
            font-weight: bold;
            position: relative;
        }
        
        .vehicle-filter-dialog .dialog-close {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .vehicle-filter-dialog .dialog-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .vehicle-filter-dialog .dialog-body {
            padding: 20px;
        }
        
        .filter-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 8px 0;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .filter-option:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
        
        .filter-option.selected {
            border-color: #1890ff;
            background: #e6f7ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }
        
        .filter-option input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .filter-option .option-info {
            flex: 1;
        }
        
        .filter-option .option-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .filter-option .option-desc {
            font-size: 12px;
            color: #666;
        }
        
        .filter-option .option-count {
            background: #1890ff;
            color: #fff;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        .dialog-actions {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .dialog-btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .dialog-btn.primary {
            background: #1890ff;
            color: #fff;
        }
        
        .dialog-btn.primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }
        
        .dialog-btn.secondary {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .dialog-btn.secondary:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 弹窗背景遮罩 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
            display: none;
        }
        input[readonly], textarea[readonly] {
            background: #fff !important;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <h1 class="page-title">
                <img src="style/img/logo.png" alt="logo" style="height: 36px; margin-right: 12px; vertical-align: middle;">
                120调度指挥市县联动子系统
            </h1>
            <div class="header-tools">
                <div class="header-tool-btn" onclick="showTaskPanel()">
                    <i class="fa fa-tasks"></i> 任务信息
                </div>
                <div class="header-tool-btn" onclick="showDataPanel()">
                    <i class="fa fa-database"></i> 数据查询
                </div>
                <div class="header-tool-btn" onclick="showQueryPanel()">
                    <i class="fa fa-search"></i> 数据统计
                </div>
                <div class="header-tool-btn" onclick="refreshData()">
                    <i class="fa fa-refresh"></i> 页面刷新
                </div>
                <!-- 暂时注释掉系统设置按钮
                <div class="header-tool-btn" onclick="showSystemSettings()">
                    <i class="fa fa-cog"></i> 系统设置
                </div>
                -->
            </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicator">
            <div class="status-item">
                <i class="fa fa-building" style="color: #1890ff;"></i>
                <span>中心: <span id="topCenterCount">5</span></span>
                <button id="centerFilterBtn" class="vehicle-filter-btn" onclick="showCenterFilterDialog()" title="按中心过滤">
                    <i class="fa fa-filter"></i> 全部中心
                </button>
            </div>
            <div class="status-item">
                <i class="fa fa-ambulance" style="color: #1890ff;"></i>
                <span>车辆: <span id="topVehicleCount">6</span></span>
                <button id="vehicleFilterBtn" class="vehicle-filter-btn" onclick="showVehicleFilterDialog()" title="按状态过滤车辆">
                    <i class="fa fa-filter"></i> 全部车辆
                </button>
            </div>
            <div class="status-item">
                <i class="fa fa-exclamation-triangle" style="color: #ff4d4f;"></i>
                <span>事故: <span id="topAccidentCount">3</span></span>
            </div>
            <div class="status-item">
                <i class="fa fa-hospital-o" style="color: #52c41a;"></i>
                <span>医院: <span id="topHospitalCount">6</span></span>
            </div>
            <div class="status-item">
                <i class="fa fa-building" style="color: #fa8c16;"></i>
                <span>分站: <span id="topStationCount">6</span></span>
            </div>
        </div>
        
        <!-- 地图容器 -->
        <div class="map-container">
            <div id="cityCountyMap"></div>
            
            <!-- 连接状态显示面板 -->
            <div class="connection-status-panel collapsed" id="connectionStatusPanel">
                <div class="panel-title" style="height: 30px;" onclick="toggleConnectionPanel()">
                    🌐 中心状态
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="status-items" id="statusItems">
                    <!-- 状态项将由JavaScript动态生成 -->
                </div>
                <div class="connection-summary" id="connectionSummary" style="display: block;">
                    <!-- 收缩时显示的汇总信息 -->
                </div>
            </div>
        </div>
        
        <!-- 地图工具栏 - 完全参考second.html -->
        <div class="map-toolbar" id="map-toolbar">
            <!-- 工具栏内容 -->
            <div class="toolbar-content">
                <!-- 车辆工具组 - 已隐藏 -->
                <div class="toolbar-group" id="vehicle-group" style="display: none;">
                    <div class="toolbar-group-title" onclick="toggleToolbarGroup('vehicle-group')">车辆工具</div>
                    <div class="vehicle-tools">
                        <div id="follow-btn" class="map-tool-btn" data-tooltip="跟随车辆" data-icon="🎯" data-icon-active="⏹️" onclick="followVehicle()">跟随</div>
                        <div id="track-btn" class="map-tool-btn" data-tooltip="显示轨迹" data-icon="📍" data-icon-active="👁️" onclick="showTrack()">轨迹</div>
                        <div id="config-btn" class="map-tool-btn" data-tooltip="显示配置" data-icon="⚙️" data-icon-active="✅" onclick="showConfig()">配置</div>
                    </div>
                </div>

                <!-- 地图资源组 -->
                <div class="toolbar-group" id="map-resources-group">
                    <div class="toolbar-group-title" onclick="toggleToolbarGroup('map-resources-group')">地图资源</div>
                    <div class="map-tools">
                        <div id="vehicles-display-btn" class="map-tool-btn active" data-tooltip="车辆显示" data-icon="🚗" data-icon-active="✅" onclick="toggleVehicleDisplay()">车辆</div>
                        <div id="accidents-display-btn" class="map-tool-btn active" data-tooltip="事故任务显示" data-icon="🚨" data-icon-active="✅" onclick="toggleAccidentDisplay()">事故</div>
                        <div id="stations-display-btn" class="map-tool-btn" data-tooltip="分站显示" data-icon="🏥" data-icon-active="✅" onclick="toggleStationDisplay()">分站</div>
                        <div id="hospitals-display-btn" class="map-tool-btn" data-tooltip="医院显示" data-icon="🏥" data-icon-active="✅" onclick="toggleHospitalDisplay()">医院</div>
                        <div id="bloodstations-display-btn" class="map-tool-btn" data-tooltip="血站显示" data-icon="🏥" data-icon-active="✅" onclick="toggleBloodDisplay()">血站</div>
                        <div id="cdc-display-btn" class="map-tool-btn" data-tooltip="疾控中心显示" data-icon="🏥" data-icon-active="✅" onclick="toggleCdcDisplay()">疾控</div>
                    </div>
                </div>
                
                <!-- 地图操作组 -->
                <div class="toolbar-group" id="map-tools-group">
                    <div class="toolbar-group-title" onclick="toggleToolbarGroup('map-tools-group')">地图工具</div>
                    <div class="map-tools">
                        <div id="zoomin-btn" class="map-tool-btn" data-tooltip="放大地图" data-icon="🔍" data-icon-active="🔍" onclick="zoomIn()">放大</div>
                        <div id="zoomout-btn" class="map-tool-btn" data-tooltip="缩小地图" data-icon="🔎" data-icon-active="🔎" onclick="zoomOut()">缩小</div>
                        <div id="scale-btn" class="map-tool-btn" data-tooltip="比例尺" data-icon="📐" data-icon-active="📏" onclick="toggleScale()">比例</div>
                        <div id="traffic-btn" class="map-tool-btn" data-tooltip="路况信息" data-icon="🚦" data-icon-active="🟢" onclick="toggleTraffic()">路况</div>
                        <div id="measure-btn" class="map-tool-btn" data-tooltip="测距工具" data-icon="📏" data-icon-active="✏️" onclick="measureDistance()">测距</div>
                        <div id="marker-btn" class="map-tool-btn" data-tooltip="标注工具" data-icon="📌" data-icon-active="✅" onclick="toggleMarkerTool()">标注</div>
                        <div class="range-dropdown">
                            <div id="range-btn" class="map-tool-btn" data-tooltip="范围工具" data-icon="⭕" data-icon-active="🎯" onclick="toggleRangeMenu()">范围</div>
                            <div class="range-menu" id="range-menu">
                                <div class="range-menu-item" data-radius="1000" onclick="drawRange(1000)">1公里</div>
                                <div class="range-menu-item" data-radius="2000" onclick="drawRange(2000)">2公里</div>
                                <div class="range-menu-item" data-radius="3000" onclick="drawRange(3000)">3公里</div>
                                <div class="range-menu-item" data-radius="5000" onclick="drawRange(5000)">5公里</div>
                                <div class="range-menu-item" data-radius="10000" onclick="drawRange(10000)">10公里</div>
                                <div class="range-menu-item" data-radius="20000" onclick="drawRange(20000)">20公里</div>
                                <div class="range-menu-item" data-radius="30000" onclick="drawRange(30000)">30公里</div>
                                <div class="range-menu-item" data-radius="40000" onclick="drawRange(40000)">40公里</div>
                                <div class="range-menu-item" data-radius="50000" onclick="drawRange(50000)">50公里</div>
                            </div>
                        </div>
                        <div id="overview-btn" class="map-tool-btn" data-tooltip="鹰眼地图" data-icon="🗺️" data-icon-active="👁️" onclick="toggleOverview()">鹰眼</div>
                        <div id="clear-btn" class="map-tool-btn" data-tooltip="清除标注" data-icon="🧹" data-icon-active="🧹" onclick="clearMeasure()">清除</div>

                    </div>
                </div>
                

            </div>
        </div>
        

        

        
        <!-- 底部功能菜单迁移到顶部-->
        <!-- <div class="bottom-menu">
            <div class="menu-item" onclick="showTaskPanel()">
                <i class="fa fa-tasks"></i>
                任务信息
            </div>
            <div class="menu-item" onclick="showDataPanel()">
                <i class="fa fa-database"></i>
                数据模块
            </div>
            <div class="menu-item" onclick="showQueryPanel()">
                <i class="fa fa-search"></i>
                查询模块
            </div>
        </div> -->
        
        <!-- 任务信息面板 - 使用easyui-window -->
        <div id="taskPanel" class="easyui-window" title="任务信息管理" style="width: 1220px; height: 680px;"
             data-options="modal:false,closed:true,resizable:true,maximizable:true,minimizable:false,collapsible:true">
            <!-- 任务信息内容 -->
            <div id="taskContent" style="height: 100%; overflow: hidden;">
                <!-- 工具栏 -->
                <div style="padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
                    <div style="float: left;">
                        <label style="margin-right: 8px;">急救分中心:</label>
                        <select id="taskCenterFilter" class="easyui-combobox" style="width: 120px; margin-right: 15px;"
                                data-options="panelHeight:'auto',editable:false,readonly:false">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </select>
                        <label style="margin-right: 8px;">状态:</label>
                        <select id="taskStatusFilter" class="easyui-combobox" style="width: 100px; margin-right: 15px;"
                                data-options="panelHeight:'auto',editable:false,readonly:false">
                            <option value="0">全部</option>
                            <option value="7">未调度-预约</option>
                            <option value="1">未调度-落单</option>
                            <option value="6">未调度-待派</option>
                            <option value="2">已调度</option>
                            <option value="3">已完成</option>
                            <option value="4">已撤销</option>
                        </select>
                        <label style="margin-right: 8px;">呼叫时间:</label>
                        <input id="beginTimeFilter" class="easyui-datetimebox" style="width: 160px; margin-right: 15px;">
                        <label style="margin-right: 8px;">结束时间:</label>
                        <input id="endTimeFilter" class="easyui-datetimebox" style="width: 160px; margin-right: 15px;">

                    </div>
                    <div style="float: right;">
                        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-search" onclick="filterTasks()">查询</a>
                        <button class="easyui-linkbutton" iconCls="icon-add" onclick="createRegionalTask()" 
                                style="margin-right: 5px;">联动派车</button>
                        <button class="easyui-linkbutton" iconCls="icon-save" onclick="exportTasks()">导出</button>
                    </div>
                    <div style="clear: both;"></div>
                </div>
                
                <!-- 任务列表表格 -->
                <div style="padding: 10px; height: calc(100% - 70px);">
                    <table class="easyui-datagrid" id="taskGrid" style="width: 100%; height: 100%;"
                           pagination="true" singleSelect="true"
                          border="false" striped="true" pageSize="15" scrollbarSize="18">
                        <thead>
                            <tr>
                                <th field="regionName" width="150">分中心</th>
                                <th field="id" width="150">事件编号</th>
                                <th field="eventName" width="360" formatter="formatEventName">事件名称</th>
                                <th field="status" width="80" formatter="formatStatus">状态</th>
                                <th field="eventType" width="80" formatter="formatEventType">事件类型</th>
                                <th field="eventSrcCodeName" width="80">事件来源</th>
                                <th field="callTypeCodeName" width="80">呼叫类型</th>
                                <th field="majorCall" width="150">主诉</th>
                                <th field="address" width="250">现场地址</th>
                                <th field="addressWait" width="250">等车地址</th>
                                <th field="callIn" width="120">呼救电话</th>
                                <th field="callInTimes" width="150" formatter="formatDateTime">来电时间</th>
                                <th field="contacter" width="100">联系人</th>
                                <th field="contact" width="120">联系电话</th>
                                <th field="patientAmount" width="80">患者人数</th>
                                <th field="patientName" width="100">患者姓名</th>
                                <th field="patientGender" width="80" formatter="formatGender">患者性别</th>
                                <th field="patientRace" width="100" formatter="formatPatientRace">患者民族</th>
                                <th field="patientAge" width="80">患者年龄</th>
                                <th field="patientCountry" width="100" formatter="formatPatientCountry">患者籍贯</th>
                                <th field="patientDiagnosis" width="150">患者病状</th>
                                <th field="centerRemark" width="120">120备注</th>
                                <th field="action" width="120" formatter="formatActions">操作</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 数据模块面板 - 使用easyui-window -->
        <div id="dataPanel" class="easyui-window" title="数据模块管理" style="width: 980px; height: 680px;"
             data-options="modal:false,closed:true,resizable:false,maximizable:false,minimizable:false,collapsible:true">
            <!-- EasyUI Tabs - 参考callList.html结构 -->
            <div id="dataTabs" class="easyui-tabs" style="width: 100%; height: 100%;">
                <div title="🏥 医院管理" style="display: none;">
                    <!-- 医院查询工具栏 -->
                    <div style="padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
                        <div style="float: left;">
                        <label style="margin-right: 8px;">分中心:</label>
                            <select id="hospitalCenterFilter" class="easyui-combobox" style="width: 120px; margin-right: 15px;"
                                    data-options="panelHeight:'auto',editable:false,readonly:false">
                            <option value="">全部中心</option>
                                <!-- 这里由globalCenterData数据动态填充 -->
                        </select>
                        <label style="margin-right: 8px;">医院名称:</label>
                            <input id="hospitalNameFilter" class="easyui-textbox" style="width: 150px; margin-right: 15px;"
                                   data-options="prompt:'请输入医院名称'" />
                            <label style="margin-right: 8px;">医院等级:</label>
                            <select id="hospitalLevelFilter" class="easyui-combobox" style="width: 120px; margin-right: 15px;"
                                    data-options="panelHeight:'auto',editable:false,readonly:false">
                                <option value="">全部等级</option>
                                <option value="11">三级甲等</option>
                                <option value="12">三级乙等</option>
                                <option value="13">三级丙等</option>
                                <option value="21">二级甲等</option>
                                <option value="22">二级乙等</option>
                                <option value="23">二级丙等</option>
                                <option value="31">一级甲等</option>
                                <option value="32">一级乙等</option>
                                <option value="33">一级丙等</option>
                                <option value="40">乡镇卫生院</option>
                                <option value="50">社区医疗服务中心</option>
                                <option value="60">诊所</option>
                                <option value="99">其他</option>
                            </select>
                    </div>
                        <div style="float: right;">
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-search" onclick="searchHospitals()">查询</a>
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="resetHospitals()" style="margin-left: 5px;">重置</a>
                        </div>
                        <div style="clear: both;"></div>
                    </div>
                    <div style="padding: 10px; height: calc(100% - 70px);">
                        <table class="easyui-datagrid" id="hospitalsGrid" style="width: 100%; height: 100%;"
                               pagination="false" singleSelect="true" rownumbers="true"
                            border="false" striped="true">
                            <thead>
                                <tr>
                                    <th field="name" width="200">医院名称</th>
                                    <th field="regionName" width="150">所属中心</th>
                                    <th field="hospitalLevel" width="100">医院等级</th>
                                    <th field="hospitalBeds" width="80">床位数</th>
                                    <th field="icuBeds" width="80">ICU床位</th>
                                    <th field="isErAvailable" width="80">急诊科</th>
                                    <th field="erLevel" width="100">急诊等级</th>
                                    <th field="contactNumber" width="120">联系电话</th>
                                    <th field="linkman" width="100">联系人</th>
                                    <th field="address" width="250">地址</th>
                                    <th field="chestPainCenter" width="150" formatter="formatChestPainCenter">胸痛中心</th>
                                    <th field="strokeCenter" width="150" formatter="formatStrokeCenter">卒中中心</th>
                                    <th field="traumaCenter" width="150" formatter="formatTraumaCenter">创伤中心</th>
                                    <th field="maternalCenter" width="150" formatter="formatMaternalCenter">孕产妇救治中心</th>
                                    <th field="neonatalCenter" width="150" formatter="formatNeonatalCenter">新生儿救治中心</th>
                                    <th field="action" width="60">操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                
                <div title="🩸 血站管理" style="display: none;">
                    <!-- 血站查询工具栏 -->
                    <div style="padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
                        <div style="float: left;">
                        <label style="margin-right: 8px;">分中心:</label>
                            <select id="bloodstationCenterFilter" class="easyui-combobox" style="width: 120px; margin-right: 15px;"
                                    data-options="panelHeight:'auto',editable:false,readonly:false">
                            <option value="">全部中心</option>
                                <!-- 这里由globalCenterData数据动态填充 -->
                        </select>
                        <label style="margin-right: 8px;">血站名称:</label>
                            <input id="bloodstationNameFilter" class="easyui-textbox" style="width: 150px; margin-right: 15px;"
                                   data-options="prompt:'请输入血站名称'" />
                    </div>
                        <div style="float: right;">
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-search" onclick="searchBloodstations()">查询</a>
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="resetBloodstations()" style="margin-left: 5px;">重置</a>
                        </div>
                        <div style="clear: both;"></div>
                    </div>
                    <div style="padding: 10px; height: calc(100% - 70px);">
                        <table class="easyui-datagrid" id="bloodstationsGrid" style="width: 100%; height: 100%;"
                               pagination="false" singleSelect="true" rownumbers="true" fit="true"
                               fitColumns="false" border="false" striped="true">
                            <thead>
                                <tr>
                                    <th field="name" width="180">血站名称</th>
                                    <th field="regionName" width="120">所属中心</th>
                                    <th field="contactNumber" width="140">联系电话</th>
                                    <th field="linkman" width="100">联系人</th>
                                    <th field="address" width="200">地址</th>
                                    <th field="descript" width="250">描述</th>
                                    <th field="remark" width="250">备注</th>
                                    <th field="action" width="60">操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                
                <div title="🧪 疾控中心" style="display: none;">
                    <!-- 疾控中心查询工具栏 -->
                    <div style="padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
                        <div style="float: left;">
                        <label style="margin-right: 8px;">分中心:</label>
                            <select id="cdcCenterFilter" class="easyui-combobox" style="width: 120px; margin-right: 15px;"
                                    data-options="panelHeight:'auto',editable:false,readonly:false">
                            <option value="">全部中心</option>
                                <!-- 这里由globalCenterData数据动态填充 -->
                        </select>
                        <label style="margin-right: 8px;">中心名称:</label>
                            <input id="cdcNameFilter" class="easyui-textbox" style="width: 150px; margin-right: 15px;"
                                   data-options="prompt:'请输入疾控中心名称'" />
                    </div>
                        <div style="float: right;">
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-search" onclick="searchCdc()">查询</a>
                            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="resetCdc()" style="margin-left: 5px;">重置</a>
                        </div>
                        <div style="clear: both;"></div>
                    </div>
                    <div style="padding: 10px; height: calc(100% - 70px);">
                        <table class="easyui-datagrid" id="cdcGrid" style="width: 100%; height: 100%;"
                               pagination="false" singleSelect="true" rownumbers="true" fit="true"
                               fitColumns="false" border="false" striped="true">
                            <thead>
                                <tr>
                                    <th field="name" width="180">疾控中心</th>
                                    <th field="regionName" width="120">所属中心</th>
                                    <th field="contactNumber" width="140">联系电话</th>
                                    <th field="linkman" width="100">联系人</th>
                                    <th field="address" width="200">地址</th>
                                    <th field="descript" width="250">描述</th>
                                    <th field="remark" width="250">备注</th>
                                    <th field="action" width="60">操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                
                <div title="📞 电话本" style="display: none;" iconCls="icon-phonebooks">
                    <iframe name="phoneBooks" id="phoneBooksIframe" scrolling="no" src="phonebookes.html" frameborder="0" style="width:100%;height:100%;"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 查询模块面板 - 使用easyui-window -->
        <div id="queryPanel" class="easyui-window" title="查询模块管理" style="width: 800px; height: 580px;"
             data-options="modal:false,closed:true,resizable:false,maximizable:false,minimizable:false,collapsible:true">
            <!-- 查询模块内容将在这里加载 -->
            <div id="queryContent" style="padding: 15px; height: calc(100% - 30px); overflow: auto;">
                <h3>🔍 查询模块管理</h3>
                <p>查询模块管理功能正在开发中...</p>
                <div style="margin-top: 20px;">
                    <h4>主要功能：</h4>
                    <ul>
                        <li>事件查询与检索</li>
                        <li>车辆信息查询</li>
                        <li>人员信息查询</li>
                        <li>统计报表查询</li>
                        <li>自定义查询条件</li>
                    </ul>
                </div>
                <div style="margin-top: 20px;">
                    <h4>快捷查询：</h4>
                    <div style="margin-top: 10px;">
                        <button class="easyui-linkbutton" style="width: 120px; margin: 5px;">今日事件</button>
                        <button class="easyui-linkbutton" style="width: 120px; margin: 5px;">在线车辆</button>
                        <button class="easyui-linkbutton" style="width: 120px; margin: 5px;">值班人员</button>
                        <button class="easyui-linkbutton" style="width: 120px; margin: 5px;">系统日志</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 车辆状态过滤弹窗 -->
        <div class="dialog-overlay" id="vehicleFilterOverlay" onclick="closeVehicleFilterDialog()"></div>
        <div class="vehicle-filter-dialog" id="vehicleFilterDialog">
            <div class="dialog-header">
                <span>🚗 车辆状态过滤</span>
                <span class="dialog-close" onclick="closeVehicleFilterDialog()">&times;</span>
            </div>
            <div class="dialog-body">
                <div class="filter-option" onclick="toggleFilterOption('all')">
                    <input type="checkbox" id="filter-all" checked>
                    <div class="option-info">
                        <div class="option-title">全部车辆</div>
                        <div class="option-desc">显示所有车辆，不进行状态过滤</div>
                    </div>
                    <span class="option-count" id="count-all">6</span>
                </div>
                
                <div class="filter-option" onclick="toggleFilterOption('standby')">
                    <input type="checkbox" id="filter-standby">
                    <div class="option-info">
                        <div class="option-title">待命状态</div>
                        <div class="option-desc">显示在分站待命，可随时出车的车辆</div>
                    </div>
                    <span class="option-count" id="count-standby">0</span>
                </div>
                
                <div class="filter-option" onclick="toggleFilterOption('dispatch')">
                    <input type="checkbox" id="filter-dispatch">
                    <div class="option-info">
                        <div class="option-title">任务状态</div>
                        <div class="option-desc">显示正在执行任务（发车、在途、到达等）的车辆</div>
                    </div>
                    <span class="option-count" id="count-dispatch">0</span>
                </div>
                
                <div class="filter-option" onclick="toggleFilterOption('stop')">
                    <input type="checkbox" id="filter-stop">
                    <div class="option-info">
                        <div class="option-title">暂停状态</div>
                        <div class="option-desc">显示报停、维修中的车辆</div>
                    </div>
                    <span class="option-count" id="count-stop">0</span>
                </div>
                
                <div class="filter-option" onclick="toggleFilterOption('offduty')">
                    <input type="checkbox" id="filter-offduty">
                    <div class="option-info">
                        <div class="option-title">下班状态</div>
                        <div class="option-desc">显示已下班、不在值班时间的车辆</div>
                    </div>
                    <span class="option-count" id="count-offduty">0</span>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="dialog-btn secondary" onclick="resetVehicleFilter()">重置</button>
                <button class="dialog-btn primary" onclick="applyVehicleFilter()">应用过滤</button>
            </div>
        </div>
        
        <!-- 中心过滤弹窗 -->
        <div class="dialog-overlay" id="centerFilterOverlay" onclick="closeCenterFilterDialog()"></div>
        <div class="vehicle-filter-dialog" id="centerFilterDialog">
            <div class="dialog-header">
                <span>🏢 中心过滤</span>
                <span class="dialog-close" onclick="closeCenterFilterDialog()">&times;</span>
            </div>
            <div class="dialog-body">
                <div class="filter-option" onclick="toggleCenterFilterOption('all')">
                    <input type="checkbox" id="center-filter-all" checked>
                    <div class="option-info">
                        <div class="option-title">全部中心</div>
                        <div class="option-desc">显示所有中心的车辆，不进行中心过滤</div>
                    </div>
                </div>
                
                <!-- 移除了硬编码的中心选项，现在由接口动态生成 -->
            </div>
            <div class="dialog-actions">
                <button class="dialog-btn secondary" onclick="resetCenterFilter()">重置</button>
                <button class="dialog-btn primary" onclick="applyCenterFilter()">应用过滤</button>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script type="text/javascript" src="script/linq.min.js"></script>
    <script type="text/javascript" src="script/edcui.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript" src="script/pinyin.js"></script>
    <script type="text/javascript" src="script/checkbox.js"></script>
    <script type="text/javascript" src="script/audio.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="script/ajax.js"></script>
    <script type="text/javascript">
        // 地图相关全局变量
        let cityCountyMap = null;
        let vehicleMarkers = [];
        let hospitalMarkers = [];
        let stationMarkers = [];
        let accidentMarkers = [];
        let currentPanel = null;
        let dataModuleInitialized = false; // 防止重复初始化数据模块
        let centerStatusData = null; // 全局变量存储中心通讯状态
        let globalCountryData = []; // 全局变量存储籍贯字典数据（数组形式）
        let globalRaceData = []; // 全局变量存储民族字典数据（数组形式）
        let globalPatientGenderData = []; // 全局变量存储性别字典数据（数组形式）
        let globalCenterData = []; // 全局变量存储分中心字典数据（数组形式）
        let globalRegionConfigData = []; // 全局变量存储各分中心配置信息（数组形式）

        // 定时刷新相关变量
        let taskRefreshTimer = null; // 任务刷新定时器
        let vehicleRefreshTimer = null; // 车辆刷新定时器
        let lastTaskData = []; // 上次的任务数据，用于智能比较
        let vehicleMarkersMap = new Map(); // 车辆标记映射，key为车辆ID，value为marker对象
        
        try {
            _baseUrl = window.parent.gProxy.getBaseUrl();
        } catch (e) {
            _baseUrl = window.location.protocol + '//' + window.location.host;
        }
        
        // 车辆图标对象 - 与second.html保持一致
        let carIcons = {};
        
        // 地图工具和注释对象 - 与second.html保持一致
        let mapAnnotations = {
            scale: null,
            trafficLayer: null,
            overView: null,
            ruler: null,
            mouseTool: null,
            markers: [],
            polylines: [],
            circles: []
        };
        
        // 地图工具状态
        let mapToolsState = {
            measuring: false,
            marking: false,
            range: false
        };
        
        // 车辆状态过滤相关变量
        let vehicleFilterOptions = {
            all: true,
            standby: false,
            dispatch: false,
            stop: false,
            offduty: false
        };
        
        let vehicleFilterText = '全部车辆';
        
        // 中心过滤相关变量 - 仅初始化all选项，其余选项通过接口动态加载
        let centerFilterOptions = {
            all: true
        };
        
        let centerFilterText = '全部中心';
        

        
        // 页面初始化
        $(document).ready(function() {
            
            initMap();
            loadMockData();
            // 确保页面初始化时加载车辆数据
            // 先立即加载必要数据，显示初始计数
            loadHospitalsData();
            loadStationsData();
            loadBloodStationsData();
            loadCdcCentersData();
            loadAccidentsData();
            
            // 加载各分中心配置信息
            loadRegionConfigData();
            
            // 初始化EasyUI组件
            initEasyUIComponents();
            
            // 初始化面板拖拽功能
            initPanelDragging();
            
            // 初始化中心过滤功能
            initCenterFilter();
            
            // 初始化连接状态显示
            initConnectionStatus();
            
            // 确保连接状态面板位置正确
            setTimeout(alignConnectionPanelPosition, 500);

            // 并行查询所有需要的字典数据
            queryAllDic(["push_outer_type", "country", "race", "patient_gender"], function(data) {

                
                // 检查数据有效性并分别存储到全局变量
                if (data && (Array.isArray(data) || typeof data === 'object')) {

                    
                    // 根据字典数据结构分别保存
                                // 按typeCode分组数据
                    globalCenterData = data.filter(item => item.typeCode === 'push_outer_type');
    
                        
                    // 更新中心过滤选项
                    updateCenterFilterOptions(globalCenterData);
                    
                    // 更新顶部中心数量显示
                    const count = Array.isArray(globalCenterData) ? globalCenterData.length : 
                        (globalCenterData.content && Array.isArray(globalCenterData.content) ? globalCenterData.content.length : 
                        (globalCenterData.data && Array.isArray(globalCenterData.data) ? globalCenterData.data.length : '?'));
                    $('#topCenterCount').text(count);
                    
                    // 保存籍贯字典数据
                    // 按typeCode分组籍贯数据
                    globalCountryData = data.filter(item => item.typeCode === 'country');

                    
                    // 保存民族字典数据
                    // 按typeCode分组民族数据
                    globalRaceData = data.filter(item => item.typeCode === 'race');

                    
                    // 保存患者性别字典数据
                    // 按typeCode分组性别数据
                    globalPatientGenderData = data.filter(item => item.typeCode === 'patient_gender');

                }
                
                // 获取中心通信状态
                
                getCommunicationStatus();
                
                // 设置定时器，每30秒获取一次中心通信状态
                
                setInterval(getCommunicationStatus, 30000);
                
                // 初始化任务管理面板
                initTaskPanel();
            }, function(error) {
                console.error('[字典数据] 获取字典数据失败:', error);
            });
            
            // 定时刷新数据 (不再单独更新连接状态，已通过getCommunicationStatus定时更新)
            setInterval(updateStatusIndicators, 30000);

            // 启动智能定时刷新
            startSmartRefresh();
            
            // 点击页面其他地方关闭范围菜单
            $(document).click(function(e) {
                const rangeMenu = document.getElementById('range-menu');
                const rangeBtn = document.getElementById('range-btn');
                
                if (!$(e.target).closest('#range-btn').length && !$(e.target).closest('#range-menu').length) {
                    rangeMenu.style.display = 'none';
                }
            });
            
            // 初始化车辆状态过滤功能
            initVehicleFilter();
            
            // 初始化中心过滤功能
            initCenterFilter();
        });

        // 加载各分中心配置信息
        function loadRegionConfigData() {
            // 调用接口加载各分中心配置信息
            let params = {
                centerCode: ""
            }
            getRegionConfigByCode(params, function(data) {

                globalRegionConfigData = data;
            }, function(error) {
                console.error('[字典数据] 获取分中心配置信息失败:', error);
            });

        }

        // 启动智能定时刷新
        function startSmartRefresh() {
            // 任务每30秒刷新一次
            if (taskRefreshTimer) {
                clearInterval(taskRefreshTimer);
            }
            taskRefreshTimer = setInterval(function() {
                smartRefreshTasks();
            }, 30000);

            // 车辆每10秒刷新一次
            if (vehicleRefreshTimer) {
                clearInterval(vehicleRefreshTimer);
            }
            vehicleRefreshTimer = setInterval(function() {
                smartRefreshVehicles();
            }, 10000);

            console.log('[智能刷新] 定时器已启动 - 任务30秒，车辆10秒');
        }

        // 智能刷新任务数据
        function smartRefreshTasks() {

            // 检查任务面板是否打开
            if (!$('#taskPanel').window('options').closed) {
                // 获取当前查询条件
                const status = $('#taskStatusFilter').combobox('getValue');
                const centerCode = $('#taskCenterFilter').combobox('getValue');
                const beginTime = $('#beginTimeFilter').datetimebox('getValue');
                const endTime = $('#endTimeFilter').datetimebox('getValue');

                const params = {
                    page: 1,
                    size: 15
                };

                if (status && status !== '0') {
                    params.status = status;
                }
                if (centerCode) {
                    params.centerCode = centerCode;
                }
                if (beginTime) {
                    params.beginTime = beginTime;
                }
                if (endTime) {
                    params.endTime = endTime;
                }

                // 调用接口获取最新任务数据
                getCityLinkageEventList(params, function(data) {
                    if (data && Array.isArray(data)) {
                        smartUpdateTaskGrid(data);
                    }
                }, function(error) {
                    console.error('[智能刷新] 任务数据刷新失败:', error);
                });
            }
        }

        // 智能更新任务表格
        function smartUpdateTaskGrid(newTaskData) {

            // 比较新旧数据，只有数据发生变化时才更新表格
            const dataChanged = !arraysEqual(lastTaskData, newTaskData, 'id');

            if (dataChanged) {

                // 更新表格数据
                $('#taskGrid').datagrid('loadData', {
                    total: newTaskData.length,
                    rows: newTaskData
                });

                // 保存当前数据作为下次比较的基准
                lastTaskData = JSON.parse(JSON.stringify(newTaskData));

                console.log('[智能刷新] 任务表格更新完成');
            } else {
                console.log('[智能刷新] 任务数据无变化，跳过更新');
            }
        }

        // 智能刷新车辆数据
        function smartRefreshVehicles() {
            console.log('[智能刷新] 开始智能刷新车辆数据');

            // 检查车辆显示是否开启
            const vehicleBtn = document.getElementById('vehicles-display-btn');
            if (!vehicleBtn || !vehicleBtn.classList.contains('active')) {
                console.log('[智能刷新] 车辆显示未开启，跳过刷新');
                return;
            }

            const params = {
                centerCode: "",
                hCondition: "",
                plateNum: "",
                stationId: "",
                status: ""
            };

            // 调用接口获取最新车辆数据
            getVehicleLocations(params, function(data) {
                if (data && Array.isArray(data)) {
                    smartUpdateVehicleMarkers(data);
                }
            }, function(error) {
                console.error('[智能刷新] 车辆数据刷新失败:', error);
            });
        }

        // 智能更新车辆标记（移动而不是删除重建）
        function smartUpdateVehicleMarkers(newVehicleData) {
            console.log('[智能刷新] 智能更新车辆标记，新数据:', newVehicleData.length, '辆');

            // 更新全局车辆数据
            vehicleData = newVehicleData;

            // 更新顶部计数
            document.getElementById('topVehicleCount').textContent = vehicleData.length;

            // 创建新数据的映射
            const newVehicleMap = new Map();
            newVehicleData.forEach(vehicle => {
                const vehicleId = vehicle.plateNum || vehicle.id;
                if (vehicleId) {
                    newVehicleMap.set(vehicleId, vehicle);
                }
            });

            // 处理现有标记
            vehicleMarkersMap.forEach((marker, vehicleId) => {
                const newVehicle = newVehicleMap.get(vehicleId);
                if (newVehicle && newVehicle.longitude && newVehicle.latitude) {
                    // 车辆仍存在，更新位置
                    const newPosition = [newVehicle.longitude, newVehicle.latitude];
                    const currentPosition = marker.getPosition();

                    // 检查位置是否发生变化
                    if (!currentPosition ||
                        Math.abs(currentPosition.lng - newVehicle.longitude) > 0.0001 ||
                        Math.abs(currentPosition.lat - newVehicle.latitude) > 0.0001) {

                        console.log('[智能刷新] 车辆位置发生变化，移动标记:', vehicleId);
                        marker.setPosition(newPosition);

                        // 更新信息窗口内容
                        updateVehicleMarkerInfo(marker, newVehicle);
                    }

                    // 从新数据中移除已处理的车辆
                    newVehicleMap.delete(vehicleId);
                } else {
                    // 车辆不存在了，删除标记
                    console.log('[智能刷新] 车辆已消失，删除标记:', vehicleId);
                    marker.setMap(null);
                    vehicleMarkersMap.delete(vehicleId);
                }
            });

            // 添加新出现的车辆标记
            newVehicleMap.forEach((vehicle, vehicleId) => {
                if (vehicle.longitude && vehicle.latitude) {
                    console.log('[智能刷新] 发现新车辆，添加标记:', vehicleId);
                    const marker = createVehicleMarker(vehicle);
                    if (marker) {
                        vehicleMarkersMap.set(vehicleId, marker);
                        vehicleMarkers.push(marker);
                    }
                }
            });

            console.log('[智能刷新] 车辆标记更新完成，当前标记数:', vehicleMarkersMap.size);
        }

        // 数组比较函数
        function arraysEqual(arr1, arr2, keyField) {
            if (!arr1 || !arr2) return false;
            if (arr1.length !== arr2.length) return false;

            // 按指定字段排序后比较
            const sorted1 = arr1.slice().sort((a, b) => (a[keyField] || '').localeCompare(b[keyField] || ''));
            const sorted2 = arr2.slice().sort((a, b) => (a[keyField] || '').localeCompare(b[keyField] || ''));

            for (let i = 0; i < sorted1.length; i++) {
                if (JSON.stringify(sorted1[i]) !== JSON.stringify(sorted2[i])) {
                    return false;
                }
            }
            return true;
        }

        // 创建车辆标记
        function createVehicleMarker(vehicle) {
            try {
                const position = [vehicle.longitude, vehicle.latitude];

                // 根据车辆状态选择图标
                let iconUrl = 'style/img/vehicle_default.png';
                if (vehicle.hCondition === '待命') {
                    iconUrl = 'style/img/vehicle_standby.png';
                } else if (vehicle.hCondition === '出车' || vehicle.hCondition === '在途' || vehicle.hCondition === '到达') {
                    iconUrl = 'style/img/vehicle_dispatch.png';
                } else if (vehicle.hCondition === '报停') {
                    iconUrl = 'style/img/vehicle_stop.png';
                }

                const marker = new AMap.Marker({
                    position: position,
                    icon: iconUrl,
                    title: vehicle.plateNum || '未知车辆',
                    extData: vehicle
                });

                // 添加信息窗口
                updateVehicleMarkerInfo(marker, vehicle);

                cityCountyMap.add(marker);
                return marker;
            } catch (error) {
                console.error('[智能刷新] 创建车辆标记失败:', error);
                return null;
            }
        }

        // 更新车辆标记信息
        function updateVehicleMarkerInfo(marker, vehicle) {
            const vehicleText = `
                <div style="padding: 8px; min-width: 200px;">
                    <h4 style="margin: 0 0 8px 0; color: #1890ff;">${vehicle.plateNum || '未知车辆'}</h4>
                    <p style="margin: 4px 0;"><strong>所属中心:</strong> ${vehicle.regionName || '未知'}</p>
                    <p style="margin: 4px 0;"><strong>车辆状态:</strong> <span style="color: ${getVehicleStatusColor(vehicle.hCondition)}">${vehicle.hCondition || '未知'}</span></p>
                    <p style="margin: 4px 0;"><strong>车组人员:</strong> ${vehicle.crewInfo || '未知'}</p>
                    <p style="margin: 4px 0;"><strong>更新时间:</strong> ${vehicle.updateTime || '未知'}</p>
                </div>
            `;

            const infoWindow = new AMap.InfoWindow({
                content: vehicleText,
                offset: new AMap.Pixel(0, -30),
                autoMove: false
            });

            marker.off('click');
            marker.on('click', function() {
                infoWindow.open(cityCountyMap, marker.getPosition());
            });
        }

        // 获取车辆状态颜色
        function getVehicleStatusColor(status) {
            const colorMap = {
                '待命': '#52c41a',
                '出车': '#1890ff',
                '在途': '#1890ff',
                '到达': '#fa8c16',
                '报停': '#f5222d',
                '下班': '#8c8c8c'
            };
            return colorMap[status] || '#666666';
        }
        
        // 初始化EasyUI组件
        function initEasyUIComponents() {
            try {
                // 初始化任务信息窗口
                $('#taskPanel').window({
                    modal: false,
                    closed: true,
                    resizable: false,
                    maximizable: false,
                    minimizable: false,
                    collapsible: true,
                    center: true,
                    width: 'auto',
                    height: 'auto',
                    onOpen: function() {
                        currentPanel = 'taskPanel';
                    },
                    onClose: function() {
                        if (currentPanel === 'taskPanel') currentPanel = null;
                    }
                });
                
                // 初始化数据模块窗口
                $('#dataPanel').window({
                    modal: false,
                    closed: true,
                    resizable: false,
                    maximizable: false,
                    minimizable: false,
                    collapsible: true,
                    center: true,
                    width: 'auto',
                    height: 'auto',
                    onOpen: function() {
                        currentPanel = 'dataPanel';
                        // 窗口打开后调整内部组件大小
                        setTimeout(() => {
                            $('#dataTabs').tabs('resize');
                            $('#hospitalsGrid, #bloodstationsGrid, #cdcGrid, #phonebookGrid').datagrid('resize');
                        }, 100);
                    },
                    onClose: function() {
                        if (currentPanel === 'dataPanel') currentPanel = null;
                    },
                    onResize: function() {
                        // 窗口大小改变时调整内部组件
                        setTimeout(() => {
                            $('#dataTabs').tabs('resize');
                            $('#hospitalsGrid, #bloodstationsGrid, #cdcGrid, #phonebookGrid').datagrid('resize');
                        }, 50);
                    },
                    onCollapse: function() {
                        // 处理窗口收缩时的逻辑
                        $(this).window('resize');
                    },
                    onExpand: function() {
                        // 处理窗口展开时的逻辑
                        setTimeout(() => {
                            $('#dataTabs').tabs('resize');
                            $('#hospitalsGrid, #bloodstationsGrid, #cdcGrid, #phonebookGrid').datagrid('resize');
                        }, 50);
                    }
                });
                
                // 初始化查询模块窗口
                $('#queryPanel').window({
                    modal: false,
                    closed: true,
                    resizable: false,
                    maximizable: false,
                    minimizable: false,
                    collapsible: true,
                    center: true,
                    width: 'auto',
                    height: 'auto',
                    onOpen: function() {
                        currentPanel = 'queryPanel';
                        // 初始化查询模块内的EasyUI组件
                        setTimeout(() => {
                            $('.easyui-linkbutton').linkbutton();
                        }, 100);
                    },
                    onClose: function() {
                        if (currentPanel === 'queryPanel') currentPanel = null;
                    },
                    onCollapse: function() {
                        // 处理窗口收缩时的逻辑
                        $(this).window('resize');
                    },
                    onExpand: function() {
                        // 处理窗口展开时的逻辑
                        setTimeout(() => {
                            $('.easyui-linkbutton').linkbutton();
                        }, 50);
                    }
                });
                
    
                
            } catch (error) {
                console.error('EasyUI组件初始化失败:', error);
            }
        }

        // 按急救中心筛选所有资源
        function filterVehiclesByCenter() {
            try {
                // 获取当前中心过滤选项
                let selectedValues = ['all'];
                if (!centerFilterOptions.all) {
                    selectedValues = Object.keys(centerFilterOptions).filter(key => 
                        key !== 'all' && centerFilterOptions[key]
                    );
                }
                
                
                
                
                // 重新加载车辆数据并绘制
                const vehicleBtn = document.getElementById('vehicles-display-btn');
                if (vehicleBtn && vehicleBtn.classList.contains('active')) {
                    
                    loadVehiclesData(); // 重新加载车辆数据，应用中心过滤条件
                }
                
                // 重新加载事故数据并绘制
                const accidentBtn = document.getElementById('accidents-display-btn');
                if (accidentBtn && accidentBtn.classList.contains('active')) {
                    
                    loadAccidentsData(); // 重新加载事故数据，应用中心过滤条件
                }
                
                // 重新加载分站数据并绘制
                const stationBtn = document.getElementById('stations-display-btn');
                if (stationBtn && stationBtn.classList.contains('active')) {
                    
                    loadStationsData(); // 重新加载分站数据，应用中心过滤条件
                }
                
                // 重新加载医院数据并绘制
                const hospitalBtn = document.getElementById('hospitals-display-btn');
                if (hospitalBtn && hospitalBtn.classList.contains('active')) {
                    
                    loadHospitalsData(); // 重新加载医院数据，应用中心过滤条件
                }

                // 重新加载血站数据并绘制
                const bloodBtn = document.getElementById('bloodstations-display-btn');
                if (bloodBtn && bloodBtn.classList.contains('active')) {
                    
                    loadBloodStationsData(); // 重新加载血站数据，应用中心过滤条件
                }

                // 重新加载疾控中心数据并绘制
                const cdcBtn = document.getElementById('cdc-display-btn');
                if (cdcBtn && cdcBtn.classList.contains('active')) {
                    
                    loadCdcCentersData(); // 重新加载疾控中心数据，应用中心过滤条件
                }

                
            } catch (error) {
                console.error('筛选资源时出错:', error);
            }
        }
        
        // 绘制筛选后的车辆
        function drawFilteredVehicles(selectedCenter) {
            // 清除现有车辆标记
            vehicleMarkers.forEach(marker => {
                if (marker && marker.setMap) {
                    marker.setMap(null);
                }
            });
            vehicleMarkers = [];
            
            // 使用真实数据或备用数据
            const vehiclesToUse = vehicleData && vehicleData.length > 0 ? vehicleData : mockVehicles_backup;
            
            // 筛选车辆数据
            let filteredVehicles = vehiclesToUse;
            if (selectedCenter && selectedCenter.length > 0 && !selectedCenter.includes('all')) {
                filteredVehicles = vehiclesToUse.filter(vehicle => 
                    selectedCenter.includes(vehicle.center || vehicle.regionName)
                );
            }
            
            // 根据状态过滤条件进一步筛选
            if (!vehicleFilterOptions.all) {
                filteredVehicles = filteredVehicles.filter(vehicle => {
                    if (vehicle.hCondition === '3' && vehicleFilterOptions.offduty) return true;   // 未值班
                    if (vehicle.hCondition === '1' && vehicleFilterOptions.stop) return true;      // 报停
                    if (vehicle.hCondition === '0' && vehicle.statusCode === '0' && vehicleFilterOptions.standby) return true; // 待命
                    if (vehicle.hCondition === '0' && vehicle.statusCode !== '0' && vehicleFilterOptions.dispatch) return true; // 任务状态
                    return false;
                });
            }
            
            // 绘制筛选后的车辆
            filteredVehicles.forEach(car => {
                // 获取车辆位置坐标（兼容不同数据格式）
                const carLocation = car.location || [car.lng, car.lat];
                
                // 如果经纬度为0，不绘制车辆
                if ((carLocation[0] == 0 && carLocation[1] == 0) || !carLocation[0] || !carLocation[1]) return;
                
                // 根据车辆状态选择图标 - 与second.html保持一致
                let myIcon = null;
                if (car.hCondition == "0") {
                    if (car.statusCode == "0" || car.status == "0") {
                        // 未出车超过1公里，那么车辆变成黄色
                        const stationLat = car.stationLatitude || car.staLat;
                        const stationLng = car.stationLongitude || car.staLng;
                        var distance = getDistance(carLocation[1], carLocation[0], stationLat, stationLng);
                        if (distance > 1000) {
                            myIcon = carIcons.yellow;
                        } else {
                            // 待命图标
                            myIcon = carIcons.default;
                        }
                    } else {
                        // 非待命图标（发车、抵达等）
                        myIcon = carIcons.red;
                    }
                } else {
                    // 车辆非正常状态（报停、维修等）
                    myIcon = carIcons.blue;
                }
                
                const marker = new AMap.Marker({
                    position: carLocation,
                    icon: myIcon,
                    title: car.plateNumber || car.plateNum,
                    extData: car
                });
                
                // 设置车辆角度
                marker.setAngle(car.direction);
                
                // 创建信息窗口内容 - 与second.html保持一致
                var carStatus = "";
                if (car.hCondition != "0") {
                    switch (car.hCondition) {
                        case "1":
                            carStatus = "报停中";
                            break;
                        case "3":
                            carStatus = "未值班";
                            break;
                    }
                } else {
                    carStatus = car.statusStr;
                }
                
                // 构建车辆信息内容 - 完全参考secondScr_gaode.js格式
                let text = '';
                
                // 分站信息
                text += `<font size='2' color='#0066FF'>分站：</font>`;
                text += `<font size='2'>${car.station || car.stationName || '未知'}</font>`;
                text += `<hr style='color:#0066FF'>`;
                
                // 车牌信息  
                text += `<font size='2' color='#0066FF'>车牌：</font>`;
                text += `<font size='2'>${car.plateNumber || car.plateNum || '未知'}</font>`;
                text += `<hr style='color:#0066FF'>`;
                
                // 速度信息
                text += `<font size='2' color='#0066FF'>速度：</font>`;
                text += `${Math.round((car.speed || 0) * 100) / 100}`;
                text += `<font size='2'>km/h</font>`;
                text += `<hr style='color:#0066FF'>`;
                
                // 状态信息
                text += `<font size='2' color='#0066FF'>状态：</font>`;
                text += `<font size='2'>${carStatus}</font>`;
                text += `<hr style='color:#0066FF'>`;
                
                // ACC状态 - 使用span和float:right
                text += `<span style="float: right">${car.acc == '0' ? '熄火' : car.acc == '1' ? '点火' : ''}</span>`;
                
                const infoContent = text;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                vehicleMarkers.push(marker);
            });
            

        }
        
        // 面板拖拽功能初始化
        function initPanelDragging() {
            // 所有面板现在都是 easyui-window，自带拖拽功能，不需要自定义拖拽
            
        }
        

        
        // 初始化地图 - 增强错误处理版本，使用接口获取城市配置
        function initMap() {
            try {
                // 确保地图容器存在
                const mapContainer = document.getElementById('cityCountyMap');
                if (!mapContainer) {
                    console.error('地图容器不存在');
                    return;
                }
                
                // 清除可能存在的样式冲突
                mapContainer.style.cssText = `
                    width: 100% !important; 
                    height: 100% !important;
                    background: #e3e3e3 !important;
                    position: relative !important;
                    overflow: hidden !important;
                `;
                
                // 等待AMap完全加载
                if (typeof AMap === 'undefined') {
                    console.error('高德地图API未加载');
                    setTimeout(initMap, 1000);
                    return;
                }
                
                // 创建地图实例，使用默认中心点（后面会通过API获取城市设置）
                cityCountyMap = new AMap.Map('cityCountyMap', {
                    enableMapClick: false,
                    minZoom: 1,
                    maxZoom: 100,
                    zoom: 11,
                    // 暂时使用默认中心点，后续将通过API设置
                    center: [105.4457, 28.8757], // 临时中心点
                    mapStyle: 'amap://styles/normal',
                    viewMode: '2D',
                    pitch: 0,
                    rotation: 0,
                    animateEnable: true,
                    keyboardEnable: false,
                    doubleClickZoom: true,
                    scrollWheel: true,
                    expandZoomRange: true,
                    zooms: [3, 20]
                });
                
                // 从系统配置获取城市设置
                querySysConf('map_city',
                    function (cityData) {
                        if (cityData) {
        
                            // 设置地图城市
                            cityCountyMap.setCity(cityData);
                        } else {
                            console.warn('未获取到城市配置，使用默认中心点');
                        }
                    },
                    function (e, url, errMsg) {
                        console.error('获取城市配置失败，使用默认中心点，错误信息:', errMsg);
                    }
                );
                
                // 监听地图加载完成事件
                cityCountyMap.on('complete', function() {
    
                    
                    // 初始化车辆图标 - 与second.html保持一致
                    try {
                        carIcons = {
                            default: new AMap.Icon({ image: "style/img/ambul.png", size: new AMap.Size(48, 48) }),
                            yellow: new AMap.Icon({ image: "style/img/ambul_yellow.png", size: new AMap.Size(48, 48) }),
                            red: new AMap.Icon({ image: "style/img/ambul_red.png", size: new AMap.Size(48, 48) }),
                            blue: new AMap.Icon({ image: "style/img/ambul_blue.png", size: new AMap.Size(48, 48) })
                        };
        
                    } catch (iconError) {
                        console.error('车辆图标初始化失败:', iconError);
                    }
                    
                    // 初始化地理位置和地图设置
                    setTimeout(() => {
                        initMapSettings();
                    }, 100);
                    
                    // 加载地图数据
                    setTimeout(() => {
                        loadMapData();
                    }, 1000);
                });
                
                // 监听地图错误事件
                cityCountyMap.on('error', function(error) {
                    console.error('地图运行时错误:', error);
                });
                

                
            } catch (error) {
                console.error('地图初始化失败:', error);
            }
        }
        
        // 初始化地图设置 - 参考second.html
        function initMapSettings() {
            // 默认开启比例尺控件并更新按钮状态
            AMap.plugin(['AMap.Scale'], function() {
                mapAnnotations.scale = new AMap.Scale({
                    position: 'LB'
                });
                cityCountyMap.addControl(mapAnnotations.scale);
                
                // 设置比例尺按钮为激活状态
                const scaleBtn = document.getElementById('scale-btn');
                if (scaleBtn) scaleBtn.classList.add('active');
                
            });
            
            // 默认开启鹰眼地图并更新按钮状态 - 使用普通地图样式
            AMap.plugin(["AMap.HawkEye"], function() {
                mapAnnotations.overView = new AMap.HawkEye({
                    autoMove: true,
                    showRectangle: true,
                    showButton: false,
                    mapStyle: 'amap://styles/normal',
                    layers: [new AMap.TileLayer()],
                    width: '200px',
                    height: '150px',
                    offset: new AMap.Pixel(20, 20)
                });
                cityCountyMap.addControl(mapAnnotations.overView);
                
                // 设置鹰眼按钮为激活状态
                const overviewBtn = document.getElementById('overview-btn');
                if (overviewBtn) overviewBtn.classList.add('active');
                

                // 添加鹰眼自定义样式
                setTimeout(addHawkEyeCustomStyle, 500);
            });
        }
        
        // 添加鹰眼自定义样式
        function addHawkEyeCustomStyle() {
            const hawkEyeElements = document.querySelectorAll('.amap-hawkeye');
            
            hawkEyeElements.forEach(function(element) {
                element.style.border = '1px solid #888888';
                element.style.borderRadius = '8px';
                element.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                element.style.overflow = 'hidden';
            });
            
            if (hawkEyeElements.length === 0) {
                setTimeout(addHawkEyeCustomStyle, 200);
            }
        }
        
        // 加载地图数据 - 根据按钮状态显示相应数据
        function loadMapData() {
            // 初始化中心过滤按钮文本
            updateCenterFilterButtonText();
            
            // 检查按钮状态，只显示激活按钮对应的数据
            const vehicleBtn = document.getElementById('vehicles-display-btn');
            const accidentBtn = document.getElementById('accidents-display-btn');
            const stationBtn = document.getElementById('stations-display-btn');
            const hospitalBtn = document.getElementById('hospitals-display-btn');
            const bloodBtn = document.getElementById('bloodstations-display-btn');
            const cdcBtn = document.getElementById('cdc-display-btn');
            
            if (vehicleBtn && vehicleBtn.classList.contains('active')) {
                // 改为先加载数据，再绘制车辆
                loadVehiclesData();
            }
            
            if (accidentBtn && accidentBtn.classList.contains('active')) {
                loadAccidentsData();
            }
            
            if (stationBtn && stationBtn.classList.contains('active')) {
                loadStationsData();
            }
            
            if (hospitalBtn && hospitalBtn.classList.contains('active')) {
                drawHospitals();
            }
            
            if (bloodBtn && bloodBtn.classList.contains('active')) {
                drawBloodStations();
            }
            
            if (cdcBtn && cdcBtn.classList.contains('active')) {
                drawCdcCenters();
            }
        }
        
        // 车辆数据 - 使用API获取的实时数据
        let vehicleData = []; // 存储API返回的车辆数据
        
        // 模拟数据定义 - 仅在API获取失败时使用的备用数据（保留以防API故障）
        const mockVehicles_backup = [
            {
                id: 'V001', 
                plateNumber: '川E-001急', 
                center: '泸州市中心', 
                station: '泸州市第一医院分站', 
                status: '在线', 
                location: [105.4457, 28.8757], 
                speed: 35, 
                direction: 90,
                hCondition: '0', // 0-正常，1-报停，3-未值班
                statusCode: '0', // 0-待命，1-发车，2-抵达等
                statusStr: '待命',
                stationLatitude: 28.8657,
                stationLongitude: 105.4357,
                acc: '1' // 0-熄火，1-点火
            },
            {
                id: 'V002', 
                plateNumber: '川E-002急', 
                center: '泸州市中心', 
                station: '泸州市第二医院分站', 
                status: '出车', 
                location: [105.4557, 28.8857], 
                speed: 45, 
                direction: 180,
                hCondition: '0',
                statusCode: '1',
                statusStr: '发车',
                stationLatitude: 28.8857,
                stationLongitude: 105.4557,
                acc: '1'
            },
            {
                id: 'V003', 
                plateNumber: '川E-003急', 
                center: '泸州市中心', 
                station: '泸州市第三医院分站', 
                status: '在线', 
                location: [105.4357, 28.8657], 
                speed: 28, 
                direction: 45,
                hCondition: '0',
                statusCode: '0',
                statusStr: '待命',
                stationLatitude: 28.8657,
                stationLongitude: 105.4357,
                acc: '1'
            },
            {
                id: 'V004', 
                plateNumber: '川E-101急', 
                center: '叙永县中心', 
                station: '叙永县人民医院分站', 
                status: '在线', 
                location: [105.1234, 28.7890], 
                speed: 0, 
                direction: 0,
                hCondition: '0',
                statusCode: '0',
                statusStr: '待命',
                stationLatitude: 28.7890,
                stationLongitude: 105.1234,
                acc: '0'
            },
            {
                id: 'V005', 
                plateNumber: '川E-201急', 
                center: '古蔺县中心', 
                station: '古蔺县人民医院分站', 
                status: '维修', 
                location: [105.8123, 28.9876], 
                speed: 0, 
                direction: 0,
                hCondition: '1', // 报停状态
                statusCode: '0',
                statusStr: '报停中',
                stationLatitude: 28.9876,
                stationLongitude: 105.8123,
                acc: '0'
            },
            {
                id: 'V006', 
                plateNumber: '川E-301急', 
                center: '合江县中心', 
                station: '合江县人民医院分站', 
                status: '在线', 
                location: [105.8345, 28.8123], 
                speed: 42, 
                direction: 270,
                hCondition: '0',
                statusCode: '0',
                statusStr: '待命',
                stationLatitude: 28.8123,
                stationLongitude: 105.8345,
                acc: '1'
            },
            {
                id: 'V007', 
                plateNumber: '川E-007急', 
                center: '泸州市中心', 
                station: '泸州市第四医院分站', 
                status: '下班', 
                location: [105.4257, 28.8457], 
                speed: 0, 
                direction: 0,
                hCondition: '3', // 未值班状态
                statusCode: '0',
                statusStr: '未值班',
                stationLatitude: 28.8457,
                stationLongitude: 105.4257,
                acc: '0'
            },
            {
                id: 'V008', 
                plateNumber: '川E-102急', 
                center: '叙永县中心', 
                station: '叙永县第二医院分站', 
                status: '任务中', 
                location: [105.1334, 28.7790], 
                speed: 38, 
                direction: 120,
                hCondition: '0',
                statusCode: '2', // 抵达状态
                statusStr: '已抵达',
                stationLatitude: 28.7790,
                stationLongitude: 105.1334,
                acc: '1'
            }
        ];
        
        // 事故任务数据 - 使用API数据替代
        let eventList = [];
        
        const mockHospitals = [
            {id: 'H001', name: '泸州市第一人民医院', center: '泸州市中心', type: '三甲', location: [105.4357, 28.8657], beds: 120, emergency: true, phone: '0830-3165599', address: '江阳区茜草街道'},
            {id: 'H002', name: '泸州市第二人民医院', center: '泸州市中心', type: '三甲', location: [105.4557, 28.8857], beds: 98, emergency: true, phone: '0830-3165600', address: '龙马潭区春晖路'},
            {id: 'H003', name: '泸州市中医院', center: '泸州市中心', type: '三甲', location: [105.4657, 28.8557], beds: 85, emergency: false, phone: '0830-3165601', address: '江阳区南光路'},
            {id: 'H004', name: '叙永县人民医院', center: '叙永县中心', type: '二甲', location: [105.1234, 28.7890], beds: 65, emergency: true, phone: '0830-6232120', address: '叙永县叙永镇'},
            {id: 'H005', name: '古蔺县人民医院', center: '古蔺县中心', type: '二甲', location: [105.8123, 28.9876], beds: 58, emergency: true, phone: '0830-7202120', address: '古蔺县古蔺镇'},
            {id: 'H006', name: '合江县人民医院', center: '合江县中心', type: '二甲', location: [105.8345, 28.8123], beds: 72, emergency: true, phone: '0830-5262120', address: '合江县合江镇'}
        ];
        
        const mockBloodStations = [
            {id: 'B001', name: '泸州市血站', center: '泸州市中心', location: [105.4457, 28.8757], bloodTypes: ['A+', 'B+', 'O+', 'AB+'], stock: '充足', contact: '0830-3211001', address: '江阳区大山坪街道'},
            {id: 'B002', name: '叙永县血站', center: '叙永县中心', location: [105.1234, 28.7890], bloodTypes: ['A+', 'O+'], stock: '偏少', contact: '0830-3211002', address: '叙永县工业园区'},
            {id: 'B003', name: '古蔺县血站', center: '古蔺县中心', location: [105.8123, 28.9876], bloodTypes: ['A+', 'B+', 'O+'], stock: '正常', contact: '0830-3211003', address: '古蔺县太平镇'}
        ];
        
        const mockCdcCenters = [
            {id: 'C001', name: '泸州市疾控中心', center: '泸州市中心', location: [105.4357, 28.8857], specialties: ['传染病', '慢性病', '职业病'], status: '24小时服务', phone: '0830-3123456', address: '江阳区酒谷大道'},
            {id: 'C002', name: '叙永县疾控中心', center: '叙永县中心', location: [105.1334, 28.7990], specialties: ['传染病', '慢性病'], status: '正常服务', phone: '0830-6233456', address: '叙永县环城路'},
            {id: 'C003', name: '古蔺县疾控中心', center: '古蔺县中心', location: [105.8223, 28.9976], specialties: ['传染病', '环境卫生'], status: '正常服务', phone: '0830-7203456', address: '古蔺县金兰大道'}
        ];
        
        const mockContacts = [
            {name: '张主任', position: '主任', department: '急救调度科', officePhone: '0830-2345678', mobile: '13912345678', email: '<EMAIL>'},
            {name: '李科长', position: '科长', department: '医疗救治科', officePhone: '0830-2345679', mobile: '13912345679', email: '<EMAIL>'},
            {name: '王医生', position: '副主任医师', department: '急诊医学科', officePhone: '0830-2345680', mobile: '13912345680', email: '<EMAIL>'},
            {name: '刘护士长', position: '护士长', department: '急救护理部', officePhone: '0830-2345681', mobile: '13912345681', email: '<EMAIL>'},
            {name: '陈调度员', position: '调度员', department: '急救调度科', officePhone: '0830-2345682', mobile: '13912345682', email: '<EMAIL>'},
            {name: '黄站长', position: '站长', department: '江阳区分站', officePhone: '0830-2345683', mobile: '13912345683', email: '<EMAIL>'},
            {name: '赵队长', position: '队长', department: '纳溪区分站', officePhone: '0830-2345684', mobile: '13912345684', email: '<EMAIL>'},
            {name: '孙主管', position: '主管', department: '设备维护科', officePhone: '0830-2345685', mobile: '13912345685', email: '<EMAIL>'},
            {name: '周秘书', position: '秘书', department: '综合办公室', officePhone: '0830-2345686', mobile: '13912345686', email: '<EMAIL>'},
            {name: '吴司机', position: '司机', department: '车辆管理科', officePhone: '0830-2345687', mobile: '13912345687', email: '<EMAIL>'},
            {name: '郑技师', position: '技师', department: '通讯技术科', officePhone: '0830-2345688', mobile: '13912345688', email: '<EMAIL>'},
            {name: '钱会计', position: '会计', department: '财务科', officePhone: '0830-2345689', mobile: '13912345689', email: '<EMAIL>'}
        ];
        
        // 任务数据部分已经移除，使用真实API数据
        
        // 数据加载函数
        function loadMockData() {
            console.log('加载API数据和模拟数据...');
            console.log('通讯录数据:', mockContacts.length + '个');
            
            // 默认只加载车辆和事故数据（使用API）
            loadAccidentsData();
            loadVehiclesData();

            // 其他资源数据不自动加载，等待用户点击按钮时再加载
            console.log('分站、医院、血站、疾控中心数据将在用户点击时加载');
        }
        
        // 分站数据 - 使用API获取的实时数据
        let stationData = []; // 存储API返回的分站数据

        // 医院数据 - 使用API获取的实时数据
        let hospitalData = []; // 存储API返回的医院数据

        // 血站数据 - 使用API获取的实时数据
        let bloodStationData = []; // 存储API返回的血站数据

        // 疾控中心数据 - 使用API获取的实时数据
        let cdcCenterData = []; // 存储API返回的疾控中心数据

        // 加载实时医院数据
        function loadHospitalsData() {
            console.log('正在加载实时医院数据...');

            // 准备请求参数
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_01" // 医院类型编码
            };

            // 处理centerCode参数
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key =>
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('医院数据按中心过滤:', params.centerCode);
                } else {
                    params.centerCode = "";
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('医院数据显示全部中心');
            }

            // 调用API获取数据
            getResourceHospitalInfo(params, function(data) {
                if (data && Array.isArray(data)) {
                    hospitalData = data;
                    console.log('成功加载医院数据:', hospitalData.length + '家');
                    console.log('医院数据示例:', hospitalData.length > 0 ? JSON.stringify(hospitalData[0]) : '无数据');

                    // 更新顶部统计
                    document.getElementById('topHospitalCount').textContent = hospitalData.length;

                    // 如果医院按钮是激活状态，则绘制医院标记
                    const hospitalBtn = document.getElementById('hospitals-display-btn');
                    if (hospitalBtn && hospitalBtn.classList.contains('active')) {
                        drawHospitals();
                    }

                    // 数据模块中的医院表格不自动更新，等待用户查询
                    console.log('医院数据已加载，等待用户查询');
                } else {
                    console.error('加载医院数据失败或格式错误:', data);
                    hospitalData = [];
                    document.getElementById('topHospitalCount').textContent = '0';

                    const hospitalBtn = document.getElementById('hospitals-display-btn');
                    if (hospitalBtn && hospitalBtn.classList.contains('active')) {
                        drawHospitals();
                    }
                }
            }, function(error) {
                console.error('加载医院数据出错:', error);
                hospitalData = [];
                document.getElementById('topHospitalCount').textContent = '0';

                const hospitalBtn = document.getElementById('hospitals-display-btn');
                if (hospitalBtn && hospitalBtn.classList.contains('active')) {
                    drawHospitals();
                }
            });
        }

        // 加载实时分站数据
        function loadStationsData() {
            console.log('正在加载实时分站数据...');

            // 准备请求参数
            const params = {
                centerCode: ""   // 按中心过滤时需要填充
            };

            // 处理centerCode参数
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key =>
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('分站数据按中心过滤:', params.centerCode);
                } else {
                    params.centerCode = "";
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('分站数据显示全部中心');
            }

            // 调用API获取数据
            getCityLinkageStationInfo(params, function(data) {
                if (data && Array.isArray(data)) {
                    stationData = data;
                    console.log('成功加载分站数据:', stationData.length + '个');
                    console.log('分站数据示例:', stationData.length > 0 ? JSON.stringify(stationData[0]) : '无数据');

                    // 更新顶部统计
                    document.getElementById('topStationCount').textContent = stationData.length;

                    // 如果分站按钮是激活状态，则绘制分站标记
                    const stationBtn = document.getElementById('stations-display-btn');
                    if (stationBtn && stationBtn.classList.contains('active')) {
                        drawStations();
                    }
                } else {
                    console.error('加载分站数据失败或格式错误:', data);
                    // 如果API返回失败，清空数据
                    stationData = [];
                    document.getElementById('topStationCount').textContent = '0';

                    const stationBtn = document.getElementById('stations-display-btn');
                    if (stationBtn && stationBtn.classList.contains('active')) {
                        drawStations();
                    }
                }
            }, function(error) {
                console.error('加载分站数据出错:', error);
                // 在API调用失败的情况下，清空数据
                stationData = [];
                document.getElementById('topStationCount').textContent = '0';

                const stationBtn = document.getElementById('stations-display-btn');
                if (stationBtn && stationBtn.classList.contains('active')) {
                    drawStations();
                }
            });
        }

        // 加载实时血站数据
        function loadBloodStationsData() {
            console.log('正在加载实时血站数据...');

            // 准备请求参数
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_10" // 血站类型编码
            };

            // 处理centerCode参数
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key =>
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('血站数据按中心过滤:', params.centerCode);
                } else {
                    params.centerCode = "";
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('血站数据显示全部中心');
            }

            // 调用API获取数据
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    bloodStationData = data;
                    console.log('成功加载血站数据:', bloodStationData.length + '家');
                    console.log('血站数据示例:', bloodStationData.length > 0 ? JSON.stringify(bloodStationData[0]) : '无数据');

                    // 如果血站按钮是激活状态，则绘制血站标记
                    const bloodBtn = document.getElementById('bloodstations-display-btn');
                    if (bloodBtn && bloodBtn.classList.contains('active')) {
                        drawBloodStations();
                    }

                    // 数据模块中的血站表格不自动更新，等待用户查询
                    console.log('血站数据已加载，等待用户查询');
                } else {
                    console.error('加载血站数据失败或格式错误:', data);
                    bloodStationData = [];

                    const bloodBtn = document.getElementById('bloodstations-display-btn');
                    if (bloodBtn && bloodBtn.classList.contains('active')) {
                        drawBloodStations();
                    }
                }
            }, function(error) {
                console.error('加载血站数据出错:', error);
                bloodStationData = [];

                const bloodBtn = document.getElementById('bloodstations-display-btn');
                if (bloodBtn && bloodBtn.classList.contains('active')) {
                    drawBloodStations();
                }
            });
        }

        // 加载实时疾控中心数据
        function loadCdcCentersData() {
            console.log('正在加载实时疾控中心数据...');

            // 准备请求参数
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_09" // 疾控中心类型编码
            };

            // 处理centerCode参数
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key =>
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('疾控中心数据按中心过滤:', params.centerCode);
                } else {
                    params.centerCode = "";
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('疾控中心数据显示全部中心');
            }

            // 调用API获取数据
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    cdcCenterData = data;
                    console.log('成功加载疾控中心数据:', cdcCenterData.length + '家');
                    console.log('疾控中心数据示例:', cdcCenterData.length > 0 ? JSON.stringify(cdcCenterData[0]) : '无数据');

                    // 如果疾控中心按钮是激活状态，则绘制疾控中心标记
                    const cdcBtn = document.getElementById('cdc-display-btn');
                    if (cdcBtn && cdcBtn.classList.contains('active')) {
                        drawCdcCenters();
                    }

                    // 数据模块中的疾控中心表格不自动更新，等待用户查询
                    console.log('疾控中心数据已加载，等待用户查询');
                } else {
                    console.error('加载疾控中心数据失败或格式错误:', data);
                    cdcCenterData = [];

                    const cdcBtn = document.getElementById('cdc-display-btn');
                    if (cdcBtn && cdcBtn.classList.contains('active')) {
                        drawCdcCenters();
                    }
                }
            }, function(error) {
                console.error('加载疾控中心数据出错:', error);
                cdcCenterData = [];

                const cdcBtn = document.getElementById('cdc-display-btn');
                if (cdcBtn && cdcBtn.classList.contains('active')) {
                    drawCdcCenters();
                }
            });
        }
        
        // 加载实时事故数据
        function loadAccidentsData() {
            console.log('正在加载实时事故数据...');
            
            // 准备请求参数
            const params = {
                beginTime: "",
                callType: "",
                centerCode: "",   // 按中心过滤时需要填充
                endTime: "",
                eventSrc: "",
                eventType: "",
                keys: "",
                page: 1,
                size: 900,  // 固定
                sortBy: [
                    {"createTime":"1"}  // 固定
                ],
                status: 2   // 正在进行中的事故，固定为2
            };
            
            // 处理centerCode参数
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key =>
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('事故数据按中心过滤:', params.centerCode);
                } else {
                    params.centerCode = "";
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('事故数据显示全部中心');
            }
            
            // 调用API获取数据
            getCityLinkageEventList(params, function(data) {
                if (data && data.records) {
                    eventList = data.records;
                    console.log('成功加载事故数据:', eventList.length + '起');
                    
                    // 更新顶部统计
                    document.getElementById('topAccidentCount').textContent = eventList.length;
                    
                    // 如果事故按钮是激活状态，则绘制事故标记
                    const accidentBtn = document.getElementById('accidents-display-btn');
                    if (accidentBtn && accidentBtn.classList.contains('active')) {
                        drawAccidents();
                    }
                } else {
                    console.error('加载事故数据失败:', data);
                }
            }, function(error) {
                console.error('加载事故数据出错:', error);
            });
        }
        
        // 面板控制函数
        function showTaskPanel() {
            console.log('打开任务信息面板');
            
            // 使用easyui-window的open方法
            try {
                $('#taskPanel').window('open');
                $('#taskPanel').window('center');
                console.log('任务信息窗口已打开并居中');
                
                // 显示拖拽提示（仅首次）
                if (!localStorage.getItem('dragTipShown')) {
                    setTimeout(() => {
                        showDragTip();
                        localStorage.setItem('dragTipShown', 'true');
                    }, 500);
                }
                
                loadTaskContent();
                
            } catch (error) {
                console.error('打开任务信息窗口失败:', error);
                                 $.messager.alert('错误', '任务信息窗口打开失败，请刷新页面重试', 'error');
            }
        }
        
        function showDataPanel() {
            console.log('打开数据模块面板');

            // 数据模块打开时不自动加载地图资源数据
            // 数据将在各个表格初始化时自动加载全部数据
            console.log('数据模块已打开，各表格将自动加载全部数据');
            
            // 使用easyui-window的open方法
            try {
                $('#dataPanel').window('open');
                $('#dataPanel').window('center');
                currentPanel = 'dataPanel';
                console.log('数据模块窗口已打开并居中');

                // 更新分中心下拉框数据
                setTimeout(() => {
                    updateDataModuleCenterOptions();
                }, 100);
                
                // 初始化EasyUI组件（防重复）
                if (!dataModuleInitialized) {
                    setTimeout(() => {
                        const tabElement = $('#dataTabs');
                        console.log('开始初始化EasyUI Tabs, 元素存在:', tabElement.length > 0);
                        
                        try {
                            // 检查tabs是否已经初始化
                            if (!tabElement.hasClass('tabs-container')) {
                                // 初始化tabs
                                tabElement.tabs({
                                    fit: true,
                                    border: false,
                                    onSelect: function(title, index) {
                                        console.log('切换到tab:', title, 'index:', index);
                                        // tabs切换后重新调整datagrid大小
                                        setTimeout(() => {
                                            $('#hospitalsGrid, #bloodstationsGrid, #cdcGrid, #phonebookGrid').datagrid('resize');
                                        }, 100);
                                    }
                                });
                                console.log('EasyUI Tabs初始化成功');
                            } else {
                                console.log('EasyUI Tabs已经初始化过');
                            }
                            
                            dataModuleInitialized = true;
                            
                            // 初始化数据表格
                            setTimeout(() => {
                                initDataModule();
                            }, 100);
                            
                        } catch (e) {
                            console.error('EasyUI Tabs初始化失败:', e);
                            // 如果初始化失败，尝试简单初始化
                            try {
                                tabElement.tabs();
                                console.log('EasyUI Tabs简单初始化成功');
                                dataModuleInitialized = true;
                                initDataModule();
                            } catch (e2) {
                                console.error('EasyUI Tabs简单初始化也失败:', e2);
                                // 最后尝试直接初始化数据模块
                                dataModuleInitialized = true;
                                initDataModule();
                            }
                        }
                    }, 200);
                } else {
                    console.log('数据模块已经初始化过，跳过重复初始化');
                    // 已初始化的情况下，需要重新调整datagrid大小
                    setTimeout(() => {
                        $('#hospitalsGrid, #bloodstationsGrid, #cdcGrid, #phonebookGrid').datagrid('resize');
                    }, 100);
                }
                
            } catch (error) {
                console.error('打开数据模块窗口失败:', error);
                                 $.messager.alert('错误', '数据模块窗口打开失败，请刷新页面重试', 'error');
            }
        }
        
        function showQueryPanel() {
            console.log('打开查询模块面板');
            
            // 使用easyui-window的open方法
            try {
                $('#queryPanel').window('open');
                $('#queryPanel').window('center');
                console.log('查询模块窗口已打开并居中');
                
                loadQueryContent();
                
            } catch (error) {
                console.error('打开查询模块窗口失败:', error);
                                 $.messager.alert('错误', '查询模块窗口打开失败，请刷新页面重试', 'error');
            }
        }
        

        
        function closePanel(panelId) {
            // 所有面板现在都是 easyui-window
            const easyuiPanels = ['taskPanel', 'dataPanel', 'queryPanel'];
            
            if (easyuiPanels.includes(panelId)) {
                try {
                    $('#' + panelId).window('close');
                    console.log(`${panelId}窗口已关闭`);
                } catch (error) {
                    console.error(`关闭${panelId}窗口失败:`, error);
                }
            } else {
                console.warn(`未知的面板ID: ${panelId}`);
            }
        }
        
        function closeAllPanels() {
            // 关闭所有easyui-window类型的面板
            const easyuiPanels = ['taskPanel', 'dataPanel', 'queryPanel'];
            
            easyuiPanels.forEach(panelId => {
                try {
                    $('#' + panelId).window('close');
                } catch (e) {
                    console.log(`${panelId}关闭失败或已关闭:`, e);
                }
            });
            
            // 重置数据模块初始化标记，允许重新初始化
            dataModuleInitialized = false;
            console.log('所有面板已关闭');
        }
        

        
        // 地图工具栏相关变量
        let trafficLayer = null;
        let measureTool = null;
        let mouseTool = null;
        let rangeCircle = null;
        let overviewCtrl = null;
        let currentSelectedVehicle = null;
        let followInterval = null;
        
        // 标记相关全局变量
        let lastSelectedMarker = null;
        let lastSelectedPosition = null;
        let lastSelectedType = '';
        let lastSelectedDescription = '';
        
        // 工具栏组折叠/展开功能
        function toggleToolbarGroup(groupId) {
            const group = document.getElementById(groupId);
            if (group) {
                group.classList.toggle('collapsed');
                console.log(`${groupId} 折叠状态切换`);
            }
        }
        
        // 车辆工具函数
        function followVehicle() {
            const btn = document.getElementById('follow-btn');
            if (btn.classList.contains('active')) {
                // 停止跟随
                btn.classList.remove('active');
                if (followInterval) {
                    clearInterval(followInterval);
                    followInterval = null;
                }
                console.log('停止跟随车辆');
            } else {
                // 开始跟随
                btn.classList.add('active');
                if (currentSelectedVehicle) {
                    startFollowVehicle(currentSelectedVehicle);
                }
                console.log('开始跟随车辆');
            }
        }
        
        function startFollowVehicle(vehicle) {
            if (followInterval) {
                clearInterval(followInterval);
            }
            
            followInterval = setInterval(() => {
                if (vehicle && vehicle.location) {
                    cityCountyMap.setCenter(vehicle.location);
                }
            }, 2000);
        }
        
        function showTrack() {
            const btn = document.getElementById('track-btn');
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                console.log('显示车辆轨迹');
                // 这里可以添加轨迹显示逻辑
            } else {
                console.log('隐藏车辆轨迹');
                // 这里可以添加轨迹隐藏逻辑
            }
        }
        
        function showConfig() {
            const btn = document.getElementById('config-btn');
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                console.log('显示配置面板');
                // 这里可以显示配置对话框
            } else {
                console.log('隐藏配置面板');
            }
        }
        
        // 地图工具函数 - 完全参考second.html实现
        function zoomIn() {
            cityCountyMap.zoomIn();
            console.log('放大地图');
        }
        
        function zoomOut() {
            cityCountyMap.zoomOut();
            console.log('缩小地图');
        }
        
        function toggleScale() {
            const btn = document.getElementById('scale-btn');
            
            if (mapAnnotations.scale) {
                // 关闭比例尺
                cityCountyMap.removeControl(mapAnnotations.scale);
                mapAnnotations.scale = null;
                btn.classList.remove('active');
                console.log('关闭比例尺显示');
            } else {
                // 开启比例尺
                AMap.plugin('AMap.Scale', function() {
                    mapAnnotations.scale = new AMap.Scale({
                        position: 'LB'
                    });
                    cityCountyMap.addControl(mapAnnotations.scale);
                    btn.classList.add('active');
                    console.log('开启比例尺显示');
                });
            }
        }
        
        function toggleTraffic() {
            const btn = document.getElementById('traffic-btn');
            
            if (mapAnnotations.trafficLayer) {
                // 关闭路况图层
                mapAnnotations.trafficLayer.hide();
                cityCountyMap.remove(mapAnnotations.trafficLayer);
                mapAnnotations.trafficLayer = null;
                btn.classList.remove('active');
                console.log('关闭路况显示');
            } else {
                // 开启路况图层
                mapAnnotations.trafficLayer = new AMap.TileLayer.Traffic({
                    zIndex: 10,
                    zooms: [7, 20]
                });
                mapAnnotations.trafficLayer.setMap(cityCountyMap);
                btn.classList.add('active');
                console.log('开启路况显示');
            }
        }
        
        function measureDistance() {
            const btn = document.getElementById('measure-btn');
            
            if (mapToolsState.measuring) {
                // 关闭测距工具
                if (mapAnnotations.ruler) {
                    mapAnnotations.ruler.close();
                    mapAnnotations.ruler = null;
                }
                mapToolsState.measuring = false;
                btn.classList.remove('active');
                console.log('关闭测距工具');
            } else {
                // 关闭其他工具
                closeOtherMapTools('measuring');
                
                // 显示测距使用提示
                $.messager.alert('测距功能', 
                    '测距功能已开启！<br><br>' +
                    '📐 <strong>使用方法：</strong><br>' +
                    '• 在地图上点击起始位置<br>' +
                    '• 继续点击设置途径点（可多个）<br>' +
                    '• 双击结束绘制并显示总距离<br><br>' +
                    '💡 <strong>温馨提示：</strong><br>' +
                    '测距结果为直线距离，非实际道路距离。', 
                    'info'
                );
                
                // 开启测距工具
                AMap.plugin('AMap.MouseTool', function() {
                    mapAnnotations.ruler = new AMap.MouseTool(cityCountyMap);
                    
                    // 监听测距完成事件
                    mapAnnotations.ruler.on('draw', function(e) {
                        if (e.obj) {
                            mapAnnotations.polylines.push(e.obj);
                            
                            // 计算并显示距离
                            const path = e.obj.getPath();
                            if (path && path.length >= 2) {
                                let totalDistance = 0;
                                for (let i = 1; i < path.length; i++) {
                                    totalDistance += path[i-1].distance(path[i]);
                                }
                                
                                // 格式化距离显示
                                let distanceText;
                                if (totalDistance < 1000) {
                                    distanceText = Math.round(totalDistance) + '米';
                                } else {
                                    distanceText = (totalDistance / 1000).toFixed(2) + '公里';
                                }
                                
                                // 在线段末端显示距离标签
                                const endPoint = path[path.length - 1];
                                const distanceMarker = new AMap.Marker({
                                    position: endPoint,
                                    content: `<div style="background: rgba(255,102,0,0.9); color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; border: 1px solid #fff; box-shadow: 0 2px 6px rgba(0,0,0,0.3); white-space: nowrap; cursor: pointer;">总距离: ${distanceText} <span style="margin-left: 5px; font-weight: bold;">✖</span></div>`,
                                    offset: new AMap.Pixel(-50, -30)
                                });
                                
                                distanceMarker.setMap(cityCountyMap);
                                mapAnnotations.markers.push(distanceMarker);
                                
                                // 建立关联关系
                                e.obj.distanceMarker = distanceMarker;
                                distanceMarker.measureLine = e.obj;
                                
                                // 添加点击删除事件
                                e.obj.on('click', function() {
                                    deleteMeasureLine(e.obj, distanceMarker);
                                });
                                
                                distanceMarker.on('click', function() {
                                    deleteMeasureLine(e.obj, distanceMarker);
                                });
                            }
                        }
                        
                        // 测距完成后自动关闭测距工具
                        mapAnnotations.ruler.close();
                        mapAnnotations.ruler = null;
                        mapToolsState.measuring = false;
                        btn.classList.remove('active');
                    });
                    
                    // 开始绘制折线进行测距
                    mapAnnotations.ruler.polyline({
                        strokeColor: '#FF6600',
                        strokeWeight: 3,
                        strokeOpacity: 0.8,
                        strokeStyle: 'solid',
                        strokeDasharray: [5, 5]
                    });
                    
                    mapToolsState.measuring = true;
                    btn.classList.add('active');
                });
            }
        }
        
        // 标记工具 - 参考secondScr_gaode.js完整实现
        function toggleMarkerTool() {
            const btn = document.getElementById('marker-btn');
            
            if (mapToolsState.marking) {
                // 关闭标注工具
                if (mapAnnotations.mouseTool) {
                    mapAnnotations.mouseTool.close();
                    mapAnnotations.mouseTool = null;
                }
                mapToolsState.marking = false;
                btn.classList.remove('active');
                console.log('关闭标注工具');
            } else {
                // 关闭其他工具
                closeOtherMapTools('marking');
                
                // 给出标注工具使用提示 - 参考secondScr_gaode.js
                $.messager.alert('标注功能', 
                    '标注功能已开启！<br><br>' +
                    '📍 <strong>使用方法：</strong><br>' +
                    '• 在地图上点击任意位置添加标注点<br>' +
                    '• 点击后会自动弹出编辑窗口<br>' +
                    '• 可以输入标注描述信息<br>' +
                    '• 系统会自动获取坐标和地址信息<br><br>' +
                    '🎯 <strong>功能特色：</strong><br>' +
                    '• 📋 一键复制坐标信息<br>' +
                    '• 📍 一键复制地址信息<br>' +
                    '• 🔄 智能地址解析<br>' +
                    '• ✏️ 随时编辑标注内容<br><br>' +
                    '💡 <strong>温馨提示：</strong><br>' +
                    '点击标注点可以重新编辑，标注信息会以红色醒目标签显示在地图上。', 
                    'info'
                );
                
                // 开启标注工具
                AMap.plugin('AMap.MouseTool', function() {
                    mapAnnotations.mouseTool = new AMap.MouseTool(cityCountyMap);
                    
                    // 监听标注完成事件
                    mapAnnotations.mouseTool.on('draw', function(e) {
                        mapAnnotations.markers.push(e.obj);
                        
                        // 设置更美观的标注图标 - 参考secondScr_gaode.js
                        e.obj.setIcon(new AMap.Icon({
                            image: 'data:image/svg+xml;base64,' + btoa(
                                '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="30" viewBox="0 0 24 30">' +
                                    '<defs>' +
                                        '<linearGradient id="grad1" x1="0%" y1="0%" x2="0%" y2="100%">' +
                                            '<stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />' +
                                            '<stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />' +
                                        '</linearGradient>' +
                                        '<filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">' +
                                            '<feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>' +
                                        '</filter>' +
                                    '</defs>' +
                                    '<path d="M12 0C5.373 0 0 5.373 0 12c0 12 12 18 12 18s12-6 12-18C24 5.373 18.627 0 12 0z" fill="url(#grad1)" stroke="#fff" stroke-width="2" filter="url(#shadow)"/>' +
                                    '<circle cx="12" cy="12" r="5" fill="#fff"/>' +
                                    '<circle cx="12" cy="12" r="3" fill="#ff4444"/>' +
                                '</svg>'
                            ),
                            size: new AMap.Size(24, 30),
                            imageSize: new AMap.Size(24, 30),
                            imageOffset: new AMap.Pixel(0, 0)
                        }));
                        
                        // 存储标注信息到标记对象
                        e.obj.markerInfo = '';
                        e.obj.markerId = mapAnnotations.markers.length;
                        e.obj.markerPosition = e.obj.getPosition(); // 存储坐标信息
                        
                        // 添加点击事件打开编辑窗口
                        e.obj.on('click', function() {
                            // 更新范围工具的选中状态
                            lastSelectedMarker = e.obj;
                            lastSelectedPosition = e.obj.getPosition();
                            lastSelectedType = 'marker';
                            lastSelectedDescription = `标注点 ${e.obj.markerInfo || '(未命名)'}`;
                            
                            openMarkerEditWindow(e.obj);
                        });
                        
                        // 标注完成后立即打开编辑窗口
                        openMarkerEditWindow(e.obj);
                        
                        // 标注完成后自动关闭标注工具
                        mapAnnotations.mouseTool.close();
                        mapAnnotations.mouseTool = null;
                        mapToolsState.marking = false;
                        btn.classList.remove('active');
                    });
                    
                    mapAnnotations.mouseTool.marker();
                    mapToolsState.marking = true;
                    btn.classList.add('active');
                });
            }
        }
        
        function toggleRangeMenu() {
            const menu = document.getElementById('range-menu');
            const isVisible = menu.style.display === 'block';
            menu.style.display = isVisible ? 'none' : 'block';
        }
        
        function drawRange(radius) {
            // 隐藏菜单
            document.getElementById('range-menu').style.display = 'none';
            
            // 移除之前的范围圆
            if (rangeCircle) {
                cityCountyMap.remove(rangeCircle);
                rangeCircle = null;
            }
            
            // 获取地图中心作为圆心
            const center = cityCountyMap.getCenter();
            
            // 创建范围圆 - 参考second.html样式
            rangeCircle = new AMap.Circle({
                center: center,
                radius: radius,
                fillColor: '#1890ff',
                fillOpacity: 0.1,
                strokeColor: '#1890ff',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                strokeStyle: 'solid',
                zIndex: 100
            });
            
            cityCountyMap.add(rangeCircle);
            mapAnnotations.circles.push(rangeCircle);
            
            // 在圆心添加距离标签
            const radiusText = radius >= 1000 ? `${radius/1000}公里` : `${radius}米`;
            const centerMarker = new AMap.Marker({
                position: center,
                content: `<div style="background: rgba(24,144,255,0.9); color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; border: 1px solid #fff; box-shadow: 0 2px 6px rgba(0,0,0,0.3); white-space: nowrap; cursor: pointer;">范围: ${radiusText} <span style="margin-left: 5px; font-weight: bold;">✖</span></div>`,
                offset: new AMap.Pixel(-30, -30)
            });
            
            centerMarker.setMap(cityCountyMap);
            mapAnnotations.markers.push(centerMarker);
            
            // 建立关联关系
            rangeCircle.centerMarker = centerMarker;
            centerMarker.rangeCircle = rangeCircle;
            
            // 添加点击删除事件
            rangeCircle.on('click', function() {
                deleteRangeCircle(rangeCircle, centerMarker);
            });
            
            centerMarker.on('click', function() {
                deleteRangeCircle(rangeCircle, centerMarker);
            });
            
            // 激活范围按钮
            const btn = document.getElementById('range-btn');
            btn.classList.add('active');
            mapToolsState.range = true;
            
            console.log(`绘制${radiusText}范围圆`);
        }
        
        function deleteRangeCircle(circle, marker) {
            $.messager.confirm('确认删除', '确定要删除这个范围圆吗？', function(r) {
                if (r) {
                    // 移除范围圆
                    if (circle) {
                        circle.setMap(null);
                        const circleIndex = mapAnnotations.circles.indexOf(circle);
                        if (circleIndex > -1) {
                            mapAnnotations.circles.splice(circleIndex, 1);
                        }
                    }
                    
                    // 移除标签
                    if (marker) {
                        marker.setMap(null);
                        const markerIndex = mapAnnotations.markers.indexOf(marker);
                        if (markerIndex > -1) {
                            mapAnnotations.markers.splice(markerIndex, 1);
                        }
                    }
                    
                    // 重置全局范围圆
                    if (rangeCircle === circle) {
                        rangeCircle = null;
                        const btn = document.getElementById('range-btn');
                        if (btn) btn.classList.remove('active');
                        mapToolsState.range = false;
                    }
                    
                    console.log('范围圆已删除');
                }
            });
        }
        
        function toggleOverview() {
            const btn = document.getElementById('overview-btn');
            
            if (mapAnnotations.overView) {
                // 关闭鹰眼
                cityCountyMap.removeControl(mapAnnotations.overView);
                mapAnnotations.overView = null;
                btn.classList.remove('active');
                console.log('关闭鹰眼地图');
            } else {
                // 开启鹰眼 - 使用普通地图样式
                AMap.plugin(["AMap.HawkEye"], function() {
                    mapAnnotations.overView = new AMap.HawkEye({
                        autoMove: true,
                        showRectangle: true,
                        showButton: false,
                        mapStyle: 'amap://styles/normal',
                        layers: [new AMap.TileLayer()],
                        width: '200px',
                        height: '150px',
                        offset: new AMap.Pixel(20, 20)
                    });
                    cityCountyMap.addControl(mapAnnotations.overView);
                    btn.classList.add('active');
                    console.log('开启鹰眼地图');
                    
                    // 添加自定义样式
                    setTimeout(addHawkEyeCustomStyle, 500);
                });
            }
        }
        
        // 标记编辑功能 - 参考secondScr_gaode.js实现
        function openMarkerEditWindow(marker) {
            const currentInfo = marker.markerInfo || '';
            const position = marker.getPosition();
            const isNewMarker = currentInfo === ''; // 判断是否为新建标注
            
            // 获取地址信息
            AMap.plugin('AMap.Geocoder', function() {
                const geocoder = new AMap.Geocoder();
                geocoder.getAddress([position.lng, position.lat], function(status, result) {
                    let addressInfo = '获取地址失败';
                    if (status === 'complete' && result.info === 'OK') {
                        const regeocode = result.regeocode;
                        addressInfo = regeocode.formattedAddress;
                    }
                    
                    const windowTitle = isNewMarker ? '📍 新建标注' : '📍 编辑标注';
                    const inputPlaceholder = isNewMarker ? '请输入标注信息' : '输入标注信息';
                    
                    const infoWindow = new AMap.InfoWindow({
                        content: `<div style="padding: 15px; min-width: 300px; max-width: 400px;">
                            <div style="margin-bottom: 12px; font-weight: bold; color: #333; border-bottom: 2px solid #FF4444; padding-bottom: 8px; font-size: 14px;">${windowTitle}</div>
                            
                            <div style="margin-bottom: 12px;">
                                <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666; font-weight: bold;">标注描述：</label>
                                <input type="text" id="marker-edit-input" 
                                       value="${currentInfo}"
                                       placeholder="${inputPlaceholder}" 
                                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; box-sizing: border-box;"
                                       autofocus>
                            </div>
                            
                            <div style="margin-bottom: 12px; background: #f8f9fa; padding: 10px; border-radius: 6px; border-left: 4px solid #FF4444;">
                                <div style="margin-bottom: 6px;">
                                    <strong style="color: #FF4444;">🌐 坐标信息：</strong>
                                </div>
                                <div style="font-size: 12px; color: #666; line-height: 1.5;">
                                    <div><strong>经度：</strong> ${position.lng.toFixed(6)}</div>
                                    <div><strong>纬度：</strong> ${position.lat.toFixed(6)}</div>
                                    <div><strong>地址：</strong> ${addressInfo}</div>
                                </div>
                                <div style="margin-top: 8px;">
                                    <button onclick="copyCoordinates('${position.lng.toFixed(6)}', '${position.lat.toFixed(6)}')" 
                                            style="padding: 4px 8px; background: #FF4444; color: #fff; border: none; border-radius: 3px; cursor: pointer; font-size: 11px; margin-right: 5px;">
                                        📋 复制坐标
                                    </button>
                                    <button id="copy-address-btn" data-address="${addressInfo.replace(/"/g, '&quot;')}" onclick="copyAddressFromData(this);" 
                                            style="padding: 4px 8px; background: #52C41A; color: #fff; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                                        📍 复制地址
                                    </button>
                                </div>
                            </div>
                            
                            <div style="text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
                                <button onclick="saveMarkerEdit('${marker.markerId}')" 
                                        style="padding: 8px 15px; background: #1890ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">💾 保存</button>
                                <button onclick="deleteMarker('${marker.markerId}')" 
                                        style="padding: 8px 15px; background: #ff4d4f; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">🗑️ 删除</button>
                                <button onclick="closeMarkerEdit()" 
                                        style="padding: 8px 15px; background: #f5f5f5; color: #666; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold;">❌ 取消</button>
                            </div>
                        </div>`,
                        offset: new AMap.Pixel(0, -25),
                        autoMove: false  // 禁用自动移动
                    });
                    
                    infoWindow.open(cityCountyMap, marker.getPosition());
                    marker.currentInfoWindow = infoWindow;
                    
                    // 设置全局当前编辑的标记
                    window.currentEditingMarker = marker;
                    
                    // 如果是新建标注，延迟一下让输入框获得焦点
                    if (isNewMarker) {
                        setTimeout(function() {
                            const input = document.getElementById('marker-edit-input');
                            if (input) {
                                input.focus();
                                input.select(); // 选中输入框内容
                            }
                        }, 100);
                    }
                });
            });
        }

        // 保存标注编辑
        window.saveMarkerEdit = function(markerId) {
            const input = document.getElementById('marker-edit-input');
            const text = input.value.trim();
            
            if (window.currentEditingMarker) {
                const marker = window.currentEditingMarker;
                
                // 如果没有输入内容，提示用户
                if (!text) {
                    $.messager.confirm('确认', '标注内容为空，是否删除此标注？', function(r) {
                        if (r) {
                            deleteMarkerInternal(marker);
                        }
                    });
                    return;
                }
                
                // 保存标注信息
                marker.markerInfo = text;
                
                // 设置标签 - 使用红色背景白色字体
                marker.setLabel({
                    offset: new AMap.Pixel(15, -15),
                    content: `<div style="background: #ff4d4f; color: #fff; padding: 4px 8px; border-radius: 4px; border: 1px solid #fff; box-shadow: 0 2px 6px rgba(0,0,0,0.3); font-size: 12px; font-weight: bold; white-space: nowrap; max-width: 200px; word-wrap: break-word;">${text}</div>`,
                    direction: 'top'
                });
                
                // 关闭编辑窗口
                closeMarkerEdit();
                
                console.log('标注已保存:', text);
                $.messager.show({
                    title: '保存成功',
                    msg: '标注已保存：' + text,
                    timeout: 2000,
                    showType: 'slide'
                });
            }
        };

        // 删除标注
        window.deleteMarker = function(markerId) {
            if (window.currentEditingMarker) {
                $.messager.confirm('确认删除', '确定要删除这个标注吗？', function(r) {
                    if (r) {
                        deleteMarkerInternal(window.currentEditingMarker);
                    }
                });
            }
        };

        // 内部删除标注函数
        function deleteMarkerInternal(marker) {
            if (marker) {
                // 从地图上移除标记
                marker.setMap(null);
                
                // 从数组中移除
                const index = mapAnnotations.markers.indexOf(marker);
                if (index > -1) {
                    mapAnnotations.markers.splice(index, 1);
                }
                
                // 关闭编辑窗口
                closeMarkerEdit();
                
                console.log('标注已删除');
                $.messager.show({
                    title: '删除成功',
                    msg: '标注已删除',
                    timeout: 2000,
                    showType: 'slide'
                });
            }
        }

        // 关闭标注编辑
        window.closeMarkerEdit = function() {
            if (window.currentEditingMarker && window.currentEditingMarker.currentInfoWindow) {
                window.currentEditingMarker.currentInfoWindow.close();
                window.currentEditingMarker.currentInfoWindow = null;
            }
            window.currentEditingMarker = null;
        };

        // 复制坐标功能
        window.copyCoordinates = function(lng, lat) {
            const text = `${lng},${lat}`;
            copyToClipboard(text, '坐标');
        };

        // 复制地址功能
        window.copyAddressFromData = function(button) {
            const address = button.getAttribute('data-address');
            copyToClipboard(address, '地址');
        };

        // 通用复制到剪贴板功能
        function copyToClipboard(text, type) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代剪贴板API
                navigator.clipboard.writeText(text).then(function() {
                    $.messager.show({
                        title: '复制成功',
                        msg: `${type}已复制到剪贴板：${text}`,
                        timeout: 2000,
                        showType: 'slide'
                    });
                }).catch(function(err) {
                    fallbackCopyTextToClipboard(text, type);
                });
            } else {
                // 后备方案
                fallbackCopyTextToClipboard(text, type);
            }
        }

        // 后备复制功能
        function fallbackCopyTextToClipboard(text, type) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    $.messager.show({
                        title: '复制成功',
                        msg: `${type}已复制到剪贴板：${text}`,
                        timeout: 2000,
                        showType: 'slide'
                    });
                } else {
                    $.messager.alert('复制失败', '无法复制到剪贴板，请手动复制', 'error');
                }
            } catch (err) {
                $.messager.alert('复制失败', '您的浏览器不支持自动复制，请手动复制：' + text, 'error');
            }
            
            document.body.removeChild(textArea);
        }

        function clearMeasure() {
            // 清除所有测量工具
            if (mapAnnotations.mouseTool) {
                mapAnnotations.mouseTool.close();
                mapAnnotations.mouseTool = null;
            }
            
            if (mapAnnotations.ruler) {
                mapAnnotations.ruler.close();
                mapAnnotations.ruler = null;
            }
            
            // 清除所有标记（包括距离标签和标注）
            mapAnnotations.markers.forEach(marker => {
                marker.setMap(null);
            });
            mapAnnotations.markers = [];
            
            // 清除所有线段（包括测距线）
            mapAnnotations.polylines.forEach(polyline => {
                polyline.setMap(null);
            });
            mapAnnotations.polylines = [];
            
            // 清除所有圆形标记
            mapAnnotations.circles.forEach(circle => {
                circle.setMap(null);
            });
            mapAnnotations.circles = [];
            
            // 清除范围圆
            if (rangeCircle) {
                cityCountyMap.remove(rangeCircle);
                rangeCircle = null;
            }
            
            // 重置按钮状态
            ['measure-btn', 'marker-btn', 'range-btn'].forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    btn.classList.remove('active');
                }
            });
            
            // 重置工具状态
            mapToolsState.measuring = false;
            mapToolsState.marking = false;
            mapToolsState.range = false;
            
            // 关闭所有信息窗口
            cityCountyMap.clearInfoWindow();
            
            console.log('清除所有测量标注完成');
        }
        

        
        // 地图资源显示控制函数
        function toggleVehicleDisplay() {
            const btn = document.getElementById('vehicles-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 加载车辆数据并绘制
                loadVehiclesData();
                console.log('显示车辆，正在加载最新数据...');
            } else {
                // 清除车辆标记
                vehicleMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                vehicleMarkers = [];
                vehicleMarkersMap.clear(); // 清空标记映射
                console.log('隐藏车辆');
            }
        }
        
        // 加载车辆实时数据
        function loadVehiclesData() {
            console.log('[车辆数据] 正在加载实时车辆数据...');
            const params = {
                centerCode: "",  // 默认显示全部车辆
                hCondition: "",
                plateNum: "",
                stationId: "",
                status: ""
            };
            
            // 处理中心过滤条件
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key => 
                    key !== 'all' && centerFilterOptions[key]
                );
                if (selectedCenters.length > 0) {
                    params.centerCode = selectedCenters.join(',');
                    console.log('[车辆数据] 车辆数据按中心过滤:', params.centerCode);
                    
                    // 记录选中的中心名称
                    const centerNames = selectedCenters.map(centerId => getCenterDisplayName(centerId)).filter(Boolean);
                    console.log('[车辆数据] 选中的中心:', centerNames.join(', ') || '无法获取中心名称');
                } else {
                    params.centerCode = "";
                    console.log('[车辆数据] 未选中任何中心，默认显示全部');
                }
            } else {
                params.centerCode = ""; // 选择全部中心时传空字符串
                console.log('[车辆数据] 车辆数据显示全部中心');
            }
            
            console.log('[车辆数据] 正在请求车辆数据，参数:', JSON.stringify(params));
            getVehicleLocations(params, function(data) {
                if (data && Array.isArray(data)) {
                    // 替换全局变量中的模拟数据
                    vehicleData = data;
                    console.log('[车辆数据] 成功加载车辆数据:', vehicleData.length + '辆');
                    
                    if (vehicleData.length > 0) {
                        console.log('[车辆数据] 车辆数据示例:', JSON.stringify({
                            plateNum: vehicleData[0].plateNum,
                            regionName: vehicleData[0].regionName,
                            status: vehicleData[0].status,
                            hCondition: vehicleData[0].hCondition
                        }));
                    } else {
                        console.log('[车辆数据] 无车辆数据');
                    }
                    
                    // 更新顶部计数
                    document.getElementById('topVehicleCount').textContent = vehicleData.length;
                    
                    // 更新车辆状态计数
                    console.log('[车辆数据] 加载完成后更新车辆状态过滤计数');
                    updateVehicleFilterCounts();
                    
                    // 在车辆按钮激活时绘制车辆
                    const vehicleBtn = document.getElementById('vehicles-display-btn');
                    if (vehicleBtn && vehicleBtn.classList.contains('active')) {
                        console.log('[车辆数据] 车辆按钮已激活，开始绘制车辆...');
                        drawVehiclesWithStatusFilter();
                    }
                } else {
                    console.error('[车辆数据] 加载车辆数据失败或格式错误:', data);
                    // 如果API返回失败，尝试使用备用数据
                    console.log('[车辆数据] 尝试使用备用数据显示车辆');
                    vehicleData = [];
                    
                    // 更新车辆状态计数 (使用备用数据)
                    updateVehicleFilterCounts();
                    
                    const vehicleBtn = document.getElementById('vehicles-display-btn');
                    if (vehicleBtn && vehicleBtn.classList.contains('active')) {
                        drawVehiclesWithStatusFilter();
                    }
                }
            }, function(error) {
                console.error('[车辆数据] 加载车辆数据出错:', error);
                // 在API调用失败的情况下，尝试使用备用数据
                console.log('[车辆数据] API调用失败，尝试使用备用数据显示车辆');
                vehicleData = [];
                
                // 更新车辆状态计数 (使用备用数据)
                updateVehicleFilterCounts();
                
                const vehicleBtn = document.getElementById('vehicles-display-btn');
                if (vehicleBtn && vehicleBtn.classList.contains('active')) {
                    drawVehiclesWithStatusFilter();
                }
            });
        }
        
        // 事故任务显示控制
        function toggleAccidentDisplay() {
            const btn = document.getElementById('accidents-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 每次激活都重新加载最新数据
                loadAccidentsData();
                console.log('显示事故任务，正在加载最新数据...');
            } else {
                accidentMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                accidentMarkers = [];
                console.log('隐藏事故任务');
            }
        }
        
        function toggleStationDisplay() {
            const btn = document.getElementById('stations-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 加载分站数据并绘制
                loadStationsData();
                console.log('显示分站，正在加载最新数据...');
            } else {
                // 清除分站标记
                stationMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                stationMarkers = [];
                console.log('隐藏分站');
            }
        }
        
        function toggleHospitalDisplay() {
            const btn = document.getElementById('hospitals-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 加载医院数据并绘制
                loadHospitalsData();
                console.log('显示医院，正在加载最新数据...');
            } else {
                // 清除医院标记
                hospitalMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                hospitalMarkers = [];
                console.log('隐藏医院');
            }
        }
        
        // 全局变量存储血站和疾控中心标记
        let bloodStationMarkers = [];
        let cdcCenterMarkers = [];
        
        function toggleBloodDisplay() {
            const btn = document.getElementById('bloodstations-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 加载血站数据并绘制
                loadBloodStationsData();
                console.log('显示血站，正在加载最新数据...');
            } else {
                // 清除血站标记
                bloodStationMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                bloodStationMarkers = [];
                console.log('隐藏血站');
            }
        }
        
        function toggleCdcDisplay() {
            const btn = document.getElementById('cdc-display-btn');
            
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                // 加载疾控中心数据并绘制
                loadCdcCentersData();
                console.log('显示疾控中心，正在加载最新数据...');
            } else {
                // 清除疾控中心标记
                cdcCenterMarkers.forEach(marker => {
                    if (marker && marker.setMap) {
                        marker.setMap(null);
                    }
                });
                cdcCenterMarkers = [];
                console.log('隐藏疾控中心');
            }
        }
        
        // 绘制血站 - 使用API数据（已在接口层面过滤）
        function drawBloodStations() {
            // 清除之前的血站标记
            bloodStationMarkers.forEach(marker => {
                if (marker && marker.setMap) {
                    marker.setMap(null);
                }
            });
            bloodStationMarkers = [];
            
            // 检查是否有血站数据
            if (!bloodStationData || bloodStationData.length === 0) {
                console.log('没有血站数据可以显示');
                return;
            }

            console.log('开始绘制血站，数据量:', bloodStationData.length);

            // 直接绘制标记（数据已在接口层面按中心过滤）
            bloodStationData.forEach(bloodStation => {
                // 检查经纬度是否有效
                if (!bloodStation.longitude || !bloodStation.latitude) {
                    console.log('血站数据缺少有效坐标:', bloodStation.name);
                    return;
                }

                const icon = new AMap.Icon({
                    size: new AMap.Size(28, 28),
                    image: 'data:image/svg+xml;base64,' + btoa(
                        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">' +
                        '<circle cx="12" cy="12" r="11" fill="#ff4d4f" stroke="#fff" stroke-width="2"/>' +
                        '<path d="M12 6v12M6 12h12" stroke="#fff" stroke-width="3" stroke-linecap="round"/>' +
                        '<text x="12" y="8" font-family="Arial" font-size="6" fill="#fff" text-anchor="middle">B</text>' +
                        '</svg>'
                    ),
                    imageSize: new AMap.Size(28, 28)
                });
                
                const marker = new AMap.Marker({
                    position: [bloodStation.longitude, bloodStation.latitude],
                    icon: icon,
                    title: bloodStation.name,
                    extData: bloodStation
                });
                
                // 创建血站信息窗口
                let bloodStationText = '';
                bloodStationText += `<font size='2' color='#ff4d4f'>血站名称：</font>`;
                bloodStationText += `<font size='2'>${bloodStation.name}</font>`;
                bloodStationText += `<hr style='color:#ff4d4f'>`;
                bloodStationText += `<font size='2' color='#ff4d4f'>所属中心：</font>`;
                bloodStationText += `<font size='2'>${bloodStation.regionName || '未知'}</font>`;

                if (bloodStation.contactNumber) {
                bloodStationText += `<hr style='color:#ff4d4f'>`;
                    bloodStationText += `<font size='2' color='#ff4d4f'>联系电话：</font>`;
                    bloodStationText += `<font size='2'>${bloodStation.contactNumber}</font>`;
                }

                if (bloodStation.linkman) {
                bloodStationText += `<hr style='color:#ff4d4f'>`;
                    bloodStationText += `<font size='2' color='#ff4d4f'>联系人：</font>`;
                    bloodStationText += `<font size='2'>${bloodStation.linkman}</font>`;
                }

                if (bloodStation.address) {
                bloodStationText += `<hr style='color:#ff4d4f'>`;
                    bloodStationText += `<font size='2' color='#ff4d4f'>血站地址：</font>`;
                    bloodStationText += `<font size='2'>${bloodStation.address}</font>`;
                }

                if (bloodStation.descript) {
                    bloodStationText += `<hr style='color:#ff4d4f'>`;
                    bloodStationText += `<font size='2' color='#ff4d4f'>描述：</font>`;
                    bloodStationText += `<font size='2'>${bloodStation.descript}</font>`;
                }
                
                const infoContent = bloodStationText;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                bloodStationMarkers.push(marker);
            });

            console.log('血站绘制完成，共绘制', bloodStationMarkers.length, '个血站标记');
        }
        


        // 绘制疾控中心 - 使用API数据（已在接口层面过滤）
        function drawCdcCenters() {
            // 清除之前的疾控中心标记
            cdcCenterMarkers.forEach(marker => {
                if (marker && marker.setMap) {
                    marker.setMap(null);
                }
            });
            cdcCenterMarkers = [];
            
            // 检查是否有疾控中心数据
            if (!cdcCenterData || cdcCenterData.length === 0) {
                console.log('没有疾控中心数据可以显示');
                return;
            }

            console.log('开始绘制疾控中心，数据量:', cdcCenterData.length);

            // 直接绘制标记（数据已在接口层面按中心过滤）
            cdcCenterData.forEach(cdc => {
                // 检查经纬度是否有效
                if (!cdc.longitude || !cdc.latitude) {
                    console.log('疾控中心数据缺少有效坐标:', cdc.name);
                    return;
                }

                const icon = new AMap.Icon({
                    size: new AMap.Size(28, 28),
                    image: 'data:image/svg+xml;base64,' + btoa(
                        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">' +
                        '<circle cx="12" cy="12" r="11" fill="#52c41a" stroke="#fff" stroke-width="2"/>' +
                        '<path d="M8 12l2 2 4-4" stroke="#fff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>' +
                        '<text x="12" y="8" font-family="Arial" font-size="6" fill="#fff" text-anchor="middle">C</text>' +
                        '</svg>'
                    ),
                    imageSize: new AMap.Size(28, 28)
                });
                
                const marker = new AMap.Marker({
                    position: [cdc.longitude, cdc.latitude],
                    icon: icon,
                    title: cdc.name,
                    extData: cdc
                });
                
                // 创建疾控中心信息窗口
                let cdcText = '';
                cdcText += `<font size='2' color='#52c41a'>疾控中心：</font>`;
                cdcText += `<font size='2'>${cdc.name}</font>`;
                cdcText += `<hr style='color:#52c41a'>`;
                cdcText += `<font size='2' color='#52c41a'>所属中心：</font>`;
                cdcText += `<font size='2'>${cdc.regionName || '未知'}</font>`;

                if (cdc.contactNumber) {
                cdcText += `<hr style='color:#52c41a'>`;
                    cdcText += `<font size='2' color='#52c41a'>联系电话：</font>`;
                    cdcText += `<font size='2'>${cdc.contactNumber}</font>`;
                }

                if (cdc.linkman) {
                cdcText += `<hr style='color:#52c41a'>`;
                    cdcText += `<font size='2' color='#52c41a'>联系人：</font>`;
                    cdcText += `<font size='2'>${cdc.linkman}</font>`;
                }

                if (cdc.address) {
                    cdcText += `<hr style='color:#52c41a'>`;
                    cdcText += `<font size='2' color='#52c41a'>疾控地址：</font>`;
                    cdcText += `<font size='2'>${cdc.address}</font>`;
                }

                if (cdc.descript) {
                    cdcText += `<hr style='color:#52c41a'>`;
                    cdcText += `<font size='2' color='#52c41a'>描述：</font>`;
                    cdcText += `<font size='2'>${cdc.descript}</font>`;
                }
                
                const infoContent = cdcText;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                cdcCenterMarkers.push(marker);
            });

            console.log('疾控中心绘制完成，共绘制', cdcCenterMarkers.length, '个疾控中心标记');
        }



        // 更新数据模块中的分中心下拉框
        function updateDataModuleCenterOptions() {
            try {
                if (!globalCenterData || !Array.isArray(globalCenterData)) {
                    console.log('全局中心数据不可用，无法更新数据模块下拉框');
                    return;
                }

                console.log('更新数据模块分中心下拉框，数据量:', globalCenterData.length);

                // 更新医院管理的分中心下拉框
                const hospitalCenterFilter = $('#hospitalCenterFilter');
                if (hospitalCenterFilter.length > 0) {
                    // 清空现有选项
                    hospitalCenterFilter.combobox('clear');

                    // 准备数据
                    const centerOptions = [{text: '全部中心', value: ''}];
                    globalCenterData.forEach(center => {
                        if (center.codeName && center.codeVale) {
                            centerOptions.push({
                                text: center.codeVale,
                                value: center.codeName
                            });
                        }
                    });

                    // 加载数据
                    hospitalCenterFilter.combobox('loadData', centerOptions);
                    console.log('医院管理分中心下拉框已更新');
                }

                // 初始化医院等级下拉框
                const hospitalLevelFilter = $('#hospitalLevelFilter');
                if (hospitalLevelFilter.length > 0) {
                    const levelOptions = [
                        {text: '全部等级', value: ''},
                        {text: '三级甲等', value: '11'},
                        {text: '三级乙等', value: '12'},
                        {text: '三级丙等', value: '13'},
                        {text: '二级甲等', value: '21'},
                        {text: '二级乙等', value: '22'},
                        {text: '二级丙等', value: '23'},
                        {text: '一级甲等', value: '31'},
                        {text: '一级乙等', value: '32'},
                        {text: '一级丙等', value: '33'},
                        {text: '乡镇卫生院', value: '40'},
                        {text: '社区医疗服务中心', value: '50'},
                        {text: '诊所', value: '60'},
                        {text: '其他', value: '99'}
                    ];
                    hospitalLevelFilter.combobox('loadData', levelOptions);
                    console.log('医院等级下拉框已初始化');
                }

                // 更新血站管理的分中心下拉框
                const bloodstationCenterFilter = $('#bloodstationCenterFilter');
                if (bloodstationCenterFilter.length > 0) {
                    // 清空现有选项
                    bloodstationCenterFilter.combobox('clear');

                    // 准备数据
                    const centerOptions = [{text: '全部中心', value: ''}];
                    globalCenterData.forEach(center => {
                        if (center.codeName && center.codeVale) {
                            centerOptions.push({
                                text: center.codeVale,
                                value: center.codeName
                            });
                        }
                    });

                    // 加载数据
                    bloodstationCenterFilter.combobox('loadData', centerOptions);
                    console.log('血站管理分中心下拉框已更新');
                }

                // 更新疾控中心管理的分中心下拉框
                const cdcCenterFilter = $('#cdcCenterFilter');
                if (cdcCenterFilter.length > 0) {
                    // 清空现有选项
                    cdcCenterFilter.combobox('clear');

                    // 准备数据
                    const centerOptions = [{text: '全部中心', value: ''}];
                    globalCenterData.forEach(center => {
                        if (center.codeName && center.codeVale) {
                            centerOptions.push({
                                text: center.codeVale,
                                value: center.codeName
                            });
                        }
                    });

                    // 加载数据
                    cdcCenterFilter.combobox('loadData', centerOptions);
                    console.log('疾控中心管理分中心下拉框已更新');
                }

            } catch (error) {
                console.error('更新数据模块分中心下拉框失败:', error);
            }
        }
        
        // 头部工具函数
        function refreshData() {
            console.log('刷新页面...');
            window.location.reload();
        }
        
        function showSystemSettings() {
            console.log('显示系统设置');
        }
        
        // 地图绘制函数 - 使用API数据
        function drawVehicles() {
            // 调用带状态过滤的车辆绘制函数
            drawVehiclesWithStatusFilter();
        }
        
        // 此函数已被新版本的drawAccidents代替，保留函数签名以防其他地方调用
        function drawFilteredAccidents(selectedCenter) {
            // 直接调用新的绘制函数
            drawAccidents();
        }

        // 绘制事故任务标记 - 使用API数据（已在接口层面过滤）
        function drawAccidents() {
            // 清除之前的标记
            accidentMarkers.forEach(marker => marker.setMap(null));
            accidentMarkers = [];
            
            // 检查是否有数据
            if (!eventList || eventList.length === 0) {
                console.log('没有事故数据可以显示');
                return;
            }
            
            console.log('开始绘制事故任务，数据量:', eventList.length);

            // 直接绘制标记（数据已在接口层面按中心过滤）
            eventList.forEach(event => {
                // 检查经纬度是否有效
                if (!event.lng || !event.lat) {
                    console.log('事故数据缺少有效坐标:', event.id);
                    return;
                }
                
                // 根据紧急程度选择图标
                let iconName = 'gisTask.png'; // 默认图标
                
                const icon = new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: `style/img/${iconName}`,
                    imageSize: new AMap.Size(32, 32)
                });
                
                const marker = new AMap.Marker({
                    position: [event.lng, event.lat],
                    icon: icon,
                    title: event.callTypeCodeName || '事故',
                    extData: event
                });
                
                // 创建事故信息窗口
                let accidentText = '';
                accidentText += `<font size='2' color='#FF4500'>事件编号：</font>`;
                accidentText += `<font size='2'>${event.id}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>事件类型：</font>`;
                accidentText += `<font size='2'>${formatEventType(event.eventType).replace(/<[^>]*>/g, '') || '未知类型'}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>事件来源：</font>`;
                accidentText += `<font size='2'>${event.eventSrcCodeName || '未知'}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>呼叫类型：</font>`;
                accidentText += `<font size='2'>${event.callTypeCodeName || '未知类型'}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>所属中心：</font>`;
                accidentText += `<font size='2'>${event.regionName || '未知'}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>当前状态：</font>`;
                accidentText += `<font size='2' color='${getStatusColor(getStatusText(event.status))}'>${getStatusText(event.status)}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>来电时间：</font>`;
                accidentText += `<font size='2'>${formatDateTime(event.callInTimes)}</font>`;
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>现场地址：</font>`;
                accidentText += `<font size='2'>${event.address || '未知'}</font>`;
                
                if (event.majorCall) {
                    accidentText += `<hr style='color:#FF4500'>`;
                    accidentText += `<font size='2' color='#FF4500'>主诉：</font>`;
                    accidentText += `<font size='2'>${event.majorCall}</font>`;
                }
                
                if (event.contacter) {
                    accidentText += `<hr style='color:#FF4500'>`;
                    accidentText += `<font size='2' color='#FF4500'>联系人：</font>`;
                    accidentText += `<font size='2'>${event.contacter}</font>`;
                }
                
                if (event.contact) {
                    accidentText += `<hr style='color:#FF4500'>`;
                    accidentText += `<font size='2' color='#FF4500'>联系电话：</font>`;
                    accidentText += `<font size='2'>${event.contact}</font>`;
                }
                
                if (event.patientAmount) {
                accidentText += `<hr style='color:#FF4500'>`;
                accidentText += `<font size='2' color='#FF4500'>伤病员：</font>`;
                    accidentText += `<font size='2'>${event.patientAmount}人</font>`;
                }
                
                const infoContent = accidentText;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                accidentMarkers.push(marker);
            });
            
            console.log('事故任务绘制完成，共绘制', accidentMarkers.length, '个任务标记');
        }
        
        // 格式化日期时间
        function formatDateTime(timestamp) {
            if (!timestamp) return '未知';
            
            try {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            } catch (e) {
                return timestamp;
            }
        }
        
        
        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case '1': return '未调度-落单';
                case '2': return '已调度';
                case '3': return '已完成';
                case '4': return '已撤销';
                case '6': return '未调度-待派';
                case '7': return '未调度-预约';
                default: return '未知状态';
            }
        }
        
        // 辅助函数：计算两点间距离（米）
        function getDistance(lat1, lng1, lat2, lng2) {
            var radLat1 = lat1 * Math.PI / 180.0;
            var radLat2 = lat2 * Math.PI / 180.0;
            var a = radLat1 - radLat2;
            var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
            var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            s = s * 6378.137; // 地球半径
            s = Math.round(s * 10000) / 10000;
            return s * 1000; // 转换为米
        }
        
        // 获取严重程度颜色
        function getSeverityColor(severity) {
            switch(severity) {
                case '重大': return '#ff4d4f';
                case '较大': return '#fa8c16';
                case '一般': return '#1890ff';
                case '轻微': return '#52c41a';
                default: return '#333';
            }
        }
        
        // 获取状态颜色
        function getStatusColor(status) {
            switch(status) {
                case '紧急处理': return '#ff4d4f';
                case '处理中': return '#fa8c16';
                case '已完成': return '#52c41a';
                default: return '#333';
            }
        }
        
        // 绘制医院 - 使用API数据（已在接口层面过滤）
        function drawHospitals() {
            // 清除之前的医院标记
            hospitalMarkers.forEach(marker => marker.setMap(null));
            hospitalMarkers = [];
            
            // 检查是否有医院数据
            if (!hospitalData || hospitalData.length === 0) {
                console.log('没有医院数据可以显示');
                return;
            }

            console.log('开始绘制医院，数据量:', hospitalData.length);
            
            // 直接绘制标记（数据已在接口层面按中心过滤）
            hospitalData.forEach(hospital => {
                // 检查经纬度是否有效
                if (!hospital.longitude || !hospital.latitude) {
                    console.log('医院数据缺少有效坐标:', hospital.name);
                    return;
                }

                // 根据医院等级选择图标颜色
                let iconColor = '#52c41a'; // 默认绿色
                if (hospital.hospitalLevel) {
                    if (hospital.hospitalLevel.startsWith('1')) iconColor = '#ff4d4f'; // 一级红色
                    else if (hospital.hospitalLevel.startsWith('2')) iconColor = '#fa8c16'; // 二级橙色
                    else if (hospital.hospitalLevel.startsWith('3')) iconColor = '#52c41a'; // 三级绿色
                }

                const icon = new AMap.Icon({
                    size: new AMap.Size(30, 30),
                    image: 'data:image/svg+xml;base64,' + btoa(
                        '<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24">' +
                        `<circle cx="12" cy="12" r="11" fill="${iconColor}" stroke="#fff" stroke-width="2"/>` +
                        '<path d="M12 6v12M6 12h12" stroke="#fff" stroke-width="2.5" stroke-linecap="round"/>' +
                        '<text x="12" y="8" font-family="Arial" font-size="6" fill="#fff" text-anchor="middle">H</text>' +
                        '</svg>'
                    ),
                    imageSize: new AMap.Size(30, 30)
                });
                
                const marker = new AMap.Marker({
                    position: [hospital.longitude, hospital.latitude],
                    icon: icon,
                    title: hospital.name,
                    extData: hospital
                });
                
                // 创建医院信息窗口
                let hospitalText = '';
                hospitalText += `<font size='2' color='${iconColor}'>医院名称：</font>`;
                hospitalText += `<font size='2'>${hospital.name}</font>`;
                hospitalText += `<hr style='color:${iconColor}'>`;
                hospitalText += `<font size='2' color='${iconColor}'>所属中心：</font>`;
                hospitalText += `<font size='2'>${hospital.regionName || '未知'}</font>`;
                hospitalText += `<hr style='color:${iconColor}'>`;
                hospitalText += `<font size='2' color='${iconColor}'>医院等级：</font>`;
                hospitalText += `<font size='2'>${getHospitalLevelText(hospital.hospitalLevel)}</font>`;

                if (hospital.hospitalBeds) {
                    hospitalText += `<hr style='color:${iconColor}'>`;
                    hospitalText += `<font size='2' color='${iconColor}'>床位数量：</font>`;
                    hospitalText += `<font size='2'>${hospital.hospitalBeds}张</font>`;
                }

                if (hospital.contactNumber) {
                    hospitalText += `<hr style='color:${iconColor}'>`;
                    hospitalText += `<font size='2' color='${iconColor}'>联系电话：</font>`;
                    hospitalText += `<font size='2'>${hospital.contactNumber}</font>`;
                }

                hospitalText += `<hr style='color:${iconColor}'>`;
                hospitalText += `<font size='2' color='${iconColor}'>急诊科：</font>`;
                hospitalText += `<font size='2' color='${hospital.isErAvailable === '1' ? '#52c41a' : '#ff4d4f'}'>${hospital.isErAvailable === '1' ? '✅ 有' : '❌ 无'}</font>`;

                if (hospital.erLevel && hospital.isErAvailable === '1') {
                    hospitalText += `<hr style='color:${iconColor}'>`;
                    hospitalText += `<font size='2' color='${iconColor}'>急诊科等级：</font>`;
                    hospitalText += `<font size='2'>${getErLevelText(hospital.erLevel)}</font>`;
                }

                if (hospital.address) {
                    hospitalText += `<hr style='color:${iconColor}'>`;
                    hospitalText += `<font size='2' color='${iconColor}'>医院地址：</font>`;
                    hospitalText += `<font size='2'>${hospital.address}</font>`;
                }

                const infoContent = hospitalText;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                hospitalMarkers.push(marker);
            });

            console.log('医院绘制完成，共绘制', hospitalMarkers.length, '个医院标记');
        }

        // 获取医院等级文本
        function getHospitalLevelText(level) {
            const levelMap = {
                '11': '三级甲等', '12': '三级乙等', '13': '三级丙等',
                '21': '二级甲等', '22': '二级乙等', '23': '二级丙等',
                '31': '一级甲等', '32': '一级乙等', '33': '一级丙等',
                '40': '乡镇卫生院', '50': '社区医疗服务中心', '60': '诊所', '99': '其他'
            };
            return levelMap[level] || '未知等级';
        }

        // 获取急诊科等级文本
        function getErLevelText(level) {
            const levelMap = {
                '1': '一级急诊科', '2': '二级急诊科', '3': '三级急诊科', '4': '重点急诊科', '0': '未评级'
            };
            return levelMap[level] || '未知等级';
        }

        // 胸痛中心等级文本
        function getChestPainCenterText(isCenter, level) {
            if (isCenter !== '1') return '无';
            const levelMap = {
                '1': '标准版(国家级)', '2': '基础版(省级)', '3': '培训中', '0': '规划中'
            };
            return levelMap[level] || '未知等级';
        }

        // 卒中中心等级文本
        function getStrokeCenterText(isCenter, level) {
            if (isCenter !== '1') return '无';
            const levelMap = {
                '1': '综合卒中中心(国家级)', '2': '初级卒中中心(省级)', '3': '培训中', '0': '规划中'
            };
            return levelMap[level] || '未知等级';
        }

        // 创伤中心等级文本
        function getTraumaCenterText(isCenter, level) {
            if (isCenter !== '1') return '无';
            const levelMap = {
                '1': '一级(国家级)', '2': '二级(省级)', '3': '三级(市级)'
            };
            return levelMap[level] || '未知等级';
        }

        // 孕产妇救治中心等级文本
        function getMaternalCenterText(isCenter, level) {
            if (isCenter !== '1') return '无';
            const levelMap = {
                '1': '一级(国家级)', '2': '二级(省级)', '3': '三级(市级)'
            };
            return levelMap[level] || '未知等级';
        }

        // 新生儿救治中心等级文本
        function getNeonatalCenterText(isCenter, level) {
            if (isCenter !== '1') return '无';
            const levelMap = {
                '1': '一级(国家级)', '2': '二级(省级)', '3': '三级(市级)'
            };
            return levelMap[level] || '未知等级';
        }


        
        function drawStations() {
            // 清除之前的分站标记
            stationMarkers.forEach(marker => marker.setMap(null));
            stationMarkers = [];

            // 检查是否有分站数据
            if (!stationData || stationData.length === 0) {
                console.log('没有分站数据可以显示');
                return;
            }

            console.log('开始绘制分站，数据量:', stationData.length);

            stationData.forEach(station => {
                // 检查经纬度是否有效
                if (!station.stationLongitude || !station.stationLatitude) {
                    console.log('分站数据缺少有效坐标:', station.stationName);
                    return;
                }

                // 根据分站等级选择图标颜色
                let iconColor = '#fa8c16'; // 默认橙色
                switch(station.grade) {
                    case '1': iconColor = '#ff4d4f'; break; // 一级红色
                    case '2': iconColor = '#fa8c16'; break; // 二级橙色
                    case '3': iconColor = '#52c41a'; break; // 三级绿色
                }

                const icon = new AMap.Icon({
                    size: new AMap.Size(28, 28),
                    image: 'data:image/svg+xml;base64,' + btoa(
                        '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">' +
                        `<circle cx="12" cy="12" r="11" fill="${iconColor}" stroke="#fff" stroke-width="2"/>` +
                        '<path d="M7 12h10M7 8h10M7 16h10" stroke="#fff" stroke-width="2" stroke-linecap="round"/>' +
                        '<text x="12" y="8" font-family="Arial" font-size="6" fill="#fff" text-anchor="middle">S</text>' +
                        '</svg>'
                    ),
                    imageSize: new AMap.Size(28, 28)
                });
                
                const marker = new AMap.Marker({
                    position: [station.stationLongitude, station.stationLatitude],
                    icon: icon,
                    title: station.stationName,
                    extData: station
                });
                
                // 创建分站信息窗口
                let stationText = '';
                stationText += `<font size='2' color='${iconColor}'>分站名称：</font>`;
                stationText += `<font size='2'>${station.stationName}</font>`;
                stationText += `<hr style='color:${iconColor}'>`;
                stationText += `<font size='2' color='${iconColor}'>所属中心：</font>`;
                stationText += `<font size='2'>${station.regionName || '未知'}</font>`;
                stationText += `<hr style='color:${iconColor}'>`;
                stationText += `<font size='2' color='${iconColor}'>分站等级：</font>`;
                stationText += `<font size='2'>${getStationGradeText(station.grade)}</font>`;
                stationText += `<hr style='color:${iconColor}'>`;
                stationText += `<font size='2' color='${iconColor}'>急救半径：</font>`;
                stationText += `<font size='2'>${station.emergencyRadius || 0}公里</font>`;
                stationText += `<hr style='color:${iconColor}'>`;
                stationText += `<font size='2' color='${iconColor}'>在线状态：</font>`;
                stationText += `<font size='2' color='${station.isLogin === '1' ? '#52c41a' : '#ff4d4f'}'>${station.isLogin === '1' ? '在线' : '离线'}</font>`;

                if (station.stationPhone) {
                    stationText += `<hr style='color:${iconColor}'>`;
                    stationText += `<font size='2' color='${iconColor}'>联系电话：</font>`;
                    stationText += `<font size='2'>${station.stationPhone}</font>`;
                }

                if (station.rescueCenterName) {
                    stationText += `<hr style='color:${iconColor}'>`;
                    stationText += `<font size='2' color='${iconColor}'>救治能力：</font>`;
                    stationText += `<font size='2'>${station.rescueCenterName}</font>`;
                }
                
                const infoContent = stationText;
                
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                marker.on('click', function() {
                    infoWindow.open(cityCountyMap, marker.getPosition());
                });
                
                cityCountyMap.add(marker);
                stationMarkers.push(marker);
            });

            console.log('分站绘制完成，共绘制', stationMarkers.length, '个分站标记');
        }

        // 获取分站等级文本
        function getStationGradeText(grade) {
            switch(grade) {
                case '1': return '一级分站';
                case '2': return '二级分站';
                case '3': return '三级分站';
                default: return '未知等级';
            }
        }
        
        // 更新状态指示器和统计信息
        function updateStatusIndicators() {
            // 使用真实数据进行统计
            let filteredVehicles = vehicleData && vehicleData.length > 0 ? vehicleData : [];
            let filteredHospitals = hospitalData && hospitalData.length > 0 ? hospitalData : [];
            let filteredStations = stationData && stationData.length > 0 ? stationData : [];
            let filteredBloodStations = bloodStationData && bloodStationData.length > 0 ? bloodStationData : [];
            let filteredCdcCenters = cdcCenterData && cdcCenterData.length > 0 ? cdcCenterData : [];
            let filteredAccidents = eventList && eventList.length > 0 ? eventList : [];

            // 根据选中的中心过滤数据
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key => 
                    key !== 'all' && centerFilterOptions[key]
                );
                
                if (selectedCenters.length > 0) {
                    // 过滤车辆数据
                    filteredVehicles = filteredVehicles.filter(vehicle => {
                        const centerField = vehicle.regionName || vehicle.center;
                        return selectedCenters.includes(centerField);
                    });
                    
                    // 过滤医院数据
                    filteredHospitals = filteredHospitals.filter(hospital => {
                        const centerField = hospital.regionName || hospital.center;
                        return selectedCenters.includes(centerField);
                    });
                    
                    // 过滤分站数据
                    filteredStations = filteredStations.filter(station => {
                        const centerField = station.regionName || station.center;
                        return selectedCenters.includes(centerField);
                    });
                    
                    // 过滤血站数据
                    filteredBloodStations = filteredBloodStations.filter(bloodStation => {
                        const centerField = bloodStation.regionName || bloodStation.center;
                        return selectedCenters.includes(centerField);
                    });
                    
                    // 过滤疾控中心数据
                    filteredCdcCenters = filteredCdcCenters.filter(cdcCenter => {
                        const centerField = cdcCenter.regionName || cdcCenter.center;
                        return selectedCenters.includes(centerField);
                    });
                    
                    // 过滤事故数据
                    filteredAccidents = filteredAccidents.filter(accident => {
                        const centerField = accident.regionName || accident.center;
                        return selectedCenters.includes(centerField);
                    });
                }
            }
            
            console.log('中心过滤后数据统计:', {
                vehicles: filteredVehicles.length,
                hospitals: filteredHospitals.length,
                stations: filteredStations.length,
                bloodStations: filteredBloodStations.length,
                cdcCenters: filteredCdcCenters.length,
                accidents: filteredAccidents.length
            });
            
            // 根据状态过滤条件进一步筛选车辆
            if (!vehicleFilterOptions.all && filteredVehicles.length > 0) {
                filteredVehicles = filteredVehicles.filter(vehicle => {
                    // 车辆当前状况，0：上班，1：暂停，2：暂停，3：下班
                    // 车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-回院、5-调度接收、6-分站派车、7-分站接收、8-离开现场
                    
                    // 只有status等于0时才判断hCondition的状态
                    if (vehicle.status === '0') {
                        if (vehicle.hCondition === '3' && vehicleFilterOptions.offduty) return true;   // 下班
                        if (vehicle.hCondition === '1' && vehicleFilterOptions.stop) return true;      // 暂停(报停)
                        if (vehicle.hCondition === '0' && vehicleFilterOptions.standby) return true;   // 上班且待命
                    } else {
                        // 状态不是0，表示处于任务状态
                        if (vehicleFilterOptions.dispatch) return true; // 任务状态(发车、抵达等)
                    }
                    return false;
                });
            }
            
            // 更新顶部统计 - 使用过滤后的数据
            document.getElementById('topVehicleCount').textContent = filteredVehicles.length;
            document.getElementById('topAccidentCount').textContent = filteredAccidents.length;
            document.getElementById('topHospitalCount').textContent = filteredHospitals.length;
            document.getElementById('topStationCount').textContent = filteredStations.length;
            
            // 更新车辆过滤数量统计
            updateVehicleFilterCounts();
            
            // 更新中心过滤数量统计
            updateCenterFilterCounts();
            
            // 更新地图上的图标显示
            if (document.getElementById('vehicles-display-btn').classList.contains('active')) {
                drawVehiclesWithStatusFilter();
            }
            if (document.getElementById('accidents-display-btn').classList.contains('active')) {
                drawAccidents();
            }
            if (document.getElementById('hospitals-display-btn').classList.contains('active')) {
                drawHospitals();
            }
            if (document.getElementById('stations-display-btn').classList.contains('active')) {
                drawStations();
            }
            if (document.getElementById('bloodstations-display-btn').classList.contains('active')) {
                drawBloodStations();
            }
            if (document.getElementById('cdc-display-btn').classList.contains('active')) {
                drawCdcCenters();
            }
        }
        
        // 任务信息面板内容
        function loadTaskContent() {
            console.log('加载任务信息内容');
            
            // 初始化任务数据表格
            setTimeout(() => {
                $('#taskGrid').datagrid({
                    onDblClickRow: function(index, row) {
                        console.log('任务表格双击事件:', index, row);
                        locateTaskOnMap(row.id, row.lat, row.lng);
                    },
                    pagination: true,
                    pageSize: 15,
                    pageList: [10, 15, 20, 30, 50]
                });

                // 刷新EasyUI组件
                $('.easyui-linkbutton').linkbutton();

                console.log('任务表格初始化完成');
            }, 100);
        }
         
         // 任务操作函数
         function filterTasks() {
             const status = $('#taskStatusFilter').combobox('getValue');
             const centerCode = $('#taskCenterFilter').combobox('getValue');
             const beginTime = $('#beginTimeFilter').datetimebox('getValue');
             const endTime = $('#endTimeFilter').datetimebox('getValue');
             
             // 准备查询参数
             const params = {
                 page: 1,
                 size: 15
             };
             
             if (status && status !== '0') {
                 params.status = status;
             }
             if (centerCode) {
                 // 使用centerCode作为regionCode查询
                 params.centerCode = centerCode;
             }
             if (beginTime) {
                 params.beginTime = beginTime;
             }
             if (endTime) {
                 params.endTime = endTime;
             }
             
             // 调用接口获取数据
             loadTaskData(params);
         }
         
         // 移除重置和刷新功能
         
         function exportTasks() {
             $.messager.alert('导出任务', '正在导出任务数据为Excel文件...<br><br>包含内容:<br>• 任务基本信息<br>• 车辆派遣详情<br>• 执行时间统计', 'info');
         }
         
         // 加载任务数据
         function loadTaskData(params) {
             // 显示加载中提示
             $('#taskGrid').datagrid('loading');
             
             // 调用ajax.js中提供的getCityLinkageEventList接口获取数据
             getCityLinkageEventList(params, function(data) {
                 // 请求成功
                 if (data) {
                     const gridData = {
                         total: data.total || 0,
                         rows: data.records || []
                     };
                     
                     try {
                         // 将数据加载到表格
                         $('#taskGrid').datagrid('loadData', gridData);
                         
                         // 更新分页信息
                         $('#taskGrid').datagrid('getPager').pagination({
                             total: gridData.total,
                             pageNumber: data.current || 1
                         });
                         
                     } catch (e) {
                         console.error('加载数据到表格时出错:', e);
                     }
                } else {
                     console.warn('接口返回的数据为空或无效');
                     $('#taskGrid').datagrid('loadData', {total: 0, rows: []});
                }
                
                // 隐藏加载中提示
                $('#taskGrid').datagrid('loaded');
             }, function(error) {
                 // 请求失败的回调
                 console.error('接口调用失败:', error);
                 $('#taskGrid').datagrid('loaded');
                 $('#taskGrid').datagrid('loadData', {total: 0, rows: []});
             });
         }
         
         // 格式化日期时间
         function formatDateTime(val, row) {
             if (!val) return '';
             
             try {
                 // 假设后端返回的是ISO格式的日期字符串
                 const date = new Date(val);
                 const year = date.getFullYear();
                 const month = String(date.getMonth() + 1).padStart(2, '0');
                 const day = String(date.getDate()).padStart(2, '0');
                 const hours = String(date.getHours()).padStart(2, '0');
                 const minutes = String(date.getMinutes()).padStart(2, '0');
                 const seconds = String(date.getSeconds()).padStart(2, '0');
                 
                 return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
             } catch (e) {
                 return val; // 如果解析出错，直接返回原值
             }
         }
         
                 // 格式化性别字段 - 使用字典数据
        function formatGender(val, row) {
            // 优先使用全局字典数据
            if (globalPatientGenderData && Array.isArray(globalPatientGenderData)) {
                const genderItem = globalPatientGenderData.find(item => item.codeName === String(val));
                if (genderItem) {
                    return genderItem.codeVale;
                }
            }
            
            // 备用显示逻辑
            if (val === '0') return '男';
            if (val === '1') return '女';
            return '未知';
        }
        
        // 格式化患者籍贯 - 使用字典数据
        function formatPatientCountry(val, row) {
            if (!val) return '';
            
            // 使用全局字典数据
            if (globalCountryData && Array.isArray(globalCountryData)) {
                const countryItem = globalCountryData.find(item => item.codeName === String(val));
                if (countryItem) {
                    return countryItem.codeVale;
                }
            }
            
            // 如果找不到对应的字典项，返回原值
            return val;
        }
        
        // 格式化患者民族 - 使用字典数据
        function formatPatientRace(val, row) {
            if (!val) return '';
            
            // 使用全局字典数据
            if (globalRaceData && Array.isArray(globalRaceData)) {
                const raceItem = globalRaceData.find(item => item.codeName === String(val));
                if (raceItem) {
                    return raceItem.codeVale;
                }
            }
            
            // 如果找不到对应的字典项，返回原值
            return val;
        }
         
         // 格式化状态
        function formatStatus(val, row) {
            if (!val) return '';
            
            const statusMap = {
                '0': '<span style="color:#888;">全部</span>',
                '7': '<span style="color:#2db7f5;">未调度-预约</span>',
                '1': '<span style="color:#faad14;">未调度-落单</span>',
                '6': '<span style="color:#f5222d;">未调度-待派</span>',
                '2': '<span style="color:#1890ff;">已调度</span>',
                '3': '<span style="color:#52c41a;">已完成</span>',
                '4': '<span style="color:#fa541c;">已撤销</span>'
            };
            
            return statusMap[val] || val;
        }
        
        // 格式化事件名称 (address+patientName+majorCall，如果某一个没有值那么就是未知)
        function formatEventName(val, row) {
            const address = row.address || '未知';
            const patientName = row.patientName || '未知';
            const majorCall = row.majorCall || '未知';
            
            return `${address}+${patientName}+${majorCall}`;
        }
        
        // 格式化事件类型 (0-普通事件 1-重大事件)
        function formatEventType(val, row) {
            if (val === '0' || val === 0) {
                return '<span style="color:#52c41a;">普通事件</span>';
            } else if (val === '1' || val === 1) {
                return '<span style="color:#f5222d;">重大事件</span>';
            }
            return val || '';
        }

         
         // 格式化操作按钮
         function formatActions(val, row) {
            debugger;
             return '<a href="javascript:void(0)" onclick="viewTaskDetail(\'' + row.id + '\', \'' + row.regionCode + '\')">详情</a> | ' +
                    '<a href="javascript:void(0)" onclick="locateTaskOnMap(\'' + row + '\', \'' + row.lat+ '\', \'' + row.lng +'\')">定位</a>';
         }
         
         // 查看任务详情
         function viewTaskDetail(eventId, regionCode) {
             console.log('查看任务详情:', eventId, 'regionCode:', regionCode);

             if (!regionCode) {
                 $.messager.alert('提示', '缺少regionCode参数，无法打开详情页面', 'warning');
                 return;
             }

             // 通过regionCode在全局变量中查找对应的eventDetailUrl
             let eventDetailUrl = null;
             if (globalRegionConfigData && Array.isArray(globalRegionConfigData)) {
                 const regionConfig = globalRegionConfigData.find(config =>
                     config.regionCode === regionCode || config.codeName === regionCode
                 );
                 if (regionConfig && regionConfig.eventDetailUrl) {
                     eventDetailUrl = regionConfig.eventDetailUrl;
                 }
             }

             if (!eventDetailUrl) {
                 $.messager.alert('提示', '未找到该区域的事件详情页面配置', 'warning');
                 console.error('未找到regionCode对应的eventDetailUrl:', regionCode);
                 return;
             }

             // 构建完整的URL，包含任务ID参数
             const fullUrl = eventDetailUrl + (eventDetailUrl.includes('?') ? '&' : '?') + 'eventId=' + eventId;

             // 使用EasyUI window包含iframe显示详情页面
             $('<div></div>').window({
                 title: '任务详情 - ' + eventId,
                 width: 1000,
                 height: 700,
                 modal: true,
                 resizable: true,
                 maximizable: true,
                 content: '<iframe src="' + fullUrl + '" frameborder="0" style="width:100%;height:100%;"></iframe>',
                 onClose: function() {
                     $(this).window('destroy');
                 }
             });

             console.log('打开任务详情页面:', fullUrl);
         }
         
         // 初始化任务面板
         function initTaskPanel() {
             // 确保所有EasyUI组件都已经解析完成
             $.parser.parse($('#taskPanel'));
             
             // 延迟初始化，确保EasyUI组件完全渲染
             setTimeout(function() {
             // 初始化任务状态下拉框
             initTaskStatusFilter();
             
                 // 延迟初始化任务中心下拉框，确保状态下拉框已完成
                 setTimeout(function() {
             initTaskCenterFilter();
                 }, 200);
                 
                 // 初始化日期时间控件
                 $('#beginTimeFilter').datetimebox({
                     showSeconds: true,
                     timeFormat: 'HH:mm:ss',
                     value: ''
                 });
                 $('#endTimeFilter').datetimebox({
                     showSeconds: true,
                     timeFormat: 'HH:mm:ss',
                     value: ''
                 });
             
             // 配置表格分页
             $('#taskGrid').datagrid({
                 pagination: true,
                 pageSize: 15,
                 pageList: [10, 15, 20, 30, 50]
             });
             
                 // 设置默认状态为"已调度"(值为2)
                 setTimeout(function() {
                     $('#taskStatusFilter').combobox('setValue', '2');
                 }, 300);
                 
                 // 最后加载数据
             setTimeout(function() {
                 // 初始加载数据（默认查询已调度的任务）
                 loadTaskData({page: 1, size: 15, status: '2'});
                 }, 500);
                 
             }, 100);
         }
         
         // 初始化任务状态下拉框
         function initTaskStatusFilter() {
             try {
             const statusFilter = $('#taskStatusFilter');
                 
                 // 确保combobox已经完全初始化
                 if (!statusFilter.hasClass('combobox-f')) {
                     setTimeout(initTaskStatusFilter, 100);
                     return;
                 }

             // 准备状态下拉框数据
             const statusOptions = [
                 {text: '全部', value: '0'},
                 {text: '未调度-预约', value: '7'},
                 {text: '未调度-落单', value: '1'},
                 {text: '未调度-待派', value: '6'},
                 {text: '已调度', value: '2'},
                 {text: '已完成', value: '3'},
                 {text: '已撤销', value: '4'}
             ];

                 // 清空现有数据并加载新数据
                 statusFilter.combobox('clear');
             statusFilter.combobox('loadData', statusOptions);

             } catch (error) {
                 console.error('初始化任务状态下拉框失败:', error);
                 setTimeout(initTaskStatusFilter, 100);
             }
         }
         
         // 初始化任务中心下拉框
         function initTaskCenterFilter() {
            try {
                 const centerFilter = $('#taskCenterFilter');

                 // 准备下拉框数据
                 const centerOptions = [{text: '全部中心', value: ''}];
                 globalCenterData.forEach(center => {
                     if (center.codeName && center.codeVale) {
                         centerOptions.push({
                             text: center.codeVale,
                             value: center.codeName
                         });
                     }
                 });

                // 清空现有数据并加载新数据
                centerFilter.combobox('clear');
                centerFilter.combobox('loadData', centerOptions);
             
                // 设置默认值
                centerFilter.combobox('setValue', '');
                
                // 刷新组件显示
                centerFilter.combobox('reload');
                
                // 验证数据是否加载成功
                setTimeout(() => {
                    const loadedData = centerFilter.combobox('getData');
                    if (!loadedData || loadedData.length === 0) {
                        // 备用方法：直接操作DOM
                        useBackupMethodForCenterFilter(centerOptions);
                    }
                }, 200);
                
            } catch (error) {
                console.error('初始化任务中心下拉框失败:', error);
                // 发生错误时，至少保证下拉框可以使用
                setTimeout(() => {
                    $('#taskCenterFilter').combobox('loadData', [{text: '全部中心', value: ''}]);
                    $('#taskCenterFilter').combobox('setValue', '');
                }, 100);
            }
        }
        
        // 备用方法：直接操作DOM来填充中心下拉框
        function useBackupMethodForCenterFilter(centerOptions) {
            try {
                const centerFilter = $('#taskCenterFilter');
                
                // 方法1：重新创建combobox
                centerFilter.combobox('destroy');
                
                // 清空HTML选项并添加新选项
                centerFilter.empty();
                centerOptions.forEach(option => {
                    centerFilter.append(`<option value="${option.value}">${option.text}</option>`);
                });
                
                // 重新初始化combobox
                centerFilter.combobox({
                    panelHeight: 'auto',
                    editable: false,
                    readonly: false
                });
                
                // 设置默认值
                centerFilter.combobox('setValue', '');
                // 再次验证
                setTimeout(() => {
                    const finalData = centerFilter.combobox('getData');
                    if (!finalData || finalData.length === 0) {
                        console.error('所有方法都失败了，使用最简单的HTML方法');
                        // 最后的备用方案：纯HTML
                        const htmlOptions = centerOptions.map(option => 
                            `<option value="${option.value}">${option.text}</option>`
                        ).join('');
                        centerFilter.html(htmlOptions);
                    }
                }, 100);
                
            } catch (error) {
                console.error('备用方法也失败:', error);
            }
         }
         
         // 区域联动派车功能
         function createRegionalTask() {
             const content = `
                 <div style="padding: 20px;">
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">任务类型:</label>
                         <select id="newTaskType" style="width: 200px;">
                             <option value="区域联动">区域联动</option>
                             <option value="交通事故">交通事故</option>
                             <option value="工业事故">工业事故</option>
                             <option value="突发事件">突发事件</option>
                             <option value="医疗转运">医疗转运</option>
                         </select>
                     </div>
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">紧急程度:</label>
                         <select id="newTaskUrgency" style="width: 200px;">
                             <option value="A级">A级 - 最高紧急</option>
                             <option value="B级">B级 - 高危</option>
                             <option value="C级" selected>C级 - 紧急</option>
                             <option value="D级">D级 - 较紧急</option>
                             <option value="E级">E级 - 急诊</option>
                         </select>
                     </div>
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">事发区域:</label>
                         <select id="newTaskArea" style="width: 200px;">
                             <option value="泸州市区">泸州市区</option>
                             <option value="叙永县">叙永县</option>
                             <option value="古蔺县">古蔺县</option>
                             <option value="合江县">合江县</option>
                             <option value="泸县">泸县</option>
                             <option value="纳溪区">纳溪区</option>
                         </select>
                     </div>
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">事发地点:</label>
                         <input type="text" id="newTaskAddress" placeholder="请输入详细地址" style="width: 300px; padding: 5px;" />
                     </div>
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">伤病员数量:</label>
                         <input type="number" id="newTaskPatients" value="1" min="0" max="50" style="width: 100px; padding: 5px;" />
                         <span style="margin-left: 10px; color: #666;">人</span>
                     </div>
                     <div style="margin-bottom: 15px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold;">需要车辆:</label>
                         <input type="number" id="newTaskVehicles" value="1" min="1" max="10" style="width: 100px; padding: 5px;" />
                         <span style="margin-left: 10px; color: #666;">辆</span>
                     </div>
                     <div style="margin-bottom: 20px;">
                         <label style="display: inline-block; width: 100px; font-weight: bold; vertical-align: top;">事件描述:</label>
                         <textarea id="newTaskDescription" placeholder="请输入事件详细描述..." style="width: 300px; height: 80px; padding: 5px; resize: vertical;"></textarea>
                     </div>
                     <div style="background: #f6f6f6; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                         <h4 style="margin: 0 0 10px 0; color: #fa8c16;">🚑 区域联动派车说明：</h4>
                         <ul style="margin: 0; padding-left: 20px; color: #666;">
                             <li>系统将自动分析最佳车辆资源配置</li>
                             <li>优先调度就近区域的救护车辆</li>
                             <li>实时协调跨区域车辆支援</li>
                             <li>建立区域间统一指挥调度</li>
                         </ul>
                     </div>
                 </div>
             `;
             
             $('<div></div>').dialog({
                 title: '🚑 区域联动派车',
                 width: 500,
                 height: 600,
                 content: content,
                 modal: true,
                 buttons: [{
                     text: '取消',
                     handler: function() {
                         $(this).dialog('close');
                     }
                 }, {
                     text: '创建任务',
                     iconCls: 'icon-ok',
                     handler: function() {
                         const dialogRef = this;
                         createNewRegionalTask(dialogRef);
                     }
                 }],
                 onOpen: function() {
                     // 设置默认值
                     const now = new Date();
                     $('#newTaskAddress').val('泸州市江阳区茜草街道');
                 }
             });
         }
         
         // 此功能需要重构为使用真实API数据
         function createNewRegionalTask(dialogRef) {
             $.messager.alert('提示', '区域联动派车功能需要使用真实API实现，该功能正在对接中', 'info');
             $(dialogRef).dialog('close');
         }
         
         function estimateTaskTime(urgency, patientCount) {
             const baseTime = {
                 'A级': 15,
                 'B级': 25,
                 'C级': 35,
                 'D级': 45,
                 'E级': 60
             };
             
             const additional = Math.max(0, (patientCount - 1) * 5);
             return (baseTime[urgency] || 30) + additional + '分钟';
         }

         
         function getStatusColor(status) {
             const colors = {
                 '待派车': '#faad14',
                 '执行中': '#1890ff',
                 '已完成': '#52c41a',
                 '已取消': '#f5222d'
             };
             return colors[status] || '#000';
         }

        // 查询模块面板内容
        function loadQueryContent() {
            const content = `
                <div style="margin-bottom: 15px;">
                    <h3>统计查询</h3>
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="date" id="queryStartDate" style="padding: 5px;">
                        <input type="date" id="queryEndDate" style="padding: 5px;">
                        <button onclick="queryStatistics()">查询统计</button>
                        <button onclick="autoRefreshStats()">自动刷新</button>
                    </div>
                </div>
                <div class="easyui-tabs" style="width:100%;height:300px">
                    <div title="派车统计" style="padding:10px">
                        <div id="dispatchStats">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div>
                                    <h4>今日派车统计</h4>
                                    <p>总派车次数: <span id="totalDispatch">15</span></p>
                                    <p>泸州市中心: <span id="luzhouDispatch">8</span></p>
                                    <p>叙永县中心: <span id="xuyongDispatch">3</span></p>
                                    <p>古蔺县中心: <span id="gulinDispatch">2</span></p>
                                    <p>合江县中心: <span id="hejiangDispatch">2</span></p>
                                </div>
                                <div>
                                    <h4>平均响应时间</h4>
                                    <p>整体平均: <span id="avgResponse">5.2分钟</span></p>
                                    <p>泸州市中心: <span id="luzhouResponse">4.8分钟</span></p>
                                    <p>叙永县中心: <span id="xuyongResponse">6.1分钟</span></p>
                                    <p>古蔺县中心: <span id="gulinResponse">5.5分钟</span></p>
                                    <p>合江县中心: <span id="hejiangResponse">5.8分钟</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div title="事故统计" style="padding:10px">
                        <div id="accidentStats">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div>
                                    <h4>事故类型统计</h4>
                                    <p>交通事故: <span id="trafficAccidents">12</span></p>
                                    <p>工伤事故: <span id="workAccidents">5</span></p>
                                    <p>突发疾病: <span id="suddenIllness">8</span></p>
                                    <p>其他事故: <span id="otherAccidents">3</span></p>
                                </div>
                                <div>
                                    <h4>严重程度分布</h4>
                                    <p>重大事故: <span id="majorAccidents">1</span></p>
                                    <p>较大事故: <span id="largeAccidents">3</span></p>
                                    <p>一般事故: <span id="normalAccidents">15</span></p>
                                    <p>轻微事故: <span id="minorAccidents">9</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('queryContent').innerHTML = content;
        }
         
         // 格式化胸痛中心
         function formatChestPainCenter(value) {
             return value === '无' ? 
                 '<span style="color:#999">无</span>' : 
                 `<span style="color:#52c41a;font-weight:bold">${value}</span>`;
         }

         // 格式化卒中中心
         function formatStrokeCenter(value) {
             return value === '无' ? 
                 '<span style="color:#999">无</span>' : 
                 `<span style="color:#1890ff;font-weight:bold">${value}</span>`;
         }

         // 格式化创伤中心
         function formatTraumaCenter(value) {
             return value === '无' ? 
                 '<span style="color:#999">无</span>' : 
                 `<span style="color:#fa8c16;font-weight:bold">${value}</span>`;
         }

         // 格式化孕产妇救治中心
         function formatMaternalCenter(value) {
             return value === '无' ? 
                 '<span style="color:#999">无</span>' : 
                 `<span style="color:#eb2f96;font-weight:bold">${value}</span>`;
         }

         // 格式化新生儿救治中心
         function formatNeonatalCenter(value) {
             return value === '无' ? 
                 '<span style="color:#999">无</span>' : 
                 `<span style="color:#722ed1;font-weight:bold">${value}</span>`;
         }
         
         function showAccidentDetailDialog(event) {
             const content = `
                 <div style="padding: 15px;">
                     <h3>${event.callTypeCodeName || '事故事件'}</h3>
                     <p><strong>事件编号:</strong> ${event.id}</p>
                     <p><strong>所属中心:</strong> ${event.regionName || '未知'}</p>
                     <p><strong>紧急程度:</strong> ${event.emergencyDegree || '未知'}</p>
                     <p><strong>当前状态:</strong> ${getStatusText(event.status)}</p>
                     <p><strong>现场地点:</strong> ${event.address || '未知'}</p>
                     <p><strong>来电时间:</strong> ${formatDateTime(event.callInTimes)}</p>
                     <p><strong>主诉信息:</strong> ${event.majorCall || '无'}</p>
                     <h4>患者信息：</h4>
                         <div style="border: 1px solid #ddd; padding: 8px; margin: 5px 0; border-radius: 4px;">
                         <p><strong>姓名:</strong> ${event.patientName || '未知'} | <strong>年龄:</strong> ${event.patientAge || '未知'} | <strong>性别:</strong> ${formatGender(event.patientGender)}</p>
                         <p><strong>人数:</strong> ${event.patientAmount || '未知'} | <strong>联系人:</strong> ${event.contacter || '未知'} | <strong>联系电话:</strong> ${event.contact || '未知'}</p>
                         </div>
                     <h4>其他信息：</h4>
                     <p><strong>呼救电话:</strong> ${event.callIn || '未知'}</p>
                     <p><strong>特殊要求:</strong> ${event.specialRequirement || '无'}</p>
                     <p><strong>120备注:</strong> ${event.centerRemark || '无'}</p>
                 </div>
             `;
             
             $('<div></div>').dialog({
                 title: '事故详情',
                 width: 500,
                 height: 400,
                 content: content,
                 modal: true,
                 buttons: [{
                     text: '关闭',
                     handler: function() {
                         $(this).dialog('close');
                     }
                 }, {
                     text: '地图定位',
                     handler: function() {
                         cityCountyMap.setZoomAndCenter(15, accident.location);
                         $(this).dialog('close');
                     }
                 }]
             });
         }

         
         function dialPhone(phone) {
             $.messager.alert('拨号', `正在拨号: ${phone}`, 'info');
             console.log('拨号:', phone);
         }
         
         function queryStatistics() {
             const startDate = document.getElementById('queryStartDate').value;
             const endDate = document.getElementById('queryEndDate').value;
             
             console.log('查询统计数据:', startDate, endDate);
             $.messager.alert('查询统计', '正在查询统计数据...', 'info');
         }
         
         function autoRefreshStats() {
             console.log('开启自动刷新统计数据');
             $.messager.alert('自动刷新', '已开启自动刷新，每30秒更新一次', 'info');
         }
         
         function viewDetail(accidentId) {
             const accident = mockAccidents.find(a => a.id === accidentId);
             if (accident) {
                 showAccidentDetailDialog(accident);
             }
         }
         
         function locateOnMap(accidentId) {
             const accident = mockAccidents.find(a => a.id === accidentId);
             if (accident) {
                 cityCountyMap.setZoomAndCenter(15, accident.location);
             }
         }

        // 确保连接状态面板存在
        function ensureConnectionPanelExists() {
            let panel = document.getElementById('connectionPanel');
            if (!panel) {
                panel = document.createElement('div');
                panel.id = 'connectionPanel';
                panel.className = 'connection-panel';
                panel.innerHTML = `
                    <div class="connection-panel-header">
                        <span>中心连接状态</span>
                        <button class="minimize-btn" onclick="toggleConnectionPanel()">&minus;</button>
                    </div>
                    <div class="connection-panel-body">
                        <div id="connectionSummary"></div>
                        <div id="statusItems"></div>
                    </div>
                `;
                document.body.appendChild(panel);
            }
        }

        // 确保中心过滤对话框存在
        function ensureCenterFilterDialogExists() {
            let overlay = document.getElementById('centerFilterOverlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.id = 'centerFilterOverlay';
                overlay.className = 'dialog-overlay';
                overlay.style.display = 'none';
                overlay.setAttribute('onclick', 'closeCenterFilterDialog()');
                document.body.appendChild(overlay);
            }

            let dialog = document.getElementById('centerFilterDialog');
            if (!dialog) {
                dialog = document.createElement('div');
                dialog.id = 'centerFilterDialog';
                dialog.className = 'filter-dialog';
                dialog.style.display = 'none';
                dialog.innerHTML = `
                    <div class="dialog-header">
                        <span>中心过滤</span>
                        <button class="close-btn" onclick="closeCenterFilterDialog()">×</button>
                    </div>
                    <div class="dialog-body"></div>
                    <div class="dialog-footer">
                        <button onclick="resetCenterFilter()">重置</button>
                        <button onclick="applyCenterFilter()" class="primary-btn">应用</button>
                    </div>
                `;
                document.body.appendChild(dialog);
            }

            // 确保对话框内容区域存在
            let dialogBody = dialog.querySelector('.dialog-body');
            if (!dialogBody) {
                dialogBody = document.createElement('div');
                dialogBody.className = 'dialog-body';
                dialog.appendChild(dialogBody);
            }
        }
        
        // 辅助函数
        function deleteMeasureLine(polyline, marker) {
             $.messager.confirm('确认删除', '确定要删除这条测距线段吗？', function(r) {
                 if (r) {
                     // 移除测距线段
                     if (polyline) {
                         polyline.setMap(null);
                         const polylineIndex = mapAnnotations.polylines.indexOf(polyline);
                         if (polylineIndex > -1) {
                             mapAnnotations.polylines.splice(polylineIndex, 1);
                         }
                     }
                     
                     // 移除距离标签
                     if (marker) {
                         marker.setMap(null);
                         const markerIndex = mapAnnotations.markers.indexOf(marker);
                         if (markerIndex > -1) {
                             mapAnnotations.markers.splice(markerIndex, 1);
                         }
                     }
                 }
             });
         }
         
         function openMarkerEditWindow(marker) {
             const currentInfo = marker.markerInfo || '';
             const position = marker.getPosition();
             const isNewMarker = currentInfo === '';
             
             // 获取地址信息
             AMap.plugin('AMap.Geocoder', function() {
                 const geocoder = new AMap.Geocoder();
                 geocoder.getAddress([position.lng, position.lat], function(status, result) {
                     let addressInfo = '获取地址失败';
                     if (status === 'complete' && result.info === 'OK') {
                         const regeocode = result.regeocode;
                         addressInfo = regeocode.formattedAddress;
                     }
                     
                     const windowTitle = isNewMarker ? '📍 新建标注' : '📍 编辑标注';
                     
                     const infoWindow = new AMap.InfoWindow({
                         content: `<div style="padding: 15px; min-width: 300px; max-width: 400px;">
                             <div style="margin-bottom: 12px; font-weight: bold; color: #333; border-bottom: 2px solid #FF4444; padding-bottom: 8px; font-size: 14px;">${windowTitle}</div>
                             
                             <div style="margin-bottom: 12px;">
                                 <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #666; font-weight: bold;">标注描述：</label>
                                 <input type="text" id="marker-edit-input" 
                                        value="${currentInfo}"
                                        placeholder="请输入标注信息" 
                                        style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; box-sizing: border-box;"
                                        autofocus>
                             </div>
                             
                             <div style="margin-bottom: 12px; background: #f8f9fa; padding: 10px; border-radius: 6px; border-left: 4px solid #FF4444;">
                                 <div style="margin-bottom: 6px;">
                                     <strong style="color: #FF4444;">🌐 坐标信息：</strong>
                                 </div>
                                 <div style="font-size: 12px; color: #666; line-height: 1.5;">
                                     <div><strong>经度：</strong> ${position.lng.toFixed(6)}</div>
                                     <div><strong>纬度：</strong> ${position.lat.toFixed(6)}</div>
                                     <div><strong>地址：</strong> ${addressInfo}</div>
                                 </div>
                             </div>
                             
                             <div style="text-align: right; border-top: 1px solid #eee; padding-top: 10px;">
                                 <button onclick="saveMarkerEdit('${marker.markerId}')" 
                                         style="padding: 8px 15px; background: #1890ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">💾 保存</button>
                                 <button onclick="deleteMarker('${marker.markerId}')" 
                                         style="padding: 8px 15px; background: #ff4d4f; color: #fff; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 6px; font-weight: bold;">🗑️ 删除</button>
                                 <button onclick="closeMarkerEdit()" 
                                         style="padding: 8px 15px; background: #f5f5f5; color: #666; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold;">❌ 取消</button>
                             </div>
                         </div>`,
                         offset: new AMap.Pixel(0, -25),
                         autoMove: false
                     });
                     
                     infoWindow.open(cityCountyMap, marker.getPosition());
                     marker.currentInfoWindow = infoWindow;
                     
                     // 设置全局当前编辑的标记
                     window.currentEditingMarker = marker;
                     
                     // 延迟让输入框获得焦点
                     setTimeout(function() {
                         const input = document.getElementById('marker-edit-input');
                         if (input) {
                             input.focus();
                             input.select();
                         }
                     }, 100);
                 });
             });
         }
         
         function closeOtherMapTools(exceptTool) {
             if (exceptTool !== 'measuring' && mapToolsState.measuring) {
                 if (mapAnnotations.ruler) {
                     mapAnnotations.ruler.close();
                     mapAnnotations.ruler = null;
                 }
                 mapToolsState.measuring = false;
                 const btn = document.getElementById('measure-btn');
                 if (btn) btn.classList.remove('active');
             }
             
             if (exceptTool !== 'marking' && mapToolsState.marking) {
                 if (mapAnnotations.mouseTool) {
                     mapAnnotations.mouseTool.close();
                     mapAnnotations.mouseTool = null;
                 }
                 mapToolsState.marking = false;
                 const btn = document.getElementById('marker-btn');
                 if (btn) btn.classList.remove('active');
             }
         }
         
         // 全局函数用于标注编辑
         window.saveMarkerEdit = function(markerId) {
             const input = document.getElementById('marker-edit-input');
             const text = input.value.trim();
             
             if (window.currentEditingMarker) {
                 const marker = window.currentEditingMarker;
                 
                 if (!text) {
                     $.messager.confirm('确认', '标注内容为空，是否删除此标注？', function(r) {
                         if (r) {
                             deleteMarkerInternal(marker);
                         }
                     });
                     return;
                 }
                 
                 marker.markerInfo = text;
                 
                 // 更新标注显示
                 marker.setLabel({
                     content: text,
                     offset: new AMap.Pixel(0, -30),
                     style: {
                         background: '#ff4d4f',
                         color: '#fff',
                         padding: '4px 8px',
                         borderRadius: '4px',
                         fontSize: '12px',
                         fontWeight: 'bold',
                         border: '1px solid #fff',
                         boxShadow: '0 2px 6px rgba(0,0,0,0.3)'
                     }
                 });
                 
                 if (marker.currentInfoWindow) {
                     marker.currentInfoWindow.close();
                 }
                 
                 console.log('标注保存成功:', text);
             }
         };
         
         window.deleteMarker = function(markerId) {
             if (window.currentEditingMarker) {
                 $.messager.confirm('确认删除', '确定要删除此标注吗？', function(r) {
                     if (r) {
                         deleteMarkerInternal(window.currentEditingMarker);
                     }
                 });
             }
         };
         
         window.closeMarkerEdit = function() {
             if (window.currentEditingMarker && window.currentEditingMarker.currentInfoWindow) {
                 window.currentEditingMarker.currentInfoWindow.close();
             }
         };
         
         function deleteMarkerInternal(marker) {
             marker.setMap(null);
             const markerIndex = mapAnnotations.markers.indexOf(marker);
             if (markerIndex > -1) {
                 mapAnnotations.markers.splice(markerIndex, 1);
             }
             if (marker.currentInfoWindow) {
                 marker.currentInfoWindow.close();
             }
             console.log('标注已删除');
         }
         
         // 显示拖拽功能提示
         function showDragTip() {
             const tipDiv = document.createElement('div');
             tipDiv.style.cssText = `
                 position: fixed;
                 top: 50%;
                 left: 50%;
                 transform: translate(-50%, -50%);
                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                 color: white;
                 padding: 20px 30px;
                 border-radius: 12px;
                 box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                 z-index: 9999;
                 font-size: 14px;
                 text-align: center;
                 backdrop-filter: blur(10px);
                 border: 1px solid rgba(255,255,255,0.2);
                 animation: tipFadeIn 0.5s ease;
             `;
             
             tipDiv.innerHTML = `
                 <div style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">
                     🎯 面板拖拽功能说明
                 </div>
                 <div style="line-height: 1.6; margin-bottom: 15px;">
                     📌 <strong>拖拽：</strong>按住面板标题栏可拖动窗口<br>
                     🔄 <strong>重置：</strong>双击标题栏回到中心位置<br>
                     ❌ <strong>关闭：</strong>点击右上角×号关闭面板
                 </div>
                 <button onclick="this.parentElement.remove()" 
                         style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); 
                                padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">
                     我知道了
                 </button>
             `;
             
             document.body.appendChild(tipDiv);
             
             // 5秒后自动消失
             setTimeout(() => {
                 if (tipDiv.parentNode) {
                     tipDiv.style.animation = 'tipFadeOut 0.5s ease';
                     setTimeout(() => tipDiv.remove(), 500);
                 }
             }, 5000);
         }
         
         // 血站相关格式化函数
         function formatBloodAction(value, row) {
             return `<a href="javascript:void(0)" onclick="locateBloodStation('${row.id}')">定位</a>`;
         }
         
         // 疾控中心相关格式化函数  
         function formatCdcAction(value, row) {
             return `<a href="javascript:void(0)" onclick="locateCdcCenter('${row.id}')">定位</a>`;
         }
         
         // 连接状态管理功能
        function initConnectionStatus() {
            // 确保连接状态面板存在
            ensureConnectionPanelExists();
            
            // 添加连接状态面板的样式
            addConnectionPanelStyles();
            
            // 获取中心通信状态
            getCommunicationStatus();
            
            console.log('连接状态监控初始化完成');
        }

        // 添加连接状态面板的样式
        function addConnectionPanelStyles() {
            // 如果样式已存在，不重复添加
            if (document.getElementById('connection-panel-styles')) return;
            
            const style = document.createElement('style');
            style.id = 'connection-panel-styles';
            style.innerHTML = `
                .connection-status-panel {
                    position: absolute;
                    top: 50px; /* 调整顶部位置，与地图资源保持水平一致 */
                    right: 10px;
                    width: 240px;
                    background: white;
                    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
                    border-radius: 4px;
                    z-index: 100;
                    max-height: 70vh;
                    overflow: hidden;
                }
                
                .connection-status-panel .panel-title {
                    padding: 8px 12px;
                    background: #f5f5f5;
                    font-weight: bold;
                    cursor: pointer;
                    border-bottom: 1px solid #e8e8e8;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .connection-status-panel .status-items {
                    max-height: calc(70vh - 40px);
                    overflow-y: auto;
                }
                
                .connection-status-panel .status-item {
                    padding: 8px 12px;
                    border-bottom: 1px solid #f0f0f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .connection-status-panel .status-dot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background-color: #bfbfbf;
                }
                
                .connection-status-panel .status-dot.online {
                    background-color: #52c41a;
                }
                
                .connection-status-panel .status-dot.offline {
                    background-color: #ff4d4f;
                }
                
                .connection-status-panel .status-dot.error {
                    background-color: #faad14;
                }
                
                .connection-status-panel .connection-summary {
                    padding: 8px 12px;
                    background: #f5f5f5;
                    border-top: 1px solid #e8e8e8;
                    display: none;
                }
                
                .connection-status-panel.collapsed .status-items {
                    display: none;
                }
            `;
            
            document.head.appendChild(style);
            console.log('已添加连接状态面板样式');
        }
        
        // 旧的函数保持向后兼容，但使用新的逻辑
        function updateConnectionStatus() {
            console.log('调用旧的updateConnectionStatus函数，转为调用getCommunicationStatus');
            getCommunicationStatus();
        }

        /**
 * 获取所有中心通讯状态
 * 调用后端接口获取所有市县联动分中心的通讯状态
 */
function getCommunicationStatus() {
    console.log('[中心状态] 开始获取中心通讯状态');
    try {
        // 如果还没有获取中心字典数据，则先获取
        if (!globalCenterData || !Array.isArray(globalCenterData) || globalCenterData.length === 0) {
            console.log('[中心状态] 中心字典数据不存在，先获取字典数据');
            queryAllDic(["push_outer_type"], function(data) {
                console.log('[中心状态] 获取字典数据完成，数据类型:', typeof data, '是否数组:', Array.isArray(data));
                console.log('[中心状态] 字典数据内容:', data);
                
                if (data && (Array.isArray(data) || typeof data === 'object')) {
                    globalCenterData = data;
                    console.log('[中心状态] 已设置全局中心数据');
                    updateCenterFilterOptions(data);
                } else {
                    console.warn('[中心状态] 获取的字典数据无效，使用备用数据');
                    // 使用备用数据
                    const backupData = [
                        { codeName: 'center1', codeVale: '市区中心' },
                        { codeName: 'center2', codeVale: '北区中心' },
                        { codeName: 'center3', codeVale: '南区中心' },
                        { codeName: 'center4', codeVale: '东区中心' },
                        { codeName: 'center5', codeVale: '西区中心' }
                    ];
                    globalCenterData = backupData;
                    updateCenterFilterOptions(backupData);
                }
                
                // 再获取通讯状态
                fetchCommunicationStatus();
            });
        } else {
            // 已有中心字典数据，直接获取通讯状态
            console.log('[中心状态] 中心字典数据已存在，数量:', globalCenterData.length);
            fetchCommunicationStatus();
        }
    } catch (error) {
        console.error('[中心状态] 获取中心通讯状态异常:', error);
    }
}

// 实际发起请求获取通讯状态的函数
function fetchCommunicationStatus() {
    console.log('[中心状态] 发起通讯状态请求');
    
    // 使用ajax.js中定义的函数获取中心通讯状态
    getAllCommunicationStatus(
        function(data) {
            console.log('[中心状态] 获取通讯状态成功, 数据类型:', typeof data, Array.isArray(data) ? 'Array' : 'Object');
            
            // 确保数据是数组形式
            if (data) {
                if (!Array.isArray(data)) {
                    console.log('[中心状态] 通讯状态数据不是数组，尝试提取数组数据');
                    
                    // 如果data是对象且有data属性，则使用data.data
                    if (data.data && Array.isArray(data.data)) {
                        centerStatusData = data.data;
                        console.log('[中心状态] 从响应中提取data属性作为状态数据，状态数量:', centerStatusData.length);
                    } else {
                        // 如果无法解析为数组，创建一个空数组
                        centerStatusData = [];
                        console.warn('[中心状态] 无法从响应中提取有效的状态数据数组');
                    }
                } else {
                    centerStatusData = data;
                    console.log('[中心状态] 获取到状态数据数组，数量:', centerStatusData.length);
                }
            } else {
                centerStatusData = [];
                console.warn('[中心状态] 获取到空的通讯状态数据');
            }

            // 渲染中心状态面板
            renderConnectionStatus();
        },
        function(e, url, errMsg) {
            console.error('[中心状态] 获取中心通讯状态失败:', errMsg);
            
            // 失败时也尝试渲染，使用之前的数据
            renderConnectionStatus();
        }
    );
}

// 定期刷新中心通讯状态 - 30秒一次
function startCenterStatusMonitor() {
    // 立即获取一次
    getCommunicationStatus();
    
    // 设置定时器每30秒获取一次
    setInterval(function() {
        getCommunicationStatus();
    }, 30000);
        }

function renderConnectionStatus() {
    console.log('[中心状态] 开始渲染中心状态面板');
    
    // 确保连接状态面板存在
    ensureConnectionPanelExists();
    
    const statusItems = document.getElementById('statusItems');
    const summary = document.getElementById('connectionSummary');
    
    if (!statusItems) {
        console.error('[中心状态] statusItems元素未找到，无法显示中心状态');
        return;
    }
    
    let onlineCount = 0;
    let offlineCount = 0;
    let errorCount = 0;
    
    // 使用备用数据确保始终能显示中心列表
    let centersToDisplay = [];
    
    // 检查是否有全局中心数据
    if (!globalCenterData || !Array.isArray(globalCenterData) || globalCenterData.length === 0) {
        console.log('[中心状态] 全局中心数据不可用，使用备用数据');
        console.log('[中心状态] globalCenterData =', globalCenterData);
        
        // 使用备用数据
        centersToDisplay = [
            { codeName: 'center1', codeVale: '市区中心' },
            { codeName: 'center2', codeVale: '北区中心' },
            { codeName: 'center3', codeVale: '南区中心' },
            { codeName: 'center4', codeVale: '东区中心' },
            { codeName: 'center5', codeVale: '西区中心' }
        ];
        
        // 更新全局变量
        globalCenterData = centersToDisplay;
        console.log('[中心状态] 已设置备用中心数据，数量:', centersToDisplay.length);
        
        // 更新过滤选项
        updateCenterFilterOptions(globalCenterData);
    } else {
        // 使用全局存储的中心数据
        centersToDisplay = globalCenterData;
        console.log('[中心状态] 使用全局中心数据，数量:', centersToDisplay.length);
    }
    
    // 使用获取到的状态数据进行渲染
    let html = '';
        
    // 使用确保可用的中心数据创建中心列表
    centersToDisplay.forEach(center => {
        // 从状态数据中查找对应的中心状态
        let centerStatus = null;
        if (centerStatusData && Array.isArray(centerStatusData)) {
            centerStatus = centerStatusData.find(
                s => s.centerCode === center.codeName || s.centerName === center.codeVale
            );
        }
            
            // 提取配置信息
            let configInfo = '';
            if (center.extendInfo) {
                try {
                    const config = JSON.parse(center.extendInfo);
                    configInfo = config.type === 'local' ? '(本地)' : '(云端)';
                } catch(e) {}
            }
            
            // 解析状态
            let statusClass = 'offline';
            let statusTitle = '离线';
            let responseTime = '';
            let lastCommTime = '';
            
            if (centerStatus) {
                if (centerStatus.status === '1') {
                    statusClass = 'online';
                    statusTitle = '在线';
                    onlineCount++;
                } else if (centerStatus.status === '2') {
                    statusClass = 'error';
                    statusTitle = '异常';
                    errorCount++;
                } else {
                    offlineCount++;
                }
                
                responseTime = centerStatus.responseTime ? '响应时间: '+centerStatus.responseTime+'ms' : '';
                lastCommTime = centerStatus.lastCommTime ? '最后通讯: '+centerStatus.lastCommTime : '';
            } else {
                offlineCount++;
            }
            
            // 获取中心显示名称
            const centerName = center.codeVale || center.name || center.codeName || '未命名中心';
            
            // 生成状态项HTML
            html += `
                <div class="status-item">
                <span class="center-name">${centerName}</span>
                    <div class="connection-status-indicator">
                    <div class="status-dot ${statusClass}" 
                         title="${statusTitle} ${configInfo} 
                         ${responseTime} 
                         ${lastCommTime}">
                    </div>
                        </div>
                    </div>
                `;
            });
            
            statusItems.innerHTML = html;
            
            // 更新收缩时的汇总信息
    if (summary) {
        let summaryHtml = `<span style="color: #52c41a;">${onlineCount}在线</span>`;
        
        if (errorCount > 0) {
            summaryHtml += ` <span style="color: #faad14;">${errorCount}异常</span>`;
        }
        
        summaryHtml += ` <span style="color: #ff4d4f;">${offlineCount}离线</span>`;
        
        summary.innerHTML = summaryHtml;
    }
            
    console.log(`连接状态更新: ${onlineCount}在线, ${errorCount}异常, ${offlineCount}离线`);
        }

        // 收缩/展开功能
        // 确保连接状态面板存在
function ensureConnectionPanelExists() {
    console.log('确保连接状态面板存在');
    
    // 检查连接状态面板是否存在
    let panel = document.getElementById('connectionStatusPanel');
    
    // 添加窗口大小变化时的位置调整
    if (!window._connectionPanelResizeListenerAdded) {
        window.addEventListener('resize', function() {
            alignConnectionPanelPosition();
        });
        window._connectionPanelResizeListenerAdded = true;
    }
    
    if (!panel) {
        console.log('连接状态面板不存在，创建面板');
        
        // 创建连接状态面板
        panel = document.createElement('div');
        panel.className = 'connection-status-panel collapsed';
        panel.id = 'connectionStatusPanel';
        panel.style.top = '50px'; // 确保与CSS样式保持一致，设置顶部位置
        
        // 创建面板标题
        const title = document.createElement('div');
        title.className = 'panel-title';
        title.onclick = toggleConnectionPanel;
        title.innerHTML = '🌐 中心状态 <span class="toggle-icon">▼</span>';
        
        // 创建状态项容器
        const statusItems = document.createElement('div');
        statusItems.className = 'status-items';
        statusItems.id = 'statusItems';
        
        // 创建连接汇总
        const summary = document.createElement('div');
        summary.className = 'connection-summary';
        summary.id = 'connectionSummary';
        summary.style.display = 'block';
        
                    // 组装面板
        panel.appendChild(title);
        panel.appendChild(statusItems);
        panel.appendChild(summary);
        
        // 设置与地图资源相同的定位位置
        alignConnectionPanelPosition();
        
        // 添加到地图容器中
        const mapContainer = document.getElementById('cityCountyMap');
        if (mapContainer) {
            mapContainer.parentElement.appendChild(panel);
            console.log('连接状态面板已创建并添加到DOM中');
        } else {
            console.error('地图容器不存在，无法添加连接状态面板');
        }
    } else {
        console.log('连接状态面板已存在');
    }
    
    return panel;
}

// 调整连接状态面板位置，与地图资源组保持一致
function alignConnectionPanelPosition() {
    const panel = document.getElementById('connectionStatusPanel');
    const mapResources = document.getElementById('map-resources-group');
    
    if (!panel || !mapResources) {
        return;
    }
    
    // 获取地图资源组的位置信息
    const mapResourcesRect = mapResources.getBoundingClientRect();
    
    // 根据地图资源组的位置设置面板位置
    const newTop = mapResourcesRect.top + window.scrollY;
    
    if (newTop > 0) {
        panel.style.top = newTop + 'px';
        console.log('连接状态面板位置已与地图资源组对齐:', newTop + 'px');
    } else {
        // 如果获取到的位置不正确，使用默认值
        panel.style.top = '50px';
    }
}

        function toggleConnectionPanel() {
    console.log('切换连接状态面板');
            const panel = document.getElementById('connectionStatusPanel');
            const summary = document.getElementById('connectionSummary');
    
    if (!panel || !summary) {
        console.error('连接状态面板或汇总元素不存在');
        return;
    }
            
            panel.classList.toggle('collapsed');
            
            if (panel.classList.contains('collapsed')) {
                summary.style.display = 'block';
            } else {
                summary.style.display = 'none';
            }
        }

        // 数据模块EasyUI实现 - 参考callList.html的easyui-datagrid结构
        function initDataModule() {
            console.log('开始初始化数据模块...');
            
            try {
                // 先初始化tabs组件（简化配置，参考second.html）
                $('#dataTabs').tabs({
                    fit: false,
                    border: false,
                    onSelect: function(title, index) {
                        console.log('切换到Tab:', title, index);
                        // tabs切换时调整datagrid尺寸
                        setTimeout(() => {
                            switch(index) {
                                case 0:
                                    $('#hospitalsGrid').datagrid('resize');
                                    break;
                                case 1:
                                    $('#bloodstationsGrid').datagrid('resize');
                                    break;
                                case 2:
                                    $('#cdcGrid').datagrid('resize');
                                    break;
                                case 3:
                                    $('#phonebookGrid').datagrid('resize');
                                    break;
                            }
                        }, 100);
                    }
                });
                
                console.log('EasyUI Tabs初始化完成');
                
                // 等待tabs渲染完成后设置数据和格式化函数
                setTimeout(() => {
                    console.log('开始设置数据表格数据和格式化...');
                    
                    // 设置各表格的数据和格式化函数
                    setupHospitalsGrid();
                    setupBloodstationsGrid(); 
                    setupCdcGrid();
                    
                    console.log('数据模块初始化完成');
                    
                }, 200);
                
            } catch (error) {
                console.error('数据模块初始化失败:', error);
            }
        }

        function setupHospitalsGrid() {
            console.log('正在设置医院表格数据，数据行数:', hospitalData.length);
            try {
                // 设置列格式化函数
                var columns = $('#hospitalsGrid').datagrid('options').columns[0];
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].field === 'hospitalBeds') {
                        columns[i].formatter = function(value) { return (value || 0) + '张'; };
                    } else if (columns[i].field === 'icuBeds') {
                        columns[i].formatter = function(value) { return (value || 0) + '张'; };
                    } else if (columns[i].field === 'isErAvailable') {
                        columns[i].formatter = function(value) { return value === '有' ? '✅ 有' : '❌ 无'; };
                    } else if (columns[i].field === 'chestPainCenter') {
                        columns[i].formatter = formatChestPainCenter;
                    } else if (columns[i].field === 'strokeCenter') {
                        columns[i].formatter = formatStrokeCenter;
                    } else if (columns[i].field === 'traumaCenter') {
                        columns[i].formatter = formatTraumaCenter;
                    } else if (columns[i].field === 'maternalCenter') {
                        columns[i].formatter = formatMaternalCenter;
                    } else if (columns[i].field === 'neonatalCenter') {
                        columns[i].formatter = formatNeonatalCenter;
                    } else if (columns[i].field === 'action') {
                        columns[i].formatter = function(value, row, index) {
                            return '<a href="javascript:void(0)" onclick="locateHospitalByIndex(' + index + ')">定位</a>';
                        };
                    }
                }

                // 添加双击事件处理
                $('#hospitalsGrid').datagrid({
                    onDblClickRow: function(index, row) {
                        console.log('医院表格双击事件:', index, row);
                        locateHospitalOnMap(row);
                    }
                });

                // 自动加载全部医院数据
                loadAllHospitals();
                console.log('医院表格已初始化，正在加载全部数据');
                console.log('医院表格数据设置成功');
            } catch (e) {
                console.error('医院表格数据设置失败:', e);
            }
        }

        function setupBloodstationsGrid() {
            console.log('正在设置血站表格数据，数据行数:', bloodStationData.length);
            try {
                // 设置列格式化函数
                var columns = $('#bloodstationsGrid').datagrid('options').columns[0];
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].field === 'action') {
                        columns[i].formatter = function(value, row, index) {
                            return '<a href="javascript:void(0)" onclick="locateBloodstationByIndex(' + index + ')">定位</a>';
                        };
                    }
                }

                // 添加双击事件处理
                $('#bloodstationsGrid').datagrid({
                    onDblClickRow: function(index, row) {
                        console.log('血站表格双击事件:', index, row);
                        locateBloodstationOnMap(row);
                    }
                });

                // 自动加载全部血站数据
                loadAllBloodStations();
                console.log('血站表格已初始化，正在加载全部数据');
                console.log('血站表格数据设置成功');
            } catch (e) {
                console.error('血站表格数据设置失败:', e);
            }
        }

        function setupCdcGrid() {
            console.log('正在设置疾控中心表格数据，数据行数:', cdcCenterData.length);
            try {
                // 设置列格式化函数
                var columns = $('#cdcGrid').datagrid('options').columns[0];
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].field === 'action') {
                        columns[i].formatter = function(value, row, index) {
                            return '<a href="javascript:void(0)" onclick="locateCdcByIndex(' + index + ')">定位</a>';
                        };
                    }
                }

                // 添加双击事件处理
                $('#cdcGrid').datagrid({
                    onDblClickRow: function(index, row) {
                        console.log('疾控中心表格双击事件:', index, row);
                        locateCdcOnMap(row);
                    }
                });

                // 自动加载全部疾控中心数据
                loadAllCdcCenters();
                console.log('疾控中心表格已初始化，正在加载全部数据');
                console.log('疾控中心表格数据设置成功');
            } catch (e) {
                console.error('疾控中心表格数据设置失败:', e);
            }
        }

    

        // 加载全部医院数据（用于初始化显示）
        function loadAllHospitals() {
            console.log('加载全部医院数据');

            // 准备接口参数 - 不传任何过滤条件，获取全部数据
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_01"
            };

            // 调用医院查询接口
            getResourceHospitalInfo(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('全部医院数据加载成功，数量:', data.length);

                    const gridData = data.map((hospital, index) => ({
                        index: index,
                        id: hospital.id,
                        name: hospital.name || '未知',
                        regionName: hospital.regionName || '未知',
                        hospitalLevel: getHospitalLevelText(hospital.hospitalLevel),
                        hospitalBeds: hospital.hospitalBeds || 0,
                        icuBeds: hospital.icuBeds || 0,
                        isErAvailable: hospital.isErAvailable === '1' ? '有' : '无',
                        erLevel: hospital.erLevel ? getErLevelText(hospital.erLevel) : '无',
                        contactNumber: hospital.contactNumber || '无',
                        linkman: hospital.linkman || '无',
                        address: hospital.address || '无',
                        chestPainCenter: getChestPainCenterText(hospital.isChestPainCenter, hospital.chestPainCenterLevel),
                        strokeCenter: getStrokeCenterText(hospital.isStrokeCenter, hospital.strokeCenterLevel),
                        traumaCenter: getTraumaCenterText(hospital.isTraumaCenter, hospital.traumaCenterLevel),
                        maternalCenter: getMaternalCenterText(hospital.isMaternalCenter, hospital.maternalCenterLevel),
                        neonatalCenter: getNeonatalCenterText(hospital.isNeonatalCenter, hospital.neonatalCenterLevel),
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                        descript: hospital.descript || '无'
                    }));

                    $('#hospitalsGrid').datagrid('loadData', gridData);
                } else {
                    console.error('全部医院数据加载失败:', data);
                    $('#hospitalsGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('全部医院数据接口调用失败:', error);
                $('#hospitalsGrid').datagrid('loadData', []);
            });
        }

        // 加载全部血站数据（用于初始化显示）
        function loadAllBloodStations() {
            console.log('加载全部血站数据');

            // 准备接口参数 - 不传任何过滤条件，获取全部数据
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_10" // 血站类型编码
            };

            // 调用血站查询接口
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('全部血站数据加载成功，数量:', data.length);

                    const gridData = data.map((station, index) => ({
                        index: index,
                        name: station.name || '未知',
                        regionName: station.regionName || '未知',
                        contactNumber: station.contactNumber || '无',
                        linkman: station.linkman || '无',
                        address: station.address || '无',
                        descript: station.descript || '无',
                        remark: station.remark || '无',
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                    }));

                    $('#bloodstationsGrid').datagrid('loadData', gridData);
                } else {
                    console.error('全部血站数据加载失败:', data);
                    $('#bloodstationsGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('全部血站数据接口调用失败:', error);
                $('#bloodstationsGrid').datagrid('loadData', []);
            });
        }

        // 加载全部疾控中心数据（用于初始化显示）
        function loadAllCdcCenters() {
            console.log('加载全部疾控中心数据');

            // 准备接口参数 - 不传任何过滤条件，获取全部数据
            const params = {
                centerCode: "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_09" // 疾控中心类型编码
            };

            // 调用疾控中心查询接口
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('全部疾控中心数据加载成功，数量:', data.length);

                    const gridData = data.map((center, index) => ({
                        index: index,
                        name: center.name || '未知',
                        regionName: center.regionName || '未知',
                        contactNumber: center.contactNumber || '无',
                        linkman: center.linkman || '无',
                        address: center.address || '无',
                        descript: center.descript || '无',
                        remark: center.remark || '无',
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                    }));

                    $('#cdcGrid').datagrid('loadData', gridData);
                } else {
                    console.error('全部疾控中心数据加载失败:', data);
                    $('#cdcGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('全部疾控中心数据接口调用失败:', error);
                $('#cdcGrid').datagrid('loadData', []);
            });
        }

        // 独立的查询功能 - 每个tab有自己的查询函数
        function searchHospitals() {
            const centerFilter = $('#hospitalCenterFilter').combobox('getValue');
            const nameFilter = $('#hospitalNameFilter').textbox('getValue');
            const levelFilter = $('#hospitalLevelFilter').combobox('getValue');

            console.log('医院查询条件:', {
                centerCode: centerFilter,
                hospitalName: nameFilter,
                hospitalLevel: levelFilter
            });

            // 准备接口参数
            const params = {
                centerCode: centerFilter || "",
                contactNumber: "",
                descript: "",
                hospitalLevel: levelFilter || "",
                hospitalName: nameFilter || "",
                linkman: "",
                name: "",
                remark: "",
                typeCode: "ZYLX_01"
            };

            // 调用医院查询接口
            getResourceHospitalInfo(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('医院查询成功，结果数量:', data.length);

                    const gridData = data.map((hospital, index) => ({
                        index: index,
                        id: hospital.id,
                        name: hospital.name || '未知',
                        regionName: hospital.regionName || '未知',
                        hospitalLevel: getHospitalLevelText(hospital.hospitalLevel),
                        hospitalBeds: hospital.hospitalBeds || 0,
                        icuBeds: hospital.icuBeds || 0,
                        isErAvailable: hospital.isErAvailable === '1' ? '有' : '无',
                        erLevel: hospital.erLevel ? getErLevelText(hospital.erLevel) : '无',
                        contactNumber: hospital.contactNumber || '无',
                        linkman: hospital.linkman || '无',
                        address: hospital.address || '无',
                        chestPainCenter: getChestPainCenterText(hospital.isChestPainCenter, hospital.chestPainCenterLevel),
                        strokeCenter: getStrokeCenterText(hospital.isStrokeCenter, hospital.strokeCenterLevel),
                        traumaCenter: getTraumaCenterText(hospital.isTraumaCenter, hospital.traumaCenterLevel),
                        maternalCenter: getMaternalCenterText(hospital.isMaternalCenter, hospital.maternalCenterLevel),
                        neonatalCenter: getNeonatalCenterText(hospital.isNeonatalCenter, hospital.neonatalCenterLevel),
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                        descript: hospital.descript || '无'
                    }));

                    $('#hospitalsGrid').datagrid('loadData', gridData);
                    console.log('医院查询完成, 结果数量:', gridData.length);
                } else {
                    console.error('医院查询失败或数据格式错误:', data);
                    $('#hospitalsGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('医院查询接口调用失败:', error);
                $('#hospitalsGrid').datagrid('loadData', []);
            });
        }

        function resetHospitals() {
            $('#hospitalCenterFilter').combobox('setValue', '');
            $('#hospitalNameFilter').textbox('setValue', '');
            $('#hospitalLevelFilter').combobox('setValue', '');
            $('#hospitalsGrid').datagrid('loadData', []);
            console.log('医院查询已重置');
        }

        function searchBloodstations() {
            const centerFilter = $('#bloodstationCenterFilter').combobox('getValue');
            const nameFilter = $('#bloodstationNameFilter').textbox('getValue');

            console.log('血站查询条件:', {
                centerCode: centerFilter,
                name: nameFilter
            });

            // 准备接口参数
            const params = {
                centerCode: centerFilter || "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: nameFilter || "",
                remark: "",
                typeCode: "ZYLX_10" // 血站类型编码
            };

            // 调用血站查询接口
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('血站查询成功，结果数量:', data.length);

                    const gridData = data.map((station, index) => ({
                        index: index,
                        name: station.name || '未知',
                        regionName: station.regionName || '未知',
                        contactNumber: station.contactNumber || '无',
                        linkman: station.linkman || '无',
                        address: station.address || '无',
                        descript: station.descript || '无',
                        remark: station.remark || '无',
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                    }));

                    $('#bloodstationsGrid').datagrid('loadData', gridData);
                    console.log('血站查询完成, 结果数量:', gridData.length);
                } else {
                    console.error('血站查询失败或数据格式错误:', data);
                    $('#bloodstationsGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('血站查询接口调用失败:', error);
                $('#bloodstationsGrid').datagrid('loadData', []);
            });
        }

        function resetBloodstations() {
            $('#bloodstationCenterFilter').combobox('setValue', '');
            $('#bloodstationNameFilter').textbox('setValue', '');
            $('#bloodstationsGrid').datagrid('loadData', []);
            console.log('血站查询已重置');
        }

        function searchCdc() {
            const centerFilter = $('#cdcCenterFilter').combobox('getValue');
            const nameFilter = $('#cdcNameFilter').textbox('getValue');

            console.log('疾控中心查询条件:', {
                centerCode: centerFilter,
                name: nameFilter
            });

            // 准备接口参数
            const params = {
                centerCode: centerFilter || "",
                contactNumber: "",
                descript: "",
                hospitalLevel: "",
                hospitalName: "",
                linkman: "",
                name: nameFilter || "",
                remark: "",
                typeCode: "ZYLX_09" // 疾控中心类型编码
            };

            // 调用疾控中心查询接口
            getResourceInfoByTypeCode(params, function(data) {
                if (data && Array.isArray(data)) {
                    console.log('疾控中心查询成功，结果数量:', data.length);

                    const gridData = data.map((center, index) => ({
                        index: index,
                        name: center.name || '未知',
                        regionName: center.regionName || '未知',
                        contactNumber: center.contactNumber || '无',
                        linkman: center.linkman || '无',
                        address: center.address || '无',
                        descript: center.descript || '无',
                        remark: center.remark || '无',
                        longitude: hospital.longitude || '',
                        latitude: hospital.latitude || '',
                    }));

                    $('#cdcGrid').datagrid('loadData', gridData);
                    console.log('疾控中心查询完成, 结果数量:', gridData.length);
                } else {
                    console.error('疾控中心查询失败或数据格式错误:', data);
                    $('#cdcGrid').datagrid('loadData', []);
                }
            }, function(error) {
                console.error('疾控中心查询接口调用失败:', error);
                $('#cdcGrid').datagrid('loadData', []);
            });
        }

        function resetCdc() {
            $('#cdcCenterFilter').combobox('setValue', '');
            $('#cdcNameFilter').textbox('setValue', '');
            $('#cdcGrid').datagrid('loadData', []);
            console.log('疾控中心查询已重置');
        }

        function searchPhonebook() {
            const nameFilter = $('#phonebookNameFilter').textbox('getValue');
            
            let filteredData = mockContacts.filter(item => {
                return (!nameFilter || 
                       item.name.indexOf(nameFilter) >= 0 || 
                       item.department.indexOf(nameFilter) >= 0 ||
                       item.position.indexOf(nameFilter) >= 0);
            });
            $('#phonebookGrid').datagrid('loadData', {total: filteredData.length, rows: filteredData});
            console.log('电话本查询完成, 结果数量:', filteredData.length);
        }

        function resetPhonebook() {
            $('#phonebookNameFilter').textbox('setValue', '');
            $('#phonebookGrid').datagrid('loadData', {total: mockContacts.length, rows: mockContacts});
            console.log('电话本查询已重置');
        }

        // 操作函数
        function editHospital(index) {
            if (hospitalData && hospitalData[index]) {
                $.messager.alert('编辑医院', `编辑医院: ${hospitalData[index].name}`, 'info');
            } else {
                $.messager.alert('错误', '医院数据不存在', 'error');
            }
        }



        // 医院索引定位函数（供操作列使用）
        function locateHospitalByIndex(index) {
            const rows = $('#hospitalsGrid').datagrid('getRows');
            if (rows && rows[index]) {
                // 需要从原始数据中获取坐标信息
                const hospitalName = rows[index].name;
                const hospital = hospitalData.find(h => h.name === hospitalName);
                if (hospital) {
                    locateHospitalOnMap(hospital);
                } else {
                    $.messager.alert('定位失败', '未找到医院数据', 'error');
                }
            } else {
                $.messager.alert('定位失败', '医院数据不存在', 'error');
            }
        }

        // 医院双击定位函数
        function locateHospitalOnMap(hospital) {
            console.log('双击定位医院:', hospital);

            if (!hospital) {
                $.messager.alert('定位失败', '医院数据不存在', 'error');
                return;
            }

            if (hospital.longitude && hospital.latitude) {
                // 关闭数据模块窗口
                $('#dataPanel').window('close');

                // 开启医院图标显示
                const hospitalBtn = document.getElementById('hospitals-display-btn');
                if (hospitalBtn && !hospitalBtn.classList.contains('active')) {
                    hospitalBtn.classList.add('active');
                    loadHospitalsData(); // 加载并显示医院数据
                    console.log('已开启医院图标显示');
                }

                // 地图定位到医院
                cityCountyMap.setZoomAndCenter(16, [hospital.longitude, hospital.latitude]);

                // 显示成功提示
                $.messager.show({
                    title: '定位成功',
                    msg: `已在地图上定位到: ${hospital.name}`,
                    timeout: 3000,
                    showType: 'slide'
                });

                // 创建临时标记突出显示医院位置
                const marker = new AMap.Marker({
                    position: [hospital.longitude, hospital.latitude],
                    icon: 'style/img/hospital_highlight.png',
                    animation: 'AMAP_ANIMATION_BOUNCE',
                    title: '医院位置: ' + hospital.name
                });

                cityCountyMap.add(marker);

                // 5秒后移除临时标记
                setTimeout(() => {
                    cityCountyMap.remove(marker);
                }, 5000);

                console.log('医院定位成功:', hospital.name, hospital.longitude, hospital.latitude);
            } else {
                $.messager.alert('定位失败', '该医院缺少坐标信息，无法在地图上定位', 'warning');
                console.error('医院缺少坐标信息:', hospital);
            }
        }

        function editBloodstation(index) {
            if (bloodStationData && bloodStationData[index]) {
                $.messager.alert('编辑血站', `编辑血站: ${bloodStationData[index].name}`, 'info');
            } else {
                $.messager.alert('错误', '血站数据不存在', 'error');
            }
        }

        // 血站索引定位函数（供操作列使用）
        function locateBloodstationByIndex(index) {
            const rows = $('#bloodstationsGrid').datagrid('getRows');
            if (rows && rows[index]) {
                // 需要从原始数据中获取坐标信息
                const stationName = rows[index].name;
                const bloodstation = bloodStationData.find(b => b.name === stationName);
                if (bloodstation) {
                    locateBloodstationOnMap(bloodstation);
                } else {
                    $.messager.alert('定位失败', '未找到血站数据', 'error');
                }
            } else {
                $.messager.alert('定位失败', '血站数据不存在', 'error');
            }
        }

        // 血站双击定位函数
        function locateBloodstationOnMap(bloodstation) {
            console.log('双击定位血站:', bloodstation);

            if (!bloodstation) {
                $.messager.alert('定位失败', '血站数据不存在', 'error');
                return;
            }

            if (bloodstation.longitude && bloodstation.latitude) {
                // 关闭数据模块窗口
                $('#dataPanel').window('close');

                // 开启血站图标显示
                const bloodstationBtn = document.getElementById('bloodstations-display-btn');
                if (bloodstationBtn && !bloodstationBtn.classList.contains('active')) {
                    bloodstationBtn.classList.add('active');
                    loadBloodStationsData(); // 加载并显示血站数据
                    console.log('已开启血站图标显示');
                }

                // 地图定位到血站
                cityCountyMap.setZoomAndCenter(16, [bloodstation.longitude, bloodstation.latitude]);

                // 显示成功提示
                $.messager.show({
                    title: '定位成功',
                    msg: `已在地图上定位到: ${bloodstation.name}`,
                    timeout: 3000,
                    showType: 'slide'
                });

                // 创建临时标记突出显示血站位置
                const marker = new AMap.Marker({
                    position: [bloodstation.longitude, bloodstation.latitude],
                    icon: 'style/img/bloodstation_highlight.png',
                    animation: 'AMAP_ANIMATION_BOUNCE',
                    title: '血站位置: ' + bloodstation.name
                });

                cityCountyMap.add(marker);

                // 5秒后移除临时标记
                setTimeout(() => {
                    cityCountyMap.remove(marker);
                }, 5000);

                console.log('血站定位成功:', bloodstation.name, bloodstation.longitude, bloodstation.latitude);
            } else {
                $.messager.alert('定位失败', '该血站缺少坐标信息，无法在地图上定位', 'warning');
                console.error('血站缺少坐标信息:', bloodstation);
            }
        }

        // 疾控中心索引定位函数（供操作列使用）
        function locateCdcByIndex(index) {
            const rows = $('#cdcGrid').datagrid('getRows');
            if (rows && rows[index]) {
                // 需要从原始数据中获取坐标信息
                const cdcName = rows[index].name;
                const cdcCenter = cdcCenterData.find(c => c.name === cdcName);
                if (cdcCenter) {
                    locateCdcOnMap(cdcCenter);
                } else {
                    $.messager.alert('定位失败', '未找到疾控中心数据', 'error');
                }
            } else {
                $.messager.alert('定位失败', '疾控中心数据不存在', 'error');
            }
        }

        // 疾控中心双击定位函数
        function locateCdcOnMap(cdcCenter) {
            console.log('双击定位疾控中心:', cdcCenter);

            if (!cdcCenter) {
                $.messager.alert('定位失败', '疾控中心数据不存在', 'error');
                return;
            }

            if (cdcCenter.longitude && cdcCenter.latitude) {
                // 关闭数据模块窗口
                $('#dataPanel').window('close');

                // 开启疾控中心图标显示
                const cdcBtn = document.getElementById('cdc-display-btn');
                if (cdcBtn && !cdcBtn.classList.contains('active')) {
                    cdcBtn.classList.add('active');
                    loadCdcCentersData(); // 加载并显示疾控中心数据
                    console.log('已开启疾控中心图标显示');
                }

                // 地图定位到疾控中心
                cityCountyMap.setZoomAndCenter(16, [cdcCenter.longitude, cdcCenter.latitude]);

                // 显示成功提示
                $.messager.show({
                    title: '定位成功',
                    msg: `已在地图上定位到: ${cdcCenter.name}`,
                    timeout: 3000,
                    showType: 'slide'
                });

                // 创建临时标记突出显示疾控中心位置
                const marker = new AMap.Marker({
                    position: [cdcCenter.longitude, cdcCenter.latitude],
                    icon: 'style/img/cdc_highlight.png',
                    animation: 'AMAP_ANIMATION_BOUNCE',
                    title: '疾控中心位置: ' + cdcCenter.name
                });

                cityCountyMap.add(marker);

                // 5秒后移除临时标记
                setTimeout(() => {
                    cityCountyMap.remove(marker);
                }, 5000);

                console.log('疾控中心定位成功:', cdcCenter.name, cdcCenter.longitude, cdcCenter.latitude);
            } else {
                $.messager.alert('定位失败', '该疾控中心缺少坐标信息，无法在地图上定位', 'warning');
                console.error('疾控中心缺少坐标信息:', cdcCenter);
            }
        }

        // 任务双击定位函数
        function locateTaskOnMap(eventId, lat, lng)   {

            if (lng && lat) {
                // 关闭任务信息窗口
                $('#taskPanel').window('close');

                // 开启事故任务图标显示
                const accidentBtn = document.getElementById('accidents-display-btn');
                if (accidentBtn && !accidentBtn.classList.contains('active')) {
                    accidentBtn.classList.add('active');
                    loadAccidentsData(); // 加载并显示事故任务数据
                    console.log('已开启事故任务图标显示');
                }

                // 地图定位到任务位置
                cityCountyMap.setZoomAndCenter(16, [lng, lat]);

                // 显示成功提示
                $.messager.show({
                    title: '定位成功',
                    msg: `已在地图上定位到任务: ${task.eventName || task.id}`,
                    timeout: 3000,
                    showType: 'slide'
                });

                // 创建临时标记突出显示任务位置
                const marker = new AMap.Marker({
                    position: [lng, lat],
                    icon: 'style/img/accident_highlight.png',
                    animation: 'AMAP_ANIMATION_BOUNCE',
                    title: '任务位置: ' + (task.eventName || task.id)
                });

                cityCountyMap.add(marker);

                // 5秒后移除临时标记
                setTimeout(() => {
                    cityCountyMap.remove(marker);
                }, 5000);

                console.log('任务定位成功:', task.eventName || task.id, lng, lat);
            } else {
                $.messager.alert('定位失败', '该任务缺少坐标信息，无法在地图上定位', 'warning');
                console.error('任务缺少坐标信息:', task);
            }
        }



        function editCdc(index) {
            if (cdcCenterData && cdcCenterData[index]) {
                $.messager.alert('编辑疾控中心', `编辑疾控中心: ${cdcCenterData[index].name}`, 'info');
            } else {
                $.messager.alert('错误', '疾控中心数据不存在', 'error');
            }
        }



        function editContact(index) {
            $.messager.alert('编辑联系人', `编辑联系人: ${mockContacts[index].name}`, 'info');
        }

        // 数据模块操作函数
         function filterBloodStations() {
             const centerFilter = document.getElementById('bloodCenterFilter').value;
             let filteredData = mockBloodStations;
             
             if (centerFilter) {
                 filteredData = filteredData.filter(b => b.center === centerFilter);
             }
             
             $('#bloodTable').datagrid('loadData', filteredData);
             console.log('筛选血站:', centerFilter || '所有中心');
         }
         
         function filterCdcCenters() {
             const centerFilter = document.getElementById('cdcCenterFilter').value;
             let filteredData = mockCdcCenters;
             
             if (centerFilter) {
                 filteredData = filteredData.filter(c => c.center === centerFilter);
             }
             
             $('#cdcTable').datagrid('loadData', filteredData);
             console.log('筛选疾控中心:', centerFilter || '所有中心');
         }
         
         function locateAllBloodStations() {
             console.log('在地图上显示所有血站');
             drawBloodStations();
             $.messager.alert('血站位置', '已在地图上显示所有血站位置', 'info');
         }
         
         function locateAllCdcCenters() {
             console.log('在地图上显示所有疾控中心');
             drawCdcCenters();
             $.messager.alert('疾控中心位置', '已在地图上显示所有疾控中心位置', 'info');
         }
         

         

         
         function checkBloodStock() {
             const lowStockStations = mockBloodStations.filter(b => b.stock === 'low');
             
             if (lowStockStations.length > 0) {
                 let message = '⚠️ 血库库存警报！\n\n以下血站库存偏少：\n';
                 lowStockStations.forEach(station => {
                     message += `• ${station.name} (${station.center})\n`;
                 });
                 message += '\n请及时联系补充血液库存！';
                 $.messager.alert('血站库存', message, 'warning');
             } else {
                 $.messager.alert('血站库存', '✅ 所有血站库存正常，无需补充。', 'info');
             }
             
             console.log('血库库存检查完成');
         }
         
         function contactCdcCenter() {
             const selected = $('#cdcTable').datagrid('getSelected');
             if (selected) {
                 $.messager.alert('联系疾控中心', `正在联系 ${selected.name}<br><br>专业领域: ${selected.specialties.join(', ')}<br>联系方式: 已自动拨打电话`, 'info');
                 console.log('联系疾控中心:', selected.name);
             } else {
                 $.messager.alert('提示', '请先选择一个疾控中心', 'warning');
             }
         }
         
         function exportHospitalData() {
             console.log('导出医院数据');
             $.messager.alert('导出医院数据', '正在导出医院数据为Excel文件...<br><br>包含内容:<br>• 医院基本信息<br>• 床位统计<br>• 急诊科设置情况', 'info');
         }
         
         function exportContacts() {
             console.log('导出通讯录数据');
             $.messager.alert('导出通讯录', '正在导出通讯录数据...<br><br>包含内容:<br>• 所有机构联系方式<br>• 分类信息<br>• 紧急联系人', 'info');
         }
         
         // ===========================================
         // 车辆状态过滤功能
         // ===========================================
         
         // 初始化车辆状态过滤功能
         function initVehicleFilter() {
             console.log('初始化车辆状态过滤功能...');
             updateVehicleFilterCounts();
         }
         
                 // 显示车辆状态过滤弹窗
        function showVehicleFilterDialog() {
            console.log('[车辆状态过滤] 显示车辆状态过滤弹窗');
            
            // 每次打开弹窗前强制更新状态计数 - 确保与中心过滤保持一致
            console.log('[车辆状态过滤] 打开弹窗前更新状态计数');
            updateVehicleFilterCounts();
            updateFilterCheckboxes();
            
            document.getElementById('vehicleFilterOverlay').style.display = 'block';
            document.getElementById('vehicleFilterDialog').style.display = 'block';
            
            // 再次验证DOM中的计数值
            console.log('[车辆状态过滤] DOM中的计数值 - 全部:', 
                document.getElementById('count-all').textContent,
                '待命:', document.getElementById('count-standby').textContent,
                '任务:', document.getElementById('count-dispatch').textContent,
                '报停:', document.getElementById('count-stop').textContent,
                '下班:', document.getElementById('count-offduty').textContent
            );
        }
         
         // 关闭车辆状态过滤弹窗
         function closeVehicleFilterDialog() {
             console.log('关闭车辆状态过滤弹窗');
             document.getElementById('vehicleFilterOverlay').style.display = 'none';
             document.getElementById('vehicleFilterDialog').style.display = 'none';
         }
         
         // 切换过滤选项
         function toggleFilterOption(optionType) {
             console.log('切换过滤选项:', optionType);
             
             // 如果选择"全部车辆"，取消其他选项
             if (optionType === 'all') {
                 vehicleFilterOptions.all = !vehicleFilterOptions.all;
                 if (vehicleFilterOptions.all) {
                     vehicleFilterOptions.standby = false;
                     vehicleFilterOptions.dispatch = false;
                     vehicleFilterOptions.stop = false;
                     vehicleFilterOptions.offduty = false;
                 }
             } else {
                 // 选择具体状态，取消"全部车辆"
                 vehicleFilterOptions.all = false;
                 vehicleFilterOptions[optionType] = !vehicleFilterOptions[optionType];
                 
                 // 如果没有选择任何具体状态，自动选择"全部车辆"
                 if (!vehicleFilterOptions.standby && !vehicleFilterOptions.dispatch && 
                     !vehicleFilterOptions.stop && !vehicleFilterOptions.offduty) {
                     vehicleFilterOptions.all = true;
                 }
             }
             
             updateFilterCheckboxes();
             updateFilterOptionStyles();
         }
         
         // 更新复选框状态
         function updateFilterCheckboxes() {
             document.getElementById('filter-all').checked = vehicleFilterOptions.all;
             document.getElementById('filter-standby').checked = vehicleFilterOptions.standby;
             document.getElementById('filter-dispatch').checked = vehicleFilterOptions.dispatch;
             document.getElementById('filter-stop').checked = vehicleFilterOptions.stop;
             document.getElementById('filter-offduty').checked = vehicleFilterOptions.offduty;
         }
         
         // 更新过滤选项的视觉样式
         function updateFilterOptionStyles() {
             const options = ['all', 'standby', 'dispatch', 'stop', 'offduty'];
             
             options.forEach(option => {
                 const element = document.querySelector(`[onclick="toggleFilterOption('${option}')"]`);
                 if (element) {
                     if (vehicleFilterOptions[option]) {
                         element.classList.add('selected');
                     } else {
                         element.classList.remove('selected');
                     }
                 }
             });
         }
         
        // 更新各状态车辆数量
        function updateVehicleFilterCounts() {
            console.log('[车辆状态过滤] 开始更新状态计数...');
            // 根据当前中心筛选获取车辆
            let filteredVehicles = vehicleData;
            const originalCount = filteredVehicles.length;
            
            console.log('[车辆状态过滤] 原始车辆数据量:', originalCount);
            console.log('[车辆状态过滤] 中心过滤状态:', centerFilterOptions.all ? '全部中心' : '已选择特定中心');
            
            // 记录当前选中的中心
            let selectedCenterNames = [];
            
            if (!centerFilterOptions.all) {
                const selectedCenters = Object.keys(centerFilterOptions).filter(key => 
                    key !== 'all' && centerFilterOptions[key]
                );
                
                console.log('[车辆状态过滤] 选中的中心IDs:', selectedCenters);
                
                // 提取中心显示名称用于日志
                selectedCenters.forEach(centerId => {
                    const centerName = getCenterDisplayName(centerId);
                    if (centerName) selectedCenterNames.push(centerName);
                });
                
                // 筛选前检查几个车辆的中心字段
                if (filteredVehicles.length > 0) {
                    console.log('[车辆状态过滤] 检查车辆中心字段样本:');
                    for (let i = 0; i < Math.min(3, filteredVehicles.length); i++) {
                        const vehicle = filteredVehicles[i];
                        console.log(`车辆${i+1}: 车牌=${vehicle.plateNum}, regionName=${vehicle.regionName}, center=${vehicle.center}, regionCode=${vehicle.regionCode}`);
                    }
                }
                
                // 修正的过滤逻辑，检查多种可能的中心字段格式
                filteredVehicles = filteredVehicles.filter(vehicle => {
                    // 依次检查多种可能的中心字段
                    const centerField = vehicle.regionName || vehicle.center || '';
                    const centerCode = vehicle.regionCode || '';
                    
                    // 1. 直接匹配中心名称或ID
                    if (selectedCenters.includes(centerField)) return true;
                    
                    // 2. 检查中心代码
                    if (centerCode && selectedCenters.includes(centerCode)) return true;
                    
                    // 3. 如果上面都不匹配，尝试查找中心名称是否部分包含
                    for (const selectedCenter of selectedCenters) {
                        // 获取选中中心的显示名称
                        const centerName = getCenterDisplayName(selectedCenter);
                        if (centerName && centerField && 
                            (centerField.includes(centerName) || centerName.includes(centerField))) {
                            return true;
                        }
                    }
                    
                    return false;
                });
                
                console.log('[车辆状态过滤] 中心筛选后车辆数量:', filteredVehicles.length);
                console.log('[车辆状态过滤] 选中的中心名称:', selectedCenterNames.join(', ') || '无法获取中心名称');
            }
            
            const counts = {
                all: filteredVehicles.length,
                standby: 0,
                dispatch: 0,
                stop: 0,
                offduty: 0
            };
            
            // 车辆当前状况，0：上班，1：暂停，2：暂停，3：下班
            // 车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-回院、5-调度接收、6-分站派车、7-分站接收、8-离开现场
            
            // 各状态计数器
            let standbyCount = 0;
            let dispatchCount = 0; 
            let stopCount = 0;
            let offdutyCount = 0;
            
            filteredVehicles.forEach(vehicle => {
                const statusCode = vehicle.status || vehicle.statusCode || '0';
                
                // 只有status等于0时才判断hCondition的状态
                if (statusCode === '0') {
                    if (vehicle.hCondition === '3') {
                        counts.offduty++;  // 下班
                        offdutyCount++;
                    } else if (vehicle.hCondition === '1' || vehicle.hCondition === '2') {
                        counts.stop++;     // 暂停(报停)
                        stopCount++;
                    } else if (vehicle.hCondition === '0') {
                        counts.standby++;  // 上班且待命
                        standbyCount++;
                    }
                } else {
                    // 状态不是0，表示处于任务状态
                    counts.dispatch++;     // 任务状态
                    dispatchCount++;
                }
            });
            
            console.log('[车辆状态过滤] 状态明细计数 - 待命:', standbyCount, '任务:', dispatchCount, 
                        '报停:', stopCount, '下班:', offdutyCount, '总计:', counts.all);
             
            // 更新界面显示
            document.getElementById('count-all').textContent = counts.all;
            document.getElementById('count-standby').textContent = counts.standby;
            document.getElementById('count-dispatch').textContent = counts.dispatch;
            document.getElementById('count-stop').textContent = counts.stop;
            document.getElementById('count-offduty').textContent = counts.offduty;
            
            console.log('[车辆状态过滤] 状态计数已更新');
        }
         
         // 重置车辆过滤条件
         function resetVehicleFilter() {
             console.log('重置车辆过滤条件');
             vehicleFilterOptions = {
                 all: true,
                 standby: false,
                 dispatch: false,
                 stop: false,
                 offduty: false
             };
             vehicleFilterText = '全部车辆';
             updateFilterCheckboxes();
             updateFilterOptionStyles();
         }
         
         // 应用车辆过滤条件
                 function applyVehicleFilter() {
            console.log('应用车辆过滤条件:', vehicleFilterOptions);
            
            // 更新按钮文字
            updateVehicleFilterButtonText();
            
            // 不管车辆显示是否开启，都重新加载车辆数据并应用过滤
            loadVehiclesData();
            
            // 更新状态指示器 (这会重新绘制车辆)
            updateStatusIndicators();
            
            // 关闭弹窗
            closeVehicleFilterDialog();
            
            console.log('车辆过滤应用完成');
        }
         
         // 更新车辆过滤按钮文字
         function updateVehicleFilterButtonText() {
             const btn = document.getElementById('vehicleFilterBtn');
             if (!btn) return;
             
             let filterText = '';
             let activeFilters = [];
             
             if (vehicleFilterOptions.all) {
                 filterText = '全部车辆';
             } else {
                 if (vehicleFilterOptions.standby) activeFilters.push('待命');
                 if (vehicleFilterOptions.dispatch) activeFilters.push('任务');
                 if (vehicleFilterOptions.stop) activeFilters.push('暂停');
                 if (vehicleFilterOptions.offduty) activeFilters.push('下班');
                 
                 if (activeFilters.length > 0) {
                     filterText = activeFilters.join('+');
                     if (filterText.length > 8) {
                         filterText = activeFilters.length + '个状态';
                     }
                 } else {
                     filterText = '全部车辆';
                 }
             }
             
             vehicleFilterText = filterText;
             btn.innerHTML = `<i class="fa fa-filter"></i> ${filterText}`;
             
             // 更新按钮样式
             if (vehicleFilterOptions.all) {
                 btn.classList.remove('active');
             } else {
                 btn.classList.add('active');
             }
             
             console.log('按钮文字更新为:', filterText);
         }
         
         // 中心过滤对话框
         function showCenterFilterDialog() {
            // 显示对话框之前更新中心状态
            Object.keys(centerFilterOptions).forEach(key => {
                if (key !== 'all') {
                    const checkbox = document.getElementById('center-filter-' + key);
                    if (checkbox) {
                        checkbox.checked = centerFilterOptions[key];
                    }
                }
            });
            
            // 显示对话框
             document.getElementById('centerFilterOverlay').style.display = 'block';
             document.getElementById('centerFilterDialog').style.display = 'block';
         }
         
         // 关闭中心过滤对话框
         function closeCenterFilterDialog() {
             document.getElementById('centerFilterOverlay').style.display = 'none';
             document.getElementById('centerFilterDialog').style.display = 'none';
         }
        
        // 已移除showCenterConfigInfo函数
         
         // 更新中心过滤按钮文本
         function updateCenterFilterButtonText() {
             const btn = document.getElementById('centerFilterBtn');
             
             if (centerFilterOptions.all) {
                 btn.innerHTML = '<i class="fa fa-filter"></i> 全部中心';
                 btn.classList.remove('active');
             } else {
                 const activeCenters = [];
                 Object.keys(centerFilterOptions).forEach(key => {
                     if (key !== 'all' && centerFilterOptions[key]) {
                         activeCenters.push(key);
                     }
                 });
                 
                 if (activeCenters.length === 1) {
                 // 如果只选择了一个中心，显示该中心名称
                     const centerName = getCenterDisplayName(activeCenters[0]);
                     btn.innerHTML = '<i class="fa fa-filter"></i> ' + centerName;
             } else {
                 // 如果选择了多个中心，显示数量
                     btn.innerHTML = '<i class="fa fa-filter"></i> ' + activeCenters.length + '个中心';
                 }
                 btn.classList.add('active');
             }
             
             // 更新顶部中心数量显示
             const totalCenters = Object.keys(centerFilterOptions).length - 1; // 减去'all'
             $('#topCenterCount').text(centerFilterOptions.all ? totalCenters : Object.keys(centerFilterOptions).filter(key => key !== 'all' && centerFilterOptions[key]).length);
         }
         
         // 获取中心显示名称
         function getCenterDisplayName(centerKey) {
            // 从全局中心数据中查找对应的显示名称
            if (globalCenterData) {
                const center = globalCenterData.find(c => c.codeName === centerKey || c.codeVale === centerKey);
                if (center) {
                    return center.codeVale;
                }
            }
            return centerKey;
         }
        

         
         // 根据状态过滤条件绘制车辆
         function drawVehiclesWithStatusFilter() {
             console.log('根据状态过滤条件绘制车辆...');

             // 清除现有车辆标记
             vehicleMarkers.forEach(marker => {
                 if (marker && marker.setMap) {
                     marker.setMap(null);
                 }
             });
             vehicleMarkers = [];
             vehicleMarkersMap.clear(); // 清空标记映射
             
             // 车辆数据已在接口层面按中心过滤，直接使用
             let filteredVehicles = vehicleData;
             console.log('使用车辆数据量:', filteredVehicles.length);
             
                                 // 根据状态过滤条件进一步筛选
                    if (!vehicleFilterOptions.all) {
                        filteredVehicles = filteredVehicles.filter(vehicle => {
                            // 车辆当前状况，0：上班，1：暂停，2：暂停，3：下班
                            // 车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-回院、5-调度接收、6-分站派车、7-分站接收、8-离开现场
                            
                            // 只有status等于0时才判断hCondition的状态
                            if (vehicle.status === '0') {
                                if (vehicle.hCondition === '3' && vehicleFilterOptions.offduty) return true;   // 下班
                                if (vehicle.hCondition === '1' && vehicleFilterOptions.stop) return true;      // 暂停(报停)
                                if (vehicle.hCondition === '0' && vehicleFilterOptions.standby) return true;   // 上班且待命
                            } else {
                                // 状态不是0，表示处于任务状态
                                if (vehicleFilterOptions.dispatch) return true; // 任务状态(发车、抵达等)
                            }
                            return false;
                        });
                    }
             
             // 绘制筛选后的车辆
             filteredVehicles.forEach(car => {
                 // 如果经纬度为0，不绘制车辆
                 if (car.lng == 0 && car.lat == 0) return;
                 
                                 // 根据车辆状态选择图标
                let myIcon = null;
                // 车辆当前状况，0：上班，1：暂停，2：暂停，3：下班
                // 车辆状态，0：待命，1：发车，2：抵达，3：接诊取消、4-回院、5-调度接收、6-分站派车、7-分站接收、8-离开现场
                
                if (car.status == "0") {
                    // status为0时，根据hCondition判断状态
                    if (car.hCondition == "0") {
                        // 上班且待命
                        // 未出车超过1公里，那么车辆变成黄色
                        var distance = getDistance(car.lat, car.lng, car.stationLatitude, car.stationLongitude);
                        if (distance > 1000) {
                            myIcon = carIcons.yellow;
                        } else {
                            // 待命图标
                            myIcon = carIcons.default;
                        }
                    } else if (car.hCondition == "1" || car.hCondition == "2") {
                        // 暂停(报停)
                        myIcon = carIcons.blue;
                    } else if (car.hCondition == "3") {
                        // 下班
                        myIcon = carIcons.blue;
                    }
                } else {
                    // status不为0，表示处于任务状态（发车、抵达等）
                    myIcon = carIcons.red;
                }
                 
                 const marker = new AMap.Marker({
                     position: [car.lng, car.lat],
                     icon: myIcon,
                     title: car.plateNum,
                     extData: car
                 });
                 
                 // 设置车辆角度
                 if (car.direct) {
                     marker.setAngle(car.direct);
                 }
                 
                 // 创建信息窗口内容
                 var carStatus = "";
                 if (car.hCondition != "0") {
                     switch (car.hCondition) {
                         case "1":
                             carStatus = "报停中";
                             break;
                         case "3":
                             carStatus = "未值班";
                             break;
                     }
                 } else {
                     // 根据status获取状态描述
                     carStatus = getVehicleStatusText(car.status);
                 }
                 
                 // 构建车辆信息内容
                 let text = '';
                 
                 // 分站信息
                 text += `<font size='2' color='#0066FF'>分站：</font>`;
                 text += `<font size='2'>${car.stationName || '未知'}</font>`;
                 text += `<hr style='color:#0066FF'>`;
                 
                 // 中心信息
                 text += `<font size='2' color='#0066FF'>中心：</font>`;
                 text += `<font size='2'>${car.regionName || '未知'}</font>`;
                 text += `<hr style='color:#0066FF'>`;
                 
                 // 车牌信息  
                 text += `<font size='2' color='#0066FF'>车牌：</font>`;
                 text += `<font size='2'>${car.plateNum || '未知'}</font>`;
                 text += `<hr style='color:#0066FF'>`;
                 
                 // 速度信息
                 text += `<font size='2' color='#0066FF'>速度：</font>`;
                 text += `${Math.round((car.speed || 0) * 100) / 100}`;
                 text += `<font size='2'>km/h</font>`;
                 text += `<hr style='color:#0066FF'>`;
                 
                 // 状态信息
                 text += `<font size='2' color='#0066FF'>状态：</font>`;
                 text += `<font size='2'>${carStatus}</font>`;
                 text += `<hr style='color:#0066FF'>`;
                 
                 // ACC状态
                 text += `<font size='2' color='#0066FF'>发动机：</font>`;
                 text += `<font size='2'>${car.acc == '0' ? '熄火' : car.acc == '1' ? '运行中' : '未知'}</font>`;
                 
                 // 位置更新时间
                 if (car.positionUpdateTime) {
                     text += `<hr style='color:#0066FF'>`;
                     text += `<font size='2' color='#0066FF'>位置更新：</font>`;
                     text += `<font size='2'>${formatDateTime(car.positionUpdateTime)}</font>`;
                 }
                 
                 // 联系电话
                 if (car.contact) {
                     text += `<hr style='color:#0066FF'>`;
                     text += `<font size='2' color='#0066FF'>联系电话：</font>`;
                     text += `<font size='2'>${car.contact}</font>`;
                 }
                 
                 const infoContent = text;
                 
                 const infoWindow = new AMap.InfoWindow({
                     content: infoContent,
                     offset: new AMap.Pixel(0, -30),
                     autoMove: false
                 });
                 
                 marker.on('click', function() {
                     infoWindow.open(cityCountyMap, marker.getPosition());
                 });
                 
                 cityCountyMap.add(marker);
                 vehicleMarkers.push(marker);

                 // 添加到标记映射中，用于智能刷新
                 const vehicleId = car.plateNum || car.id;
                 if (vehicleId) {
                     vehicleMarkersMap.set(vehicleId, marker);
                 }
             });

             console.log('状态过滤后车辆绘制完成，共绘制', vehicleMarkers.length, '辆车');
         }
         
         // 根据状态码获取车辆状态文本描述
         function getVehicleStatusText(status) {
             if (!status && status !== 0) return '未知';
             
             switch (status) {
                 case '0': return '待命';
                 case '1': return '发车';
                 case '2': return '抵达';
                 case '3': return '接诊取消';
                 case '4': return '回院';
                 case '5': return '调度接收';
                 case '6': return '分站派车';
                 case '7': return '分站接收';
                 case '8': return '离开现场';
                 default: return '未知状态';
             }
         }
         
         // 中心过滤相关函数
         
         // 切换中心过滤选项
         function toggleCenterFilterOption(option) {
             const checkbox = document.getElementById('center-filter-' + option);
             
             if (option === 'all') {
                 // 如果选择"全部"，取消其他所有选项
                 Object.keys(centerFilterOptions).forEach(key => {
                     if (key !== 'all') {
                         centerFilterOptions[key] = false;
                         document.getElementById('center-filter-' + key).checked = false;
                     }
                 });
                 centerFilterOptions.all = true;
                 checkbox.checked = true;
             } else {
                 // 如果选择具体中心，取消"全部"选项
                 centerFilterOptions.all = false;
                 document.getElementById('center-filter-all').checked = false;
                 
                 // 切换当前选项
                 centerFilterOptions[option] = !centerFilterOptions[option];
                 checkbox.checked = centerFilterOptions[option];
                 
                 // 如果没有选择任何中心，自动选择"全部"
                 const hasSelectedCenter = Object.keys(centerFilterOptions).some(key => 
                     key !== 'all' && centerFilterOptions[key]
                 );
                 
                 if (!hasSelectedCenter) {
                     centerFilterOptions.all = true;
                     document.getElementById('center-filter-all').checked = true;
                 }
             }
         }
         
         // 重置中心过滤
         function resetCenterFilter() {
             Object.keys(centerFilterOptions).forEach(key => {
                 centerFilterOptions[key] = key === 'all';
                 const checkbox = document.getElementById('center-filter-' + key);
                 if (checkbox) {
                     checkbox.checked = key === 'all';
                 }
             });
         }
         
                 // 应用中心过滤
        function applyCenterFilter() {
           // 记录选中的中心
           const selectedCenters = Object.keys(centerFilterOptions).filter(key => 
               key !== 'all' && centerFilterOptions[key]
           );
           // 更新按钮文本
           updateCenterFilterButtonText();
           
           // 重新加载各种数据，应用中心过滤
           loadVehiclesData();      // 重新加载车辆数据
           loadAccidentsData();     // 重新加载事故数据
           loadHospitalsData();     // 重新加载医院数据
           loadStationsData();      // 重新加载分站数据
           loadBloodStationsData(); // 重新加载血站数据
           loadCdcCentersData();    // 重新加载疾控中心数据
           
                       // 确保车辆数据加载完成后再更新计数
            setTimeout(function() {

                // 检查vehicleData是否已加载完成
                if (vehicleData && Array.isArray(vehicleData)) {
                    // 记录数据中心分布情况
                    const centerCounts = {};
                    vehicleData.forEach(vehicle => {
                        const centerName = vehicle.regionName || vehicle.center || '未知中心';
                        centerCounts[centerName] = (centerCounts[centerName] || 0) + 1;
                    });
                }
                
                // 更新车辆状态过滤计数
                updateVehicleFilterCounts();
            }, 1000);  // 增加延时到1秒，确保数据加载完成
           
           // 更新状态指示器
           updateStatusIndicators();
           
           // 关闭弹窗
           closeCenterFilterDialog();
           
       }
         
                 // 更新中心过滤计数
        function updateCenterFilterCounts() {
            // 计算并显示中心总数
            const totalCenters = Object.keys(centerFilterOptions).length - 1; // 减去'all'
            $('#topCenterCount').text(totalCenters > 0 ? totalCenters : '?');
        }
         
                 // 初始化中心过滤功能
        function initCenterFilter() {
            // 确保中心过滤对话框存在
            ensureCenterFilterDialogExists();
            
            // 创建中心过滤对话框的"全部中心"选项（如果尚未创建）
            let dialogBody = document.querySelector('#centerFilterDialog .dialog-body');
            if (dialogBody && !dialogBody.querySelector('.filter-option')) {
                const allCenterOption = document.createElement('div');
                allCenterOption.className = 'filter-option';
                allCenterOption.setAttribute('onclick', "toggleCenterFilterOption('all')");
                allCenterOption.innerHTML = `
                    <input type="checkbox" id="center-filter-all" checked>
                    <div class="option-info">
                        <div class="option-title">全部中心</div>
                        <div class="option-desc">显示所有中心的车辆和设施</div>
                    </div>
                `;
                dialogBody.appendChild(allCenterOption);
                console.log('[中心过滤] 创建了全部中心选项');
            } else if (!dialogBody) {
                console.warn('[中心过滤] 找不到中心过滤对话框内容区域');
            }
            
            // 更新中心过滤计数和按钮文本
            updateCenterFilterCounts();
            updateCenterFilterButtonText();
            
            // 检查全局中心数据和过滤选项
            console.log('[中心过滤] 全局中心数据状态:', globalCenterData ? '已加载' : '未加载');
            console.log('[中心过滤] 中心过滤选项数量:', Object.keys(centerFilterOptions).length);
            
            // 如果全局中心数据已经加载，确保过滤选项也已更新
            if (globalCenterData && Object.keys(centerFilterOptions).length <= 1) {
                console.log('[中心过滤] 中心数据已加载但选项未更新，调用updateCenterFilterOptions');
                updateCenterFilterOptions(globalCenterData);
            }
        }

        // 更新中心过滤选项 - 根据字典数据更新
        function updateCenterFilterOptions(centerData) {        
            try {
                // 先保留all选项
                const allOption = centerFilterOptions.all;
                centerFilterOptions = { all: allOption };
                
                // 确保中心过滤对话框存在
                ensureCenterFilterDialogExists();
                
                // 清空现有对话框内容，保留"全部中心"选项
                let dialogBody = document.querySelector('#centerFilterDialog .dialog-body');
                if (dialogBody) {
                    const allCenterOption = dialogBody.querySelector('.filter-option:first-child');
                    dialogBody.innerHTML = '';
                    if (allCenterOption) {
                        dialogBody.appendChild(allCenterOption);
                    } else {
                        // 如果没有"全部中心"选项，创建一个
                        const newAllOption = document.createElement('div');
                        newAllOption.className = 'filter-option';
                        newAllOption.setAttribute('onclick', "toggleCenterFilterOption('all')");
                        newAllOption.innerHTML = `
                            <input type="checkbox" id="center-filter-all" checked>
                            <div class="option-info">
                                <div class="option-title">全部中心</div>
                                <div class="option-desc">显示所有中心的车辆和设施</div>
                            </div>
                        `;
                        dialogBody.appendChild(newAllOption);
                    }
                } else {
                    console.warn('[中心过滤] 找不到对话框内容区域');
                }
                
                // 处理不同格式的中心数据
                let centersArray = centerData;

                centersArray.forEach((center, index) => {
                    // 确保中心有必要的属性
                    let centerKey = center.codeName || center.uuid || center.id;
                    let centerName = center.codeVale || center.codeName || center.name || center.displayName || '未命名中心';
                    
                    // 如果没有可用的键，则生成一个
                    if (!centerKey) {
                        centerKey = 'center_' + centerName.replace(/\s+/g, '_') + '_' + Math.random().toString(36).substring(2, 8);
                        console.log(`[中心过滤] 中心${index+1}缺少ID，生成ID: ${centerKey}`);
                    }
                    
                    console.log(`[中心过滤] 添加中心${index+1}: ${centerKey} - ${centerName}`);
                    
                    // 将中心添加到过滤选项中
                    centerFilterOptions[centerKey] = false;
                    
                    // 如果对话框存在，添加新选项
                    if (dialogBody) {
                        // 创建过滤选项
                        const optionDiv = document.createElement('div');
                        optionDiv.className = 'filter-option';
                        optionDiv.setAttribute('onclick', `toggleCenterFilterOption('${centerKey}')`);
                        
                        optionDiv.innerHTML = `
                            <input type="checkbox" id="center-filter-${centerKey}">
                            <div class="option-info">
                                <div class="option-title">${centerName}</div>
                                <div class="option-desc">显示该中心的车辆和设施</div>
                            </div>
                        `;
                        
                        dialogBody.appendChild(optionDiv);
                    } else {
                        console.warn(`[中心过滤] 无法为中心${centerName}创建过滤选项，对话框内容区域不存在`);
                    }
                });
                
                // 更新中心过滤按钮文本
                updateCenterFilterButtonText();
                
                // 更新顶部中心计数
                const totalCenters = Object.keys(centerFilterOptions).length - 1; // 减去'all'
                $('#topCenterCount').text(totalCenters);
                
                // 保存到全局变量，确保数据一致性
                if (centersArray && centersArray.length > 0) {
                    if (!globalCenterData || globalCenterData.length === 0) {
                        globalCenterData = centersArray;
                    }
                }
            } catch (error) {
                console.error('[中心过滤] 更新中心过滤选项失败:', error);
            }
         }

    </script>
</body>
</html> 