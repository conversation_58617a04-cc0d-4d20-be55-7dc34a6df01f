<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆轨迹回放</title>
    <link rel="stylesheet" type="text/css" href="script/themes/bootstrap/easyui.css">
    <link rel="stylesheet" type="text/css" href="script/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="style/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="style/common.css" />

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        .track-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .track-header {
            background: #e0ecff;
            color: #333;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #95b8e7;
        }
        
        .track-title {
            font-size: 16px;
            font-weight: bold;
            color: #15428b;
        }
        
        .track-info {
            font-size: 14px;
            color: #666;
        }
        
        .control-panel {
            background: #f5f5f5;
            padding: 15px 20px;
            border-bottom: 1px solid #ccc;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .control-row:last-child {
            margin-bottom: 0;
        }
        
        .control-label {
            width: 80px;
            font-weight: bold;
            color: #333;
        }
        
        .control-input {
            margin-right: 20px;
        }
        

        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #track-map {
            width: 100%;
            height: 100%;
            min-height: 400px;
        }
        
        .playback-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            display: none;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .playback-btn {
            background: #4d90fe;
            border: 1px solid #3875d7;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .playback-btn:hover {
            background: #357ae8;
            border-color: #2d5aa0;
        }
        
        .playback-btn:disabled {
            background: #ccc;
            border-color: #aaa;
            cursor: not-allowed;
        }
        
        .playback-slider {
            width: 300px;
            margin: 0 10px;
        }
        
        .playback-info {
            font-size: 12px;
            color: #ecf0f1;
            min-width: 200px;
            text-align: center;
            white-space: nowrap;
        }
        
        .speed-control {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .speed-btn {
            background: #f0f0f0;
            border: 1px solid #95b8e7;
            color: #333;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .speed-btn:hover {
            background: #e5f1ff;
        }
        
        .speed-btn.active {
            background: #4d90fe;
            border-color: #3875d7;
            color: white;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .loading-content {
            text-align: center;
            color: #333;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f0f0f0;
            border-top: 4px solid #4d90fe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .track-stats {
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #dee2e6;
            display: none;
        }
        
        .stats-item {
            display: inline-block;
            margin-right: 30px;
            color: #495057;
            font-size: 14px;
        }
        
        .stats-value {
            font-weight: bold;
            color: #15428b;
        }
    </style>
</head>
<body>
    <div class="track-container">
        <!-- 头部标题 -->
        <div class="track-header">
            <div class="track-title">车辆轨迹回放</div>
            <div class="track-info" id="vehicle-info">正在加载车辆信息，将自动查询1小时内轨迹...（最多可查询1个月轨迹）</div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-row">
                <div class="control-label">开始时间:</div>
                <div class="control-input">
                    <input id="start-time" class="easyui-datetimebox" style="width: 180px;" />
                </div>
                <div class="control-label">结束时间:</div>
                <div class="control-input">
                    <input id="end-time" class="easyui-datetimebox" style="width: 180px;" />
                </div>
                <a href="javascript:void(0);" class="inAnHour">一小时内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inToday">一天内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inOneWeek">一周内</a>&nbsp;|&nbsp;<a href="javascript:void(0);" class="inOneMonth">一月内</a>
                <span style="margin-left: 20px;"></span>
                <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="query-btn">查询轨迹</a>
                <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" id="clear-btn">清除轨迹</a>
            </div>
        </div>
        
        <!-- 地图容器 -->
        <div class="map-container">
            <div id="track-map"></div>
            
            <!-- 加载遮罩 -->
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div>正在加载轨迹数据...</div>
                </div>
            </div>
            
            <!-- 回放控制器 -->
            <div class="playback-controls" id="playback-controls">
                <button class="playback-btn" id="play-btn" title="播放/暂停">
                    <span class="icon-play" id="play-icon">▶</span>
                </button>
                <button class="playback-btn" id="stop-btn" title="停止">
                    <span class="icon-stop">⏹</span>
                </button>
                <input type="range" class="playback-slider" id="progress-slider" min="0" max="100" value="0">
                <div class="playback-info" id="playback-info">00:00:00 (00:00:00 - 00:00:00)</div>
                <div class="speed-control">
                    <span style="font-size: 12px;">速度:</span>
                    <button class="speed-btn" data-speed="0.5">0.5x</button>
                    <button class="speed-btn active" data-speed="1">1x</button>
                    <button class="speed-btn" data-speed="2">2x</button>
                    <button class="speed-btn" data-speed="5">5x</button>
                </div>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="track-stats" id="track-stats">
            <div class="stats-item">
                总里程: <span class="stats-value" id="total-distance">0 km</span>
            </div>
            <div class="stats-item">
                轨迹点数: <span class="stats-value" id="total-points">0</span>
            </div>
            <div class="stats-item">
                平均速度: <span class="stats-value" id="avg-speed">0 km/h</span>
            </div>
            <div class="stats-item">
                最高速度: <span class="stats-value" id="max-speed">0 km/h</span>
            </div>
            <div class="stats-item">
                时长: <span class="stats-value" id="duration">0分钟</span>
            </div>
        </div>
    </div>
    
    <!-- 引入必要的脚本 -->
    <script type="text/javascript" src="script/jquery.min.js"></script>
    <script type="text/javascript" src="script/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="script/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="script/viewcore.js"></script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=7af39c825eb7720aede3a5caef928652"></script>
    <script type="text/javascript" src="script/gProxy.js"></script>
    <script type="text/javascript" src="script/md5-js.js"></script>
    <script type="text/javascript" src="script/crypto-js.min.js"></script>
    <script src="script/ajax.js"></script>
    
    <script>
        // 全局变量
        let map = null;
        let trackData = [];
        let currentIndex = 0;
        let isPlaying = false;
        let playInterval = null;
        let playSpeed = 1;
        let vehicleMarker = null;
        let trackPolyline = null;
        let passedPolyline = null;
        let mapApiLoaded = false;
        
        // 预定义救护车图标（参考secondScr_gaode.js）
        let carIcons = null;
        
        // 修复ajax.js中的endWith方法错误
        if (!String.prototype.endsWith) {
            String.prototype.endsWith = function(searchString, position) {
                var subjectString = this.toString();
                if (typeof position !== 'number' || !isFinite(position) || Math.floor(position) !== position || position > subjectString.length) {
                    position = subjectString.length;
                }
                position -= searchString.length;
                var lastIndex = subjectString.lastIndexOf(searchString, position);
                return lastIndex !== -1 && lastIndex === position;
            };
        }
        
        // 为兼容ajax.js中的endWith方法
        if (!String.prototype.endWith) {
            String.prototype.endWith = String.prototype.endsWith;
        }
        
        // 修复ajax.js中的null值处理
        if (typeof window._baseUrl === 'undefined') {
            window._baseUrl = "";
        }
        
        // 初始化ajax.js需要的全局变量（参考second.html的实现）
        function initializeAjaxGlobals() {
            // 初始化代理对象
            try {
                if (typeof gProxy !== 'undefined') {
                    console.log('轨迹回放页面使用C/S模式');
                    window._baseUrl = gProxy.getBaseUrl();
                } else {
                    console.log('轨迹回放页面使用B/S模式，不调用gProxyW.getUserToken()');
                    window._baseUrl = "";
                }
                
                console.log('轨迹回放页面 baseUrl:', window._baseUrl);
            } catch (e) {
                console.warn('轨迹回放页面初始化baseUrl失败，使用默认值:', e);
                window._baseUrl = "";
            }
        }

        
        // ==================== 页面URL参数说明 ====================
        // 轨迹回放页面支持以下4个URL参数：
        // 
        // 1. vehicleId （必需参数）
        //    - 类型：字符串
        //    - 说明：车辆ID，用于查询车辆信息和轨迹数据
        //    - 示例：vehicleId=123
        //
        // 2. carName （可选参数）
        //    - 类型：字符串（需URL编码）
        //    - 说明：车辆名称，用于页面显示，如果未提供会从接口获取
        //    - 示例：carName=救护车001
        //
        // 3. plateNum （可选参数）
        //    - 类型：字符串（需URL编码）
        //    - 说明：车牌号码，用于页面显示，如果未提供会从接口获取
        //    - 示例：plateNum=川L00001
        //
        // 4. startTime （可选参数）
        //    - 类型：字符串（日期时间格式）
        //    - 说明：指定查询轨迹的开始时间，查询范围为开始时间往后1小时
        //    - 支持格式：
        //      * ISO 8601格式：2024-01-15T14:30:00 或 2024-01-15T14:30:00.000Z
        //      * 通用格式：2024-01-15 14:30:00 或 2024/01/15 14:30:00
        //      * 简化格式：2024-01-15 14:30
        //    - 时间逻辑：
        //      * 有startTime：查询 [startTime] 到 [startTime + 1小时] 的轨迹
        //      * 无startTime：查询 [当前时间 - 1小时] 到 [当前时间] 的轨迹
        //    - 示例：startTime=2024-01-15T14:30:00
        //
        // 完整URL示例：
        // trackReplay.html?vehicleId=123&carName=救护车001&plateNum=川L00001&startTime=2024-01-15T14:30:00
        // 最简URL示例：
        // trackReplay.html?vehicleId=123
        // =========================================================
        
        // 页面参数
        // 从URL参数中获取车辆信息
        const urlParams = new URLSearchParams(window.location.search);
        
        // 调试：检查URL参数解析
        console.log('=== URL参数解析调试 ===');
        console.log('完整URL:', window.location.href);
        console.log('查询字符串:', window.location.search);
        console.log('URLSearchParams所有参数:');
        for (let [key, value] of urlParams) {
            console.log(`  ${key}: ${value}`);
        }
        
        // 获取车辆ID
        const vehicleId = urlParams.get('vehicleId'); 
        // 获取车辆名称,如果为空则使用空字符串
        let carName = decodeURIComponent(urlParams.get('carName') || '');
        // 获取车牌号,如果为空则使用空字符串
        let plateNum = decodeURIComponent(urlParams.get('plateNum') || '');
        // 获取开始时间参数（可选）
        const startTimeParam = urlParams.get('startTime');
        
        console.log('解析结果:', {
            vehicleId: vehicleId,
            carName: carName,
            plateNum: plateNum,
            startTime: startTimeParam,
            startTime是否为null: startTimeParam === null,
            startTime解码后: startTimeParam ? decodeURIComponent(startTimeParam) : null
        });
        console.log('==================');
        


        // 动态插入高德地图脚本
        function loadGaodeScript(onLoaded) {
            const cfg = getMapSysConfig();
            const key = cfg.gaode?.key;
            if (!key) {
                console.error('未配置高德地图 key');
                $.messager.alert('错误', '缺少高德地图 Key，请重新登录获取系统配置');
                return;
            }
            const s = document.createElement('script');
            s.type = 'text/javascript';
            s.src = `https://webapi.amap.com/maps?v=2.0&key=${key}`;
            s.onload = () => onLoaded && onLoaded();
            s.onerror = () => {
                console.error('高德地图脚本加载失败');
                $.messager.alert('错误', '高德地图脚本加载失败');
            };
            document.head.appendChild(s);
        }
        
        // GPS设备信息
        let gpsTerminal = null; // 主GPS设备号码
        let reserveGpsTerminal = null; // 备用GPS设备号码
        let vehicleInfo = null; // 完整车辆信息
        

        
        // 初始化页面
        $(document).ready(function() {
            console.log('页面开始初始化...');
            
            // 初始隐藏加载遮罩
            $('#loading-overlay').hide();
            
            // 初始化easyui组件
            $.parser.parse();
            
            console.log('开始初始化页面组件...');
            
            // 初始化ajax全局变量
            initializeAjaxGlobals();
            
            // 初始化页面和事件
            initPage();
            initEvents();
            
            // 初始化地图（在initPage中会先获取GPS设备信息）
            // initMap();
            // 先加载脚本,在初始化
            loadGaodeScript(function () {
                initMap();
            });


        });
        
        // 初始化页面信息
        function initPage() {
            console.log('初始化页面信息...');
            
            // 检查车辆ID
            if (!vehicleId) {
                $('#vehicle-info').text('错误: 未提供车辆ID');
                $.messager.alert('错误', '未提供车辆ID，无法查询轨迹', 'error');
                return;
            }
            
            // 先获取车辆信息和GPS设备号码
            getVehicleGpsInfo();
            
            // 设置默认时间
            let startTime, endTime;
            
            if (startTimeParam) {
                // 如果传递了开始时间参数，使用该时间往后1小时
                try {
                    console.log('接收到的开始时间参数:', startTimeParam);
                    
                    // 先解码URL编码的时间字符串
                    let timeStr = decodeURIComponent(startTimeParam);
                    console.log('解码后的时间字符串:', timeStr);
                    
                    // 处理不同的时间格式
                    if (timeStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
                        timeStr = timeStr.replace(' ', 'T');
                        console.log('转换为ISO格式:', timeStr);
                    }
                    
                    startTime = new Date(timeStr);
                    
                    // 检查解析后的时间是否有效
                    if (isNaN(startTime.getTime())) {
                        throw new Error('时间解析结果无效: ' + timeStr);
                    }
                    
                    endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // 往后1小时
                    
                    console.log('✅ 成功解析时间参数:', {
                        原始参数: startTimeParam,
                        解码后: timeStr,
                        解析结果: startTime,
                        开始时间: formatDateTime(startTime),
                        结束时间: formatDateTime(endTime)
                    });
                } catch (e) {
                    console.warn('❌ 开始时间参数格式错误，使用默认时间:', {
                        原始参数: startTimeParam,
                        错误: e.message
                    });
                    // 如果时间格式错误，使用默认时间
            const now = new Date();
                    startTime = new Date(now.getTime() - 60 * 60 * 1000);
                    endTime = now;
                }
            } else {
                // 如果没有传递开始时间，使用当前时间往前1小时到当前时间
                const now = new Date();
                startTime = new Date(now.getTime() - 60 * 60 * 1000);
                endTime = now;
                console.log('使用默认时间范围（当前时间往前1小时）');
            }
            
            // 使用easyui datetimebox设置默认值
            $('#start-time').datetimebox('setValue', formatDateTime(startTime));
            $('#end-time').datetimebox('setValue', formatDateTime(endTime));
            
            console.log('时间控件初始化完成，时间范围:', formatDateTime(startTime), '至', formatDateTime(endTime));
        }
        
        // 获取车辆GPS设备信息
        function getVehicleGpsInfo() {
            console.log('正在获取车辆信息和GPS设备号码...', vehicleId);
            
            // 显示加载状态
            $('#vehicle-info').text('正在获取车辆信息...');
            
            // 检查是否有必要的函数可用
            if (typeof getVehicleById === 'undefined') {
                console.error('getVehicleById函数未定义，可能是ajax.js加载问题');
                $('#vehicle-info').text('系统加载错误，请刷新页面重试');
                $.messager.alert('系统错误', '页面加载不完整，请刷新页面重试', 'error');
                return;
            }
            
            // 调用getVehicleById接口
            getVehicleById(vehicleId, 
                function(data) {
                    console.log('成功获取车辆信息:', data);
                    
                    if (data) {
                        vehicleInfo = data;
                        
                        // 更新车辆基本信息（如果URL参数中没有）
                        if (!carName && data.carName) {
                            carName = data.carName;
                        }
                        if (!plateNum && data.plateNum) {
                            plateNum = data.plateNum;
                        }
                        
                        // 提取GPS设备号码
                        gpsTerminal = data.gpsTerminal;
                        reserveGpsTerminal = data.reserveGpsTerminal;
                        
                        console.log('GPS设备信息:', {
                            gpsTerminal: gpsTerminal,
                            reserveGpsTerminal: reserveGpsTerminal
                        });
                        
                        // 更新页面显示
                        updateVehicleInfoDisplay();
                        
                        // 检查是否有GPS设备
                        if (!gpsTerminal && !reserveGpsTerminal) {
                            const errorMsg = '该车辆未配置GPS位置设备，无法获取轨迹数据。';
                            $('#vehicle-info').text(`车辆: ${carName}${plateNum ? ' (' + plateNum + ')' : ''} - ${errorMsg}`);
                            $.messager.alert('GPS设备未配置', errorMsg + '请联系管理员配置GPS设备信息。', 'warning');
                            
                            // 禁用查询按钮
                            $('#query-btn').linkbutton('disable');
                            return;
                        }
                        
                        // 启用查询按钮
                        $('#query-btn').linkbutton('enable');
                        
                        // GPS设备信息获取成功后，自动查询1小时内的轨迹数据
                        setTimeout(function() {
                            if (mapApiLoaded) {
                                console.log('GPS设备信息获取成功，自动查询1小时内轨迹数据...');
                                queryTrackData();
                            } else {
                                console.log('等待地图加载完成后再自动查询轨迹...');
                                // 等待地图加载完成
                                var checkMapInterval = setInterval(function() {
                                    if (mapApiLoaded) {
                                        clearInterval(checkMapInterval);
                                        console.log('地图加载完成，自动查询1小时内轨迹数据...');
                                        queryTrackData();
                                    }
                                }, 500);
                            }
                        }, 1000); // 延迟1秒确保页面稳定
                        
                    } else {
                        throw new Error('返回的车辆信息为空');
                    }
                },
                function(e, url, errMsg) {
                    console.error('获取车辆信息失败:', errMsg);
                    $('#vehicle-info').text(`车辆信息获取失败: ${errMsg}`);
                    $.messager.alert('错误', `获取车辆信息失败: ${errMsg}`, 'error');
                    
                    // 接口失败时，禁用查询按钮
                    $('#query-btn').linkbutton('disable');
                }
            );
        }
        
        // 更新车辆信息显示
        function updateVehicleInfoDisplay() {
            let infoText = `车辆: ${carName}${plateNum ? ' (' + plateNum + ')' : ''}`;
            
            // 显示GPS设备信息
            const gpsInfo = [];
            if (gpsTerminal) {
                gpsInfo.push(`GPS设备: ${gpsTerminal}`);
            }
            if (reserveGpsTerminal) {
                gpsInfo.push(`备用GPS: ${reserveGpsTerminal}`);
            }
            
            if (gpsInfo.length > 0) {
                infoText += ` - ${gpsInfo.join(', ')} - 可查询轨迹`;
            }
            
            $('#vehicle-info').text(infoText);
            console.log('车辆信息显示已更新:', infoText);
        }

        
        
        // 初始化地图（参考secondScr_gaode.js的实现）
        function initMap() {
            if (typeof AMap === 'undefined') {
                console.log('地图API尚未加载完成，等待中...');
                setTimeout(initMap, 100);
                return;
            }
            
            try {
                console.log('开始初始化地图...');
                
                // 创建地图实例，参考secondScr_gaode.js的配置
                map = new AMap.Map("track-map", {
                    enableMapClick: false,
                    minZoom: 1,
                    maxZoom: 100,
                    zoom: 14,
                    center: [105.445, 28.875] // 默认泸州市中心
                });
                
                // 设置地图显示的城市（参考secondScr_gaode.js）
                querySysConf('map_city',
                    function (data) {
                        map.setCity(data);
                        console.log('设置地图城市:', data);
                        
                        // 围栏边界绘制
                        drawFenceBoundaries();
                    },
                    function (e, url, errMsg) {
                        console.warn('获取城市配置失败，使用默认配置:', errMsg);
                        
                        // 即使城市配置失败也绘制围栏
                        drawFenceBoundaries();
                    }
                );
                
                // 添加比例尺控件（参考secondScr_gaode.js）
                AMap.plugin(['AMap.Scale'], function() {
                    var scale = new AMap.Scale({
                        position: 'LB'
                    });
                    map.addControl(scale);
                });
                
                // 初始化救护车图标（参考secondScr_gaode.js）
                carIcons = {
                    default: new AMap.Icon({ image: "style/img/ambul.png", size: new AMap.Size(48, 48) }),
                    yellow: new AMap.Icon({ image: "style/img/ambul_yellow.png", size: new AMap.Size(48, 48) }),
                    red: new AMap.Icon({ image: "style/img/ambul_red.png", size: new AMap.Size(48, 48) }),
                    blue: new AMap.Icon({ image: "style/img/ambul_blue.png", size: new AMap.Size(48, 48) })
                };
                
                console.log('地图初始化成功');
                mapApiLoaded = true;
            } catch (error) {
                console.error('地图初始化失败:', error);
                $.messager.alert('错误', '地图初始化失败: ' + error.message, 'error');
            }
        }
        
        /** 
         * 绘制围栏边界（参考secondScr_gaode.js）
         */
        function drawFenceBoundaries() {
            try {
                // 获取围栏配置列表
                var fenceList = getFenceListConfig();
                console.log('获取到围栏列表:', fenceList);
                
                if (!fenceList || !Array.isArray(fenceList) || fenceList.length === 0) {
                    console.log('没有围栏数据，跳过围栏绘制');
                    return;
                }
                
                var fenceShapes = []; // 存储所有围栏图形，用于地图适配
                
                // 遍历所有围栏配置
                for (var i = 0; i < fenceList.length; i++) {
                    var fence = fenceList[i];
                    console.log(`开始处理第 ${i + 1} 个围栏:`, {
                        名称: fence.fenceName,
                        类型: fence.shapeType,
                        状态: fence.status,
                        原始数据: fence
                    });
                    
                    try {
                        // 解析坐标数据（处理字符串或对象格式）
                        var coordinateData;
                        if (typeof fence.coordinateData === 'string') {
                            coordinateData = JSON.parse(fence.coordinateData);
                        } else {
                            coordinateData = fence.coordinateData;
                        }
                        console.log(`围栏 ${fence.fenceName} 的坐标数据:`, coordinateData);
                        
                        var fenceColor = fence.fenceColor || '#ff0000'; // 默认红色
                        console.log(`围栏 ${fence.fenceName} 使用颜色:`, fenceColor);
                        
                        if (fence.shapeType === 'polygon') {
                            // 绘制多边形围栏
                            console.log(`准备绘制多边形围栏: ${fence.fenceName}`);
                            drawPolygonFence(fence, coordinateData, fenceColor, fenceShapes);
                        } else if (fence.shapeType === 'circle') {
                            // 绘制圆形围栏
                            console.log(`准备绘制圆形围栏: ${fence.fenceName}`);
                            drawCircleFence(fence, coordinateData, fenceColor, fenceShapes);
                        } else {
                            console.warn(`围栏 ${fence.fenceName} 的类型 ${fence.shapeType} 不支持`);
                        }
                    } catch (parseError) {
                        console.error(`解析围栏 ${fence.fenceName} 的数据失败:`, {
                            错误: parseError,
                            原始数据: fence.coordinateData
                        });
                    }
                }
                
                console.log(`围栏绘制完成，共绘制 ${fenceShapes.length} 个围栏`);
                
            } catch (error) {
                console.error('绘制围栏边界失败:', error);
            }
        }

        /**
         * 绘制多边形围栏（参考secondScr_gaode.js）
         */
        function drawPolygonFence(fence, coordinateData, fenceColor, fenceShapes) {
            if (!coordinateData || coordinateData.length < 3) {
                console.warn(`多边形围栏 ${fence.fenceName} 坐标点不足，需要至少3个点`, {
                    坐标数据: coordinateData
                });
                return;
            }
            
            console.log(`开始转换多边形围栏 ${fence.fenceName} 的坐标格式`);
            // 转换坐标格式
            var path = coordinateData.map(function(coord) {
                return new AMap.LngLat(coord.lng, coord.lat);
            });
            console.log(`转换后的坐标路径:`, path);
            
            // 创建多边形
            var polygon = new AMap.Polygon({
                map: map,
                strokeWeight: 2,
                path: path,
                fillOpacity: 0, // 设置为0，完全透明
                strokeColor: fenceColor
            });
            
            fenceShapes.push(polygon);
            console.log(`成功绘制多边形围栏: ${fence.fenceName}`, {
                路径点数: path.length,
                边框颜色: fenceColor
            });
        }

        /**
         * 绘制圆形围栏（参考secondScr_gaode.js）
         */
        function drawCircleFence(fence, coordinateData, fenceColor, fenceShapes) {
            try {
                console.log(`开始处理圆形围栏 ${fence.fenceName} 的数据`, {
                    原始数据: coordinateData
                });
                
                // 检查coordinateData的格式
                if (!coordinateData || !coordinateData.center || typeof coordinateData.radius !== 'number') {
                    console.warn(`圆形围栏 ${fence.fenceName} 数据格式不正确`, {
                        数据: coordinateData
                    });
                    return;
                }
                
                // 获取中心点和半径
                var centerPoint = coordinateData.center;
                var radius = coordinateData.radius;
                
                console.log(`圆形围栏参数:`, {
                    中心点: centerPoint,
                    半径: radius,
                    颜色: fenceColor
                });
                
                if (!centerPoint.lng || !centerPoint.lat) {
                    console.warn(`圆形围栏 ${fence.fenceName} 中心点坐标无效`, {
                        中心点数据: centerPoint
                    });
                    return;
                }
                
                // 创建圆形
                var circle = new AMap.Circle({
                    center: new AMap.LngLat(centerPoint.lng, centerPoint.lat),
                    radius: radius,
                    strokeColor: fenceColor,
                    strokeWeight: 2,
                    fillOpacity: 0, // 设置为0，完全透明
                    strokeStyle: 'solid',
                    zIndex: 50,
                    map: map  // 直接设置map属性，确保添加到地图上
                });
                
                // 确保圆形被添加到地图上
                if (!circle.getMap()) {
                    map.add(circle);
                }
                
                fenceShapes.push(circle);
                console.log(`成功绘制圆形围栏: ${fence.fenceName}`, {
                    中心点: centerPoint,
                    半径: Math.round(radius),
                    边框颜色: fenceColor,
                    是否已添加到地图: circle.getMap() ? '是' : '否'
                });
                
            } catch (error) {
                console.error(`绘制圆形围栏 ${fence.fenceName} 失败:`, {
                    错误: error,
                    原始数据: coordinateData
                });
            }
        }
        
        // 初始化事件
        function initEvents() {
            console.log('初始化事件监听...');
            
            // 查询按钮
            $('#query-btn').click(function() {
                queryTrackData();
            });
            
            // 清除按钮
            $('#clear-btn').click(function() {
                clearTrack();
            });
            

            
            // 一小时内按钮
            $('.inAnHour').click(function() {
                const now = new Date();
                const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
                $('#start-time').datetimebox('setValue', formatDateTime(oneHourAgo));
                $('#end-time').datetimebox('setValue', formatDateTime(now));
                console.log('设置时间范围为一小时内');
            });
            
            // 一天内按钮
            $('.inToday').click(function() {
                const now = new Date();
                const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                $('#start-time').datetimebox('setValue', formatDateTime(oneDayAgo));
                $('#end-time').datetimebox('setValue', formatDateTime(now));
                console.log('设置时间范围为一天内');
            });
            
            // 一周内按钮
            $('.inOneWeek').click(function() {
                const now = new Date();
                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                $('#start-time').datetimebox('setValue', formatDateTime(oneWeekAgo));
                $('#end-time').datetimebox('setValue', formatDateTime(now));
                console.log('设置时间范围为一周内');
            });
            
            // 一月内按钮
            $('.inOneMonth').click(function() {
                const now = new Date();
                const oneMonthAgo = new Date(now.getTime() - 31 * 24 * 60 * 60 * 1000);
                $('#start-time').datetimebox('setValue', formatDateTime(oneMonthAgo));
                $('#end-time').datetimebox('setValue', formatDateTime(now));
                console.log('设置时间范围为一月内');
            });
            
            // 播放控制
            $('#play-btn').click(function() {
                if (isPlaying) {
                    pausePlayback();
                } else {
                    startPlayback();
                }
            });
            
            // 停止按钮
            $('#stop-btn').click(function() {
                stopPlayback();
            });
            
            // 进度条
            $('#progress-slider').on('input', function() {
                if (trackData.length > 0) {
                    const index = Math.floor((this.value / 100) * (trackData.length - 1));
                    setCurrentIndex(index);
                }
            });
            
            // 速度控制
            $('.speed-btn').click(function() {
                $('.speed-btn').removeClass('active');
                $(this).addClass('active');
                playSpeed = parseFloat($(this).data('speed'));
                
                // 如果正在播放，重新启动以应用新速度
                if (isPlaying) {
                    clearInterval(playInterval);
                    startPlaybackInterval();
                }
            });
        }
        
        // 查询轨迹数据
        function queryTrackData() {
            const startTime = $('#start-time').datetimebox('getValue');
            const endTime = $('#end-time').datetimebox('getValue');
            
            if (!startTime || !endTime) {
                $.messager.alert('提示', '请选择开始时间和结束时间', 'warning');
                return;
            }
            
            if (new Date(startTime) >= new Date(endTime)) {
                $.messager.alert('提示', '开始时间不能大于等于结束时间', 'warning');
                return;
            }
            
            // 检查时间范围是否超过1个月
            const startDate = new Date(startTime);
            const endDate = new Date(endTime);
            const timeDiffMs = endDate.getTime() - startDate.getTime();
            const daysDiff = timeDiffMs / (1000 * 60 * 60 * 24);
            
            if (daysDiff > 31) {
                $.messager.alert('时间范围限制', '查询时间范围不能超过1个月（31天），请缩小查询范围。', 'warning');
                return;
            }
            
            // 检查GPS设备号码
            if (!gpsTerminal && !reserveGpsTerminal) {
                $.messager.alert('GPS设备未配置', '该车辆未配置GPS位置设备，无法获取轨迹。请先配置GPS设备信息。', 'warning');
                return;
            }
            
            console.log('开始查询轨迹数据...', {
                vehicleId,
                gpsTerminal,
                reserveGpsTerminal,
                startTime, 
                endTime
            });
            
            // 显示加载遮罩
            $('#loading-overlay').show();
            
            // 隐藏之前的控制器和统计信息
            $('#playback-controls').hide();
            $('#track-stats').hide();
            
            // 优先使用主GPS设备查询，如果没有数据再使用备用设备
            tryQueryGpsHistory(gpsTerminal, 'main');
        }
        
        // 尝试查询GPS历史数据（主设备优先，无数据时使用备用设备）
        function tryQueryGpsHistory(deviceId, deviceType) {
            if (!deviceId) {
                // 如果当前设备为空，尝试使用备用设备
                if (deviceType === 'main' && reserveGpsTerminal) {
                    console.log('主GPS设备为空，尝试使用备用GPS设备:', reserveGpsTerminal);
                    tryQueryGpsHistory(reserveGpsTerminal, 'backup');
                    return;
                } else {
                    // 两个设备都没有，显示提示
                    $('#loading-overlay').hide();
                    $.messager.alert('GPS设备未配置', '该车辆未配置GPS位置设备，无法获取轨迹。请先配置GPS设备信息。', 'warning');
                    return;
                }
            }
            
            const startTime = $('#start-time').datetimebox('getValue');
            const endTime = $('#end-time').datetimebox('getValue');
            
            // 准备查询参数
            const queryParams = {
                deviceId: deviceId,
                deviceStartTime: formatApiTime(startTime),
                deviceEndTime: formatApiTime(endTime)
            };
            
            const deviceTypeName = deviceType === 'main' ? '主GPS设备' : '备用GPS设备';
            console.log(`使用${deviceTypeName}查询轨迹:`, queryParams);
            
            // 调用getGpsHistoryList接口
            getGpsHistoryList(queryParams,
                function(data) {
                    console.log(`${deviceTypeName}查询结果:`, data);
                    
                    if (data && data.gpsHistoryInfoList && data.gpsHistoryInfoList.length > 0) {
                        // 有数据，处理并显示
                        handleGpsQuerySuccess(data.gpsHistoryInfoList, deviceId, deviceTypeName);
                    } else {
                        // 没有数据
                        if (deviceType === 'main' && reserveGpsTerminal) {
                            // 主设备没有数据，尝试备用设备
                            console.log('主GPS设备没有轨迹数据，尝试使用备用GPS设备:', reserveGpsTerminal);
                            tryQueryGpsHistory(reserveGpsTerminal, 'backup');
                        } else {
                            // 备用设备也没有数据，或者只有一个设备
                            handleNoGpsData(deviceId, deviceTypeName);
                        }
                    }
                },
                function(e, url, errMsg) {
                    console.error(`${deviceTypeName}查询失败:`, errMsg);
                    
                    if (deviceType === 'main' && reserveGpsTerminal) {
                        // 主设备查询失败，尝试备用设备
                        console.log('主GPS设备查询失败，尝试使用备用GPS设备:', reserveGpsTerminal);
                        tryQueryGpsHistory(reserveGpsTerminal, 'backup');
                    } else {
                        // 备用设备也失败，或者只有一个设备
                        handleGpsQueryError(errMsg, deviceId, deviceTypeName);
                    }
                }
            );
        }
        
        // 处理GPS查询成功
        function handleGpsQuerySuccess(gpsHistoryList, deviceId, deviceTypeName) {
            $('#loading-overlay').hide();
            
            // 处理GPS历史数据
            processGpsTrackData(gpsHistoryList);
            
            // 显示轨迹
            displayTrack();
            
            // 显示统计信息
            showTrackStats();
            
            // 更新车辆信息显示
            updateVehicleInfoAfterQuery();
            
            $.messager.show({
                title: '成功',
                msg: `成功查询到 ${gpsHistoryList.length} 个轨迹点 (使用${deviceTypeName}: ${deviceId})`,
                timeout: 3000
            });
        }
        
        // 处理没有GPS数据的情况
        function handleNoGpsData(deviceId, deviceTypeName) {
            $('#loading-overlay').hide();
            
            const startTime = $('#start-time').datetimebox('getValue');
            const endTime = $('#end-time').datetimebox('getValue');
            
            $.messager.alert('无轨迹数据', 
                `没有该时间段的车辆轨迹信息，请修改查询条件。<br/><br/>查询时间: ${startTime} 至 ${endTime}<br/>GPS设备: ${deviceTypeName} (${deviceId})`, 
                'info');
        }
        
        // 处理GPS查询错误
        function handleGpsQueryError(errMsg, deviceId, deviceTypeName) {
            $('#loading-overlay').hide();
            
            // 查询失败时，直接给出失败提示
            $.messager.alert('查询失败', 
                `轨迹查询失败: ${errMsg}<br/>设备: ${deviceTypeName} (${deviceId})<br/><br/>请检查时间范围或稍后重试。`, 
                'error'
            );
        }
        
        // 处理GPS历史轨迹数据
        function processGpsTrackData(gpsHistoryList) {
            console.log('处理GPS历史轨迹数据，原始数据:', gpsHistoryList);
            
            trackData = gpsHistoryList.map(item => ({
                lng: item.gaodeLongitude,
                lat: item.gaodeLatitude,
                speed: item.speed || 0,
                direction: item.direction || 0,
                time: item.deviceLocationTime
            })).filter(item => item.lng && item.lat && !isNaN(item.lng) && !isNaN(item.lat)); // 过滤掉无效坐标
            
            // 按时间排序
            trackData.sort((a, b) => new Date(a.time) - new Date(b.time));
            
            currentIndex = 0;
            
            console.log('GPS轨迹数据处理完成，共', trackData.length, '个有效轨迹点');
        }
        
        // 处理轨迹数据
        function processTrackData(historyList) {
            console.log('处理轨迹数据，原始数据:', historyList);
            
            trackData = historyList.map(item => ({
                lng: item.gaodeLongitude || item.lng,  // 兼容模拟数据和真实接口数据
                lat: item.gaodeLatitude || item.lat,   // 兼容模拟数据和真实接口数据
                speed: item.speed || 0,
                direction: item.direction || 0,
                time: item.deviceLocationTime || item.time  // 兼容模拟数据和真实接口数据
            })).filter(item => item.lng && item.lat); // 过滤掉无效坐标
            
            // 按时间排序
            trackData.sort((a, b) => new Date(a.time) - new Date(b.time));
            
            currentIndex = 0;
            
            console.log('轨迹数据处理完成，共', trackData.length, '个有效轨迹点');
        }
        
        // 显示轨迹
        function displayTrack() {
            if (trackData.length === 0) {
                console.warn('没有轨迹数据可显示');
                return;
            }
            
            console.log('开始显示轨迹，轨迹点数:', trackData.length);
            console.log('轨迹数据示例:', trackData[0]);
            
            // 清除之前的地图对象，但不重置轨迹数据
            clearMapObjects();
            
            // 创建轨迹路径，增加验证并使用AMap.LngLat对象
            const path = trackData.filter(point => point && point.lng && point.lat && 
                                         !isNaN(point.lng) && !isNaN(point.lat))
                                 .map(point => new AMap.LngLat(point.lng, point.lat));
            
            if (path.length === 0) {
                console.error('没有有效的轨迹坐标点');
                $.messager.alert('错误', '轨迹数据无效，没有有效的坐标点', 'error');
                return;
            }
            
            console.log('有效轨迹路径点数:', path.length);
            console.log('路径示例:', path[0], path[path.length-1]);
            
            // 绘制完整轨迹线（蓝色，更细）
            trackPolyline = new AMap.Polyline({
                path: path,
                strokeColor: '#3498db',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                lineJoin: 'round',
                lineCap: 'round'
            });
            map.add(trackPolyline);
            
            // 创建已走过的轨迹线（红色，更细）
            passedPolyline = new AMap.Polyline({
                path: [],
                strokeColor: '#e74c3c',
                strokeWeight: 3,
                strokeOpacity: 0.9,
                lineJoin: 'round',
                lineCap: 'round'
            });
            map.add(passedPolyline);
            
            // 创建车辆标记，使用第一个有效点（完全参考secondScr_gaode.js的实现）
            const firstPoint = trackData.find(point => point && point.lng && point.lat);
            if (firstPoint) {
                vehicleMarker = new AMap.Marker({
                    position: new AMap.LngLat(firstPoint.lng, firstPoint.lat),
                    icon: carIcons.default,  // 使用预定义的图标对象，与secondScr_gaode.js一致
                    angle: firstPoint.direction || 0,  // 直接使用direction，与secondScr_gaode.js中的car.direct一致
                    title: `${carName} - ${firstPoint.time}`
                });
                
                // 创建车辆信息显示内容
                const infoContent = createVehicleInfoContent(firstPoint);
                const infoWindow = new AMap.InfoWindow({
                    content: infoContent,
                    offset: new AMap.Pixel(0, -30),
                    autoMove: false
                });
                
                // 为车辆标记添加点击事件显示信息窗口
                vehicleMarker.on('click', function() {
                    const currentPoint = trackData[currentIndex] || firstPoint;
                    const updatedContent = createVehicleInfoContent(currentPoint);
                    infoWindow.setContent(updatedContent);
                    infoWindow.open(map, vehicleMarker.getPosition());
                });
                
                // 存储信息窗口对象以便后续使用
                vehicleMarker.infoWindow = infoWindow;
                
                map.add(vehicleMarker);
            }
            
            // 调整地图视野
            map.setFitView([trackPolyline]);
            
            // 显示回放控制器
            $('#playback-controls').show();
            
            // 更新进度信息
            updatePlaybackInfo();
            
            // 默认打开车辆信息窗口
            if (vehicleMarker && trackData[0]) {
                setTimeout(() => {
                    const infoContent = createVehicleInfoContent(trackData[0]);
                    if (!vehicleMarker.infoWindow) {
                        vehicleMarker.infoWindow = new AMap.InfoWindow({
                            content: infoContent,
                            offset: new AMap.Pixel(0, -40)
                        });
                    }
                    vehicleMarker.infoWindow.open(map, vehicleMarker.getPosition());
                    console.log('默认打开车辆信息窗口');
                }, 500); // 延迟500ms确保地图渲染完成
            }
            
            // 显示使用提示
            $.messager.show({
                title: '轨迹显示完成',
                msg: '轨迹已成功加载！车辆信息窗口已自动打开，可查看详细信息',
                timeout: 4000,
                showType: 'slide'
            });
            
            console.log('轨迹显示完成');
        }
        
        // 清除地图上的轨迹对象，但不重置数据
        function clearMapObjects() {
            console.log('清除地图对象...');
            
            if (trackPolyline) {
                map.remove(trackPolyline);
                trackPolyline = null;
            }
            
            if (passedPolyline) {
                map.remove(passedPolyline);
                passedPolyline = null;
            }
            
            if (vehicleMarker) {
                // 关闭并清理信息窗口
                if (vehicleMarker.infoWindow) {
                    vehicleMarker.infoWindow.close();
                    vehicleMarker.infoWindow = null;
                }
                map.remove(vehicleMarker);
                vehicleMarker = null;
            }
            
            // 停止播放
            stopPlayback();
        }
        
        // 清除轨迹
        function clearTrack() {
            console.log('清除轨迹...');
            
            // 清除地图对象
            clearMapObjects();
            
            // 隐藏控制器和统计信息
            $('#playback-controls').hide();
            $('#track-stats').hide();
            
            // 重置数据
            trackData = [];
            currentIndex = 0;
        }
        
        // 开始播放
        function startPlayback() {
            if (trackData.length === 0) return;
            
            isPlaying = true;
            $('#play-icon').text('⏸');
            
            startPlaybackInterval();
        }
        
        // 开始播放间隔
        function startPlaybackInterval() {
            const interval = Math.max(100, 1000 / playSpeed); // 最小100ms间隔
            
            playInterval = setInterval(() => {
                if (currentIndex < trackData.length - 1) {
                    currentIndex++;
                    updateVehiclePosition();
                    updatePlaybackInfo();
                } else {
                    // 播放完成
                    pausePlayback();
                }
            }, interval);
        }
        
        // 暂停播放
        function pausePlayback() {
            isPlaying = false;
            $('#play-icon').text('▶');
            
            if (playInterval) {
                clearInterval(playInterval);
                playInterval = null;
            }
        }
        
        // 停止播放
        function stopPlayback() {
            pausePlayback();
            setCurrentIndex(0);
        }
        
        // 设置当前索引
        function setCurrentIndex(index) {
            currentIndex = Math.max(0, Math.min(index, trackData.length - 1));
            updateVehiclePosition();
            updatePlaybackInfo();
        }
        
        // 创建车辆信息显示内容
        function createVehicleInfoContent(point) {
            const speed = (point.speed || 0).toFixed(1);
            const lng = (point.lng || 0).toFixed(6);
            const lat = (point.lat || 0).toFixed(6);
            const direction = point.direction || 0;
            const time = point.time || '';
            
            return `
                <div style="font-size: 12px; line-height: 1.5; min-width: 200px;">
                    <div style="font-weight: bold; color: #0066FF; margin-bottom: 5px;">
                        ${carName}${plateNum ? ' (' + plateNum + ')' : ''}
                    </div>
                    <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px;">
                        <span style="color: #0066FF;">速度：</span>
                        <span>${speed} km/h</span>
                    </div>
                    <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px;">
                        <span style="color: #0066FF;">经度：</span>
                        <span>${lng}</span>
                    </div>
                    <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px;">
                        <span style="color: #0066FF;">纬度：</span>
                        <span>${lat}</span>
                    </div>
                    <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px;">
                        <span style="color: #0066FF;">方向：</span>
                        <span>${direction}°</span>
                    </div>
                    <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px;">
                        <span style="color: #0066FF;">时间：</span>
                        <span>${formatTimeOnly(time)}</span>
                    </div>
                </div>
            `;
        }
        
        // 更新车辆位置
        function updateVehiclePosition() {
            if (!vehicleMarker || !trackData[currentIndex]) return;
            
            const point = trackData[currentIndex];
            
            // 验证坐标有效性
            if (!point.lng || !point.lat || isNaN(point.lng) || isNaN(point.lat)) {
                console.warn('当前轨迹点坐标无效:', point);
                return;
            }
            
            // 更新车辆位置和方向（参考secondScr_gaode.js中的marker.setAngle(car.direct)）
            vehicleMarker.setPosition(new AMap.LngLat(point.lng, point.lat));
            vehicleMarker.setAngle(point.direction || 0);  // 直接使用direction，与secondScr_gaode.js一致
            vehicleMarker.setTitle(`${carName} - ${point.time} - ${point.speed}km/h`);
            
            // 如果信息窗口已打开，更新其内容
            if (vehicleMarker.infoWindow && vehicleMarker.infoWindow.getIsOpen()) {
                const updatedContent = createVehicleInfoContent(point);
                vehicleMarker.infoWindow.setContent(updatedContent);
            }
            
            // 更新已走过的轨迹
            const passedPath = trackData.slice(0, currentIndex + 1)
                                      .filter(p => p && p.lng && p.lat && !isNaN(p.lng) && !isNaN(p.lat))
                                      .map(p => new AMap.LngLat(p.lng, p.lat));
            if (passedPolyline && passedPath.length > 0) {
                passedPolyline.setPath(passedPath);
            }
        }
        
        // 更新播放信息
        function updatePlaybackInfo() {
            if (trackData.length === 0) return;
            
            const progress = (currentIndex / (trackData.length - 1)) * 100;
            $('#progress-slider').val(progress);
            
            // 显示当前时间和总时长
            const currentPoint = trackData[currentIndex];
            const startTime = trackData[0].time;
            const endTime = trackData[trackData.length - 1].time;
            
            if (currentPoint && currentPoint.time) {
                // 格式化时间显示（显示日期和时分秒）
                const currentTimeStr = formatTimeOnly(currentPoint.time);
                const startTimeStr = formatTimeOnly(startTime);
                const endTimeStr = formatTimeOnly(endTime);
                
                $('#playback-info').text(`${currentTimeStr} (${startTimeStr} - ${endTimeStr})`);
            } else {
                // 降级显示索引
                $('#playback-info').text(`${currentIndex + 1} / ${trackData.length}`);
            }
        }
        
        // 格式化时间（显示日期和时分秒）
        function formatTimeOnly(timeStr) {
            try {
                const date = new Date(timeStr);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (e) {
                return timeStr; // 如果解析失败，返回原始字符串
            }
        }
        

        

        
        // 显示轨迹统计信息
        function showTrackStats() {
            if (trackData.length === 0) return;
            
            console.log('计算轨迹统计信息...');
            
            // 计算总里程
            let totalDistance = 0;
            for (let i = 1; i < trackData.length; i++) {
                const prev = trackData[i - 1];
                const curr = trackData[i];
                totalDistance += getDistance(prev.lat, prev.lng, curr.lat, curr.lng);
            }
            
            // 计算平均速度和最高速度
            const speeds = trackData.map(p => p.speed).filter(s => s > 0);
            const avgSpeed = speeds.length > 0 ? (speeds.reduce((a, b) => a + b, 0) / speeds.length) : 0;
            const maxSpeed = speeds.length > 0 ? Math.max(...speeds) : 0;
            
            // 计算时长
            const startTime = new Date(trackData[0].time);
            const endTime = new Date(trackData[trackData.length - 1].time);
            const duration = Math.round((endTime - startTime) / 1000 / 60); // 分钟
            
            // 更新统计信息
            $('#total-distance').text((totalDistance / 1000).toFixed(2) + ' km');
            $('#total-points').text(trackData.length);
            $('#avg-speed').text(avgSpeed.toFixed(1) + ' km/h');
            $('#max-speed').text(maxSpeed.toFixed(1) + ' km/h');
            $('#duration').text(duration + ' 分钟');
            
            // 显示统计面板
            $('#track-stats').show();
            
            console.log('统计信息显示完成');
        }
        
        // 更新查询完成后的车辆信息显示
        function updateVehicleInfoAfterQuery() {
            let infoText = `车辆: ${carName}${plateNum ? ' (' + plateNum + ')' : ''}`;
            
            // 显示使用的GPS设备信息
            const deviceNumber = gpsTerminal || reserveGpsTerminal;
            const deviceType = gpsTerminal ? '主GPS' : '备用GPS';
            
            if (deviceNumber) {
                infoText += ` - ${deviceType}设备: ${deviceNumber} - 轨迹数据已加载`;
            } else {
                infoText += ' - 轨迹数据已加载';
            }
            
            $('#vehicle-info').text(infoText);
        }
        
        // 格式化时间为API需要的格式
        function formatApiTime(timeStr) {
            return timeStr.replace(/[-:\s]/g, '');
        }
        
        // 格式化时间显示
        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 计算两点间距离（米）
        function getDistance(lat1, lng1, lat2, lng2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng / 2) * Math.sin(dLng / 2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            return R * c;
        }
        

    </script>
</body>
</html> 